#!/usr/bin/env python3
"""
追踪数据在哪个步骤丢失了列信息
"""

import sys
import os
from pathlib import Path
import pandas as pd
import scanpy as sc
import numpy as np

def trace_column_loss():
    """追踪列信息丢失的具体步骤"""
    
    print("🔍 追踪 adata.var.columns 丢失的步骤")
    print("=" * 60)
    
    test_file = "/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.h5ad"
    
    # 步骤1: 原始加载
    print("1️⃣ 原始加载:")
    adata = sc.read_h5ad(test_file)
    print(f"   var.columns: {list(adata.var.columns)}")
    print(f"   var.shape: {adata.var.shape}")
    
    # 步骤2: 模拟 safe_matrix_conversion
    print("\n2️⃣ safe_matrix_conversion:")
    from cellload_auto import safe_matrix_conversion
    adata.X = safe_matrix_conversion(adata.X, np.float32)
    print(f"   var.columns: {list(adata.var.columns)}")
    print(f"   var.shape: {adata.var.shape}")
    
    # 步骤3: 模拟归一化
    print("\n3️⃣ 归一化:")
    from cellload_auto import _normalize_rows_to_target_inplace
    adata.X = _normalize_rows_to_target_inplace(adata.X, 1e4)
    print(f"   var.columns: {list(adata.var.columns)}")
    print(f"   var.shape: {adata.var.shape}")
    
    # 步骤4: 模拟 filter_on_target_knockdown 调用前
    print("\n4️⃣ filter_on_target_knockdown 调用前:")
    print(f"   var.columns: {list(adata.var.columns)}")
    print(f"   var.shape: {adata.var.shape}")
    
    # 检查 filter_on_target_knockdown 函数内部
    print("\n5️⃣ 检查 filter_on_target_knockdown 内部:")
    
    # 导入并检查函数
    from data_filters import filter_on_target_knockdown
    
    # 创建一个简化的测试
    print("   准备调用 filter_on_target_knockdown...")
    
    try:
        # 只调用函数开始部分，不执行完整过滤
        print(f"   调用前 var.columns: {list(adata.var.columns)}")
        
        # 这里我们需要检查函数内部的数据传递
        # 让我们手动检查传递给函数的数据
        
    except Exception as e:
        print(f"   调用失败: {e}")

def check_filter_function_input():
    """检查传递给 filter_on_target_knockdown 的数据"""
    
    print("\n" + "="*60)
    print("检查传递给 filter_on_target_knockdown 的数据")
    print("="*60)
    
    # 我们需要在 cellload_auto.py 中添加调试信息
    # 来查看传递给 filter_on_target_knockdown 的数据状态
    
    print("建议在 cellload_auto.py 的 KD 调用前添加调试信息:")
    print("""
    # 在调用 filter_on_target_knockdown 之前添加:
    logger.info(f"[DEBUG] Before KD call - var.columns: {list(adata.var.columns)}")
    logger.info(f"[DEBUG] Before KD call - var.shape: {adata.var.shape}")
    """)

def main():
    """主函数"""
    
    print("🔬 追踪 var.columns 丢失问题")
    print("=" * 60)
    
    try:
        trace_column_loss()
        check_filter_function_input()
        
        print("\n🎯 初步结论:")
        print("需要在 cellload_auto.py 中添加更多调试信息")
        print("来确定数据在哪个步骤丢失了列信息")
        
    except Exception as e:
        print(f"❌ 追踪失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
