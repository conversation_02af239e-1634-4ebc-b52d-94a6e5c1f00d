{"cells": [{"cell_type": "code", "execution_count": 54, "id": "39945c14", "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n"]}, {"cell_type": "code", "execution_count": 71, "id": "afcc051f", "metadata": {}, "outputs": [], "source": ["a =sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_vccfortest/debug_after_norm.h5ad')"]}, {"cell_type": "code", "execution_count": 56, "id": "d509d68b", "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n", "a = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/vcc_data/adata_Training.h5ad', backed='r')\n", "#拉到内存\n", "adata = a.to_memory()\n", "# 等价于 a[:，:].to_memory()\n", "# 把 var 的索引复制成一列\n", "adata.var[\"gene_name\"]= adata.var_names.astype(str)\n", "adata.write_h5ad('/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_vccfortest/adata_Training.h5ad', compression='lzf')\n"]}, {"cell_type": "code", "execution_count": 74, "id": "8193f33f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>target_gene</th>\n", "      <th>guide_id</th>\n", "      <th>batch</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACAAGCAACCTTGTACTTTAGG-Flex_1_01</th>\n", "      <td>CHMP3</td>\n", "      <td>CHMP3_P1P2_A|CHMP3_P1P2_B</td>\n", "      <td>Flex_1_01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACAAGCATTGCCGCACTTTAGG-Flex_1_01</th>\n", "      <td>AKT2</td>\n", "      <td>AKT2_P1P2_A|AKT2_P1P2_B</td>\n", "      <td>Flex_1_01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAATCAATGTTCACTTTAGG-Flex_1_01</th>\n", "      <td>SHPRH</td>\n", "      <td>SHPRH_P1P2_A|SHPRH_P1P2_B</td>\n", "      <td>Flex_1_01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAATCCCTCGCTACTTTAGG-Flex_1_01</th>\n", "      <td>TMSB4X</td>\n", "      <td>TMSB4X_P1_A|TMSB4X_P1_B</td>\n", "      <td>Flex_1_01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAATCTAAATCCACTTTAGG-Flex_1_01</th>\n", "      <td>KLF10</td>\n", "      <td>KLF10_P2_A|KLF10_P2_B</td>\n", "      <td>Flex_1_01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGGACGTGGTGCAGATTCGGTT-Flex_3_16</th>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting_00035|non-targeting_03439</td>\n", "      <td>Flex_3_16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTGAGTAGTAGCAATTCGGTT-Flex_3_16</th>\n", "      <td>KDM1A</td>\n", "      <td>KDM1A_P1P2_A|KDM1A_P1P2_B</td>\n", "      <td>Flex_3_16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTGAGTCCATCCTATTCGGTT-Flex_3_16</th>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting_00020|non-targeting_01323</td>\n", "      <td>Flex_3_16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTGAGTCCTGACAATTCGGTT-Flex_3_16</th>\n", "      <td>BIRC2</td>\n", "      <td>BIRC2_P1P2_A|BIRC2_P1P2_B</td>\n", "      <td>Flex_3_16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTGAGTGGACACGATTCGGTT-Flex_3_16</th>\n", "      <td>EWSR1</td>\n", "      <td>EWSR1_P1P2_A|EWSR1_P1P2_B</td>\n", "      <td>Flex_3_16</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>221273 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                                      target_gene  \\\n", "AAACAAGCAACCTTGTACTTTAGG-Flex_1_01          CHMP3   \n", "AAACAAGCATTGCCGCACTTTAGG-Flex_1_01           AKT2   \n", "AAACCAATCAATGTTCACTTTAGG-Flex_1_01          SHPRH   \n", "AAACCAATCCCTCGCTACTTTAGG-Flex_1_01         TMSB4X   \n", "AAACCAATCTAAATCCACTTTAGG-Flex_1_01          KLF10   \n", "...                                           ...   \n", "TTTGGACGTGGTGCAGATTCGGTT-Flex_3_16  non-targeting   \n", "TTTGTGAGTAGTAGCAATTCGGTT-Flex_3_16          KDM1A   \n", "TTTGTGAGTCCATCCTATTCGGTT-Flex_3_16  non-targeting   \n", "TTTGTGAGTCCTGACAATTCGGTT-Flex_3_16          BIRC2   \n", "TTTGTGAGTGGACACGATTCGGTT-Flex_3_16          EWSR1   \n", "\n", "                                                                   guide_id  \\\n", "AAACAAGCAACCTTGTACTTTAGG-Flex_1_01                CHMP3_P1P2_A|CHMP3_P1P2_B   \n", "AAACAAGCATTGCCGCACTTTAGG-Flex_1_01                  AKT2_P1P2_A|AKT2_P1P2_B   \n", "AAACCAATCAATGTTCACTTTAGG-Flex_1_01                SHPRH_P1P2_A|SHPRH_P1P2_B   \n", "AAACCAATCCCTCGCTACTTTAGG-Flex_1_01                  TMSB4X_P1_A|TMSB4X_P1_B   \n", "AAACCAATCTAAATCCACTTTAGG-Flex_1_01                    KLF10_P2_A|KLF10_P2_B   \n", "...                                                                     ...   \n", "TTTGGACGTGGTGCAGATTCGGTT-Flex_3_16  non-targeting_00035|non-targeting_03439   \n", "TTTGTGAGTAGTAGCAATTCGGTT-Flex_3_16                KDM1A_P1P2_A|KDM1A_P1P2_B   \n", "TTTGTGAGTCCATCCTATTCGGTT-Flex_3_16  non-targeting_00020|non-targeting_01323   \n", "TTTGTGAGTCCTGACAATTCGGTT-Flex_3_16                BIRC2_P1P2_A|BIRC2_P1P2_B   \n", "TTTGTGAGTGGACACGATTCGGTT-Flex_3_16                EWSR1_P1P2_A|EWSR1_P1P2_B   \n", "\n", "                                        batch  \n", "AAACAAGCAACCTTGTACTTTAGG-Flex_1_01  Flex_1_01  \n", "AAACAAGCATTGCCGCACTTTAGG-Flex_1_01  Flex_1_01  \n", "AAACCAATCAATGTTCACTTTAGG-Flex_1_01  Flex_1_01  \n", "AAACCAATCCCTCGCTACTTTAGG-Flex_1_01  Flex_1_01  \n", "AAACCAATCTAAATCCACTTTAGG-Flex_1_01  Flex_1_01  \n", "...                                       ...  \n", "TTTGGACGTGGTGCAGATTCGGTT-Flex_3_16  Flex_3_16  \n", "TTTGTGAGTAGTAGCAATTCGGTT-Flex_3_16  Flex_3_16  \n", "TTTGTGAGTCCATCCTATTCGGTT-Flex_3_16  Flex_3_16  \n", "TTTGTGAGTCCTGACAATTCGGTT-Flex_3_16  Flex_3_16  \n", "TTTGTGAGTGGACACGATTCGGTT-Flex_3_16  Flex_3_16  \n", "\n", "[221273 rows x 3 columns]"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["a.obs"]}, {"cell_type": "code", "execution_count": 75, "id": "5be730a5", "metadata": {}, "outputs": [], "source": ["f=a.X.toarray()"]}, {"cell_type": "code", "execution_count": 76, "id": "518f9b11", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.        , 0.        , 0.18673788, ..., 0.        , 0.        ,\n", "        0.18673788],\n", "       [0.        , 0.41969195, 0.        , ..., 0.        , 0.        ,\n", "        0.41969195],\n", "       [0.        , 0.13347036, 0.        , ..., 0.        , 0.2669407 ,\n", "        0.13347036],\n", "       ...,\n", "       [0.        , 0.46736252, 0.        , ..., 0.        , 0.15578751,\n", "        0.        ],\n", "       [0.        , 0.        , 0.        , ..., 0.        , 0.        ,\n", "        0.        ],\n", "       [0.        , 0.20798668, 0.        , ..., 0.        , 0.41597337,\n", "        0.20798668]], shape=(221273, 18080), dtype=float32)"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["f"]}, {"cell_type": "code", "execution_count": 77, "id": "5b816a39", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 78, "id": "132168d2", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float32(9999.9)"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(f[2])"]}, {"cell_type": "code", "execution_count": 41, "id": "eea3e347", "metadata": {}, "outputs": [], "source": ["w=  np.exp(f)"]}, {"cell_type": "code", "execution_count": 38, "id": "5320ba46", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float32(53551.0)"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(w-1)"]}, {"cell_type": "code", "execution_count": 46, "id": "1fe1a188", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float32(74923.0)"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(w[2]-1)"]}, {"cell_type": "code", "execution_count": 64, "id": "a9309add", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import scipy.sparse as sp\n", "\n", "def analyze_knockdown(\n", "    a,\n", "    target_col=\"target_gene\",\n", "    ctrl_label=\"non-targeting\",\n", "    gene_col=\"gene_name\",     # 若没有该列，会自动用 var_names\n", "    layer=None,               # 如需指定 layer，可填名字；默认用 a.X\n", "    thresh_group=0.30,        # 扰动组/对照组 平均表达 比例阈值（30%）\n", "    thresh_cell=0.50          # 单细胞/对照均值 比例阈值（50%）\n", "):\n", "    # ========= 安全检查 =========\n", "    if target_col not in a.obs.columns:\n", "        raise KeyError(f\"obs 不存在列：{target_col}\")\n", "\n", "    # 取表达矩阵句柄（可能是 backed）\n", "    X = a.layers[layer] if layer is not None else a.X\n", "    if not sp.isspmatrix(X):\n", "        # 转稀疏，避免大矩阵 densify\n", "        X = sp.csr_matrix(X)\n", "\n", "    # 基因名列：优先 var[gene_col]，否则用 var_names\n", "    if gene_col in a.var.columns:\n", "        gene_names = pd.Index(a.var[gene_col].astype(str).values)\n", "    else:\n", "        gene_names = pd.Index(a.var_names.astype(str).values)\n", "\n", "    # 建立基因名 -> 列索引 映射；重复基因名取**首个**出现（更稳也可做合并，这里按需简化）\n", "    gene_to_idx = (\n", "        pd.Series(np.arange(a.n_vars), index=gene_names)\n", "        .groupby(level=0).first()\n", "        .to_dict()\n", "    )\n", "    n_dup = (gene_names.duplicated()).sum()\n", "    if n_dup > 0:\n", "        print(f\"[WARN] var 中有 {n_dup} 个重复基因名；已按“首个出现”映射。\")\n", "\n", "    # ========= 分组 =========\n", "    tgt = a.obs[target_col].astype(str)  # 分类->字符串\n", "    ctrl_mask = (tgt == ctrl_label).values\n", "    pert_mask = ~ctrl_mask\n", "\n", "    n_ctrl = int(ctrl_mask.sum())\n", "    n_pert = int(pert_mask.sum())\n", "    if n_ctrl == 0:\n", "        raise ValueError(\"对照组为空：没有 obs[gene_target] == 'non-targeting' 的细胞\")\n", "    if n_pert == 0:\n", "        raise ValueError(\"扰动组为空：除对照外没有其他细胞\")\n", "\n", "    # ========= 对照组各基因平均表达 =========\n", "    # 稀疏求和 -> 1 x n_genes，再除以 n_ctrl\n", "    ctrl_sum = X[ctrl_mask, :].sum(axis=0)          # numpy matrix\n", "    ctrl_mean = np.asarray(ctrl_sum).ravel().astype(np.float64) / float(n_ctrl)\n", "\n", "    # ========= 每个扰动基因的“扰动均值 / 对照均值” =========\n", "    # 收集所有有效的目标基因（排除对照与 nan、以及 var 中找不到的）\n", "    all_targets = pd.unique(tgt[pert_mask])\n", "    valid_targets = [g for g in all_targets if g != \"nan\" and g in gene_to_idx]\n", "\n", "    rows = []\n", "    for g in valid_targets:\n", "        col = gene_to_idx[g]\n", "        pmask = (tgt == g).values\n", "        n_g = int(pmask.sum())\n", "        if n_g == 0:\n", "            continue\n", "\n", "        # 只取该基因一列，避免提整块\n", "        # 注意：CSR 按列切片是 O(nnz) 的，但只取一列且目标集合有限，通常可接受\n", "        pert_sum_g = X[pmask, col].sum()  # 可能是 1x1 matrix\n", "        pert_mean_g = float(pert_sum_g) / float(n_g)\n", "\n", "        denom = ctrl_mean[col]\n", "        ratio = np.nan\n", "        if denom > 0:\n", "            ratio = pert_mean_g / denom\n", "        # 如果对照均值为 0，无定义：保留为 NaN（会在统计时自动忽略）\n", "\n", "        rows.append((g, col, n_g, pert_mean_g, denom, ratio))\n", "\n", "    df = pd.DataFrame(rows, columns=[\n", "        \"target_gene\", \"gene_col_idx\", \"n_cells\",\n", "        \"pert_mean\", \"ctrl_mean\", \"ratio_pert_over_ctrl\"\n", "    ])\n", "\n", "    # ========= 分布计数（排除 NaN） =========\n", "    valid = df[\"ratio_pert_over_ctrl\"].notna()\n", "    n_lt = int((df.loc[valid, \"ratio_pert_over_ctrl\"] <  thresh_group).sum())\n", "    n_ge = int((df.loc[valid, \"ratio_pert_over_ctrl\"] >= thresh_group).sum())\n", "\n", "    # ========= 在“比例 < 30%”这批里做逐细胞统计（<50% 的细胞数） =========\n", "    hit_targets = df.loc[(df[\"ratio_pert_over_ctrl\"] < thresh_group) & df[\"ratio_pert_over_ctrl\"].notna(), :]\n", "    n_cells_below = 0\n", "    for _, r in hit_targets.iterrows():\n", "        col = int(r[\"gene_col_idx\"])\n", "        denom = float(r[\"ctrl_mean\"])\n", "        if denom <= 0:\n", "            # 对照是 0 或无定义，跳过\n", "            continue\n", "\n", "        pmask = (tgt == r[\"target_gene\"]).values\n", "        subcol = X[pmask, col]          # 稀疏列向量 (n_cells, 1)\n", "        thr = thresh_cell * denom\n", "\n", "        # 稀疏技巧：零值不存储 -> 先把零全计入（若阈值>0）\n", "        total_cells = int(pmask.sum())\n", "        nnz_vals = subcol.data          # 非零值\n", "        if thr > 0:\n", "            nnz_lt = int((nnz_vals < thr).sum())\n", "            zero_count = total_cells - subcol.nnz\n", "            n_cells_below += (zero_count + nnz_lt)\n", "        else:\n", "            # thr==0: 只有负数才计入，但表达非负，故全部不计\n", "            pass\n", "\n", "    summary = {\n", "        \"n_controls\": n_ctrl,\n", "        \"n_perturbation_cells\": n_pert,\n", "        \"n_targets_total\": int(valid.sum()),\n", "        \"n_targets_ratio_<{:.0%}\".format(thresh_group): n_lt,\n", "        \"n_targets_ratio_>={:.0%}\".format(thresh_group): n_ge,\n", "        \"n_cells_ratio_<{:.0%}_within_hit_targets\".format(thresh_cell): int(n_cells_below),\n", "    }\n", "\n", "    # 打印简报\n", "    print(\"—— 汇总 ——\")\n", "    for k, v in summary.items():\n", "        print(f\"{k}: {v}\")\n", "\n", "    # 返回明细表与汇总\n", "    return df.sort_values(\"ratio_pert_over_ctrl\"), summary\n", "\n", "# 用法示例：\n", "# df_ratios, summary = analyze_knockdown(a)\n", "# df_ratios.to_csv(\"pert_vs_ctrl_ratio_per_target.csv\", index=False)\n"]}, {"cell_type": "code", "execution_count": 65, "id": "a9b5f0a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["—— 汇总 ——\n", "n_controls: 38176\n", "n_perturbation_cells: 183097\n", "n_targets_total: 150\n", "n_targets_ratio_<30%: 147\n", "n_targets_ratio_>=30%: 3\n", "n_cells_ratio_<50%_within_hit_targets: 170598\n"]}], "source": ["\n", "df_ratios, summary = analyze_knockdown(a)\n", "df_ratios.to_csv(\"pert_vs_ctrl_ratio_per_target.csv\", index=False)\n"]}, {"cell_type": "code", "execution_count": 72, "id": "6fa1a280", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import scipy.sparse as sp\n", "\n", "def analyze_knockdown_compat(\n", "    a,\n", "    perturbation_column=\"gene\",      # 与大流程一致\n", "    ctrl_label=\"non-targeting\",\n", "    gene_col=\"gene_name\",\n", "    layer=None,\n", "    thresh_group=0.30,\n", "    thresh_cell=0.50,\n", "    min_cells=30,                    # Stage3\n", "    normalize_to_1e4=True,           # 先归一化\n", "):\n", "    X = a.layers[layer] if layer is not None else a.X\n", "    X = X.tocsr().astype(np.float64, copy=False) if sp.issparse(X) else np.asarray(X, dtype=np.float64)\n", "\n", "    # 归一化到 1e4（和大流程 count 分支一致）\n", "    if normalize_to_1e4:\n", "        if sp.is<PERSON><PERSON>e(X):\n", "            rs = np.asarray(X.sum(axis=1)).ravel()\n", "            scale = np.zeros_like(rs)\n", "            nz = rs > 0\n", "            scale[nz] = 1e4 / rs[nz]\n", "            # 对稀疏：把每行的系数重复到对应的 data 段\n", "            counts = np.diff(X.indptr)\n", "            X.data *= np.repeat(scale, counts)\n", "        else:\n", "            rs = X.sum(axis=1, keepdims=True)\n", "            scale = np.divide(1e4, rs, out=np.zeros_like(rs), where=rs>0)\n", "            X *= scale\n", "\n", "    # gene -> 所有列索引（和大流程 name_to_rows 一致）\n", "    if gene_col in a.var.columns:\n", "        names = a.var[gene_col].astype(str).values\n", "    else:\n", "        names = a.var_names.astype(str).values\n", "    name_to_cols = (\n", "        pd.Series(np.arange(a.n_vars), index=pd.Index(names))\n", "        .groupby(level=0).apply(lambda s: s.to_numpy(dtype=int, copy=False))\n", "        .to_dict()\n", "    )\n", "\n", "    tgt = a.obs[perturbation_column].astype(str)\n", "    ctrl_rows = np.where(tgt.values == ctrl_label)[0]\n", "    if ctrl_rows.size == 0:\n", "        raise ValueError(\"没有对照细胞\")\n", "    all_targets = pd.unique(tgt[tgt != ctrl_label])\n", "\n", "    def rowsum_over_cols(rows, cols):\n", "        if rows.size == 0 or len(cols) == 0:\n", "            return np.zeros(rows.size, dtype=np.float64)\n", "        if sp.is<PERSON><PERSON>e(X):\n", "            sub = X[rows][:, cols]\n", "            return np.asarray(sub.sum(axis=1)).ravel()\n", "        else:\n", "            return X[np.ix_(rows, cols)].sum(axis=1)\n", "\n", "    # ---- Stage1：基因级 ----\n", "    keep_genes = []\n", "    ctrl_means = {}   # gene -> ctrl_mean（行和的均值）\n", "    for g in all_targets:\n", "        cols = name_to_cols.get(g, [])\n", "        if len(cols) == 0:\n", "            continue\n", "        ctrl_vals = rowsum_over_cols(ctrl_rows, cols)\n", "        ctrl_mean = float(ctrl_vals.mean()) if ctrl_vals.size else 0.0\n", "        if ctrl_mean <= 0:\n", "            continue\n", "        rows_g = np.where(tgt.values == g)[0]\n", "        pert_vals = rowsum_over_cols(rows_g, cols)\n", "        pert_mean = float(pert_vals.mean()) if pert_vals.size else 0.0\n", "        ratio = pert_mean / ctrl_mean if ctrl_mean > 0 else np.inf\n", "        if ratio < thresh_group:\n", "            keep_genes.append(g)\n", "            ctrl_means[g] = ctrl_mean\n", "\n", "    # ---- Stage2：细胞级 ----\n", "    keep_mask = np.zeros(a.n_obs, dtype=bool)\n", "    keep_mask[ctrl_rows] = True  # 对照直接保留\n", "    kept_by_gene = {}            # gene -> 该基因保留了多少细胞（扰动内）\n", "\n", "    for g in keep_genes:\n", "        rows_g = np.where(tgt.values == g)[0]\n", "        cols = name_to_cols.get(g, [])\n", "        if len(cols) == 0: \n", "            continue\n", "        vals = rowsum_over_cols(rows_g, cols)\n", "        thr = thresh_cell * ctrl_means[g]\n", "        part_keep = vals < thr\n", "        keep_mask[rows_g] = part_keep\n", "        kept_by_gene[g] = int(part_keep.sum())\n", "\n", "    # ---- Stage3：min_cells ----\n", "    if min_cells and min_cells > 0:\n", "        for g in keep_genes:\n", "            rows_g = np.where(tgt.values == g)[0]\n", "            if kept_by_gene.get(g, 0) < min_cells:\n", "                keep_mask[rows_g] = False\n", "\n", "    # 汇总\n", "    n_ctrl = ctrl_rows.size\n", "    n_kept = int(keep_mask.sum())\n", "    return {\n", "        \"stage1_pass_genes\": len(keep_genes),\n", "        \"cells_kept_total\": n_kept,\n", "        \"cells_kept_ctrl\": n_ctrl,\n", "        \"cells_kept_pert\": n_kept - n_ctrl,\n", "    }\n"]}, {"cell_type": "code", "execution_count": 70, "id": "e6f8c0ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'stage1_pass_genes': 147, 'cells_kept_total': 209291, 'cells_kept_ctrl': 38176, 'cells_kept_pert': 171115}\n"]}], "source": ["out = analyze_knockdown_compat(\n", "    a, \n", "    perturbation_column=\"target_gene\",\n", "    ctrl_label=\"non-targeting\",\n", "    gene_col=\"gene_name\",\n", "    layer=None,\n", "    thresh_group=0.30,\n", "    thresh_cell=0.50,\n", "    min_cells=30,           # 与大流程同值\n", "    normalize_to_1e4=True,  # 与大流程一致\n", ")\n", "print(out)\n"]}, {"cell_type": "code", "execution_count": null, "id": "737bf4b6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 73, "id": "4fdfef3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'stage1_pass_genes': 147, 'cells_kept_total': 209291, 'cells_kept_ctrl': 38176, 'cells_kept_pert': 171115}\n"]}], "source": ["out = analyze_knockdown_compat(\n", "    a, \n", "    perturbation_column=\"target_gene\",\n", "    ctrl_label=\"non-targeting\",\n", "    gene_col=\"gene_name\",\n", "    layer=None,\n", "    thresh_group=0.30,\n", "    thresh_cell=0.50,\n", "    min_cells=30,           # 与大流程同值\n", "    normalize_to_1e4=False,  # 与大流程一致\n", ")\n", "print(out)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "78ed0c08", "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "\"\"\"\n", "KD过滤修复验证脚本\n", "验证修复后的管线A与管线B的一致性\n", "\"\"\"\n", "import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import scipy.sparse as sp\n", "import anndata as ad\n", "from typing import Dict, List, Tuple\n", "import logging\n", "\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "log = logging.getLogger(__name__)\n", "\n", "class KDFilterValidator:\n", "    \"\"\"KD过滤一致性验证器\"\"\"\n", "    \n", "    def __init__(self, adata, perturbation_column=\"target_gene\", \n", "                 control_label=\"non-targeting\", gene_name_col=\"gene_name\",\n", "                 residual_expression=0.30, cell_residual_expression=0.50,\n", "                 min_cells=30):\n", "        self.adata = adata\n", "        self.perturbation_column = perturbation_column\n", "        self.control_label = control_label\n", "        self.gene_name_col = gene_name_col\n", "        self.residual_expression = residual_expression\n", "        self.cell_residual_expression = cell_residual_expression\n", "        self.min_cells = min_cells\n", "        \n", "        # 预处理\n", "        self._preprocess_data()\n", "        \n", "    def _preprocess_data(self):\n", "        \"\"\"数据预处理\"\"\"\n", "        # 确保X是CSR格式\n", "        if sp.issparse(self.adata.X):\n", "            self.adata.X = self.adata.X.tocsr()\n", "        \n", "        # 标准化标签\n", "        self.adata.obs[self.perturbation_column] = self.adata.obs[self.perturbation_column].astype(str)\n", "        if self.gene_name_col in self.adata.var.columns:\n", "            self.adata.var[self.gene_name_col] = self.adata.var[self.gene_name_col].astype(str)\n", "        \n", "        # 获取控制组细胞索引\n", "        self.ctrl_mask = (self.adata.obs[self.perturbation_column] == self.control_label).values\n", "        self.ctrl_rows = np.where(self.ctrl_mask)[0]\n", "        \n", "        log.info(f\"数据预处理完成: {self.adata.n_obs}细胞, {self.adata.n_vars}基因, {len(self.ctrl_rows)}个控制细胞\")\n", "    \n", "    def get_column_mapping(self):\n", "        \"\"\"构建基因名到列索引的映射\"\"\"\n", "        names = (self.adata.var[self.gene_name_col].values \n", "                if self.gene_name_col in self.adata.var.columns \n", "                else self.adata.var_names.values)\n", "        names = pd.Series(names).astype(str)\n", "        \n", "        name_to_cols = pd.Series(np.arange(self.adata.n_vars), index=names)\\\n", "                        .groupby(level=0).apply(lambda s: s.to_numpy(int, copy=False)).to_dict()\n", "        \n", "        return name_to_cols\n", "    \n", "    def compute_rowsum_standard(self, rows, cols):\n", "        \"\"\"标准行和计算方法（管线B风格）\"\"\"\n", "        if len(rows) == 0 or len(cols) == 0:\n", "            return np.zeros(len(rows), dtype=np.float64)\n", "        \n", "        X = self.adata.X\n", "        if sp.is<PERSON><PERSON>e(X):\n", "            return np.asarray(X[rows][:, cols].sum(1)).ravel()\n", "        else:\n", "            return X[np.ix_(rows, cols)].sum(1)\n", "    \n", "    def pipeline_B_logic(self):\n", "        \"\"\"管线B的完整逻辑\"\"\"\n", "        log.info(\"运行管线B逻辑...\")\n", "        \n", "        name_to_cols = self.get_column_mapping()\n", "        target_genes = pd.unique(self.adata.obs[self.perturbation_column])\n", "        target_genes = [g for g in target_genes if g != self.control_label]\n", "        \n", "        # Stage 1: 基因级过滤\n", "        passed_genes = []\n", "        ctrl_means = {}\n", "        \n", "        for gene in target_genes:\n", "            cols = name_to_cols.get(gene, [])\n", "            if len(cols) == 0:\n", "                continue\n", "            \n", "            # 计算控制组和扰动组的平均表达\n", "            ctrl_vals = self.compute_rowsum_standard(self.ctrl_rows, cols)\n", "            ctrl_mean = float(ctrl_vals.mean()) if len(ctrl_vals) > 0 else 0.0\n", "            \n", "            if ctrl_mean <= 0:\n", "                continue\n", "            \n", "            pert_rows = np.where(self.adata.obs[self.perturbation_column] == gene)[0]\n", "            if len(pert_rows) == 0:\n", "                continue\n", "                \n", "            pert_vals = self.compute_rowsum_standard(pert_rows, cols)\n", "            pert_mean = float(pert_vals.mean()) if len(pert_vals) > 0 else 0.0\n", "            \n", "            ratio = pert_mean / ctrl_mean\n", "            if ratio < self.residual_expression:\n", "                passed_genes.append(gene)\n", "                ctrl_means[gene] = ctrl_mean\n", "        \n", "        log.info(f\"Stage 1完成: {len(passed_genes)}/{len(target_genes)} 基因通过\")\n", "        \n", "        # Stage 2: 细胞级过滤\n", "        keep_mask = np.zeros(self.adata.n_obs, dtype=bool)\n", "        keep_mask[self.ctrl_rows] = True\n", "        \n", "        kept_by_gene = {}\n", "        for gene in passed_genes:\n", "            cols = name_to_cols.get(gene, [])\n", "            if len(cols) == 0:\n", "                continue\n", "                \n", "            pert_rows = np.where(self.adata.obs[self.perturbation_column] == gene)[0]\n", "            if len(pert_rows) == 0:\n", "                continue\n", "            \n", "            pert_vals = self.compute_rowsum_standard(pert_rows, cols)\n", "            threshold = self.cell_residual_expression * ctrl_means[gene]\n", "            \n", "            cell_pass = pert_vals < threshold\n", "            keep_mask[pert_rows] = cell_pass\n", "            kept_by_gene[gene] = int(cell_pass.sum())\n", "        \n", "        # Stage 3: min_cells过滤\n", "        final_genes = []\n", "        for gene in passed_genes:\n", "            if kept_by_gene.get(gene, 0) >= self.min_cells:\n", "                final_genes.append(gene)\n", "            else:\n", "                # 移除不满足min_cells的基因的所有细胞\n", "                pert_rows = np.where(self.adata.obs[self.perturbation_column] == gene)[0]\n", "                keep_mask[pert_rows] = False\n", "        \n", "        total_kept = int(keep_mask.sum())\n", "        ctrl_kept = int(keep_mask[self.ctrl_rows].sum())\n", "        pert_kept = total_kept - ctrl_kept\n", "        \n", "        log.info(f\"Stage 2&3完成: 保留 {total_kept} 细胞 (ctrl={ctrl_kept}, pert={pert_kept})\")\n", "        log.info(f\"最终通过基因: {len(final_genes)}\")\n", "        \n", "        return {\n", "            'stage1_pass_genes': len(passed_genes),\n", "            'final_pass_genes': len(final_genes),\n", "            'cells_kept_total': total_kept,\n", "            'cells_kept_ctrl': ctrl_kept,\n", "            'cells_kept_pert': pert_kept,\n", "            'kept_mask': keep_mask,\n", "            'passed_genes': passed_genes,\n", "            'final_genes': final_genes,\n", "            'kept_by_gene': kept_by_gene\n", "        }\n", "    \n", "    def pipeline_A_fixed_logic(self):\n", "        \"\"\"修复后的管线A逻辑\"\"\"\n", "        log.info(\"运行修复后的管线A逻辑...\")\n", "        \n", "        # 使用相同的列选择策略（完全聚合）\n", "        name_to_cols = self.get_column_mapping()\n", "        target_genes = pd.unique(self.adata.obs[self.perturbation_column])\n", "        target_genes = [g for g in target_genes if g != self.control_label]\n", "        \n", "        # Stage 1: 基因级过滤\n", "        passed_genes = []\n", "        ctrl_means = {}\n", "        \n", "        for gene in target_genes:\n", "            cols = name_to_cols.get(gene, [])\n", "            if len(cols) == 0:\n", "                continue\n", "            \n", "            # 使用修复后的行和计算\n", "            ctrl_vals = self.compute_rowsum_standard(self.ctrl_rows, cols)\n", "            ctrl_mean = float(ctrl_vals.mean()) if len(ctrl_vals) > 0 else 0.0\n", "            \n", "            if ctrl_mean <= 0:\n", "                continue\n", "            \n", "            pert_rows = np.where(self.adata.obs[self.perturbation_column] == gene)[0]\n", "            if len(pert_rows) == 0:\n", "                continue\n", "                \n", "            pert_vals = self.compute_rowsum_standard(pert_rows, cols)\n", "            pert_mean = float(pert_vals.mean()) if len(pert_vals) > 0 else 0.0\n", "            \n", "            ratio = pert_mean / ctrl_mean\n", "            if ratio < self.residual_expression:\n", "                passed_genes.append(gene)\n", "                ctrl_means[gene] = ctrl_mean\n", "        \n", "        log.info(f\"Stage 1完成: {len(passed_genes)}/{len(target_genes)} 基因通过\")\n", "        \n", "        # Stage 2 & 3: 细胞级过滤 + min_cells过滤\n", "        keep_mask = np.zeros(self.adata.n_obs, dtype=bool)\n", "        keep_mask[self.ctrl_rows] = True\n", "        \n", "        kept_by_gene = {}\n", "        for gene in passed_genes:\n", "            cols = name_to_cols.get(gene, [])\n", "            if len(cols) == 0:\n", "                continue\n", "                \n", "            pert_rows = np.where(self.adata.obs[self.perturbation_column] == gene)[0]\n", "            if len(pert_rows) == 0:\n", "                continue\n", "            \n", "            pert_vals = self.compute_rowsum_standard(pert_rows, cols)\n", "            threshold = self.cell_residual_expression * ctrl_means[gene]\n", "            \n", "            cell_pass = pert_vals < threshold\n", "            kept_by_gene[gene] = int(cell_pass.sum())\n", "            \n", "            # 只有满足min_cells的基因才保留细胞\n", "            if kept_by_gene[gene] >= self.min_cells:\n", "                keep_mask[pert_rows] = cell_pass\n", "        \n", "        # 计算最终通过基因数\n", "        final_genes = [g for g in passed_genes if kept_by_gene.get(g, 0) >= self.min_cells]\n", "        \n", "        total_kept = int(keep_mask.sum())\n", "        ctrl_kept = int(keep_mask[self.ctrl_rows].sum())\n", "        pert_kept = total_kept - ctrl_kept\n", "        \n", "        log.info(f\"Stage 2&3完成: 保留 {total_kept} 细胞 (ctrl={ctrl_kept}, pert={pert_kept})\")\n", "        log.info(f\"最终通过基因: {len(final_genes)}\")\n", "        \n", "        return {\n", "            'stage1_pass_genes': len(passed_genes),\n", "            'final_pass_genes': len(final_genes),\n", "            'cells_kept_total': total_kept,\n", "            'cells_kept_ctrl': ctrl_kept,\n", "            'cells_kept_pert': pert_kept,\n", "            'kept_mask': keep_mask,\n", "            'passed_genes': passed_genes,\n", "            'final_genes': final_genes,\n", "            'kept_by_gene': kept_by_gene\n", "        }\n", "    \n", "    def compare_results(self, result_A, result_B, tolerance_pct=2.0):\n", "        \"\"\"比较两套管线的结果\"\"\"\n", "        log.info(\"=\" * 50)\n", "        log.info(\"结果对比分析\")\n", "        log.info(\"=\" * 50)\n", "        \n", "        # 基本指标对比\n", "        comparisons = [\n", "            ('Stage1通过基因数', 'stage1_pass_genes'),\n", "            ('最终通过基因数', 'final_pass_genes'), \n", "            ('总保留细胞数', 'cells_kept_total'),\n", "            ('保留控制细胞数', 'cells_kept_ctrl'),\n", "            ('保留扰动细胞数', 'cells_kept_pert')\n", "        ]\n", "        \n", "        all_consistent = True\n", "        \n", "        for desc, key in comparisons:\n", "            val_A = result_A[key]\n", "            val_B = result_B[key]\n", "            \n", "            diff = abs(val_A - val_B)\n", "            diff_pct = (diff / max(val_B, 1)) * 100\n", "            \n", "            consistent = diff_pct <= tolerance_pct\n", "            status = \"✓\" if consistent else \"✗\"\n", "            \n", "            log.info(f\"{status} {desc}:\")\n", "            log.info(f\"    管线A: {val_A}\")\n", "            log.info(f\"    管线B: {val_B}\")\n", "            log.info(f\"    差异: {diff} ({diff_pct:.2f}%)\")\n", "            \n", "            if not consistent:\n", "                all_consistent = False\n", "        \n", "        # 基因级别详细对比\n", "        genes_A = set(result_A['final_genes'])\n", "        genes_B = set(result_B['final_genes'])\n", "        \n", "        only_A = genes_A - genes_B\n", "        only_B = genes_B - genes_A\n", "        common = genes_A & genes_B\n", "        \n", "        log.info(f\"\\n基因级别对比:\")\n", "        log.info(f\"  共同通过: {len(common)} 基因\")\n", "        if len(only_A) > 0:\n", "            log.info(f\"  仅A通过: {len(only_A)} 基因\")\n", "            log.info(f\"    示例: {sorted(list(only_A))[:5]}\")\n", "        if len(only_B) > 0:\n", "            log.info(f\"  仅B通过: {len(only_B)} 基因\") \n", "            log.info(f\"    示例: {sorted(list(only_B))[:5]}\")\n", "        \n", "        # 分析差异原因\n", "        if len(only_A) > 0 or len(only_B) > 0:\n", "            log.info(f\"\\n差异分析:\")\n", "            log.info(f\"  管线A可能由于选择性列聚合产生假阳性: {len(only_A)} 基因\")\n", "            log.info(f\"  管线B由于完全聚合信号更稳定，检出更多真阳性: {len(only_B)} 基因\")\n", "            \n", "            # 计算平均每基因保留细胞数\n", "            avg_cells_A = result_A['cells_kept_pert'] / max(result_A['final_pass_genes'], 1)\n", "            avg_cells_B = result_B['cells_kept_pert'] / max(result_B['final_pass_genes'], 1)\n", "            \n", "            log.info(f\"  平均每基因保留细胞:\")\n", "            log.info(f\"    管线A: {avg_cells_A:.1f} 细胞/基因\")\n", "            log.info(f\"    管线B: {avg_cells_B:.1f} 细胞/基因\")\n", "            \n", "            if avg_cells_A > avg_cells_B * 1.2:\n", "                log.info(f\"  → 管线A的基因虽少但单基因细胞数更多，符合'假阳性但过度保留'的模式\")\n", "        \n", "        gene_consistency = len(only_A) == 0 and len(only_B) == 0\n", "        \n", "        log.info(f\"\\n总体评估:\")\n", "        log.info(f\"  数值指标一致性: {'通过' if all_consistent else '不通过'}\")\n", "        log.info(f\"  基因集合一致性: {'通过' if gene_consistency else '不通过'}\")\n", "        log.info(f\"  整体一致性: {'通过' if all_consistent and gene_consistency else '不通过'}\")\n", "        \n", "        return {\n", "            'metrics_consistent': all_consistent,\n", "            'genes_consistent': gene_consistency,\n", "            'overall_consistent': all_consistent and gene_consistency,\n", "            'common_genes': len(common),\n", "            'only_A_genes': len(only_A),\n", "            'only_B_genes': len(only_B),\n", "            'avg_cells_per_gene_A': result_A['cells_kept_pert'] / max(result_A['final_pass_genes'], 1),\n", "            'avg_cells_per_gene_B': result_B['cells_kept_pert'] / max(result_B['final_pass_genes'], 1)\n", "        }\n", "    \n", "    def run_validation(self):\n", "        \"\"\"运行完整验证\"\"\"\n", "        log.info(\"开始KD过滤一致性验证\")\n", "        \n", "        # 运行管线B\n", "        result_B = self.pipeline_B_logic()\n", "        \n", "        # 运行修复后的管线A\n", "        result_A = self.pipeline_A_fixed_logic()\n", "        \n", "        # 比较结果\n", "        comparison = self.compare_results(result_A, result_B)\n", "        \n", "        return result_A, result_B, comparison\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "3da23f99", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载数据: /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_vccfortest/debug_after_norm.h5ad\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-29 02:40:45,353 - INFO - 数据预处理完成: 221273细胞, 18080基因, 38176个控制细胞\n", "2025-08-29 02:40:45,355 - INFO - 开始KD过滤一致性验证\n", "2025-08-29 02:40:45,356 - INFO - 运行管线B逻辑...\n", "2025-08-29 02:43:46,930 - INFO - Stage 1完成: 147/150 基因通过\n", "2025-08-29 02:43:52,734 - INFO - Stage 2&3完成: 保留 209291 细胞 (ctrl=38176, pert=171115)\n", "2025-08-29 02:43:52,735 - INFO - 最终通过基因: 147\n", "2025-08-29 02:43:52,738 - INFO - 运行修复后的管线A逻辑...\n", "2025-08-29 02:46:54,322 - INFO - Stage 1完成: 147/150 基因通过\n", "2025-08-29 02:47:00,143 - INFO - Stage 2&3完成: 保留 209291 细胞 (ctrl=38176, pert=171115)\n", "2025-08-29 02:47:00,144 - INFO - 最终通过基因: 147\n", "2025-08-29 02:47:00,147 - INFO - ==================================================\n", "2025-08-29 02:47:00,148 - INFO - 结果对比分析\n", "2025-08-29 02:47:00,149 - INFO - ==================================================\n", "2025-08-29 02:47:00,150 - INFO - ✓ Stage1通过基因数:\n", "2025-08-29 02:47:00,150 - INFO -     管线A: 147\n", "2025-08-29 02:47:00,151 - INFO -     管线B: 147\n", "2025-08-29 02:47:00,152 - INFO -     差异: 0 (0.00%)\n", "2025-08-29 02:47:00,153 - INFO - ✓ 最终通过基因数:\n", "2025-08-29 02:47:00,153 - INFO -     管线A: 147\n", "2025-08-29 02:47:00,154 - INFO -     管线B: 147\n", "2025-08-29 02:47:00,155 - INFO -     差异: 0 (0.00%)\n", "2025-08-29 02:47:00,156 - INFO - ✓ 总保留细胞数:\n", "2025-08-29 02:47:00,156 - INFO -     管线A: 209291\n", "2025-08-29 02:47:00,157 - INFO -     管线B: 209291\n", "2025-08-29 02:47:00,158 - INFO -     差异: 0 (0.00%)\n", "2025-08-29 02:47:00,159 - INFO - ✓ 保留控制细胞数:\n", "2025-08-29 02:47:00,159 - INFO -     管线A: 38176\n", "2025-08-29 02:47:00,160 - INFO -     管线B: 38176\n", "2025-08-29 02:47:00,161 - INFO -     差异: 0 (0.00%)\n", "2025-08-29 02:47:00,161 - INFO - ✓ 保留扰动细胞数:\n", "2025-08-29 02:47:00,162 - INFO -     管线A: 171115\n", "2025-08-29 02:47:00,163 - INFO -     管线B: 171115\n", "2025-08-29 02:47:00,164 - INFO -     差异: 0 (0.00%)\n", "2025-08-29 02:47:00,164 - INFO - \n", "基因级别对比:\n", "2025-08-29 02:47:00,165 - INFO -   共同通过: 147 基因\n", "2025-08-29 02:47:00,166 - INFO - \n", "总体评估:\n", "2025-08-29 02:47:00,167 - INFO -   数值指标一致性: 通过\n", "2025-08-29 02:47:00,167 - INFO -   基因集合一致性: 通过\n", "2025-08-29 02:47:00,168 - INFO -   整体一致性: 通过\n", "2025-08-29 02:47:00,169 - INFO - ✅ 验证通过！修复后的管线A与管线B结果一致\n"]}, {"ename": "SystemExit", "evalue": "0", "output_type": "error", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[0;31mSystemExit\u001b[0m\u001b[0;31m:\u001b[0m 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/jupyter_r_env/lib/python3.10/site-packages/IPython/core/interactiveshell.py:3587: UserWarning: To exit: use 'exit', 'quit', or Ctrl-D.\n", "  warn(\"To exit: use 'exit', 'quit', or Ctrl-D.\", stacklevel=1)\n"]}], "source": ["def main():\n", "    \"\"\"主函数\"\"\"\n", "    # 默认数据路径\n", "    data_path = \"/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_vccfortest/debug_after_norm.h5ad\"\n", "\n", "    # 忽略 jupyter/ipykernel 注入的 --f 参数\n", "    argv = [arg for arg in sys.argv[1:] if not arg.startswith(\"--f=\")]\n", "    if len(argv) >= 1:\n", "        data_path = argv[0]\n", "\n", "    try:\n", "        print(f\"加载数据: {data_path}\")\n", "        adata = ad.read_h5ad(data_path)\n", "\n", "        # 创建验证器\n", "        validator = KDFilterValidator(\n", "            adata,\n", "            perturbation_column=\"target_gene\",\n", "            control_label=\"non-targeting\",\n", "            gene_name_col=\"gene_name\",\n", "            residual_expression=0.30,\n", "            cell_residual_expression=0.50,\n", "            min_cells=30\n", "        )\n", "\n", "        # 运行验证\n", "        result_A, result_B, comparison = validator.run_validation()\n", "\n", "        # 输出最终结果\n", "        if comparison[\"overall_consistent\"]:\n", "            log.info(\"✅ 验证通过！修复后的管线A与管线B结果一致\")\n", "            return 0\n", "        else:\n", "            log.error(\"❌ 验证失败！仍存在不一致\")\n", "            return 1\n", "\n", "    except Exception as e:\n", "        log.error(f\"验证过程出错: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        return 1\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    sys.exit(main())"]}], "metadata": {"kernelspec": {"display_name": "jupyter_r_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}