{"cells": [{"cell_type": "markdown", "id": "4f49d657", "metadata": {}, "source": ["\n", "# 🔬 批量检查 AnnData（backed='r'）并导出 obs/var 与唯一值清单\n", "\n", "目标：\n", "- 扫描指定文件夹，收集所有 `.h5ad` / `.h5` 文件的**绝对路径**；\n", "- **逐个**使用 **Scanpy** 以 `backed='r'` 只读方式打开，**观察** `obs` / `var`；\n", "- 将 `obs`、`var` 分别保存为 CSV；\n", "- **输出 obs 的不重复值**（每一列的 unique 集合），并且在存在分组列（如 `cell_type`）时，对候选的**扰动列**做**分组唯一值清单**（为后续“按细胞类型微调”留出空间）；\n", "- 控制代码风格：**尽量少 if**（仅保留必要判断）。\n", "\n", "> 注：该 Notebook 仅读取 `obs/var`（它们始终常驻内存），不会把巨大的 `X` 矩阵载入内存。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "5421812d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["扫描目录： /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public\n", "导出目录： /data/ioz_whr_wsx/codes/dataexplore/.h5ad/obs_var_exports\n"]}], "source": ["\n", "# ===== 配置区 =====\n", "from pathlib import Path\n", "\n", "# 目标目录（可修改为你的路径）\n", "FOLDER = Path(\"/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public\")\n", "\n", "# 允许的后缀集合\n", "SUFFIXES = {\".h5ad\", \".h5\"}\n", "\n", "# 导出目录（相对当前 Notebook 运行目录）\n", "EXPORT_DIR = Path(\"./obs_var_exports\")\n", "EXPORT_DIR.mkdir(exist_ok=True)\n", "\n", "print(\"扫描目录：\", FOLDER.resolve())\n", "print(\"导出目录：\", EXPORT_DIR.resolve())\n"]}, {"cell_type": "code", "execution_count": 6, "id": "831b2bb1", "metadata": {}, "outputs": [], "source": ["\n", "import scanpy as sc\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "# ---- 辅助：挑选列 ----\n", "def find_cols(cols, keywords):\n", "    \"\"\"从列名中筛选包含任一关键字的列（忽略大小写），返回 Index。\"\"\"\n", "    keep = [c for c in cols if any(k in c.lower() for k in keywords)]\n", "    return pd.Index(keep)\n", "\n", "def pick_first(cols, candidates):\n", "    \"\"\"在 candidates 中按顺序挑出第一个存在于 cols 的列名；找不到返回 None。\"\"\"\n", "    return next((c for c in candidates if c in cols), None)\n", "\n", "# ---- 辅助：通用读取（优先 h5ad，其次 anndata.read 通用入口），backed='r' ----\n", "def read_adata_backed(path):\n", "    # 少 if：失败即继续尝试下一个 reader（EAFP 风格）\n", "    try:\n", "        return sc.read_h5ad(str(path), backed=\"r\")\n", "    except Exception:\n", "        pass\n", "    try:\n", "        return sc.read(str(path), backed=\"r\")\n", "    except Exception as e:\n", "        # 两种方式都失败则抛出最后的异常\n", "        raise e\n"]}, {"cell_type": "code", "execution_count": 7, "id": "20b5a8e8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>#</th>\n", "      <th>path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   #                                               path\n", "0  1  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...\n", "1  2  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...\n", "2  3  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...\n", "3  4  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...\n", "4  5  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...\n", "5  6  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...\n", "6  7  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc..."]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["找到 7 个候选文件\n"]}], "source": ["\n", "# ===== 发现文件（绝对路径） =====\n", "files = sorted([p.resolve() for p in FOLDER.iterdir() if p.suffix in SUFFIXES])\n", "from IPython.display import display\n", "display(pd.DataFrame({\"#\": range(1, len(files)+1), \"path\": [str(p) for p in files]}))\n", "print(f\"找到 {len(files)} 个候选文件\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "4e2804d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "[1/7] 处理：/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_train.h5\n", "obs.head():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>target_gene</th>\n", "      <th>guide_id</th>\n", "      <th>batch</th>\n", "      <th>batch_var</th>\n", "      <th>cell_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACAAGCAACCTTGTACTTTAGG-Flex_1_01</th>\n", "      <td>CHMP3</td>\n", "      <td>CHMP3_P1P2_A|CHMP3_P1P2_B</td>\n", "      <td>Flex_1_01</td>\n", "      <td>Flex_1_01</td>\n", "      <td>ARC_H1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACAAGCATTGCCGCACTTTAGG-Flex_1_01</th>\n", "      <td>AKT2</td>\n", "      <td>AKT2_P1P2_A|AKT2_P1P2_B</td>\n", "      <td>Flex_1_01</td>\n", "      <td>Flex_1_01</td>\n", "      <td>ARC_H1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAATCAATGTTCACTTTAGG-Flex_1_01</th>\n", "      <td>SHPRH</td>\n", "      <td>SHPRH_P1P2_A|SHPRH_P1P2_B</td>\n", "      <td>Flex_1_01</td>\n", "      <td>Flex_1_01</td>\n", "      <td>ARC_H1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAATCCCTCGCTACTTTAGG-Flex_1_01</th>\n", "      <td>TMSB4X</td>\n", "      <td>TMSB4X_P1_A|TMSB4X_P1_B</td>\n", "      <td>Flex_1_01</td>\n", "      <td>Flex_1_01</td>\n", "      <td>ARC_H1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAATCTAAATCCACTTTAGG-Flex_1_01</th>\n", "      <td>KLF10</td>\n", "      <td>KLF10_P2_A|KLF10_P2_B</td>\n", "      <td>Flex_1_01</td>\n", "      <td>Flex_1_01</td>\n", "      <td>ARC_H1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   target_gene                   guide_id  \\\n", "AAACAAGCAACCTTGTACTTTAGG-Flex_1_01       CHMP3  CHMP3_P1P2_A|CHMP3_P1P2_B   \n", "AAACAAGCATTGCCGCACTTTAGG-Flex_1_01        AKT2    AKT2_P1P2_A|AKT2_P1P2_B   \n", "AAACCAATCAATGTTCACTTTAGG-Flex_1_01       SHPRH  SHPRH_P1P2_A|SHPRH_P1P2_B   \n", "AAACCAATCCCTCGCTACTTTAGG-Flex_1_01      TMSB4X    TMSB4X_P1_A|TMSB4X_P1_B   \n", "AAACCAATCTAAATCCACTTTAGG-Flex_1_01       KLF10      KLF10_P2_A|KLF10_P2_B   \n", "\n", "                                        batch  batch_var cell_type  \n", "AAACAAGCAACCTTGTACTTTAGG-Flex_1_01  Flex_1_01  Flex_1_01    ARC_H1  \n", "AAACAAGCATTGCCGCACTTTAGG-Flex_1_01  Flex_1_01  Flex_1_01    ARC_H1  \n", "AAACCAATCAATGTTCACTTTAGG-Flex_1_01  Flex_1_01  Flex_1_01    ARC_H1  \n", "AAACCAATCCCTCGCTACTTTAGG-Flex_1_01  Flex_1_01  Flex_1_01    ARC_H1  \n", "AAACCAATCTAAATCCACTTTAGG-Flex_1_01  Flex_1_01  Flex_1_01    ARC_H1  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["var.head():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [SAMD11, NOC2L, KLHL17, PLEKHN1, PERM1]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>guide_id</th>\n", "      <td>189</td>\n", "    </tr>\n", "    <tr>\n", "      <th>target_gene</th>\n", "      <td>151</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batch</th>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batch_var</th>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_type</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             n_unique\n", "guide_id          189\n", "target_gene       151\n", "batch              48\n", "batch_var          48\n", "cell_type           1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "[2/7] 处理：/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_val_template.h5ad\n", "obs.head():\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/home/<USER>/miniconda3/envs/jupyter_r_env/lib/python3.10/site-packages/anndata/_core/anndata.py:1756: UserWarning: Observation names are not unique. To make them unique, call `.obs_names_make_unique`.\n", "  utils.warn_names_duplicates(\"obs\")\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>target_gene</th>\n", "      <th>batch_var</th>\n", "      <th>cell_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CCAAAGCCATTAGCGAATCCCAAC-Flex_2_06</th>\n", "      <td>PACSIN3</td>\n", "      <td>Flex_2_06</td>\n", "      <td>H1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ACTAGTGCAACCCTATATCCCAAC-Flex_2_06</th>\n", "      <td>PACSIN3</td>\n", "      <td>Flex_2_06</td>\n", "      <td>H1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GCTTGCGTCGGTAACGAGTGAGTG-Flex_3_10</th>\n", "      <td>PACSIN3</td>\n", "      <td>Flex_3_10</td>\n", "      <td>H1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TGTGAGGTCCTTTGAGAGTGAGTG-Flex_2_10</th>\n", "      <td>PACSIN3</td>\n", "      <td>Flex_2_10</td>\n", "      <td>H1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTGATCAGTTAACGGAATGTTGAC-Flex_1_04</th>\n", "      <td>PACSIN3</td>\n", "      <td>Flex_1_04</td>\n", "      <td>H1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   target_gene  batch_var cell_type\n", "CCAAAGCCATTAGCGAATCCCAAC-Flex_2_06     PACSIN3  Flex_2_06        H1\n", "ACTAGTGCAACCCTATATCCCAAC-Flex_2_06     PACSIN3  Flex_2_06        H1\n", "GCTTGCGTCGGTAACGAGTGAGTG-Flex_3_10     PACSIN3  Flex_3_10        H1\n", "TGTGAGGTCCTTTGAGAGTGAGTG-Flex_2_10     PACSIN3  Flex_2_10        H1\n", "TTGATCAGTTAACGGAATGTTGAC-Flex_1_04     PACSIN3  Flex_1_04        H1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["var.head():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [SAMD11, NOC2L, KLHL17, PLEKHN1, PERM1]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>target_gene</th>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batch_var</th>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_type</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             n_unique\n", "target_gene        51\n", "batch_var          48\n", "cell_type           1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "[3/7] 处理：/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/hepg2.h5\n", "obs.head():\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>batch_var</th>\n", "      <th>target_gene</th>\n", "      <th>cell_type</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGACGCCCT-39</th>\n", "      <td>39</td>\n", "      <td>PHF10</td>\n", "      <td>ENSG00000130024</td>\n", "      <td>P1P2</td>\n", "      <td>6279_PHF10_P1P2_ENSG00000130024</td>\n", "      <td>PHF10_-_170124315.23-P1P2|PHF10_+_170124573.23...</td>\n", "      <td>0.084000</td>\n", "      <td>21488.0</td>\n", "      <td>0.091101</td>\n", "      <td>hepg239</td>\n", "      <td>PHF10</td>\n", "      <td>hepg2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGATGGCAC-43</th>\n", "      <td>43</td>\n", "      <td>TFAM</td>\n", "      <td>ENSG00000108064</td>\n", "      <td>P1P2</td>\n", "      <td>8832_TFAM_P1P2_ENSG00000108064</td>\n", "      <td>TFAM_+_60145205.23-P1P2|TFAM_-_60145223.23-P1P2</td>\n", "      <td>0.071596</td>\n", "      <td>23912.0</td>\n", "      <td>2.079665</td>\n", "      <td>hepg243</td>\n", "      <td>TFAM</td>\n", "      <td>hepg2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCTGGAGT-49</th>\n", "      <td>49</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11272_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_03468|non-targeting_01711</td>\n", "      <td>0.083381</td>\n", "      <td>12257.0</td>\n", "      <td>-0.601160</td>\n", "      <td>hepg249</td>\n", "      <td>non-targeting</td>\n", "      <td>hepg2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGGTCGTCC-18</th>\n", "      <td>18</td>\n", "      <td>TFAM</td>\n", "      <td>ENSG00000108064</td>\n", "      <td>P1P2</td>\n", "      <td>8832_TFAM_P1P2_ENSG00000108064</td>\n", "      <td>TFAM_+_60145205.23-P1P2|TFAM_-_60145223.23-P1P2</td>\n", "      <td>0.078540</td>\n", "      <td>8989.0</td>\n", "      <td>-0.152877</td>\n", "      <td>hepg218</td>\n", "      <td>TFAM</td>\n", "      <td>hepg2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGTCGCTAT-20</th>\n", "      <td>20</td>\n", "      <td>PTPN1</td>\n", "      <td>ENSG00000196396</td>\n", "      <td>P1P2</td>\n", "      <td>6927_PTPN1_P1P2_ENSG00000196396</td>\n", "      <td>PTPN1_-_49126945.23-P1P2|PTPN1_+_49127138.23-P1P2</td>\n", "      <td>0.105561</td>\n", "      <td>9909.0</td>\n", "      <td>-0.601168</td>\n", "      <td>hepg220</td>\n", "      <td>PTPN1</td>\n", "      <td>hepg2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     gem_group           gene          gene_id     transcript  \\\n", "cell_barcode                                                                    \n", "AAACCCAAGACGCCCT-39         39          PHF10  ENSG00000130024           P1P2   \n", "AAACCCAAGATGGCAC-43         43           TFAM  ENSG00000108064           P1P2   \n", "AAACCCAAGCTGGAGT-49         49  non-targeting    non-targeting  non-targeting   \n", "AAACCCAAGGTCGTCC-18         18           TFAM  ENSG00000108064           P1P2   \n", "AAACCCAAGTCGCTAT-20         20          PTPN1  ENSG00000196396           P1P2   \n", "\n", "                                                     gene_transcript  \\\n", "cell_barcode                                                           \n", "AAACCCAAGACGCCCT-39                  6279_PHF10_P1P2_ENSG00000130024   \n", "AAACCCAAGATGGCAC-43                   8832_TFAM_P1P2_ENSG00000108064   \n", "AAACCCAAGCTGGAGT-49  11272_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGGTCGTCC-18                   8832_TFAM_P1P2_ENSG00000108064   \n", "AAACCCAAGTCGCTAT-20                  6927_PTPN1_P1P2_ENSG00000196396   \n", "\n", "                                                               sgID_AB  \\\n", "cell_barcode                                                             \n", "AAACCCAAGACGCCCT-39  PHF10_-_170124315.23-P1P2|PHF10_+_170124573.23...   \n", "AAACCCAAGATGGCAC-43    TFAM_+_60145205.23-P1P2|TFAM_-_60145223.23-P1P2   \n", "AAACCCAAGCTGGAGT-49            non-targeting_03468|non-targeting_01711   \n", "AAACCCAAGGTCGTCC-18    TFAM_+_60145205.23-P1P2|TFAM_-_60145223.23-P1P2   \n", "AAACCCAAGTCGCTAT-20  PTPN1_-_49126945.23-P1P2|PTPN1_+_49127138.23-P1P2   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI batch_var  \\\n", "cell_barcode                                                            \n", "AAACCCAAGACGCCCT-39     0.084000    21488.0        0.091101   hepg239   \n", "AAACCCAAGATGGCAC-43     0.071596    23912.0        2.079665   hepg243   \n", "AAACCCAAGCTGGAGT-49     0.083381    12257.0       -0.601160   hepg249   \n", "AAACCCAAGGTCGTCC-18     0.078540     8989.0       -0.152877   hepg218   \n", "AAACCCAAGTCGCTAT-20     0.105561     9909.0       -0.601168   hepg220   \n", "\n", "                       target_gene cell_type  \n", "cell_barcode                                  \n", "AAACCCAAGACGCCCT-39          PHF10     hepg2  \n", "AAACCCAAGATGGCAC-43           TFAM     hepg2  \n", "AAACCCAAGCTGGAGT-49  non-targeting     hepg2  \n", "AAACCCAAGGTCGTCC-18           TFAM     hepg2  \n", "AAACCCAAGTCGCTAT-20          PTPN1     hepg2  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["var.head():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [SAMD11, NOC2L, KLHL17, PLEKHN1, PERM1]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>mitopercent</th>\n", "      <td>9372</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z_gemgroup_UMI</th>\n", "      <td>9343</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UMI_count</th>\n", "      <td>7973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sgID_AB</th>\n", "      <td>201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_transcript</th>\n", "      <td>201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>target_gene</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batch_var</th>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gem_group</th>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>transcript</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_type</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 n_unique\n", "mitopercent          9372\n", "z_gemgroup_UMI       9343\n", "UMI_count            7973\n", "sgID_AB               201\n", "gene_transcript       201\n", "target_gene            69\n", "gene_id                69\n", "gene                   69\n", "batch_var              56\n", "gem_group              56\n", "transcript              4\n", "cell_type               1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "[4/7] 处理：/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/jurkat.h5\n", "obs.head():\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>batch_var</th>\n", "      <th>target_gene</th>\n", "      <th>cell_type</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGCACCAGA-42</th>\n", "      <td>42</td>\n", "      <td>EIF4B</td>\n", "      <td>ENSG00000063046</td>\n", "      <td>P1P2</td>\n", "      <td>2562_EIF4B_P1P2_ENSG00000063046</td>\n", "      <td>EIF4B_+_53400192.23-P1P2|EIF4B_-_53400313.23-P1P2</td>\n", "      <td>0.037697</td>\n", "      <td>4828.0</td>\n", "      <td>-1.484244</td>\n", "      <td>jurkat42</td>\n", "      <td>EIF4B</td>\n", "      <td>jurkat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCAGATAT-4</th>\n", "      <td>4</td>\n", "      <td>DAXX</td>\n", "      <td>ENSG00000204209</td>\n", "      <td>P1</td>\n", "      <td>2031_DAXX_P1_ENSG00000204209</td>\n", "      <td>DAXX_+_33290608.23-P1|DAXX_+_33290683.23-P1</td>\n", "      <td>0.063256</td>\n", "      <td>9675.0</td>\n", "      <td>-0.092674</td>\n", "      <td>jurkat4</td>\n", "      <td>DAXX</td>\n", "      <td>jurkat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCTAGATA-2</th>\n", "      <td>2</td>\n", "      <td>METTL3</td>\n", "      <td>ENSG00000165819</td>\n", "      <td>P1P2</td>\n", "      <td>5004_METTL3_P1P2_ENSG00000165819</td>\n", "      <td>METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...</td>\n", "      <td>0.069885</td>\n", "      <td>16985.0</td>\n", "      <td>1.696233</td>\n", "      <td>jurkat2</td>\n", "      <td>METTL3</td>\n", "      <td>jurkat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCTGGAGT-37</th>\n", "      <td>37</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11082_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_02191|non-targeting_03218</td>\n", "      <td>0.055775</td>\n", "      <td>20475.0</td>\n", "      <td>2.116577</td>\n", "      <td>jurkat37</td>\n", "      <td>non-targeting</td>\n", "      <td>jurkat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGGGCCTCT-51</th>\n", "      <td>51</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11102_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_02307|non-targeting_02721</td>\n", "      <td>0.047619</td>\n", "      <td>12642.0</td>\n", "      <td>-0.410600</td>\n", "      <td>jurkat51</td>\n", "      <td>non-targeting</td>\n", "      <td>jurkat</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     gem_group           gene          gene_id     transcript  \\\n", "cell_barcode                                                                    \n", "AAACCCAAGCACCAGA-42         42          EIF4B  ENSG00000063046           P1P2   \n", "AAACCCAAGCAGATAT-4           4           DAXX  ENSG00000204209             P1   \n", "AAACCCAAGCTAGATA-2           2         METTL3  ENSG00000165819           P1P2   \n", "AAACCCAAGCTGGAGT-37         37  non-targeting    non-targeting  non-targeting   \n", "AAACCCAAGGGCCTCT-51         51  non-targeting    non-targeting  non-targeting   \n", "\n", "                                                     gene_transcript  \\\n", "cell_barcode                                                           \n", "AAACCCAAGCACCAGA-42                  2562_EIF4B_P1P2_ENSG00000063046   \n", "AAACCCAAGCAGATAT-4                      2031_DAXX_P1_ENSG00000204209   \n", "AAACCCAAGCTAGATA-2                  5004_METTL3_P1P2_ENSG00000165819   \n", "AAACCCAAGCTGGAGT-37  11082_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGGGCCTCT-51  11102_non-targeting_non-targeting_non-targeting   \n", "\n", "                                                               sgID_AB  \\\n", "cell_barcode                                                             \n", "AAACCCAAGCACCAGA-42  EIF4B_+_53400192.23-P1P2|EIF4B_-_53400313.23-P1P2   \n", "AAACCCAAGCAGATAT-4         DAXX_+_33290608.23-P1|DAXX_+_33290683.23-P1   \n", "AAACCCAAGCTAGATA-2   METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...   \n", "AAACCCAAGCTGGAGT-37            non-targeting_02191|non-targeting_03218   \n", "AAACCCAAGGGCCTCT-51            non-targeting_02307|non-targeting_02721   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI batch_var  \\\n", "cell_barcode                                                            \n", "AAACCCAAGCACCAGA-42     0.037697     4828.0       -1.484244  jurkat42   \n", "AAACCCAAGCAGATAT-4      0.063256     9675.0       -0.092674   jurkat4   \n", "AAACCCAAGCTAGATA-2      0.069885    16985.0        1.696233   jurkat2   \n", "AAACCCAAGCTGGAGT-37     0.055775    20475.0        2.116577  jurkat37   \n", "AAACCCAAGGGCCTCT-51     0.047619    12642.0       -0.410600  jurkat51   \n", "\n", "                       target_gene cell_type  \n", "cell_barcode                                  \n", "AAACCCAAGCACCAGA-42          EIF4B    jurkat  \n", "AAACCCAAGCAGATAT-4            DAXX    jurkat  \n", "AAACCCAAGCTAGATA-2          METTL3    jurkat  \n", "AAACCCAAGCTGGAGT-37  non-targeting    jurkat  \n", "AAACCCAAGGGCCTCT-51  non-targeting    jurkat  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["var.head():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [SAMD11, NOC2L, KLHL17, PLEKHN1, PERM1]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>mitopercent</th>\n", "      <td>21245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z_gemgroup_UMI</th>\n", "      <td>21080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UMI_count</th>\n", "      <td>12550</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sgID_AB</th>\n", "      <td>201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_transcript</th>\n", "      <td>201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>target_gene</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batch_var</th>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gem_group</th>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>transcript</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_type</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 n_unique\n", "mitopercent         21245\n", "z_gemgroup_UMI      21080\n", "UMI_count           12550\n", "sgID_AB               201\n", "gene_transcript       201\n", "target_gene            69\n", "gene_id                69\n", "gene                   69\n", "batch_var              55\n", "gem_group              55\n", "transcript              4\n", "cell_type               1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "[5/7] 处理：/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/k562.h5\n", "obs.head():\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>core_scale_factor</th>\n", "      <th>core_adjusted_UMI_count</th>\n", "      <th>batch_var</th>\n", "      <th>target_gene</th>\n", "      <th>cell_type</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGATAGCAT-11</th>\n", "      <td>11</td>\n", "      <td>MED1</td>\n", "      <td>ENSG00000125686</td>\n", "      <td>P1P2</td>\n", "      <td>4942_MED1_P1P2_ENSG00000125686</td>\n", "      <td>MED1_-_37607464.23-P1P2|MED1_+_37607273.23-P1P2</td>\n", "      <td>0.130973</td>\n", "      <td>8475.0</td>\n", "      <td>-1.217506</td>\n", "      <td>1.050188</td>\n", "      <td>8069.984863</td>\n", "      <td>k56211</td>\n", "      <td>MED1</td>\n", "      <td>k562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCGAGGAG-47</th>\n", "      <td>47</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11059_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_02010|non-targeting_01147</td>\n", "      <td>0.092709</td>\n", "      <td>12221.0</td>\n", "      <td>1.096669</td>\n", "      <td>0.600264</td>\n", "      <td>20359.363281</td>\n", "      <td>k56247</td>\n", "      <td>non-targeting</td>\n", "      <td>k562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCGTCTGC-27</th>\n", "      <td>27</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11020_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_01763|non-targeting_02345</td>\n", "      <td>0.087307</td>\n", "      <td>14638.0</td>\n", "      <td>0.760877</td>\n", "      <td>0.813253</td>\n", "      <td>17999.330078</td>\n", "      <td>k56227</td>\n", "      <td>non-targeting</td>\n", "      <td>k562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGGAGGGTG-47</th>\n", "      <td>47</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11097_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_02274|non-targeting_01075</td>\n", "      <td>0.144649</td>\n", "      <td>11241.0</td>\n", "      <td>0.813080</td>\n", "      <td>0.600264</td>\n", "      <td>18726.748047</td>\n", "      <td>k56247</td>\n", "      <td>non-targeting</td>\n", "      <td>k562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGGTCACTT-18</th>\n", "      <td>18</td>\n", "      <td>MED12</td>\n", "      <td>ENSG00000184634</td>\n", "      <td>P1P2</td>\n", "      <td>4932_MED12_P1P2_ENSG00000184634</td>\n", "      <td>MED12_+_70338481.23-P1P2|MED12_+_70338552.23-P1P2</td>\n", "      <td>0.165869</td>\n", "      <td>9821.0</td>\n", "      <td>-1.167454</td>\n", "      <td>1.193645</td>\n", "      <td>8227.741211</td>\n", "      <td>k56218</td>\n", "      <td>MED12</td>\n", "      <td>k562</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     gem_group           gene          gene_id     transcript  \\\n", "cell_barcode                                                                    \n", "AAACCCAAGATAGCAT-11         11           MED1  ENSG00000125686           P1P2   \n", "AAACCCAAGCGAGGAG-47         47  non-targeting    non-targeting  non-targeting   \n", "AAACCCAAGCGTCTGC-27         27  non-targeting    non-targeting  non-targeting   \n", "AAACCCAAGGAGGGTG-47         47  non-targeting    non-targeting  non-targeting   \n", "AAACCCAAGGTCACTT-18         18          MED12  ENSG00000184634           P1P2   \n", "\n", "                                                     gene_transcript  \\\n", "cell_barcode                                                           \n", "AAACCCAAGATAGCAT-11                   4942_MED1_P1P2_ENSG00000125686   \n", "AAACCCAAGCGAGGAG-47  11059_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGCGTCTGC-27  11020_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGGAGGGTG-47  11097_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGGTCACTT-18                  4932_MED12_P1P2_ENSG00000184634   \n", "\n", "                                                               sgID_AB  \\\n", "cell_barcode                                                             \n", "AAACCCAAGATAGCAT-11    MED1_-_37607464.23-P1P2|MED1_+_37607273.23-P1P2   \n", "AAACCCAAGCGAGGAG-47            non-targeting_02010|non-targeting_01147   \n", "AAACCCAAGCGTCTGC-27            non-targeting_01763|non-targeting_02345   \n", "AAACCCAAGGAGGGTG-47            non-targeting_02274|non-targeting_01075   \n", "AAACCCAAGGTCACTT-18  MED12_+_70338481.23-P1P2|MED12_+_70338552.23-P1P2   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \\\n", "cell_barcode                                                  \n", "AAACCCAAGATAGCAT-11     0.130973     8475.0       -1.217506   \n", "AAACCCAAGCGAGGAG-47     0.092709    12221.0        1.096669   \n", "AAACCCAAGCGTCTGC-27     0.087307    14638.0        0.760877   \n", "AAACCCAAGGAGGGTG-47     0.144649    11241.0        0.813080   \n", "AAACCCAAGGTCACTT-18     0.165869     9821.0       -1.167454   \n", "\n", "                     core_scale_factor  core_adjusted_UMI_count batch_var  \\\n", "cell_barcode                                                                \n", "AAACCCAAGATAGCAT-11           1.050188              8069.984863    k56211   \n", "AAACCCAAGCGAGGAG-47           0.600264             20359.363281    k56247   \n", "AAACCCAAGCGTCTGC-27           0.813253             17999.330078    k56227   \n", "AAACCCAAGGAGGGTG-47           0.600264             18726.748047    k56247   \n", "AAACCCAAGGTCACTT-18           1.193645              8227.741211    k56218   \n", "\n", "                       target_gene cell_type  \n", "cell_barcode                                  \n", "AAACCCAAGATAGCAT-11           MED1      k562  \n", "AAACCCAAGCGAGGAG-47  non-targeting      k562  \n", "AAACCCAAGCGTCTGC-27  non-targeting      k562  \n", "AAACCCAAGGAGGGTG-47  non-targeting      k562  \n", "AAACCCAAGGTCACTT-18          MED12      k562  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["var.head():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [SAMD11, NOC2L, KLHL17, PLEKHN1, PERM1]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>mitopercent</th>\n", "      <td>18402</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z_gemgroup_UMI</th>\n", "      <td>18184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>core_adjusted_UMI_count</th>\n", "      <td>18174</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UMI_count</th>\n", "      <td>11422</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_transcript</th>\n", "      <td>153</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sgID_AB</th>\n", "      <td>153</td>\n", "    </tr>\n", "    <tr>\n", "      <th>target_gene</th>\n", "      <td>54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <td>54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene</th>\n", "      <td>54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gem_group</th>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batch_var</th>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>core_scale_factor</th>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>transcript</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_type</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         n_unique\n", "mitopercent                 18402\n", "z_gemgroup_UMI              18184\n", "core_adjusted_UMI_count     18174\n", "UMI_count                   11422\n", "gene_transcript               153\n", "sgID_AB                       153\n", "target_gene                    54\n", "gene_id                        54\n", "gene                           54\n", "gem_group                      48\n", "batch_var                      48\n", "core_scale_factor              48\n", "transcript                      4\n", "cell_type                       1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "[6/7] 处理：/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/k562_gwps.h5\n", "obs.head():\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>core_scale_factor</th>\n", "      <th>core_adjusted_UMI_count</th>\n", "      <th>batch_var</th>\n", "      <th>target_gene</th>\n", "      <th>cell_type</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAGTGAAG-129</th>\n", "      <td>129</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11147_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_02596|non-targeting_01746</td>\n", "      <td>0.142966</td>\n", "      <td>6589.0</td>\n", "      <td>-0.841324</td>\n", "      <td>0.745740</td>\n", "      <td>8835.518555</td>\n", "      <td>k562_gwps129</td>\n", "      <td>non-targeting</td>\n", "      <td>k562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGATACATG-238</th>\n", "      <td>238</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11000_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_01628|non-targeting_03433</td>\n", "      <td>0.107690</td>\n", "      <td>10976.0</td>\n", "      <td>-0.351153</td>\n", "      <td>1.055636</td>\n", "      <td>10397.521484</td>\n", "      <td>k562_gwps238</td>\n", "      <td>non-targeting</td>\n", "      <td>k562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCACTCCG-164</th>\n", "      <td>164</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11119_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_02458|non-targeting_03087</td>\n", "      <td>0.096043</td>\n", "      <td>5560.0</td>\n", "      <td>0.415484</td>\n", "      <td>0.409556</td>\n", "      <td>13575.676758</td>\n", "      <td>k562_gwps164</td>\n", "      <td>non-targeting</td>\n", "      <td>k562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCATTGTC-31</th>\n", "      <td>31</td>\n", "      <td>KDM2B</td>\n", "      <td>ENSG00000089094</td>\n", "      <td>ENST00000377069.4</td>\n", "      <td>4320_KDM2B_ENST00000377069.4_ENSG00000089094</td>\n", "      <td>KDM2B_-_122018450.23-ENST00000377069.4|KDM2B_+...</td>\n", "      <td>0.101658</td>\n", "      <td>16467.0</td>\n", "      <td>1.041185</td>\n", "      <td>1.063271</td>\n", "      <td>15487.114258</td>\n", "      <td>k562_gwps31</td>\n", "      <td>KDM2B</td>\n", "      <td>k562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCCACTCG-32</th>\n", "      <td>32</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11213_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_03027|non-targeting_00744</td>\n", "      <td>0.098446</td>\n", "      <td>20011.0</td>\n", "      <td>1.292765</td>\n", "      <td>1.138995</td>\n", "      <td>17568.994141</td>\n", "      <td>k562_gwps32</td>\n", "      <td>non-targeting</td>\n", "      <td>k562</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      gem_group           gene          gene_id  \\\n", "cell_barcode                                                      \n", "AAACCCAAGAGTGAAG-129        129  non-targeting    non-targeting   \n", "AAACCCAAGATACATG-238        238  non-targeting    non-targeting   \n", "AAACCCAAGCACTCCG-164        164  non-targeting    non-targeting   \n", "AAACCCAAGCATTGTC-31          31          KDM2B  ENSG00000089094   \n", "AAACCCAAGCCACTCG-32          32  non-targeting    non-targeting   \n", "\n", "                             transcript  \\\n", "cell_barcode                              \n", "AAACCCAAGAGTGAAG-129      non-targeting   \n", "AAACCCAAGATACATG-238      non-targeting   \n", "AAACCCAAGCACTCCG-164      non-targeting   \n", "AAACCCAAGCATTGTC-31   ENST00000377069.4   \n", "AAACCCAAGCCACTCG-32       non-targeting   \n", "\n", "                                                      gene_transcript  \\\n", "cell_barcode                                                            \n", "AAACCCAAGAGTGAAG-129  11147_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGATACATG-238  11000_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGCACTCCG-164  11119_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGCATTGTC-31      4320_KDM2B_ENST00000377069.4_ENSG00000089094   \n", "AAACCCAAGCCACTCG-32   11213_non-targeting_non-targeting_non-targeting   \n", "\n", "                                                                sgID_AB  \\\n", "cell_barcode                                                              \n", "AAACCCAAGAGTGAAG-129            non-targeting_02596|non-targeting_01746   \n", "AAACCCAAGATACATG-238            non-targeting_01628|non-targeting_03433   \n", "AAACCCAAGCACTCCG-164            non-targeting_02458|non-targeting_03087   \n", "AAACCCAAGCATTGTC-31   KDM2B_-_122018450.23-ENST00000377069.4|KDM2B_+...   \n", "AAACCCAAGCCACTCG-32             non-targeting_03027|non-targeting_00744   \n", "\n", "                      mitopercent  UMI_count  z_gemgroup_UMI  \\\n", "cell_barcode                                                   \n", "AAACCCAAGAGTGAAG-129     0.142966     6589.0       -0.841324   \n", "AAACCCAAGATACATG-238     0.107690    10976.0       -0.351153   \n", "AAACCCAAGCACTCCG-164     0.096043     5560.0        0.415484   \n", "AAACCCAAGCATTGTC-31      0.101658    16467.0        1.041185   \n", "AAACCCAAGCCACTCG-32      0.098446    20011.0        1.292765   \n", "\n", "                      core_scale_factor  core_adjusted_UMI_count  \\\n", "cell_barcode                                                       \n", "AAACCCAAGAGTGAAG-129           0.745740              8835.518555   \n", "AAACCCAAGATACATG-238           1.055636             10397.521484   \n", "AAACCCAAGCACTCCG-164           0.409556             13575.676758   \n", "AAACCCAAGCATTGTC-31            1.063271             15487.114258   \n", "AAACCCAAGCCACTCG-32            1.138995             17568.994141   \n", "\n", "                         batch_var    target_gene cell_type  \n", "cell_barcode                                                 \n", "AAACCCAAGAGTGAAG-129  k562_gwps129  non-targeting      k562  \n", "AAACCCAAGATACATG-238  k562_gwps238  non-targeting      k562  \n", "AAACCCAAGCACTCCG-164  k562_gwps164  non-targeting      k562  \n", "AAACCCAAGCATTGTC-31    k562_gwps31          KDM2B      k562  \n", "AAACCCAAGCCACTCG-32    k562_gwps32  non-targeting      k562  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["var.head():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [SAMD11, NOC2L, KLHL17, PLEKHN1, PERM1]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>z_gemgroup_UMI</th>\n", "      <td>109139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mitopercent</th>\n", "      <td>108816</td>\n", "    </tr>\n", "    <tr>\n", "      <th>core_adjusted_UMI_count</th>\n", "      <td>108724</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UMI_count</th>\n", "      <td>22852</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_transcript</th>\n", "      <td>708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sgID_AB</th>\n", "      <td>708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>core_scale_factor</th>\n", "      <td>267</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batch_var</th>\n", "      <td>267</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gem_group</th>\n", "      <td>267</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene</th>\n", "      <td>184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>target_gene</th>\n", "      <td>184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <td>184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>transcript</th>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_type</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         n_unique\n", "z_gemgroup_UMI             109139\n", "mitopercent                108816\n", "core_adjusted_UMI_count    108724\n", "UMI_count                   22852\n", "gene_transcript               708\n", "sgID_AB                       708\n", "core_scale_factor             267\n", "batch_var                     267\n", "gem_group                     267\n", "gene                          184\n", "target_gene                   184\n", "gene_id                       184\n", "transcript                      7\n", "cell_type                       1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "[7/7] 处理：/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/rpe1.h5\n", "obs.head():\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>core_scale_factor</th>\n", "      <th>core_adjusted_UMI_count</th>\n", "      <th>batch_var</th>\n", "      <th>target_gene</th>\n", "      <th>cell_type</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAGAGAAC-35</th>\n", "      <td>35</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11209_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_02989|non-targeting_02406</td>\n", "      <td>0.056552</td>\n", "      <td>8753.0</td>\n", "      <td>0.368225</td>\n", "      <td>0.600710</td>\n", "      <td>14571.081055</td>\n", "      <td>rpe135</td>\n", "      <td>non-targeting</td>\n", "      <td>rpe1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCGCACAA-2</th>\n", "      <td>2</td>\n", "      <td>TFAM</td>\n", "      <td>ENSG00000108064</td>\n", "      <td>P1P2</td>\n", "      <td>8832_TFAM_P1P2_ENSG00000108064</td>\n", "      <td>TFAM_+_60145205.23-P1P2|TFAM_-_60145223.23-P1P2</td>\n", "      <td>0.022038</td>\n", "      <td>11934.0</td>\n", "      <td>0.083506</td>\n", "      <td>0.936004</td>\n", "      <td>12749.941406</td>\n", "      <td>rpe12</td>\n", "      <td>TFAM</td>\n", "      <td>rpe1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCGGACAT-34</th>\n", "      <td>34</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11238_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_03171|non-targeting_03032</td>\n", "      <td>0.058144</td>\n", "      <td>12469.0</td>\n", "      <td>0.970058</td>\n", "      <td>0.709628</td>\n", "      <td>17571.187500</td>\n", "      <td>rpe134</td>\n", "      <td>non-targeting</td>\n", "      <td>rpe1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGCTCGACC-34</th>\n", "      <td>34</td>\n", "      <td>TAZ</td>\n", "      <td>ENSG00000102125</td>\n", "      <td>P1P2</td>\n", "      <td>8703_TAZ_P1P2_ENSG00000102125</td>\n", "      <td>TAZ_-_153640125.23-P1P2|TAZ_+_153639918.23-P1P2</td>\n", "      <td>0.053064</td>\n", "      <td>16075.0</td>\n", "      <td>1.957035</td>\n", "      <td>0.709628</td>\n", "      <td>22652.726562</td>\n", "      <td>rpe134</td>\n", "      <td>TAZ</td>\n", "      <td>rpe1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGTGCACCC-42</th>\n", "      <td>42</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>10829_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_00483|non-targeting_03391</td>\n", "      <td>0.058406</td>\n", "      <td>12961.0</td>\n", "      <td>1.024948</td>\n", "      <td>0.725025</td>\n", "      <td>17876.626953</td>\n", "      <td>rpe142</td>\n", "      <td>non-targeting</td>\n", "      <td>rpe1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     gem_group           gene          gene_id     transcript  \\\n", "cell_barcode                                                                    \n", "AAACCCAAGAGAGAAC-35         35  non-targeting    non-targeting  non-targeting   \n", "AAACCCAAGCGCACAA-2           2           TFAM  ENSG00000108064           P1P2   \n", "AAACCCAAGCGGACAT-34         34  non-targeting    non-targeting  non-targeting   \n", "AAACCCAAGCTCGACC-34         34            TAZ  ENSG00000102125           P1P2   \n", "AAACCCAAGTGCACCC-42         42  non-targeting    non-targeting  non-targeting   \n", "\n", "                                                     gene_transcript  \\\n", "cell_barcode                                                           \n", "AAACCCAAGAGAGAAC-35  11209_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGCGCACAA-2                    8832_TFAM_P1P2_ENSG00000108064   \n", "AAACCCAAGCGGACAT-34  11238_non-targeting_non-targeting_non-targeting   \n", "AAACCCAAGCTCGACC-34                    8703_TAZ_P1P2_ENSG00000102125   \n", "AAACCCAAGTGCACCC-42  10829_non-targeting_non-targeting_non-targeting   \n", "\n", "                                                             sgID_AB  \\\n", "cell_barcode                                                           \n", "AAACCCAAGAGAGAAC-35          non-targeting_02989|non-targeting_02406   \n", "AAACCCAAGCGCACAA-2   TFAM_+_60145205.23-P1P2|TFAM_-_60145223.23-P1P2   \n", "AAACCCAAGCGGACAT-34          non-targeting_03171|non-targeting_03032   \n", "AAACCCAAGCTCGACC-34  TAZ_-_153640125.23-P1P2|TAZ_+_153639918.23-P1P2   \n", "AAACCCAAGTGCACCC-42          non-targeting_00483|non-targeting_03391   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \\\n", "cell_barcode                                                  \n", "AAACCCAAGAGAGAAC-35     0.056552     8753.0        0.368225   \n", "AAACCCAAGCGCACAA-2      0.022038    11934.0        0.083506   \n", "AAACCCAAGCGGACAT-34     0.058144    12469.0        0.970058   \n", "AAACCCAAGCTCGACC-34     0.053064    16075.0        1.957035   \n", "AAACCCAAGTGCACCC-42     0.058406    12961.0        1.024948   \n", "\n", "                     core_scale_factor  core_adjusted_UMI_count batch_var  \\\n", "cell_barcode                                                                \n", "AAACCCAAGAGAGAAC-35           0.600710             14571.081055    rpe135   \n", "AAACCCAAGCGCACAA-2            0.936004             12749.941406     rpe12   \n", "AAACCCAAGCGGACAT-34           0.709628             17571.187500    rpe134   \n", "AAACCCAAGCTCGACC-34           0.709628             22652.726562    rpe134   \n", "AAACCCAAGTGCACCC-42           0.725025             17876.626953    rpe142   \n", "\n", "                       target_gene cell_type  \n", "cell_barcode                                  \n", "AAACCCAAGAGAGAAC-35  non-targeting      rpe1  \n", "AAACCCAAGCGCACAA-2            TFAM      rpe1  \n", "AAACCCAAGCGGACAT-34  non-targeting      rpe1  \n", "AAACCCAAGCTCGACC-34            TAZ      rpe1  \n", "AAACCCAAGTGCACCC-42  non-targeting      rpe1  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["var.head():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [SAMD11, NOC2L, KLHL17, PLEKHN1, PERM1]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>mitopercent</th>\n", "      <td>22135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z_gemgroup_UMI</th>\n", "      <td>21926</td>\n", "    </tr>\n", "    <tr>\n", "      <th>core_adjusted_UMI_count</th>\n", "      <td>21911</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UMI_count</th>\n", "      <td>12556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_transcript</th>\n", "      <td>184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sgID_AB</th>\n", "      <td>184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>target_gene</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gene</th>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gem_group</th>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batch_var</th>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>core_scale_factor</th>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>transcript</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_type</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         n_unique\n", "mitopercent                 22135\n", "z_gemgroup_UMI              21926\n", "core_adjusted_UMI_count     21911\n", "UMI_count                   12556\n", "gene_transcript               184\n", "sgID_AB                       184\n", "target_gene                    69\n", "gene_id                        69\n", "gene                           69\n", "gem_group                      56\n", "batch_var                      56\n", "core_scale_factor              56\n", "transcript                      4\n", "cell_type                       1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n", "/tmp/ipykernel_3366717/3842836975.py:55: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  obs.groupby(group_col)[pcol]\n", "/tmp/ipykernel_3366717/3842836975.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  for level, sub in obs.groupby(group_col):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>path</th>\n", "      <th>obs_csv</th>\n", "      <th>var_csv</th>\n", "      <th>obs_nunique_csv</th>\n", "      <th>group_col_used</th>\n", "      <th>pert_cols_used</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "      <td>obs_var_exports/competition_train.h5__obs.csv</td>\n", "      <td>obs_var_exports/competition_train.h5__var.csv</td>\n", "      <td>obs_var_exports/competition_train.h5__obs_nuni...</td>\n", "      <td>cell_type</td>\n", "      <td>target_gene</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "      <td>obs_var_exports/competition_val_template.h5ad_...</td>\n", "      <td>obs_var_exports/competition_val_template.h5ad_...</td>\n", "      <td>obs_var_exports/competition_val_template.h5ad_...</td>\n", "      <td>cell_type</td>\n", "      <td>target_gene</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "      <td>obs_var_exports/hepg2.h5__obs.csv</td>\n", "      <td>obs_var_exports/hepg2.h5__var.csv</td>\n", "      <td>obs_var_exports/hepg2.h5__obs_nunique.csv</td>\n", "      <td>cell_type</td>\n", "      <td>gene,gene_id,gene_transcript,target_gene</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "      <td>obs_var_exports/jurkat.h5__obs.csv</td>\n", "      <td>obs_var_exports/jurkat.h5__var.csv</td>\n", "      <td>obs_var_exports/jurkat.h5__obs_nunique.csv</td>\n", "      <td>cell_type</td>\n", "      <td>gene,gene_id,gene_transcript,target_gene</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "      <td>obs_var_exports/k562.h5__obs.csv</td>\n", "      <td>obs_var_exports/k562.h5__var.csv</td>\n", "      <td>obs_var_exports/k562.h5__obs_nunique.csv</td>\n", "      <td>cell_type</td>\n", "      <td>gene,gene_id,gene_transcript,target_gene</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "      <td>obs_var_exports/k562_gwps.h5__obs.csv</td>\n", "      <td>obs_var_exports/k562_gwps.h5__var.csv</td>\n", "      <td>obs_var_exports/k562_gwps.h5__obs_nunique.csv</td>\n", "      <td>cell_type</td>\n", "      <td>gene,gene_id,gene_transcript,target_gene</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...</td>\n", "      <td>obs_var_exports/rpe1.h5__obs.csv</td>\n", "      <td>obs_var_exports/rpe1.h5__var.csv</td>\n", "      <td>obs_var_exports/rpe1.h5__obs_nunique.csv</td>\n", "      <td>cell_type</td>\n", "      <td>gene,gene_id,gene_transcript,target_gene</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                path  \\\n", "0  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...   \n", "1  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...   \n", "2  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...   \n", "3  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...   \n", "4  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...   \n", "5  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...   \n", "6  /data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc...   \n", "\n", "                                             obs_csv  \\\n", "0      obs_var_exports/competition_train.h5__obs.csv   \n", "1  obs_var_exports/competition_val_template.h5ad_...   \n", "2                  obs_var_exports/hepg2.h5__obs.csv   \n", "3                 obs_var_exports/jurkat.h5__obs.csv   \n", "4                   obs_var_exports/k562.h5__obs.csv   \n", "5              obs_var_exports/k562_gwps.h5__obs.csv   \n", "6                   obs_var_exports/rpe1.h5__obs.csv   \n", "\n", "                                             var_csv  \\\n", "0      obs_var_exports/competition_train.h5__var.csv   \n", "1  obs_var_exports/competition_val_template.h5ad_...   \n", "2                  obs_var_exports/hepg2.h5__var.csv   \n", "3                 obs_var_exports/jurkat.h5__var.csv   \n", "4                   obs_var_exports/k562.h5__var.csv   \n", "5              obs_var_exports/k562_gwps.h5__var.csv   \n", "6                   obs_var_exports/rpe1.h5__var.csv   \n", "\n", "                                     obs_nunique_csv group_col_used  \\\n", "0  obs_var_exports/competition_train.h5__obs_nuni...      cell_type   \n", "1  obs_var_exports/competition_val_template.h5ad_...      cell_type   \n", "2          obs_var_exports/hepg2.h5__obs_nunique.csv      cell_type   \n", "3         obs_var_exports/jurkat.h5__obs_nunique.csv      cell_type   \n", "4           obs_var_exports/k562.h5__obs_nunique.csv      cell_type   \n", "5      obs_var_exports/k562_gwps.h5__obs_nunique.csv      cell_type   \n", "6           obs_var_exports/rpe1.h5__obs_nunique.csv      cell_type   \n", "\n", "                             pert_cols_used  \n", "0                               target_gene  \n", "1                               target_gene  \n", "2  gene,gene_id,gene_transcript,target_gene  \n", "3  gene,gene_id,gene_transcript,target_gene  \n", "4  gene,gene_id,gene_transcript,target_gene  \n", "5  gene,gene_id,gene_transcript,target_gene  \n", "6  gene,gene_id,gene_transcript,target_gene  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "导出完成。清单： /data/ioz_whr_wsx/codes/dataexplore/.h5ad/obs_var_exports/manifest.csv\n"]}], "source": ["\n", "# ===== 主循环：逐个只读打开、导出、统计 =====\n", "from IPython.display import display\n", "\n", "manifest_rows = []\n", "\n", "for idx, path in enumerate(files, 1):\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(f\"[{idx}/{len(files)}] 处理：{path}\")\n", "    adata = read_adata_backed(path)\n", "\n", "    # 复制到普通 DataFrame（obs/var 本就常驻内存，这里 copy 便于后续保存）\n", "    obs = adata.obs.copy()\n", "    var = adata.var.copy()\n", "\n", "    # 基础浏览\n", "    print(\"obs.head():\")\n", "    display(obs.head())\n", "    print(\"var.head():\")\n", "    display(var.head())\n", "\n", "    # 文件基名（保留原文件名，避免多重 .suffix 处理导致信息缺失）\n", "    base = path.name\n", "\n", "    # 导出 obs / var\n", "    obs_csv = EXPORT_DIR / f\"{base}__obs.csv\"\n", "    var_csv = EXPORT_DIR / f\"{base}__var.csv\"\n", "    obs.to_csv(obs_csv)\n", "    var.to_csv(var_csv)\n", "\n", "    # obs 的每列唯一值个数\n", "    nunique_df = obs.nunique(dropna=False).to_frame(\"n_unique\").sort_values(\"n_unique\", ascending=False)\n", "    display(nunique_df)\n", "    nunique_csv = EXPORT_DIR / f\"{base}__obs_nunique.csv\"\n", "    nunique_df.to_csv(nunique_csv)\n", "\n", "    # 所有列的唯一值逐列导出（尽量少 if：直接循环所有列）\n", "    for col in obs.columns:\n", "        uniq_vals = pd.Series(pd.unique(obs[col].astype(\"object\")), name=col)\n", "        uniq_csv = EXPORT_DIR / f\"{base}__obs_unique__{col}.csv\"\n", "        # 为避免路径或空格问题，简单替换\n", "        safe_path = str(uniq_csv).replace(\"/\", \"_\").replace(\" \", \"_\")\n", "        uniq_vals.to_csv(safe_path, index=False, header=True)\n", "\n", "    # 候选扰动列（关键字匹配）\n", "    pert_cols = find_cols(obs.columns, [\"perturb\", \"gene\", \"treatment\", \"condition\", \"target\"])\n", "    # 候选分组列（尽量找 cell 类型相关列，给“按细胞微调”留空间）\n", "    group_col = pick_first(obs.columns, [\"cell_type\", \"celltype\", \"cell_line\", \"line\", \"lineage\"])\n", "\n", "    # 分组导出：对每个扰动列，按 group_col 做唯一值统计与清单\n", "    # 仅在二者都存在时执行（两处必要的 if）\n", "    if len(pert_cols) and group_col:\n", "        for pcol in pert_cols:\n", "            # 每个 cell-type 下该扰动列的唯一值个数\n", "            g = (\n", "                obs.groupby(group_col)[pcol]\n", "                .nunique(dropna=False)\n", "                .sort_values(ascending=False)\n", "                .to_frame(\"n_unique\")\n", "            )\n", "            g_csv = EXPORT_DIR / f\"{base}__{pcol}__by_{group_col}__nunique.csv\"\n", "            g.to_csv(g_csv)\n", "\n", "            # 同时导出每个组别的唯一值清单（给后续“微调”用）\n", "            for level, sub in obs.groupby(group_col):\n", "                uv = pd.Series(pd.unique(sub[pcol].astype(\"object\")), name=pcol)\n", "                lv = str(level).replace(\"/\", \"_\").replace(\" \", \"_\")\n", "                out = EXPORT_DIR / f\"{base}__{pcol}__values__{group_col}={lv}.csv\"\n", "                uv.to_csv(out, index=False, header=True)\n", "\n", "    # 记录清单\n", "    manifest_rows.append({\n", "        \"path\": str(path),\n", "        \"obs_csv\": str(obs_csv),\n", "        \"var_csv\": str(var_csv),\n", "        \"obs_nunique_csv\": str(nunique_csv),\n", "        \"group_col_used\": group_col if group_col else \"\",\n", "        \"pert_cols_used\": \",\".join(list(pert_cols)) if len(pert_cols) else \"\"\n", "    })\n", "\n", "# 汇总导出清单\n", "manifest = pd.DataFrame(manifest_rows)\n", "manifest_csv = EXPORT_DIR / \"manifest.csv\"\n", "manifest.to_csv(manifest_csv, index=False)\n", "display(manifest)\n", "print(\"\\n导出完成。清单：\", manifest_csv.resolve())\n"]}, {"cell_type": "markdown", "id": "98dbb1e8", "metadata": {}, "source": ["\n", "## 备注与小贴士\n", "\n", "- 该流程使用 `backed='r'` 只读模式打开文件，不会写入源 `.h5ad/.h5`；如需修复重复的 `obs_names`，请在**非 backed 模式**下另存一份再改：  \n", "  ```python\n", "  adata = sc.read_h5ad(\"file.h5ad\")  # 无 backed\n", "  adata.obs_names_make_unique()\n", "  adata.write_h5ad(\"file_fixed.h5ad\")\n", "  ```\n", "- 如果部分 `.h5` 不是 AnnData，而是自定义 HDF5 布局，`scanpy` 将无法读入；此时请针对该格式的文档/工具处理。\n", "- “按细胞类型微调”的空间：本 Notebook 会在检测到 `cell_type`（或同义列名）时，按该列对候选扰动列（包含 `perturb/gene/treatment/condition/target` 关键字）做**分组唯一值清单**，对应 CSV 位于 `obs_var_exports/`。\n", "- 想缩减导出文件数，可以注释掉“逐列导出唯一值清单”的循环，仅保留 `nunique` 汇总。\n"]}], "metadata": {"kernelspec": {"display_name": "jupyter_r_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}