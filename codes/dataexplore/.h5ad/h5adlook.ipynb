{"cells": [{"cell_type": "code", "execution_count": null, "id": "84dac1ec", "metadata": {}, "outputs": [], "source": ["import scanpy as sc"]}, {"cell_type": "code", "execution_count": 1, "id": "92a1d372", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "处理文件: competition_val_template.h5ad\n"]}, {"ename": "BlockingIOError", "evalue": "[Errno 11] Unable to synchronously open file (unable to lock file, errno = 11, error message = 'Resource temporarily unavailable')", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mBlockingIOError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 17\u001b[39m\n\u001b[32m     14\u001b[39m base_name = fname.replace(\u001b[33m'\u001b[39m\u001b[33m.\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33m_\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m     16\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m fname.endswith(\u001b[33m\"\u001b[39m\u001b[33m.h5ad\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m---> \u001b[39m\u001b[32m17\u001b[39m     adata = \u001b[43manndata\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_h5ad\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbacked\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mr\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     18\u001b[39m     \u001b[38;5;66;03m# 创建变量并添加到全局命名空间\u001b[39;00m\n\u001b[32m     19\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m data_type \u001b[38;5;129;01min\u001b[39;00m [\u001b[33m'\u001b[39m\u001b[33mobs\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mvar\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mX\u001b[39m\u001b[33m'\u001b[39m]:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/single_cell_preprocess/lib/python3.11/site-packages/anndata/_io/h5ad.py:221\u001b[39m, in \u001b[36mread_h5ad\u001b[39m\u001b[34m(filename, backed, as_sparse, as_sparse_fmt, chunk_size)\u001b[39m\n\u001b[32m    219\u001b[39m         mode = \u001b[33m\"\u001b[39m\u001b[33mr+\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    220\u001b[39m     \u001b[38;5;28;01<PERSON>ert\u001b[39;00m mode \u001b[38;5;129;01min\u001b[39;00m {\u001b[33m\"\u001b[39m\u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mr+\u001b[39m\u001b[33m\"\u001b[39m}\n\u001b[32m--> \u001b[39m\u001b[32m221\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mread_h5ad_backed\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    223\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m as_sparse_fmt \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m (sparse.csr_matrix, sparse.csc_matrix):\n\u001b[32m    224\u001b[39m     msg = \u001b[33m\"\u001b[39m\u001b[33mDense formats can only be read to CSR or CSC matrices at this time.\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/single_cell_preprocess/lib/python3.11/site-packages/anndata/_io/h5ad.py:152\u001b[39m, in \u001b[36mread_h5ad_backed\u001b[39m\u001b[34m(filename, mode)\u001b[39m\n\u001b[32m    147\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mread_h5ad_backed\u001b[39m(\n\u001b[32m    148\u001b[39m     filename: \u001b[38;5;28mstr\u001b[39m | PathLike[\u001b[38;5;28mstr\u001b[39m], mode: Literal[\u001b[33m\"\u001b[39m\u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mr+\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m    149\u001b[39m ) -> AnnData:\n\u001b[32m    150\u001b[39m     d = \u001b[38;5;28mdict\u001b[39m(filename=filename, filemode=mode)\n\u001b[32m--> \u001b[39m\u001b[32m152\u001b[39m     f = \u001b[43mh5py\u001b[49m\u001b[43m.\u001b[49m\u001b[43mFile\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    154\u001b[39m     attributes = [\u001b[33m\"\u001b[39m\u001b[33mobsm\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mvarm\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mobsp\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mvarp\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33muns\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mlayers\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m    155\u001b[39m     df_attributes = [\u001b[33m\"\u001b[39m\u001b[33mobs\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mvar\u001b[39m\u001b[33m\"\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/single_cell_preprocess/lib/python3.11/site-packages/h5py/_hl/files.py:564\u001b[39m, in \u001b[36mFile.__init__\u001b[39m\u001b[34m(self, name, mode, driver, libver, userblock_size, swmr, rdcc_nslots, rdcc_nbytes, rdcc_w0, track_order, fs_strategy, fs_persist, fs_threshold, fs_page_size, page_buf_size, min_meta_keep, min_raw_keep, locking, alignment_threshold, alignment_interval, meta_block_size, **kwds)\u001b[39m\n\u001b[32m    555\u001b[39m     fapl = make_fapl(driver, libver, rdcc_nslots, rdcc_nbytes, rdcc_w0,\n\u001b[32m    556\u001b[39m                      locking, page_buf_size, min_meta_keep, min_raw_keep,\n\u001b[32m    557\u001b[39m                      alignment_threshold=alignment_threshold,\n\u001b[32m    558\u001b[39m                      alignment_interval=alignment_interval,\n\u001b[32m    559\u001b[39m                      meta_block_size=meta_block_size,\n\u001b[32m    560\u001b[39m                      **kwds)\n\u001b[32m    561\u001b[39m     fcpl = make_fcpl(track_order=track_order, fs_strategy=fs_strategy,\n\u001b[32m    562\u001b[39m                      fs_persist=fs_persist, fs_threshold=fs_threshold,\n\u001b[32m    563\u001b[39m                      fs_page_size=fs_page_size)\n\u001b[32m--> \u001b[39m\u001b[32m564\u001b[39m     fid = \u001b[43mmake_fid\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muserblock_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfapl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfcpl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mswmr\u001b[49m\u001b[43m=\u001b[49m\u001b[43mswmr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    566\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(libver, \u001b[38;5;28mtuple\u001b[39m):\n\u001b[32m    567\u001b[39m     \u001b[38;5;28mself\u001b[39m._libver = libver\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/single_cell_preprocess/lib/python3.11/site-packages/h5py/_hl/files.py:238\u001b[39m, in \u001b[36mmake_fid\u001b[39m\u001b[34m(name, mode, userblock_size, fapl, fcpl, swmr)\u001b[39m\n\u001b[32m    236\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m swmr \u001b[38;5;129;01mand\u001b[39;00m swmr_support:\n\u001b[32m    237\u001b[39m         flags |= h5f.ACC_SWMR_READ\n\u001b[32m--> \u001b[39m\u001b[32m238\u001b[39m     fid = \u001b[43mh5f\u001b[49m\u001b[43m.\u001b[49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mflags\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfapl\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfapl\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    239\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m mode == \u001b[33m'\u001b[39m\u001b[33mr+\u001b[39m\u001b[33m'\u001b[39m:\n\u001b[32m    240\u001b[39m     fid = h5f.open(name, h5f.ACC_RDWR, fapl=fapl)\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/_objects.pyx:56\u001b[39m, in \u001b[36mh5py._objects.with_phil.wrapper\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/_objects.pyx:57\u001b[39m, in \u001b[36mh5py._objects.with_phil.wrapper\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/h5f.pyx:102\u001b[39m, in \u001b[36mh5py.h5f.open\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31mBlockingIOError\u001b[39m: [Errno 11] Unable to synchronously open file (unable to lock file, errno = 11, error message = 'Resource temporarily unavailable')"]}], "source": ["import os\n", "import anndata\n", "import h5py\n", "\n", "folder = \"/data/casia_iva/competition_support_set\"\n", "files = [f for f in os.listdir(folder) if f.endswith(\".h5ad\") or f.endswith(\".h5\")]\n", "\n", "# 存储所有生成的变量名\n", "generated_vars = []\n", "\n", "for fname in files:\n", "    print(f\"\\n处理文件: {fname}\")\n", "    fpath = os.path.join(folder, fname)\n", "    base_name = fname.replace('.', '_')\n", "    \n", "    if fname.endswith(\".h5ad\"):\n", "        adata = sc.read_h5ad(fpath)\n", "        # 创建变量并添加到全局命名空间\n", "        for data_type in ['obs', 'var', 'X']:\n", "            var_name = f\"{base_name}_{data_type}\"\n", "            globals()[var_name] = getattr(adata, data_type)\n", "            generated_vars.append(var_name)\n", "            print(f\"创建变量: {var_name} (类型: {type(globals()[var_name]).__name__})\")\n", "    \n", "    elif fname.endswith(\".h5\"):\n", "        with h5py.File(fpath, \"r\") as f:\n", "            def get_data(name):\n", "                if name not in f:\n", "                    return None\n", "                obj = f[name]\n", "                if isinstance(obj, h5py.Dataset):\n", "                    return obj[()]\n", "                elif isinstance(obj, h5py.Group):\n", "                    return {k: v[()] if isinstance(v, h5py.Dataset) else v for k, v in obj.items()}\n", "                return None\n", "            \n", "            # 创建变量并添加到全局命名空间\n", "            for data_type in ['obs', 'var', 'X']:\n", "                var_name = f\"{base_name}_{data_type}\"\n", "                data = get_data(data_type)\n", "                globals()[var_name] = data\n", "                generated_vars.append(var_name)\n", "                print(f\"创建变量: {var_name} (类型: {type(data).__name__})\")\n", "\n", "# 在<PERSON><PERSON><PERSON>中输出可用变量名\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"在Jupyter中可直接使用的变量名列表：\")\n", "print(\"=\"*50)\n", "for var_name in generated_vars:\n", "    print(f\"• {var_name}\")\n", "print(\"\\n提示：复制变量名到新cell中直接使用\")\n", "print(\"=\"*50)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--------------------------------------------------\n", "处理文件: competition_val_template.h5ad\n", "❌ 读取失败: [<PERSON>rrno 11] Unable to synchronously open file (unable to lock file, errno = 11, error message = 'Resource temporarily unavailable')\n", "\n", "==================================================\n", "可用全局变量列表:\n", "==================================================\n", "==================================================\n", "\n"]}], "source": ["import os\n", "import scanpy as sc\n", "\n", "folder = \"/data/casia_iva/competition_support_set\"\n", "files = [f for f in os.listdir(folder) if f.endswith(\".h5ad\")]\n", "\n", "# 存储所有生成的变量名\n", "generated_vars = []\n", "\n", "for fname in files:\n", "    print(f\"\\n{'-'*50}\\n处理文件: {fname}\")\n", "    fpath = os.path.join(folder, fname)\n", "    base_name = fname.replace('.', '_')\n", "    \n", "    try:\n", "        # 使用Scanpy读取h5ad文件（避免文件锁定问题）\n", "        adata = sc.read_h5ad(fpath)\n", "        \n", "        # 创建全局变量（<PERSON><PERSON><PERSON>中直接使用）\n", "        for data_type in ['obs', 'var', 'X']:\n", "            var_name = f\"{base_name}_{data_type}\"\n", "            globals()[var_name] = getattr(adata, data_type)\n", "            generated_vars.append(var_name)\n", "        \n", "        # 打印关键信息\n", "        print(f\"\\n● OBS (细胞元数据):\")\n", "        print(adata.obs.head(3))  # 显示前3行\n", "        \n", "        print(f\"\\n● VAR (基因元数据):\")\n", "        print(adata.var.head(3))  # 显示前3行\n", "        \n", "        print(f\"\\n● X (表达矩阵):\")\n", "        print(f\"  形状: {adata.X.shape} | 数据类型: {type(adata.X).__name__}\")\n", "        \n", "        # 计算并打印唯一基因数量（注意：基因信息在var中）\n", "        if 'gene' in adata.var.columns:\n", "            unique_genes = adata.var['gene'].nunique()\n", "            print(f\"\\n● 唯一基因数量: {unique_genes}\")\n", "        else:\n", "            print(f\"\\n⚠ 未找到'gene'列，使用索引计数 | 唯一基因: {adata.n_vars}\")\n", "    \n", "    except Exception as e:\n", "        print(f\"❌ 读取失败: {str(e)}\")\n", "\n", "# 输出可用变量名（<PERSON>py<PERSON>中直接复制使用）\n", "print(f\"\\n{'='*50}\\n可用全局变量列表:\\n{'='*50}\")\n", "for var_name in generated_vars:\n", "    print(f\"→ {var_name}\")\n", "print(f\"{'='*50}\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "jupyter_r_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}