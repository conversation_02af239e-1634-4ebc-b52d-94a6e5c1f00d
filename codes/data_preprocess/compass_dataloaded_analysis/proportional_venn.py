#!/usr/bin/env python3
"""
绘制比例韦恩图，每个文件一个圆圈，大小与基因数量成正比
"""

import h5py
import pandas as pd
import numpy as np
import os
import glob
from collections import defaultdict
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Circle
import seaborn as sns
from pathlib import Path
import math

def read_categorical_data(group, column_name):
    """
    读取h5ad文件中的分类数据
    """
    try:
        if column_name in group:
            col_group = group[column_name]
            if isinstance(col_group, h5py.Group) and 'categories' in col_group and 'codes' in col_group:
                # 这是分类数据
                categories = col_group['categories'][:]
                codes = col_group['codes'][:]
                
                # 处理字符串编码
                if hasattr(categories[0], 'decode'):
                    categories = [x.decode('utf-8') for x in categories]
                
                # 映射codes到categories
                data = []
                for code in codes:
                    if code >= 0 and code < len(categories):
                        data.append(categories[code])
                    else:
                        data.append(None)
                
                return set([x for x in data if x is not None and str(x) != 'nan'])
            else:
                # 普通数据
                data = group[column_name][:]
                if hasattr(data[0], 'decode'):
                    data = [x.decode('utf-8') for x in data]
                return set([str(x) for x in data if x is not None and str(x) != 'nan'])
    except Exception as e:
        print(f"  - 读取{column_name}时出错: {e}")
    
    return set()

def extract_genes_from_h5ad(file_path, obs_column, var_column=None):
    """
    从h5ad文件中提取基因信息
    """
    try:
        with h5py.File(file_path, 'r') as f:
            obs_genes = set()
            var_genes = set()
            
            # 提取obs中的基因信息
            if 'obs' in f:
                obs_genes = read_categorical_data(f['obs'], obs_column)
            
            # 提取var中的基因信息
            if 'var' in f and var_column:
                var_genes = read_categorical_data(f['var'], var_column)
                if len(var_genes) == 0 and '_index' in f['var']:
                    # 如果没有找到指定列，尝试使用_index
                    index_data = f['var']['_index'][:]
                    if hasattr(index_data[0], 'decode'):
                        index_data = [x.decode('utf-8') for x in index_data]
                    var_genes = set([str(x) for x in index_data if x is not None])
            
            return obs_genes, var_genes
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return set(), set()

def calculate_circle_positions(n_circles):
    """
    计算圆圈的位置，使它们尽可能均匀分布
    """
    positions = []
    
    if n_circles == 1:
        positions = [(0, 0)]
    elif n_circles == 2:
        positions = [(-2, 0), (2, 0)]
    elif n_circles <= 6:
        # 圆形排列
        angles = np.linspace(0, 2*np.pi, n_circles, endpoint=False)
        radius = 4
        for angle in angles:
            x = radius * np.cos(angle)
            y = radius * np.sin(angle)
            positions.append((x, y))
    else:
        # 多层圆形排列
        inner_count = min(6, n_circles)
        outer_count = n_circles - inner_count
        
        # 内圈
        angles_inner = np.linspace(0, 2*np.pi, inner_count, endpoint=False)
        radius_inner = 3
        for angle in angles_inner:
            x = radius_inner * np.cos(angle)
            y = radius_inner * np.sin(angle)
            positions.append((x, y))
        
        # 外圈
        if outer_count > 0:
            angles_outer = np.linspace(0, 2*np.pi, outer_count, endpoint=False)
            radius_outer = 6
            for angle in angles_outer:
                x = radius_outer * np.cos(angle)
                y = radius_outer * np.sin(angle)
                positions.append((x, y))
    
    return positions

def create_proportional_venn_diagram(datasets, output_dir):
    """
    创建比例韦恩图，圆圈大小与基因数量成正比
    """
    # 设置颜色
    colors = plt.cm.Set3(np.linspace(0, 1, len(datasets)))
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 16))
    
    # 计算圆圈大小
    gene_counts = [len(gene_set) for gene_set in datasets.values()]
    max_genes = max(gene_counts)
    min_genes = min(gene_counts)
    
    # 设置圆圈半径范围
    max_radius = 2.5
    min_radius = 0.3
    
    # 计算每个圆圈的半径（与基因数量的平方根成正比，这样面积与基因数量成正比）
    radii = []
    for count in gene_counts:
        if max_genes == min_genes:
            radius = max_radius
        else:
            # 使用平方根缩放，使面积与基因数量成正比
            normalized = (math.sqrt(count) - math.sqrt(min_genes)) / (math.sqrt(max_genes) - math.sqrt(min_genes))
            radius = min_radius + normalized * (max_radius - min_radius)
        radii.append(radius)
    
    # 计算圆圈位置
    positions = calculate_circle_positions(len(datasets))
    
    # 绘制圆圈
    circles = []
    labels = []
    
    for i, ((name, gene_set), (x, y), radius) in enumerate(zip(datasets.items(), positions, radii)):
        # 创建圆圈
        circle = Circle((x, y), radius, alpha=0.4, 
                       facecolor=colors[i], edgecolor='black', linewidth=2)
        ax.add_patch(circle)
        circles.append(circle)
        
        # 简化标签名称
        if 'HEK293T' in name:
            short_name = 'HEK293T'
        elif 'HCT116' in name:
            short_name = 'HCT116'
        elif 'competition_train' in name:
            short_name = 'Training Set'
        elif 'competition_val' in name:
            short_name = 'Validation Set'
        elif 'Replogle' in name:
            if 'K562_gwps' in name:
                short_name = 'Replogle\nK562 GWPS'
            elif 'K562_essential' in name:
                short_name = 'Replogle\nK562 Essential'
            elif 'rpe1' in name:
                short_name = 'Replogle\nRPE1'
            else:
                short_name = 'Replogle'
        elif 'JIANG' in name:
            if 'INS' in name:
                short_name = 'JIANG\nINS'
            elif 'TNFA' in name:
                short_name = 'JIANG\nTNFA'
            elif 'IFNG' in name:
                short_name = 'JIANG\nIFNG'
            elif 'IFNB' in name:
                short_name = 'JIANG\nIFNB'
            elif 'TGFB' in name:
                short_name = 'JIANG\nTGFB'
            else:
                short_name = 'JIANG'
        elif 'Nadig' in name:
            if 'jurkat' in name:
                short_name = 'Nadig\nJurkat'
            elif 'hepg2' in name:
                short_name = 'Nadig\nHepG2'
            else:
                short_name = 'Nadig'
        else:
            short_name = name[:10]
        
        # 添加标签在圆圈中心
        ax.text(x, y, f'{short_name}\n{len(gene_set):,}', 
                ha='center', va='center', fontsize=9, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
        
        labels.append(short_name)
    
    # 计算重叠统计
    all_genes = set()
    for gene_set in datasets.values():
        all_genes.update(gene_set)
    
    # 计算所有数据集的交集
    all_intersection = set.intersection(*datasets.values()) if datasets else set()
    
    # 设置图形属性
    max_coord = max([max(abs(x), abs(y)) for x, y in positions]) + max(radii) + 1
    ax.set_xlim(-max_coord, max_coord)
    ax.set_ylim(-max_coord, max_coord)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 添加标题
    plt.title('Proportional Multi-Dataset Gene Overlap Analysis\n(Circle Size Proportional to Gene Count)', 
              fontsize=18, fontweight='bold', pad=30)
    
    # 添加图例说明圆圈大小
    legend_text = 'Circle Size Legend:\n'
    legend_text += f'• Largest: {max(gene_counts):,} genes\n'
    legend_text += f'• Smallest: {min(gene_counts):,} genes\n'
    legend_text += f'• Area ∝ Gene Count'
    
    ax.text(-max_coord + 0.5, max_coord - 1, legend_text, fontsize=11, ha='left', va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    # 添加统计信息
    stats_text = f'Overall Statistics:\n'
    stats_text += f'• Total datasets: {len(datasets)}\n'
    stats_text += f'• Total unique genes: {len(all_genes):,}\n'
    stats_text += f'• All datasets intersection: {len(all_intersection):,} genes\n'
    
    if len(all_intersection) > 0:
        stats_text += f'• Common genes: {", ".join(sorted(list(all_intersection))[:3])}'
        if len(all_intersection) > 3:
            stats_text += f' (+{len(all_intersection)-3} more)'
    
    ax.text(max_coord - 0.5, max_coord - 1, stats_text, fontsize=11, ha='right', va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'proportional_venn_diagram.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"比例韦恩图已保存到: {output_file}")
    
    return {
        'total_datasets': len(datasets),
        'total_unique_genes': len(all_genes),
        'all_intersection': len(all_intersection),
        'common_genes': sorted(list(all_intersection)),
        'gene_counts': dict(zip(datasets.keys(), gene_counts))
    }

def main():
    # 定义文件路径
    hek293t_file = "/data/ioz_lx/hjx/morph/processed_data/HEK293T_filtered_dual_guide_cells_Training.h5ad"
    hct116_file = "/data/ioz_lx/hjx/morph/processed_data/HCT116_filtered_dual_guide_cells_Training.h5ad"
    compass_dir = "/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p"
    train_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_train.h5"
    val_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_val_template.h5ad"
    
    output_dir = "/data/ioz_whr_wsx/codes/data_preprocess/compass_dataloaded_analysis"
    
    # 收集所有数据集
    datasets = {}
    
    print("=" * 60)
    print("收集所有数据集的基因信息")
    print("=" * 60)
    
    # 1. HEK293T和HCT116
    if os.path.exists(hek293t_file):
        print("处理 HEK293T...")
        obs_genes, _ = extract_genes_from_h5ad(hek293t_file, 'target_gene', 'gene_id')
        datasets['HEK293T'] = obs_genes
        print(f"  - HEK293T: {len(obs_genes)} 个基因")
    
    if os.path.exists(hct116_file):
        print("处理 HCT116...")
        obs_genes, _ = extract_genes_from_h5ad(hct116_file, 'target_gene', 'gene_id')
        datasets['HCT116'] = obs_genes
        print(f"  - HCT116: {len(obs_genes)} 个基因")
    
    # 2. Compass文件
    compass_files = glob.glob(os.path.join(compass_dir, "**/*.h5ad"), recursive=True)
    for file_path in compass_files:
        file_name = os.path.basename(file_path)
        dataset_name = os.path.basename(os.path.dirname(file_path))
        key = f"{dataset_name}_{file_name.replace('.h5ad', '')}"
        
        print(f"处理 {key}...")
        obs_genes, _ = extract_genes_from_h5ad(file_path, 'gene', 'gene_name')
        datasets[key] = obs_genes
        print(f"  - {key}: {len(obs_genes)} 个基因")
    
    # 3. 训练集和测试集
    if os.path.exists(train_file):
        print("处理训练集...")
        obs_genes, _ = extract_genes_from_h5ad(train_file, 'target_gene')
        datasets['competition_train'] = obs_genes
        print(f"  - 训练集: {len(obs_genes)} 个基因")
    
    if os.path.exists(val_file):
        print("处理测试集...")
        obs_genes, _ = extract_genes_from_h5ad(val_file, 'target_gene')
        datasets['competition_val'] = obs_genes
        print(f"  - 测试集: {len(obs_genes)} 个基因")
    
    print(f"\n总共收集了 {len(datasets)} 个数据集")
    
    # 创建比例韦恩图
    print("\n" + "=" * 60)
    print("创建比例韦恩图")
    print("=" * 60)
    
    results = create_proportional_venn_diagram(datasets, output_dir)
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print(f"总数据集数量: {results['total_datasets']}")
    print(f"总唯一基因数: {results['total_unique_genes']:,}")
    print(f"所有数据集交集: {results['all_intersection']} 个基因")
    if results['common_genes']:
        print(f"共同基因: {', '.join(results['common_genes'])}")
    
    print(f"\n各数据集基因数量:")
    for name, count in sorted(results['gene_counts'].items(), key=lambda x: x[1], reverse=True):
        print(f"  - {name}: {count:,} 个基因")

if __name__ == "__main__":
    main()
