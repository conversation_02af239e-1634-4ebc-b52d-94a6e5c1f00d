#!/usr/bin/env python3
"""
提取每个文件的基因列表，保存为单独文件，然后绘制真正的韦恩图
"""

import h5py
import pandas as pd
import numpy as np
import os
import glob
from collections import defaultdict
import matplotlib.pyplot as plt
from matplotlib_venn import venn2, venn3
import matplotlib_venn as venn
from pathlib import Path

def read_categorical_data(group, column_name):
    """
    读取h5ad文件中的分类数据
    """
    try:
        if column_name in group:
            col_group = group[column_name]
            if isinstance(col_group, h5py.Group) and 'categories' in col_group and 'codes' in col_group:
                # 这是分类数据
                categories = col_group['categories'][:]
                codes = col_group['codes'][:]
                
                # 处理字符串编码
                if hasattr(categories[0], 'decode'):
                    categories = [x.decode('utf-8') for x in categories]
                
                # 映射codes到categories
                data = []
                for code in codes:
                    if code >= 0 and code < len(categories):
                        data.append(categories[code])
                    else:
                        data.append(None)
                
                return set([x for x in data if x is not None and str(x) != 'nan'])
            else:
                # 普通数据
                data = group[column_name][:]
                if hasattr(data[0], 'decode'):
                    data = [x.decode('utf-8') for x in data]
                return set([str(x) for x in data if x is not None and str(x) != 'nan'])
    except Exception as e:
        print(f"  - 读取{column_name}时出错: {e}")
    
    return set()

def extract_genes_from_h5ad(file_path, obs_column, var_column=None):
    """
    从h5ad文件中提取基因信息
    """
    try:
        with h5py.File(file_path, 'r') as f:
            obs_genes = set()
            var_genes = set()
            
            # 提取obs中的基因信息
            if 'obs' in f:
                obs_genes = read_categorical_data(f['obs'], obs_column)
            
            # 提取var中的基因信息
            if 'var' in f and var_column:
                var_genes = read_categorical_data(f['var'], var_column)
                if len(var_genes) == 0 and '_index' in f['var']:
                    # 如果没有找到指定列，尝试使用_index
                    index_data = f['var']['_index'][:]
                    if hasattr(index_data[0], 'decode'):
                        index_data = [x.decode('utf-8') for x in index_data]
                    var_genes = set([str(x) for x in index_data if x is not None])
            
            return obs_genes, var_genes
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return set(), set()

def save_gene_lists(datasets, output_dir):
    """
    保存每个数据集的基因列表到单独的文件
    """
    gene_lists_dir = os.path.join(output_dir, "gene_lists")
    os.makedirs(gene_lists_dir, exist_ok=True)
    
    print("保存基因列表到单独文件...")
    
    for dataset_name, gene_set in datasets.items():
        # 清理文件名
        safe_name = dataset_name.replace('/', '_').replace(' ', '_').replace('.', '_')
        file_path = os.path.join(gene_lists_dir, f"{safe_name}_genes.txt")
        
        # 保存基因列表
        with open(file_path, 'w') as f:
            for gene in sorted(gene_set):
                f.write(f"{gene}\n")
        
        print(f"  - {dataset_name}: {len(gene_set)} 个基因 -> {file_path}")
    
    return gene_lists_dir

def create_upset_plot_data(datasets, output_dir):
    """
    创建UpSet图的数据格式
    """
    # 获取所有基因
    all_genes = set()
    for gene_set in datasets.values():
        all_genes.update(gene_set)
    
    # 创建二进制矩阵
    dataset_names = list(datasets.keys())
    data_matrix = []
    
    for gene in sorted(all_genes):
        row = []
        for dataset_name in dataset_names:
            row.append(1 if gene in datasets[dataset_name] else 0)
        data_matrix.append([gene] + row)
    
    # 保存为CSV
    columns = ['Gene'] + dataset_names
    df = pd.DataFrame(data_matrix, columns=columns)
    
    upset_file = os.path.join(output_dir, "upset_plot_data.csv")
    df.to_csv(upset_file, index=False)
    print(f"UpSet图数据已保存到: {upset_file}")
    
    return upset_file

def create_pairwise_venn_diagrams(datasets, output_dir):
    """
    创建重要数据集之间的两两韦恩图
    """
    print("创建重要数据集的两两韦恩图...")
    
    # 定义重要的数据集对
    important_pairs = [
        ('HEK293T', 'HCT116'),
        ('HEK293T', 'competition_train'),
        ('HCT116', 'competition_train'),
        ('competition_train', 'competition_val'),
    ]
    
    # 添加Compass数据集与训练集的对比
    compass_datasets = [name for name in datasets.keys() if any(x in name for x in ['Replogle', 'JIANG', 'Nadig'])]
    for compass_name in compass_datasets:
        if len(datasets[compass_name]) > 100:  # 只对较大的数据集创建韦恩图
            important_pairs.append((compass_name, 'competition_train'))
    
    for dataset1, dataset2 in important_pairs:
        if dataset1 in datasets and dataset2 in datasets:
            set1 = datasets[dataset1]
            set2 = datasets[dataset2]
            
            plt.figure(figsize=(10, 8))
            
            # 创建韦恩图
            venn_diagram = venn2([set1, set2], (dataset1, dataset2))
            
            # 计算重叠
            intersection = set1.intersection(set2)
            
            plt.title(f'Gene Overlap: {dataset1} vs {dataset2}\n'
                     f'Overlap: {len(intersection)} genes', 
                     fontsize=12, fontweight='bold')
            
            # 添加统计信息
            plt.figtext(0.02, 0.02, 
                       f'{dataset1}: {len(set1)} genes\n'
                       f'{dataset2}: {len(set2)} genes\n'
                       f'Intersection: {len(intersection)} genes\n'
                       f'Union: {len(set1.union(set2))} genes',
                       fontsize=10, ha='left')
            
            plt.tight_layout()
            
            # 保存图片
            safe_name1 = dataset1.replace('/', '_').replace(' ', '_')
            safe_name2 = dataset2.replace('/', '_').replace(' ', '_')
            output_file = os.path.join(output_dir, f'venn_{safe_name1}_vs_{safe_name2}.png')
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"  - {dataset1} vs {dataset2}: {len(intersection)} 个重叠基因")

def create_three_way_venn_diagrams(datasets, output_dir):
    """
    创建三路韦恩图
    """
    print("创建三路韦恩图...")
    
    # 重要的三路组合
    three_way_combinations = [
        ('HEK293T', 'HCT116', 'competition_train'),
        ('HEK293T', 'competition_train', 'competition_val'),
        ('HCT116', 'competition_train', 'competition_val'),
    ]
    
    # 添加最大的Compass数据集
    largest_compass = None
    max_size = 0
    for name, gene_set in datasets.items():
        if any(x in name for x in ['Replogle', 'JIANG', 'Nadig']) and len(gene_set) > max_size:
            max_size = len(gene_set)
            largest_compass = name
    
    if largest_compass:
        three_way_combinations.append((largest_compass, 'competition_train', 'competition_val'))
    
    for dataset1, dataset2, dataset3 in three_way_combinations:
        if all(name in datasets for name in [dataset1, dataset2, dataset3]):
            set1 = datasets[dataset1]
            set2 = datasets[dataset2]
            set3 = datasets[dataset3]
            
            plt.figure(figsize=(12, 8))
            
            # 创建韦恩图
            venn_diagram = venn3([set1, set2, set3], (dataset1, dataset2, dataset3))
            
            # 计算重叠
            intersection_all = set1.intersection(set2).intersection(set3)
            
            plt.title(f'Gene Overlap: {dataset1} vs {dataset2} vs {dataset3}\n'
                     f'Three-way overlap: {len(intersection_all)} genes', 
                     fontsize=12, fontweight='bold')
            
            # 添加统计信息
            plt.figtext(0.02, 0.02, 
                       f'{dataset1}: {len(set1)} genes\n'
                       f'{dataset2}: {len(set2)} genes\n'
                       f'{dataset3}: {len(set3)} genes\n'
                       f'Three-way intersection: {len(intersection_all)} genes',
                       fontsize=10, ha='left')
            
            plt.tight_layout()
            
            # 保存图片
            safe_names = [name.replace('/', '_').replace(' ', '_') for name in [dataset1, dataset2, dataset3]]
            output_file = os.path.join(output_dir, f'venn3_{"_vs_".join(safe_names)}.png')
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"  - {dataset1} vs {dataset2} vs {dataset3}: {len(intersection_all)} 个三路重叠基因")

def create_summary_statistics(datasets, output_dir):
    """
    创建详细的统计摘要
    """
    print("创建统计摘要...")
    
    # 计算所有可能的重叠
    dataset_names = list(datasets.keys())
    n = len(dataset_names)
    
    # 创建重叠统计
    overlap_stats = []
    
    # 单个数据集统计
    for name in dataset_names:
        overlap_stats.append({
            'Type': 'Single',
            'Datasets': name,
            'Gene_Count': len(datasets[name]),
            'Percentage': 100.0
        })
    
    # 两两重叠
    for i in range(n):
        for j in range(i+1, n):
            name1, name2 = dataset_names[i], dataset_names[j]
            intersection = datasets[name1].intersection(datasets[name2])
            union = datasets[name1].union(datasets[name2])
            
            overlap_stats.append({
                'Type': 'Pairwise',
                'Datasets': f'{name1} ∩ {name2}',
                'Gene_Count': len(intersection),
                'Percentage': len(intersection) / len(union) * 100 if len(union) > 0 else 0
            })
    
    # 保存统计结果
    stats_df = pd.DataFrame(overlap_stats)
    stats_file = os.path.join(output_dir, "detailed_overlap_statistics.csv")
    stats_df.to_csv(stats_file, index=False)
    print(f"详细统计已保存到: {stats_file}")
    
    return stats_df

def main():
    # 定义文件路径 - 新批次数据
    morph_data_dir = "/data/ioz_lx/hjx/morph/processed_data"
    train_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_train.h5"
    val_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_val_template.h5ad"

    output_dir = "/data/ioz_whr_wsx/codes/data_preprocess/compass_dataloaded_analysis"
    
    # 收集所有数据集
    datasets = {}
    
    print("=" * 60)
    print("收集所有数据集的基因信息")
    print("=" * 60)

    # 1. 处理新批次的morph数据
    morph_files = glob.glob(os.path.join(morph_data_dir, "*.h5ad"))
    for file_path in morph_files:
        file_name = os.path.basename(file_path)
        dataset_name = file_name.replace('.h5ad', '').replace('_Training', '')

        print(f"处理 {dataset_name}...")

        # 统一处理方式：所有新批次文件都使用target_gene作为obs列，gene_id作为var列
        obs_genes, var_genes = extract_genes_from_h5ad(file_path, 'target_gene', 'gene_id')

        # 如果obs中没有基因，尝试从var中获取
        if len(obs_genes) == 0 and len(var_genes) > 0:
            obs_genes = var_genes
            print(f"    从var中获取基因: {len(var_genes)} 个")

        # 如果还是没有，尝试其他可能的列名
        if len(obs_genes) == 0:
            obs_genes, _ = extract_genes_from_h5ad(file_path, 'gene', 'gene_name')
            if len(obs_genes) == 0:
                obs_genes, _ = extract_genes_from_h5ad(file_path, 'gene_symbol', 'gene_symbol')

        datasets[dataset_name] = obs_genes
        print(f"  - {dataset_name}: {len(obs_genes)} 个基因")

    # 2. 训练集和测试集（作为参考）
    if os.path.exists(train_file):
        print("处理训练集...")
        obs_genes, _ = extract_genes_from_h5ad(train_file, 'target_gene')
        datasets['competition_train'] = obs_genes
        print(f"  - 训练集: {len(obs_genes)} 个基因")

    if os.path.exists(val_file):
        print("处理测试集...")
        obs_genes, _ = extract_genes_from_h5ad(val_file, 'target_gene')
        datasets['competition_val'] = obs_genes
        print(f"  - 测试集: {len(obs_genes)} 个基因")
    
    print(f"\n总共收集了 {len(datasets)} 个数据集")
    
    # 保存基因列表
    print("\n" + "=" * 60)
    print("保存基因列表")
    print("=" * 60)
    gene_lists_dir = save_gene_lists(datasets, output_dir)
    
    # 创建UpSet图数据
    print("\n" + "=" * 60)
    print("创建UpSet图数据")
    print("=" * 60)
    upset_file = create_upset_plot_data(datasets, output_dir)
    
    # 创建韦恩图
    print("\n" + "=" * 60)
    print("创建韦恩图")
    print("=" * 60)
    create_pairwise_venn_diagrams(datasets, output_dir)
    create_three_way_venn_diagrams(datasets, output_dir)
    
    # 创建统计摘要
    print("\n" + "=" * 60)
    print("创建统计摘要")
    print("=" * 60)
    stats_df = create_summary_statistics(datasets, output_dir)
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print(f"基因列表保存在: {gene_lists_dir}")
    print(f"UpSet图数据: {upset_file}")
    print(f"所有结果保存在: {output_dir}")

if __name__ == "__main__":
    main()
