#!/usr/bin/env python3
"""
创建积累折线图和直方图，显示基因重叠的累积情况
"""

import pandas as pd
import numpy as np
import os
import glob
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

def load_gene_lists(gene_lists_dir):
    """
    从保存的文件中加载基因列表
    """
    datasets = {}
    
    for file_path in sorted(glob.glob(os.path.join(gene_lists_dir, "*_genes.txt"))):
        file_name = os.path.basename(file_path)
        dataset_name = file_name.replace('_genes.txt', '')
        
        # 读取基因列表
        with open(file_path, 'r') as f:
            genes = set([line.strip() for line in f if line.strip()])
        
        datasets[dataset_name] = genes
        print(f"加载 {dataset_name}: {len(genes)} 个基因")
    
    return datasets

def simplify_dataset_names(datasets):
    """
    简化数据集名称以便显示
    """
    simplified = {}

    for name, gene_set in datasets.items():
        if 'HEK293T' in name:
            short_name = 'HEK293T'
        elif 'HCT116' in name:
            short_name = 'HCT116'
        elif 'competition_train' in name:
            short_name = 'Training'
        elif 'competition_val' in name:
            short_name = 'Validation'
        elif 'K562_gwps' in name:
            short_name = 'K562_GWPS'
        elif 'K562_essential' in name:
            short_name = 'K562_Essential'
        elif 'rpe1' in name:
            short_name = 'RPE1'
        elif 'GSE264667_jurkat' in name:
            short_name = 'Jurkat'
        elif 'GSE264667_hepg2' in name:
            short_name = 'HepG2'
        else:
            short_name = name[:15]

        simplified[short_name] = gene_set

    return simplified

def create_cumulative_overlap_plot(datasets, output_dir):
    """
    创建积累折线图和直方图
    """
    print("创建积累折线图和直方图...")
    
    # 获取训练集和验证集
    train_genes = datasets.get('Training', set())
    val_genes = datasets.get('Validation', set())
    
    print(f"训练集基因数: {len(train_genes)}")
    print(f"验证集基因数: {len(val_genes)}")
    
    # 排除训练集和验证集，按基因数量从小到大排序其他数据集
    other_datasets = {name: genes for name, genes in datasets.items() 
                     if name not in ['Training', 'Validation']}
    
    sorted_datasets = sorted(other_datasets.items(), key=lambda x: len(x[1]))
    
    print(f"其他数据集排序 (从小到大):")
    for name, genes in sorted_datasets:
        print(f"  {name}: {len(genes)} 个基因")
    
    # 计算累积重叠
    cumulative_genes = set()
    cumulative_train_overlap = []
    cumulative_val_overlap = []
    dataset_names = []
    dataset_sizes = []
    
    for name, genes in sorted_datasets:
        cumulative_genes.update(genes)
        
        # 计算与训练集和验证集的重叠
        train_overlap = len(cumulative_genes.intersection(train_genes))
        val_overlap = len(cumulative_genes.intersection(val_genes))
        
        cumulative_train_overlap.append(train_overlap)
        cumulative_val_overlap.append(val_overlap)
        dataset_names.append(name)
        dataset_sizes.append(len(cumulative_genes))
        
        print(f"累积到 {name}: 总基因 {len(cumulative_genes)}, 训练集重叠 {train_overlap}, 验证集重叠 {val_overlap}")
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12), height_ratios=[2, 1])
    
    # 上图：累积折线图
    x_positions = range(len(dataset_names))
    
    # 绘制折线
    ax1.plot(x_positions, cumulative_train_overlap, 'r-o', linewidth=3, markersize=8, 
             label=f'Training Set Overlap (Max: {max(cumulative_train_overlap)})', alpha=0.8)
    ax1.plot(x_positions, cumulative_val_overlap, 'g-s', linewidth=3, markersize=8, 
             label=f'Validation Set Overlap (Max: {max(cumulative_val_overlap)})', alpha=0.8)
    
    # 添加目标线
    ax1.axhline(y=151, color='red', linestyle='--', alpha=0.7, label='Training Set Total (151)')
    ax1.axhline(y=51, color='green', linestyle='--', alpha=0.7, label='Validation Set Total (51)')
    
    # 设置x轴标签
    ax1.set_xticks(x_positions)
    ax1.set_xticklabels(dataset_names, rotation=45, ha='right')
    ax1.set_ylabel('Cumulative Overlap Gene Count')
    ax1.set_title('Cumulative Gene Overlap with Training and Validation Sets\n(Datasets Ordered by Size)', 
                  fontsize=14, fontweight='bold')
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (train_val, val_val) in enumerate(zip(cumulative_train_overlap, cumulative_val_overlap)):
        ax1.text(i, train_val + 2, str(train_val), ha='center', va='bottom', fontsize=8, color='red')
        ax1.text(i, val_val + 2, str(val_val), ha='center', va='bottom', fontsize=8, color='green')
    
    # 下图：直方图 - 显示最终累积基因在训练集/验证集中的分布
    final_cumulative_genes = cumulative_genes
    
    # 计算各类基因数量
    in_train_only = final_cumulative_genes.intersection(train_genes) - val_genes
    in_val_only = final_cumulative_genes.intersection(val_genes) - train_genes
    in_both = final_cumulative_genes.intersection(train_genes).intersection(val_genes)
    in_neither = final_cumulative_genes - train_genes - val_genes
    
    categories = ['In Training\nOnly', 'In Validation\nOnly', 'In Both\nTrain & Val', 'In Neither\nTrain nor Val']
    positive_counts = [len(in_train_only), len(in_val_only), len(in_both), 0]
    negative_counts = [0, 0, 0, -len(in_neither)]
    
    x_pos = range(len(categories))
    
    # 绘制正向柱状图（在训练集或验证集中）
    bars_pos = ax2.bar(x_pos, positive_counts, color=['red', 'green', 'orange', 'gray'], 
                       alpha=0.7, label='In Train/Val Sets')
    
    # 绘制负向柱状图（不在训练集和验证集中）
    bars_neg = ax2.bar(x_pos, negative_counts, color=['red', 'green', 'orange', 'gray'], 
                       alpha=0.7, label='Not in Train/Val Sets')
    
    # 添加数值标签
    for i, (pos_val, neg_val) in enumerate(zip(positive_counts, negative_counts)):
        if pos_val > 0:
            ax2.text(i, pos_val + max(positive_counts)*0.01, str(pos_val), 
                    ha='center', va='bottom', fontweight='bold')
        if neg_val < 0:
            ax2.text(i, neg_val - abs(min(negative_counts))*0.01, str(abs(neg_val)), 
                    ha='center', va='top', fontweight='bold')
    
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(categories)
    ax2.set_ylabel('Gene Count')
    ax2.set_title(f'Final Cumulative Gene Distribution\n(Total Cumulative Genes: {len(final_cumulative_genes):,})', 
                  fontsize=12, fontweight='bold')
    ax2.axhline(y=0, color='black', linewidth=1)
    ax2.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = (
        f'Statistics:\n'
        f'• Total cumulative genes: {len(final_cumulative_genes):,}\n'
        f'• In training set: {len(final_cumulative_genes.intersection(train_genes)):,}\n'
        f'• In validation set: {len(final_cumulative_genes.intersection(val_genes)):,}\n'
        f'• In both sets: {len(in_both):,}\n'
        f'• In neither set: {len(in_neither):,}'
    )
    
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=10, 
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'cumulative_gene_overlap_analysis.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"积累折线图和直方图已保存到: {output_file}")
    
    # 保存详细数据
    cumulative_data = {
        'Dataset': dataset_names,
        'Cumulative_Gene_Count': dataset_sizes,
        'Train_Overlap': cumulative_train_overlap,
        'Val_Overlap': cumulative_val_overlap
    }
    
    cumulative_df = pd.DataFrame(cumulative_data)
    data_file = os.path.join(output_dir, 'cumulative_overlap_data.csv')
    cumulative_df.to_csv(data_file, index=False)
    print(f"累积数据已保存到: {data_file}")
    
    return {
        'final_cumulative_genes': len(final_cumulative_genes),
        'final_train_overlap': max(cumulative_train_overlap),
        'final_val_overlap': max(cumulative_val_overlap),
        'in_train_only': len(in_train_only),
        'in_val_only': len(in_val_only),
        'in_both': len(in_both),
        'in_neither': len(in_neither)
    }

def main():
    output_dir = "/data/ioz_whr_wsx/codes/data_preprocess/compass_dataloaded_analysis"
    gene_lists_dir = os.path.join(output_dir, "gene_lists")
    
    print("=" * 60)
    print("创建积累基因重叠分析")
    print("=" * 60)
    
    # 加载基因列表
    datasets = load_gene_lists(gene_lists_dir)
    print(f"\n加载了 {len(datasets)} 个数据集")
    
    # 简化数据集名称
    simplified_datasets = simplify_dataset_names(datasets)
    
    # 创建积累折线图和直方图
    print("\n" + "=" * 60)
    print("创建积累折线图和直方图")
    print("=" * 60)
    
    results = create_cumulative_overlap_plot(simplified_datasets, output_dir)
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print(f"最终累积基因数: {results['final_cumulative_genes']:,}")
    print(f"与训练集重叠: {results['final_train_overlap']} / 151")
    print(f"与验证集重叠: {results['final_val_overlap']} / 51")
    print(f"仅在训练集: {results['in_train_only']}")
    print(f"仅在验证集: {results['in_val_only']}")
    print(f"在两个集合中: {results['in_both']}")
    print(f"都不在: {results['in_neither']}")

if __name__ == "__main__":
    main()
