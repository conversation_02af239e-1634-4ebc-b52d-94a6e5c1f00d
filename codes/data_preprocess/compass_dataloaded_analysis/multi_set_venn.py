#!/usr/bin/env python3
"""
绘制多集合韦恩图，每个文件作为一个独立的集合
"""

import h5py
import pandas as pd
import numpy as np
import os
import glob
from collections import defaultdict
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Ellipse
import seaborn as sns
from pathlib import Path

def read_categorical_data(group, column_name):
    """
    读取h5ad文件中的分类数据
    """
    try:
        if column_name in group:
            col_group = group[column_name]
            if isinstance(col_group, h5py.Group) and 'categories' in col_group and 'codes' in col_group:
                # 这是分类数据
                categories = col_group['categories'][:]
                codes = col_group['codes'][:]
                
                # 处理字符串编码
                if hasattr(categories[0], 'decode'):
                    categories = [x.decode('utf-8') for x in categories]
                
                # 映射codes到categories
                data = []
                for code in codes:
                    if code >= 0 and code < len(categories):
                        data.append(categories[code])
                    else:
                        data.append(None)
                
                return set([x for x in data if x is not None and str(x) != 'nan'])
            else:
                # 普通数据
                data = group[column_name][:]
                if hasattr(data[0], 'decode'):
                    data = [x.decode('utf-8') for x in data]
                return set([str(x) for x in data if x is not None and str(x) != 'nan'])
    except Exception as e:
        print(f"  - 读取{column_name}时出错: {e}")
    
    return set()

def extract_genes_from_h5ad(file_path, obs_column, var_column=None):
    """
    从h5ad文件中提取基因信息
    """
    try:
        with h5py.File(file_path, 'r') as f:
            obs_genes = set()
            var_genes = set()
            
            # 提取obs中的基因信息
            if 'obs' in f:
                obs_genes = read_categorical_data(f['obs'], obs_column)
            
            # 提取var中的基因信息
            if 'var' in f and var_column:
                var_genes = read_categorical_data(f['var'], var_column)
                if len(var_genes) == 0 and '_index' in f['var']:
                    # 如果没有找到指定列，尝试使用_index
                    index_data = f['var']['_index'][:]
                    if hasattr(index_data[0], 'decode'):
                        index_data = [x.decode('utf-8') for x in index_data]
                    var_genes = set([str(x) for x in index_data if x is not None])
            
            return obs_genes, var_genes
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return set(), set()

def create_multi_set_venn_diagram(datasets, output_dir):
    """
    创建多集合韦恩图，使用椭圆表示每个数据集
    """
    # 设置颜色
    colors = plt.cm.Set3(np.linspace(0, 1, len(datasets)))
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    
    # 计算椭圆的位置
    n_sets = len(datasets)
    angles = np.linspace(0, 2*np.pi, n_sets, endpoint=False)
    
    # 椭圆参数
    center_radius = 3
    ellipse_width = 4
    ellipse_height = 2.5
    
    # 绘制椭圆
    ellipses = []
    labels = []
    
    for i, (name, gene_set) in enumerate(datasets.items()):
        # 计算椭圆中心位置
        x = center_radius * np.cos(angles[i])
        y = center_radius * np.sin(angles[i])
        
        # 计算椭圆的旋转角度
        angle_deg = np.degrees(angles[i])
        
        # 创建椭圆
        ellipse = Ellipse((x, y), ellipse_width, ellipse_height, 
                         angle=angle_deg, alpha=0.3, 
                         facecolor=colors[i], edgecolor='black', linewidth=2)
        ax.add_patch(ellipse)
        ellipses.append(ellipse)
        
        # 添加标签
        label_x = (center_radius + ellipse_width/2 + 0.5) * np.cos(angles[i])
        label_y = (center_radius + ellipse_width/2 + 0.5) * np.sin(angles[i])
        
        # 简化标签名称
        short_name = name.replace('_', '\n').replace('.h5ad', '').replace('.h5', '')
        if len(short_name) > 20:
            short_name = short_name[:20] + '...'
        
        ax.text(label_x, label_y, f'{short_name}\n({len(gene_set)} genes)', 
                ha='center', va='center', fontsize=9, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        labels.append(short_name)
    
    # 计算重叠统计
    all_genes = set()
    for gene_set in datasets.values():
        all_genes.update(gene_set)
    
    # 计算各种重叠
    overlap_stats = {}
    dataset_names = list(datasets.keys())
    
    # 计算所有数据集的交集
    all_intersection = set.intersection(*datasets.values()) if datasets else set()
    
    # 计算两两交集
    pairwise_overlaps = {}
    for i in range(len(dataset_names)):
        for j in range(i+1, len(dataset_names)):
            name1, name2 = dataset_names[i], dataset_names[j]
            overlap = datasets[name1].intersection(datasets[name2])
            pairwise_overlaps[f"{name1} ∩ {name2}"] = len(overlap)
    
    # 设置图形属性
    ax.set_xlim(-8, 8)
    ax.set_ylim(-8, 8)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 添加标题
    plt.title('Multi-Dataset Gene Overlap Analysis\n(Each Ellipse Represents One Dataset)', 
              fontsize=16, fontweight='bold', pad=20)
    
    # 添加统计信息
    stats_text = f'Dataset Statistics:\n'
    for name, gene_set in datasets.items():
        short_name = name.split('_')[0] if '_' in name else name
        stats_text += f'• {short_name}: {len(gene_set):,} genes\n'
    
    stats_text += f'\nOverall Statistics:\n'
    stats_text += f'• Total unique genes: {len(all_genes):,}\n'
    stats_text += f'• All datasets intersection: {len(all_intersection):,} genes\n'
    
    if len(all_intersection) > 0:
        stats_text += f'• Common genes: {", ".join(sorted(list(all_intersection))[:5])}'
        if len(all_intersection) > 5:
            stats_text += f' (+{len(all_intersection)-5} more)'
    
    # 添加统计信息到图中
    ax.text(-7.5, -7, stats_text, fontsize=10, ha='left', va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.9))
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'multi_set_venn_diagram.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"多集合韦恩图已保存到: {output_file}")
    
    # 创建详细的重叠统计表
    create_overlap_matrix(datasets, output_dir)
    
    return {
        'total_datasets': len(datasets),
        'total_unique_genes': len(all_genes),
        'all_intersection': len(all_intersection),
        'common_genes': sorted(list(all_intersection))
    }

def create_overlap_matrix(datasets, output_dir):
    """
    创建重叠矩阵热图
    """
    dataset_names = list(datasets.keys())
    n = len(dataset_names)
    
    # 创建重叠矩阵
    overlap_matrix = np.zeros((n, n))
    
    for i in range(n):
        for j in range(n):
            if i == j:
                overlap_matrix[i][j] = len(datasets[dataset_names[i]])
            else:
                overlap = datasets[dataset_names[i]].intersection(datasets[dataset_names[j]])
                overlap_matrix[i][j] = len(overlap)
    
    # 创建热图
    plt.figure(figsize=(12, 10))
    
    # 简化标签名称
    short_names = []
    for name in dataset_names:
        if 'competition' in name:
            short_names.append(name.replace('competition_', ''))
        else:
            parts = name.split('_')
            if len(parts) >= 2:
                short_names.append(f"{parts[0]}_{parts[1]}")
            else:
                short_names.append(name[:15])
    
    # 绘制热图
    sns.heatmap(overlap_matrix, 
                xticklabels=short_names, 
                yticklabels=short_names,
                annot=True, 
                fmt='.0f', 
                cmap='YlOrRd',
                square=True,
                cbar_kws={'label': 'Number of Overlapping Genes'})
    
    plt.title('Gene Overlap Matrix\n(Diagonal shows total genes per dataset)', 
              fontsize=14, fontweight='bold')
    plt.xlabel('Datasets')
    plt.ylabel('Datasets')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    plt.tight_layout()
    
    # 保存热图
    heatmap_file = os.path.join(output_dir, 'gene_overlap_heatmap.png')
    plt.savefig(heatmap_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"重叠矩阵热图已保存到: {heatmap_file}")
    
    # 保存重叠矩阵为CSV
    overlap_df = pd.DataFrame(overlap_matrix, 
                             index=short_names, 
                             columns=short_names)
    matrix_file = os.path.join(output_dir, 'gene_overlap_matrix.csv')
    overlap_df.to_csv(matrix_file)
    print(f"重叠矩阵已保存到: {matrix_file}")

def main():
    # 定义文件路径
    hek293t_file = "/data/ioz_lx/hjx/morph/processed_data/HEK293T_filtered_dual_guide_cells_Training.h5ad"
    hct116_file = "/data/ioz_lx/hjx/morph/processed_data/HCT116_filtered_dual_guide_cells_Training.h5ad"
    compass_dir = "/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p"
    train_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_train.h5"
    val_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_val_template.h5ad"
    
    output_dir = "/data/ioz_whr_wsx/codes/data_preprocess/compass_dataloaded_analysis"
    
    # 收集所有数据集
    datasets = {}
    
    print("=" * 60)
    print("收集所有数据集的基因信息")
    print("=" * 60)
    
    # 1. HEK293T和HCT116
    if os.path.exists(hek293t_file):
        print("处理 HEK293T...")
        obs_genes, _ = extract_genes_from_h5ad(hek293t_file, 'target_gene', 'gene_id')
        datasets['HEK293T'] = obs_genes
        print(f"  - HEK293T: {len(obs_genes)} 个基因")
    
    if os.path.exists(hct116_file):
        print("处理 HCT116...")
        obs_genes, _ = extract_genes_from_h5ad(hct116_file, 'target_gene', 'gene_id')
        datasets['HCT116'] = obs_genes
        print(f"  - HCT116: {len(obs_genes)} 个基因")
    
    # 2. Compass文件
    compass_files = glob.glob(os.path.join(compass_dir, "**/*.h5ad"), recursive=True)
    for file_path in compass_files:
        file_name = os.path.basename(file_path)
        dataset_name = os.path.basename(os.path.dirname(file_path))
        key = f"{dataset_name}_{file_name.replace('.h5ad', '')}"
        
        print(f"处理 {key}...")
        obs_genes, _ = extract_genes_from_h5ad(file_path, 'gene', 'gene_name')
        datasets[key] = obs_genes
        print(f"  - {key}: {len(obs_genes)} 个基因")
    
    # 3. 训练集和测试集
    if os.path.exists(train_file):
        print("处理训练集...")
        obs_genes, _ = extract_genes_from_h5ad(train_file, 'target_gene')
        datasets['competition_train'] = obs_genes
        print(f"  - 训练集: {len(obs_genes)} 个基因")
    
    if os.path.exists(val_file):
        print("处理测试集...")
        obs_genes, _ = extract_genes_from_h5ad(val_file, 'target_gene')
        datasets['competition_val'] = obs_genes
        print(f"  - 测试集: {len(obs_genes)} 个基因")
    
    print(f"\n总共收集了 {len(datasets)} 个数据集")
    
    # 创建多集合韦恩图
    print("\n" + "=" * 60)
    print("创建多集合韦恩图")
    print("=" * 60)
    
    results = create_multi_set_venn_diagram(datasets, output_dir)
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print(f"总数据集数量: {results['total_datasets']}")
    print(f"总唯一基因数: {results['total_unique_genes']:,}")
    print(f"所有数据集交集: {results['all_intersection']} 个基因")
    if results['common_genes']:
        print(f"共同基因: {', '.join(results['common_genes'])}")

if __name__ == "__main__":
    main()
