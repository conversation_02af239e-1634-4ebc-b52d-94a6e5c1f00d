#!/usr/bin/env python3
"""
单独分析每个h5ad文件中的基因信息，生成统计表和韦恩图
"""

import h5py
import pandas as pd
import numpy as np
import os
import glob
from collections import defaultdict
import matplotlib.pyplot as plt
import matplotlib_venn as venn
from pathlib import Path

def read_categorical_data(group, column_name):
    """
    读取h5ad文件中的分类数据
    """
    try:
        if column_name in group:
            col_group = group[column_name]
            if isinstance(col_group, h5py.Group) and 'categories' in col_group and 'codes' in col_group:
                # 这是分类数据
                categories = col_group['categories'][:]
                codes = col_group['codes'][:]
                
                # 处理字符串编码
                if hasattr(categories[0], 'decode'):
                    categories = [x.decode('utf-8') for x in categories]
                
                # 映射codes到categories
                data = []
                for code in codes:
                    if code >= 0 and code < len(categories):
                        data.append(categories[code])
                    else:
                        data.append(None)
                
                return set([x for x in data if x is not None and str(x) != 'nan'])
            else:
                # 普通数据
                data = group[column_name][:]
                if hasattr(data[0], 'decode'):
                    data = [x.decode('utf-8') for x in data]
                return set([str(x) for x in data if x is not None and str(x) != 'nan'])
    except Exception as e:
        print(f"  - 读取{column_name}时出错: {e}")
    
    return set()

def extract_genes_from_h5ad(file_path, obs_column, var_column=None):
    """
    从h5ad文件中提取基因信息
    """
    try:
        print(f"正在处理文件: {os.path.basename(file_path)}")
        
        with h5py.File(file_path, 'r') as f:
            obs_genes = set()
            var_genes = set()
            
            # 提取obs中的基因信息
            if 'obs' in f:
                obs_genes = read_categorical_data(f['obs'], obs_column)
                print(f"  - 从obs.{obs_column}提取到 {len(obs_genes)} 个唯一基因")
                if len(obs_genes) > 0:
                    print(f"  - 示例基因: {list(obs_genes)[:5]}")
            
            # 提取var中的基因信息
            if 'var' in f and var_column:
                var_genes = read_categorical_data(f['var'], var_column)
                if len(var_genes) == 0 and '_index' in f['var']:
                    # 如果没有找到指定列，尝试使用_index
                    index_data = f['var']['_index'][:]
                    if hasattr(index_data[0], 'decode'):
                        index_data = [x.decode('utf-8') for x in index_data]
                    var_genes = set([str(x) for x in index_data if x is not None])
                    print(f"  - 使用var._index，提取到 {len(var_genes)} 个唯一基因")
                else:
                    print(f"  - 从var.{var_column}提取到 {len(var_genes)} 个唯一基因")
            
            return obs_genes, var_genes
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return set(), set()

def create_venn_diagram_for_files(file_data_dict, output_dir):
    """
    为不同的文件组合创建韦恩图
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. HEK293T vs HCT116 vs Training Set
    if 'HEK293T' in file_data_dict and 'HCT116' in file_data_dict and 'competition_train' in file_data_dict:
        hek_genes = file_data_dict['HEK293T']['obs_genes']
        hct_genes = file_data_dict['HCT116']['obs_genes']
        train_genes = file_data_dict['competition_train']['obs_genes']
        
        plt.figure(figsize=(12, 8))
        venn_diagram = venn.venn3([hek_genes, hct_genes, train_genes], 
                                 ('HEK293T', 'HCT116', 'Training Set'))
        plt.title('Gene Overlap: HEK293T vs HCT116 vs Training Set', fontsize=14, fontweight='bold')
        
        # 计算重叠统计
        all_three = hek_genes.intersection(hct_genes).intersection(train_genes)
        hek_hct = hek_genes.intersection(hct_genes) - train_genes
        hek_train = hek_genes.intersection(train_genes) - hct_genes
        hct_train = hct_genes.intersection(train_genes) - hek_genes
        
        plt.figtext(0.02, 0.02, 
                    f'HEK293T: {len(hek_genes)} genes\n'
                    f'HCT116: {len(hct_genes)} genes\n'
                    f'Training: {len(train_genes)} genes\n'
                    f'All three overlap: {len(all_three)} genes',
                    fontsize=10, ha='left')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'venn_HEK293T_HCT116_Training.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"HEK293T vs HCT116 vs Training Set 韦恩图已保存")
        print(f"  - 三者重叠基因数: {len(all_three)}")
        if len(all_three) > 0:
            print(f"  - 重叠基因示例: {list(all_three)[:10]}")
    
    # 2. 每个Compass文件 vs Training Set vs Validation Set
    compass_files = [k for k in file_data_dict.keys() if any(x in k for x in ['Replogle', 'JIANG', 'Nadig'])]
    train_genes = file_data_dict.get('competition_train', {}).get('obs_genes', set())
    val_genes = file_data_dict.get('competition_val', {}).get('obs_genes', set())
    
    for compass_file in compass_files:
        compass_genes = file_data_dict[compass_file]['obs_genes']
        
        if len(compass_genes) > 0:
            plt.figure(figsize=(12, 8))
            venn_diagram = venn.venn3([compass_genes, train_genes, val_genes], 
                                     (compass_file.replace('_', '\n'), 'Training Set', 'Validation Set'))
            plt.title(f'Gene Overlap: {compass_file} vs Training vs Validation', fontsize=12, fontweight='bold')
            
            # 计算重叠统计
            all_three = compass_genes.intersection(train_genes).intersection(val_genes)
            compass_train = compass_genes.intersection(train_genes) - val_genes
            compass_val = compass_genes.intersection(val_genes) - train_genes
            train_val = train_genes.intersection(val_genes) - compass_genes
            
            plt.figtext(0.02, 0.02, 
                        f'{compass_file}: {len(compass_genes)} genes\n'
                        f'Training: {len(train_genes)} genes\n'
                        f'Validation: {len(val_genes)} genes\n'
                        f'All three overlap: {len(all_three)} genes',
                        fontsize=9, ha='left')
            
            plt.tight_layout()
            safe_filename = compass_file.replace('/', '_').replace(' ', '_')
            plt.savefig(os.path.join(output_dir, f'venn_{safe_filename}_Train_Val.png'), dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"{compass_file} vs Training vs Validation 韦恩图已保存")
            print(f"  - 三者重叠基因数: {len(all_three)}")

def main():
    # 定义文件路径
    hek293t_file = "/data/ioz_lx/hjx/morph/processed_data/HEK293T_filtered_dual_guide_cells_Training.h5ad"
    hct116_file = "/data/ioz_lx/hjx/morph/processed_data/HCT116_filtered_dual_guide_cells_Training.h5ad"
    compass_dir = "/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p"
    train_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_train.h5"
    val_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_val_template.h5ad"
    
    # 输出目录
    output_dir = "/data/ioz_whr_wsx/codes/data_preprocess/compass_dataloaded_analysis"
    
    # 存储结果
    file_gene_stats = {}
    
    print("=" * 60)
    print("开始分析基因信息")
    print("=" * 60)
    
    # 1. 处理HEK293T和HCT116文件
    print("\n1. 处理HEK293T和HCT116文件...")
    
    if os.path.exists(hek293t_file):
        obs_genes, var_genes = extract_genes_from_h5ad(hek293t_file, 'target_gene', 'gene_id')
        file_gene_stats['HEK293T'] = {
            'obs_genes': obs_genes,
            'var_genes': var_genes,
            'file_path': hek293t_file
        }
    else:
        print(f"文件不存在: {hek293t_file}")
    
    if os.path.exists(hct116_file):
        obs_genes, var_genes = extract_genes_from_h5ad(hct116_file, 'target_gene', 'gene_id')
        file_gene_stats['HCT116'] = {
            'obs_genes': obs_genes,
            'var_genes': var_genes,
            'file_path': hct116_file
        }
    else:
        print(f"文件不存在: {hct116_file}")
    
    # 2. 处理compass目录下的所有文件（单独处理每个文件）
    print("\n2. 处理compass目录下的文件...")
    
    compass_files = glob.glob(os.path.join(compass_dir, "**/*.h5ad"), recursive=True)
    print(f"找到 {len(compass_files)} 个h5ad文件")
    
    for file_path in compass_files:
        file_name = os.path.basename(file_path)
        dataset_name = os.path.basename(os.path.dirname(file_path))
        key = f"{dataset_name}_{file_name.replace('.h5ad', '')}"
        
        obs_genes, var_genes = extract_genes_from_h5ad(file_path, 'gene', 'gene_name')
        file_gene_stats[key] = {
            'obs_genes': obs_genes,
            'var_genes': var_genes,
            'file_path': file_path
        }
    
    # 3. 处理训练集和测试集
    print("\n3. 处理训练集和测试集...")
    
    if os.path.exists(train_file):
        obs_genes, _ = extract_genes_from_h5ad(train_file, 'target_gene')
        file_gene_stats['competition_train'] = {
            'obs_genes': obs_genes,
            'var_genes': set(),
            'file_path': train_file
        }
    else:
        print(f"文件不存在: {train_file}")
    
    if os.path.exists(val_file):
        obs_genes, _ = extract_genes_from_h5ad(val_file, 'target_gene')
        file_gene_stats['competition_val'] = {
            'obs_genes': obs_genes,
            'var_genes': set(),
            'file_path': val_file
        }
    else:
        print(f"文件不存在: {val_file}")
    
    # 生成统计表
    print("\n" + "=" * 60)
    print("生成统计表")
    print("=" * 60)
    
    stats_data = []
    for file_key, data in file_gene_stats.items():
        stats_data.append({
            'File': file_key,
            'Obs_Gene_Count': len(data['obs_genes']),
            'Var_Gene_Count': len(data['var_genes']),
            'File_Path': data['file_path']
        })
    
    stats_df = pd.DataFrame(stats_data)
    print(stats_df.to_string(index=False))
    
    # 保存统计表
    stats_file = os.path.join(output_dir, 'individual_gene_statistics.csv')
    stats_df.to_csv(stats_file, index=False)
    print(f"\n统计表已保存到: {stats_file}")
    
    # 生成韦恩图
    print("\n" + "=" * 60)
    print("生成韦恩图")
    print("=" * 60)
    
    create_venn_diagram_for_files(file_gene_stats, output_dir)
    
    print(f"\n所有分析结果已保存到: {output_dir}")

if __name__ == "__main__":
    main()
