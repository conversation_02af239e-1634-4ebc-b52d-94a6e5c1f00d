#!/usr/bin/env python3
"""
使用专门的工具创建多集合韦恩图
"""

import pandas as pd
import numpy as np
import os
import glob
import matplotlib.pyplot as plt
from matplotlib_venn import venn2, venn3
import seaborn as sns
from pathlib import Path

def load_gene_lists(gene_lists_dir):
    """
    从保存的文件中加载基因列表
    """
    datasets = {}
    
    for file_path in glob.glob(os.path.join(gene_lists_dir, "*_genes.txt")):
        file_name = os.path.basename(file_path)
        dataset_name = file_name.replace('_genes.txt', '')
        
        # 读取基因列表
        with open(file_path, 'r') as f:
            genes = set([line.strip() for line in f if line.strip()])
        
        datasets[dataset_name] = genes
        print(f"加载 {dataset_name}: {len(genes)} 个基因")
    
    return datasets

def create_upset_style_plot(datasets, output_dir):
    """
    创建类似UpSet图的可视化
    """
    # 读取UpSet数据
    upset_file = os.path.join(output_dir, "upset_plot_data.csv")
    if not os.path.exists(upset_file):
        print("UpSet数据文件不存在")
        return
    
    df = pd.read_csv(upset_file)
    
    # 计算每种组合的基因数量
    dataset_cols = [col for col in df.columns if col != 'Gene']
    
    # 创建组合统计
    combinations = []
    for _, row in df.iterrows():
        combo = tuple([col for col in dataset_cols if row[col] == 1])
        if combo:  # 只考虑至少在一个数据集中的基因
            combinations.append(combo)
    
    # 统计每种组合的频率
    from collections import Counter
    combo_counts = Counter(combinations)
    
    # 只显示出现频率较高的组合
    top_combinations = dict(combo_counts.most_common(20))
    
    # 创建可视化
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 上图：组合频率条形图
    combo_names = []
    combo_values = []
    
    for combo, count in top_combinations.items():
        if len(combo) == 1:
            name = combo[0]
        elif len(combo) == 2:
            name = f"{combo[0]} ∩ {combo[1]}"
        elif len(combo) == 3:
            name = f"{combo[0]} ∩ {combo[1]} ∩ {combo[2]}"
        else:
            name = f"{len(combo)} datasets"
        
        combo_names.append(name)
        combo_values.append(count)
    
    bars = ax1.barh(range(len(combo_names)), combo_values)
    ax1.set_yticks(range(len(combo_names)))
    ax1.set_yticklabels([name.replace('_', '\n') for name in combo_names], fontsize=8)
    ax1.set_xlabel('Number of Genes')
    ax1.set_title('Gene Set Intersections (Top 20)', fontsize=14, fontweight='bold')
    
    # 添加数值标签
    for i, (bar, value) in enumerate(zip(bars, combo_values)):
        ax1.text(bar.get_width() + max(combo_values)*0.01, bar.get_y() + bar.get_height()/2, 
                str(value), ha='left', va='center', fontsize=8)
    
    # 下图：数据集大小比较
    dataset_names = list(datasets.keys())
    dataset_sizes = [len(datasets[name]) for name in dataset_names]
    
    # 简化数据集名称
    short_names = []
    for name in dataset_names:
        if 'HEK293T' in name:
            short_names.append('HEK293T')
        elif 'HCT116' in name:
            short_names.append('HCT116')
        elif 'competition_train' in name:
            short_names.append('Train')
        elif 'competition_val' in name:
            short_names.append('Val')
        elif 'Replogle' in name:
            if 'K562_gwps' in name:
                short_names.append('Rep_K562_GWPS')
            elif 'K562_essential' in name:
                short_names.append('Rep_K562_Ess')
            elif 'rpe1' in name:
                short_names.append('Rep_RPE1')
        elif 'JIANG' in name:
            if 'INS' in name:
                short_names.append('JIANG_INS')
            elif 'TNFA' in name:
                short_names.append('JIANG_TNFA')
            elif 'IFNG' in name:
                short_names.append('JIANG_IFNG')
            elif 'IFNB' in name:
                short_names.append('JIANG_IFNB')
            elif 'TGFB' in name:
                short_names.append('JIANG_TGFB')
        elif 'Nadig' in name:
            if 'jurkat' in name:
                short_names.append('Nadig_Jurkat')
            elif 'hepg2' in name:
                short_names.append('Nadig_HepG2')
        else:
            short_names.append(name[:15])
    
    # 按大小排序
    sorted_data = sorted(zip(short_names, dataset_sizes), key=lambda x: x[1], reverse=True)
    sorted_names, sorted_sizes = zip(*sorted_data)
    
    bars2 = ax2.bar(range(len(sorted_names)), sorted_sizes, 
                   color=plt.cm.Set3(np.linspace(0, 1, len(sorted_names))))
    ax2.set_xticks(range(len(sorted_names)))
    ax2.set_xticklabels(sorted_names, rotation=45, ha='right', fontsize=8)
    ax2.set_ylabel('Number of Genes')
    ax2.set_title('Dataset Sizes', fontsize=14, fontweight='bold')
    ax2.set_yscale('log')  # 使用对数刻度因为差异很大
    
    # 添加数值标签
    for bar, size in zip(bars2, sorted_sizes):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.1, 
                str(size), ha='center', va='bottom', fontsize=8, rotation=90)
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'multi_set_analysis.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"多集合分析图已保存到: {output_file}")

def create_heatmap_visualization(datasets, output_dir):
    """
    创建基因重叠热图
    """
    dataset_names = list(datasets.keys())
    n = len(dataset_names)
    
    # 创建重叠矩阵
    overlap_matrix = np.zeros((n, n))
    jaccard_matrix = np.zeros((n, n))
    
    for i in range(n):
        for j in range(n):
            set_i = datasets[dataset_names[i]]
            set_j = datasets[dataset_names[j]]
            
            if i == j:
                overlap_matrix[i][j] = len(set_i)
                jaccard_matrix[i][j] = 1.0
            else:
                intersection = set_i.intersection(set_j)
                union = set_i.union(set_j)
                
                overlap_matrix[i][j] = len(intersection)
                jaccard_matrix[i][j] = len(intersection) / len(union) if len(union) > 0 else 0
    
    # 简化标签
    short_names = []
    for name in dataset_names:
        if 'HEK293T' in name:
            short_names.append('HEK293T')
        elif 'HCT116' in name:
            short_names.append('HCT116')
        elif 'competition_train' in name:
            short_names.append('Training')
        elif 'competition_val' in name:
            short_names.append('Validation')
        elif 'Replogle' in name:
            if 'K562_gwps' in name:
                short_names.append('Rep_K562_GWPS')
            elif 'K562_essential' in name:
                short_names.append('Rep_K562_Ess')
            elif 'rpe1' in name:
                short_names.append('Rep_RPE1')
        elif 'JIANG' in name:
            cytokine = name.split('_')[2] if len(name.split('_')) > 2 else 'JIANG'
            short_names.append(f'JIANG_{cytokine}')
        elif 'Nadig' in name:
            cell_type = 'Jurkat' if 'jurkat' in name else 'HepG2'
            short_names.append(f'Nadig_{cell_type}')
        else:
            short_names.append(name[:10])
    
    # 创建两个热图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 重叠数量热图
    sns.heatmap(overlap_matrix, 
                xticklabels=short_names, 
                yticklabels=short_names,
                annot=True, 
                fmt='.0f', 
                cmap='YlOrRd',
                square=True,
                cbar_kws={'label': 'Number of Overlapping Genes'},
                ax=ax1)
    ax1.set_title('Gene Overlap Count Matrix', fontsize=12, fontweight='bold')
    ax1.tick_params(axis='x', rotation=45)
    ax1.tick_params(axis='y', rotation=0)
    
    # Jaccard相似度热图
    sns.heatmap(jaccard_matrix, 
                xticklabels=short_names, 
                yticklabels=short_names,
                annot=True, 
                fmt='.3f', 
                cmap='viridis',
                square=True,
                cbar_kws={'label': 'Jaccard Similarity'},
                ax=ax2)
    ax2.set_title('Jaccard Similarity Matrix', fontsize=12, fontweight='bold')
    ax2.tick_params(axis='x', rotation=45)
    ax2.tick_params(axis='y', rotation=0)
    
    plt.tight_layout()
    
    # 保存图片
    heatmap_file = os.path.join(output_dir, 'gene_overlap_heatmaps.png')
    plt.savefig(heatmap_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"基因重叠热图已保存到: {heatmap_file}")

def create_key_venn_diagrams(datasets, output_dir):
    """
    创建关键的韦恩图组合
    """
    print("创建关键韦恩图...")
    
    # 关键的三路韦恩图
    key_combinations = [
        ('HEK293T', 'HCT116', 'competition_train'),
        ('HEK293T', 'competition_train', 'competition_val'),
        ('Replogle_K562_gwps_raw_singlecell_01_aligned_cellload_filtered', 'competition_train', 'competition_val'),
    ]
    
    for combo in key_combinations:
        if all(name in datasets for name in combo):
            set1 = datasets[combo[0]]
            set2 = datasets[combo[1]]
            set3 = datasets[combo[2]]
            
            plt.figure(figsize=(10, 8))
            
            # 简化标签
            labels = []
            for name in combo:
                if 'HEK293T' in name:
                    labels.append('HEK293T')
                elif 'HCT116' in name:
                    labels.append('HCT116')
                elif 'competition_train' in name:
                    labels.append('Training Set')
                elif 'competition_val' in name:
                    labels.append('Validation Set')
                elif 'Replogle_K562_gwps' in name:
                    labels.append('Replogle K562 GWPS')
                else:
                    labels.append(name[:15])
            
            venn_diagram = venn3([set1, set2, set3], labels)
            
            intersection_all = set1.intersection(set2).intersection(set3)
            
            plt.title(f'Gene Overlap: {" vs ".join(labels)}\n'
                     f'Three-way overlap: {len(intersection_all)} genes', 
                     fontsize=12, fontweight='bold')
            
            plt.tight_layout()
            
            # 保存图片
            safe_names = [name.replace(' ', '_') for name in labels]
            output_file = os.path.join(output_dir, f'key_venn_{"_".join(safe_names)}.png')
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"  - {' vs '.join(labels)}: {len(intersection_all)} 个三路重叠基因")

def main():
    output_dir = "/data/ioz_whr_wsx/codes/data_preprocess/compass_dataloaded_analysis"
    gene_lists_dir = os.path.join(output_dir, "gene_lists")
    
    print("=" * 60)
    print("创建多集合韦恩图可视化")
    print("=" * 60)
    
    # 加载基因列表
    datasets = load_gene_lists(gene_lists_dir)
    
    print(f"\n加载了 {len(datasets)} 个数据集")
    
    # 创建各种可视化
    print("\n" + "=" * 60)
    print("创建UpSet风格分析图")
    print("=" * 60)
    create_upset_style_plot(datasets, output_dir)
    
    print("\n" + "=" * 60)
    print("创建重叠热图")
    print("=" * 60)
    create_heatmap_visualization(datasets, output_dir)
    
    print("\n" + "=" * 60)
    print("创建关键韦恩图")
    print("=" * 60)
    create_key_venn_diagrams(datasets, output_dir)
    
    print("\n" + "=" * 60)
    print("多集合韦恩图分析完成")
    print("=" * 60)
    print(f"所有结果保存在: {output_dir}")

if __name__ == "__main__":
    main()
