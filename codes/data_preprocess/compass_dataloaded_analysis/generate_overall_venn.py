#!/usr/bin/env python3
"""
基于已有的统计结果，生成总的韦恩图
"""

import h5py
import pandas as pd
import numpy as np
import os
import glob
from collections import defaultdict
import matplotlib.pyplot as plt
import matplotlib_venn as venn
from pathlib import Path

def read_categorical_data(group, column_name):
    """
    读取h5ad文件中的分类数据
    """
    try:
        if column_name in group:
            col_group = group[column_name]
            if isinstance(col_group, h5py.Group) and 'categories' in col_group and 'codes' in col_group:
                # 这是分类数据
                categories = col_group['categories'][:]
                codes = col_group['codes'][:]
                
                # 处理字符串编码
                if hasattr(categories[0], 'decode'):
                    categories = [x.decode('utf-8') for x in categories]
                
                # 映射codes到categories
                data = []
                for code in codes:
                    if code >= 0 and code < len(categories):
                        data.append(categories[code])
                    else:
                        data.append(None)
                
                return set([x for x in data if x is not None and str(x) != 'nan'])
            else:
                # 普通数据
                data = group[column_name][:]
                if hasattr(data[0], 'decode'):
                    data = [x.decode('utf-8') for x in data]
                return set([str(x) for x in data if x is not None and str(x) != 'nan'])
    except Exception as e:
        print(f"  - 读取{column_name}时出错: {e}")
    
    return set()

def extract_genes_from_h5ad(file_path, obs_column, var_column=None):
    """
    从h5ad文件中提取基因信息
    """
    try:
        with h5py.File(file_path, 'r') as f:
            obs_genes = set()
            var_genes = set()
            
            # 提取obs中的基因信息
            if 'obs' in f:
                obs_genes = read_categorical_data(f['obs'], obs_column)
            
            # 提取var中的基因信息
            if 'var' in f and var_column:
                var_genes = read_categorical_data(f['var'], var_column)
                if len(var_genes) == 0 and '_index' in f['var']:
                    # 如果没有找到指定列，尝试使用_index
                    index_data = f['var']['_index'][:]
                    if hasattr(index_data[0], 'decode'):
                        index_data = [x.decode('utf-8') for x in index_data]
                    var_genes = set([str(x) for x in index_data if x is not None])
            
            return obs_genes, var_genes
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return set(), set()

def create_overall_venn_diagram(output_dir):
    """
    创建总的韦恩图：Compass数据集 vs 训练集 vs 测试集
    """
    # 定义文件路径
    compass_dir = "/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p"
    train_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_train.h5"
    val_file = "/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_val_template.h5ad"
    
    print("正在收集所有Compass文件的基因...")
    
    # 收集所有Compass文件的基因
    all_compass_genes = set()
    compass_files = glob.glob(os.path.join(compass_dir, "**/*.h5ad"), recursive=True)
    
    for file_path in compass_files:
        file_name = os.path.basename(file_path)
        print(f"  - 处理: {file_name}")
        obs_genes, _ = extract_genes_from_h5ad(file_path, 'gene', 'gene_name')
        all_compass_genes.update(obs_genes)
        print(f"    当前Compass总基因数: {len(all_compass_genes)}")
    
    print(f"Compass数据集总基因数: {len(all_compass_genes)}")
    
    # 获取训练集基因
    print("正在处理训练集...")
    train_genes = set()
    if os.path.exists(train_file):
        train_genes, _ = extract_genes_from_h5ad(train_file, 'target_gene')
        print(f"训练集基因数: {len(train_genes)}")
    
    # 获取测试集基因
    print("正在处理测试集...")
    val_genes = set()
    if os.path.exists(val_file):
        val_genes, _ = extract_genes_from_h5ad(val_file, 'target_gene')
        print(f"测试集基因数: {len(val_genes)}")
    
    # 计算重叠统计
    compass_train_overlap = all_compass_genes.intersection(train_genes)
    compass_val_overlap = all_compass_genes.intersection(val_genes)
    train_val_overlap = train_genes.intersection(val_genes)
    all_three_overlap = all_compass_genes.intersection(train_genes).intersection(val_genes)
    
    print(f"\n重叠统计:")
    print(f"Compass ∩ 训练集: {len(compass_train_overlap)} 个基因")
    print(f"Compass ∩ 测试集: {len(compass_val_overlap)} 个基因")
    print(f"训练集 ∩ 测试集: {len(train_val_overlap)} 个基因")
    print(f"三者都重叠: {len(all_three_overlap)} 个基因")
    
    if len(all_three_overlap) > 0:
        print(f"三者重叠的基因: {sorted(list(all_three_overlap))}")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建韦恩图
    plt.figure(figsize=(12, 8))
    
    # 绘制三集合韦恩图
    venn_diagram = venn.venn3([all_compass_genes, train_genes, val_genes], 
                             ('Compass Datasets\n(All Files Combined)', 'Training Set', 'Validation Set'))
    
    # 设置标题
    plt.title('Overall Gene Overlap Analysis\nCompass Datasets vs Training Set vs Validation Set', 
              fontsize=16, fontweight='bold', pad=20)
    
    # 添加统计信息
    stats_text = (
        f'Dataset Statistics:\n'
        f'• Compass: {len(all_compass_genes):,} genes\n'
        f'• Training: {len(train_genes):,} genes\n'
        f'• Validation: {len(val_genes):,} genes\n\n'
        f'Overlap Statistics:\n'
        f'• Compass ∩ Training: {len(compass_train_overlap):,} genes\n'
        f'• Compass ∩ Validation: {len(compass_val_overlap):,} genes\n'
        f'• Training ∩ Validation: {len(train_val_overlap):,} genes\n'
        f'• All Three: {len(all_three_overlap):,} genes'
    )
    
    plt.figtext(0.02, 0.02, stats_text, fontsize=11, ha='left', va='bottom',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'overall_venn_compass_train_val.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n总韦恩图已保存到: {output_file}")
    
    # 保存重叠基因列表
    overlap_data = {
        'compass_train_overlap': sorted(list(compass_train_overlap)),
        'compass_val_overlap': sorted(list(compass_val_overlap)),
        'train_val_overlap': sorted(list(train_val_overlap)),
        'all_three_overlap': sorted(list(all_three_overlap))
    }
    
    # 保存为CSV文件
    max_len = max(len(v) for v in overlap_data.values()) if overlap_data else 0
    if max_len > 0:
        overlap_df_data = {}
        for key, genes in overlap_data.items():
            overlap_df_data[key] = genes + [''] * (max_len - len(genes))
        
        overlap_df = pd.DataFrame(overlap_df_data)
        overlap_file = os.path.join(output_dir, 'overall_gene_overlaps.csv')
        overlap_df.to_csv(overlap_file, index=False)
        print(f"重叠基因列表已保存到: {overlap_file}")
    
    return {
        'compass_genes': len(all_compass_genes),
        'train_genes': len(train_genes),
        'val_genes': len(val_genes),
        'overlaps': {
            'compass_train': len(compass_train_overlap),
            'compass_val': len(compass_val_overlap),
            'train_val': len(train_val_overlap),
            'all_three': len(all_three_overlap)
        }
    }

def main():
    output_dir = "/data/ioz_whr_wsx/codes/data_preprocess/compass_dataloaded_analysis"
    
    print("=" * 60)
    print("生成总的韦恩图")
    print("=" * 60)
    
    results = create_overall_venn_diagram(output_dir)
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print(f"Compass数据集: {results['compass_genes']:,} 个基因")
    print(f"训练集: {results['train_genes']:,} 个基因")
    print(f"测试集: {results['val_genes']:,} 个基因")
    print(f"三者重叠: {results['overlaps']['all_three']:,} 个基因")

if __name__ == "__main__":
    main()
