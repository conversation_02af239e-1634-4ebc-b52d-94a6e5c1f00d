{"cells": [{"cell_type": "code", "execution_count": 3, "id": "e1fce413", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/jupyter_r_env/lib/python3.10/site-packages/rpy2/ipython/rmagic.py:96: UserWarning: The Python package `pandas` is strongly recommended when using `rpy2.ipython`. Unfortunately it could not be loaded, as we did not manage to load `numpy` in the first place (error: No module named 'numpy').\n", "  warnings.warn('The Python package `pandas` is strongly '\n"]}], "source": ["# 先加载 R magic 扩展\n", "%load_ext rpy2.ipython\n", "\n", "# 设置输入目录（你可以替换为其他目录）\n", "input_dir = \"/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "0252b9e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Bulk_RNAseq_Seurat_object_IFNG_and_TGFB_stim.rds \n", "[✔] Has counts matrix\n", "[✔] Has data matrix\n", "\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/HClust_Pathway_celltype_specific_genelist.rds \n", "[SKIP] Not a Seurat object.\n", "\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Pathway_Exclusive_genelist.rds \n", "[SKIP] Not a Seurat object.\n", "\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Pathway_genelist.rds \n", "[SKIP] Not a Seurat object.\n", "\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_IFNB_Perturb_seq.rds \n", "[✔] Has counts matrix\n", "[✔] Has data matrix\n", "\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_IFNG_Perturb_seq.rds \n", "[✔] Has counts matrix\n", "[✔] Has data matrix\n", "\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_INS_Perturb_seq.rds \n", "[✔] Has counts matrix\n", "[✔] Has data matrix\n", "\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_TGFB_Perturb_seq.rds \n", "[✔] Has counts matrix\n", "[✔] Has data matrix\n", "\n", "[INFO] Checking: /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_TNFA_Perturb_seq.rds \n"]}], "source": ["%%R -i input_dir\n", "suppressMessages(library(Seurat))\n", "\n", "# 获取所有 .rds 文件\n", "rds_files <- list.files(input_dir, pattern = \"\\\\.rds$\", full.names = TRUE)\n", "if (length(rds_files) == 0) stop(\"No RDS files found.\")\n", "\n", "for (rds_file in rds_files) {\n", "  cat(\"\\n[INFO] Checking:\", rds_file, \"\\n\")\n", "  obj <- tryCatch(readRDS(rds_file), error=function(e) {\n", "    cat(\"[ERROR] Cannot read RDS:\", e$message, \"\\n\")\n", "    return(NULL)\n", "  })\n", "  if (is.null(obj) || !\"Seurat\" %in% class(obj)) {\n", "    cat(\"[SKIP] Not a Seurat object.\\n\")\n", "    next\n", "  }\n", "\n", "  <PERSON><PERSON><PERSON><PERSON><PERSON>(obj) <- \"RNA\"\n", "  a <- obj[[\"RNA\"]]\n", "  s <- slotNames(a)\n", "  cat(if (\"counts\" %in% s) \"[✔] Has counts matrix\\n\" else \"[✘] No counts matrix\\n\")\n", "  cat(if (\"data\"   %in% s) \"[✔] Has data matrix\\n\"   else \"[✘] No data matrix\\n\")\n", "}\n"]}, {"cell_type": "code", "execution_count": 4, "id": "3274a7e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["找到 5 个H5AD文件\n", "\n", "处理文件: Se<PERSON><PERSON>_object_TGFB_Perturb_seq.RNA.counts.h5ad\n", "\n", "============================================================\n", "检测文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_TGFB_Perturb_seq.RNA.counts.h5ad\n", "============================================================\n", "✓ 文件读取成功\n", "  - 细胞数: 236606\n", "  - 基因数: 33525\n", "\n", "1. X矩阵检查:\n", "  - X类型: <class 'scipy.sparse._csr.csr_matrix'>\n", "  - X形状: (236606, 33525)\n", "  - 稀疏矩阵格式: csr\n", "  - data类型: float64\n", "  - indices类型: int32\n", "  - indptr类型: int32\n", "  - 包含NaN: False\n", "  - 包含Inf: False\n", "  - 包含负值: False\n", "  - 数值范围: [1.000, 4075.000]\n", "\n", "2. obs检查:\n", "  - obs形状: (236606, 15)\n", "  - obs列: ['orig.ident', 'nCount_RNA', 'nFeature_RNA', 'sample', 'cell_type', 'pathway', 'percent.mito', 'sample_ID', 'Batch_info', 'bc1_well', 'bc2_well', 'bc3_well', 'guide', 'gene', 'mixscale_score']\n", "    orig.ident: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    nCount_RNA: float64\n", "    nFeature_RNA: int32\n", "    sample: category\n", "      分类数: 6, 使用数: 6\n", "      categories dtype: string\n", "    cell_type: category\n", "      分类数: 6, 使用数: 6\n", "      categories dtype: string\n", "    pathway: category\n", "      分类数: 1, 使用数: 1\n", "      categories dtype: string\n", "    percent.mito: float64\n", "    sample_ID: category\n", "      分类数: 16, 使用数: 16\n", "      categories dtype: string\n", "    Batch_info: category\n", "      分类数: 2, 使用数: 2\n", "      categories dtype: string\n", "    bc1_well: category\n", "      分类数: 25, 使用数: 25\n", "      categories dtype: string\n", "    bc2_well: category\n", "      分类数: 97, 使用数: 97\n", "      categories dtype: string\n", "    bc3_well: category\n", "      分类数: 97, 使用数: 97\n", "      categories dtype: string\n", "    guide: category\n", "      分类数: 170, 使用数: 170\n", "      categories dtype: string\n", "    gene: category\n", "      分类数: 53, 使用数: 53\n", "      categories dtype: string\n", "    mixscale_score: float64\n", "\n", "3. var检查:\n", "  - var形状: (33525, 5)\n", "  - var列: ['vst.mean', 'vst.variance', 'vst.variance.expected', 'vst.variance.standardized', 'vst.variable']\n", "  - var index类型: object\n", "    vst.mean: float64\n", "    vst.variance: float64\n", "    vst.variance.expected: float64\n", "    vst.variance.standardized: float64\n", "    vst.variable: bool\n", "\n", "4. 数值一致性检查:\n", "  - 行和范围: [542.0, 85032.0]\n", "  - 行和均值: 5048.2\n", "  - 零行数: 0\n", "  - 行和是否整数样: True\n", "  - 最大值: 4075.000 (可能是counts数据)\n", "\n", "5. 关键操作测试:\n", "  ✓ 稀疏矩阵data转float32成功\n", "  ✓ 稀疏矩阵求和成功\n", "  ✓ NaN检测成功: 0个NaN\n", "\n", "6. 检测总结:\n", "  ✓ 未发现明显问题\n", "\n", "处理文件: Se<PERSON><PERSON>_object_IFNG_Perturb_seq.RNA.counts.h5ad\n", "\n", "============================================================\n", "检测文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_IFNG_Perturb_seq.RNA.counts.h5ad\n", "============================================================\n", "✓ 文件读取成功\n", "  - 细胞数: 245240\n", "  - 基因数: 33525\n", "\n", "1. X矩阵检查:\n", "  - X类型: <class 'scipy.sparse._csr.csr_matrix'>\n", "  - X形状: (245240, 33525)\n", "  - 稀疏矩阵格式: csr\n", "  - data类型: float64\n", "  - indices类型: int32\n", "  - indptr类型: int32\n", "  - 包含NaN: False\n", "  - 包含Inf: False\n", "  - 包含负值: False\n", "  - 数值范围: [1.000, 5340.000]\n", "\n", "2. obs检查:\n", "  - obs形状: (245240, 15)\n", "  - obs列: ['orig.ident', 'nCount_RNA', 'nFeature_RNA', 'sample', 'cell_type', 'pathway', 'percent.mito', 'sample_ID', 'Batch_info', 'bc1_well', 'bc2_well', 'bc3_well', 'guide', 'gene', 'mixscale_score']\n", "    orig.ident: category\n", "      分类数: 28, 使用数: 28\n", "      categories dtype: string\n", "    nCount_RNA: float64\n", "    nFeature_RNA: int32\n", "    sample: category\n", "      分类数: 7, 使用数: 7\n", "      categories dtype: string\n", "    cell_type: category\n", "      分类数: 6, 使用数: 6\n", "      categories dtype: string\n", "    pathway: category\n", "      分类数: 1, 使用数: 1\n", "      categories dtype: string\n", "    percent.mito: float64\n", "    sample_ID: category\n", "      分类数: 16, 使用数: 16\n", "      categories dtype: string\n", "    Batch_info: category\n", "      分类数: 2, 使用数: 2\n", "      categories dtype: string\n", "    bc1_well: category\n", "      分类数: 25, 使用数: 25\n", "      categories dtype: string\n", "    bc2_well: category\n", "      分类数: 97, 使用数: 97\n", "      categories dtype: string\n", "    bc3_well: category\n", "      分类数: 97, 使用数: 97\n", "      categories dtype: string\n", "    guide: category\n", "      分类数: 191, 使用数: 191\n", "      categories dtype: string\n", "    gene: category\n", "      分类数: 60, 使用数: 60\n", "      categories dtype: string\n", "    mixscale_score: float64\n", "\n", "3. var检查:\n", "  - var形状: (33525, 7)\n", "  - var列: ['gene_name', 'vst.mean', 'vst.variance', 'vst.variance.expected', 'vst.variance.standardized', 'vst.variable', '_index']\n", "  - var index类型: object\n", "    gene_name: object\n", "    vst.mean: float64\n", "    vst.variance: float64\n", "    vst.variance.expected: float64\n", "    vst.variance.standardized: float64\n", "    vst.variable: bool\n", "    _index: object\n", "\n", "4. 数值一致性检查:\n", "  - 行和范围: [543.0, 88482.0]\n", "  - 行和均值: 5695.4\n", "  - 零行数: 0\n", "  - 行和是否整数样: True\n", "  - 最大值: 5340.000 (可能是counts数据)\n", "\n", "5. 关键操作测试:\n", "  ✓ 稀疏矩阵data转float32成功\n", "  ✓ 稀疏矩阵求和成功\n", "  ✓ NaN检测成功: 0个NaN\n", "\n", "6. 检测总结:\n", "  ✓ 未发现明显问题\n", "\n", "处理文件: Se<PERSON><PERSON>_object_IFNB_Perturb_seq.RNA.counts.h5ad\n", "\n", "============================================================\n", "检测文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_IFNB_Perturb_seq.RNA.counts.h5ad\n", "============================================================\n", "✓ 文件读取成功\n", "  - 细胞数: 328542\n", "  - 基因数: 34025\n", "\n", "1. X矩阵检查:\n", "  - X类型: <class 'scipy.sparse._csr.csr_matrix'>\n", "  - X形状: (328542, 34025)\n", "  - 稀疏矩阵格式: csr\n", "  - data类型: float64\n", "  - indices类型: int32\n", "  - indptr类型: int32\n", "  - 包含NaN: False\n", "  - 包含Inf: False\n", "  - 包含负值: False\n", "  - 数值范围: [1.000, 4888.000]\n", "\n", "2. obs检查:\n", "  - obs形状: (328542, 17)\n", "  - obs列: ['orig.ident', 'nCount_RNA', 'nFeature_RNA', 'sample', 'bc1_well', 'bc2_well', 'bc3_well', 'percent.mito', 'cell_type', 'pathway', 'RNA_snn_res.0.9', 'seurat_clusters', 'sample_ID', 'Batch_info', 'guide', 'gene', 'mixscale_score']\n", "    orig.ident: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    nCount_RNA: float64\n", "    nFeature_RNA: int32\n", "    sample: category\n", "      分类数: 7, 使用数: 7\n", "      categories dtype: string\n", "    bc1_well: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    bc2_well: category\n", "      分类数: 95, 使用数: 95\n", "      categories dtype: string\n", "    bc3_well: category\n", "      分类数: 96, 使用数: 96\n", "      categories dtype: string\n", "    percent.mito: float64\n", "    cell_type: category\n", "      分类数: 6, 使用数: 6\n", "      categories dtype: string\n", "    pathway: category\n", "      分类数: 1, 使用数: 1\n", "      categories dtype: string\n", "    RNA_snn_res.0.9: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    seurat_clusters: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    sample_ID: category\n", "      分类数: 16, 使用数: 16\n", "      categories dtype: string\n", "    Batch_info: category\n", "      分类数: 2, 使用数: 2\n", "      categories dtype: string\n", "    guide: category\n", "      分类数: 197, 使用数: 197\n", "      categories dtype: string\n", "    gene: category\n", "      分类数: 62, 使用数: 62\n", "      categories dtype: string\n", "    mixscale_score: float64\n", "\n", "3. var检查:\n", "  - var形状: (34025, 7)\n", "  - var列: ['gene_name', 'vst.mean', 'vst.variance', 'vst.variance.expected', 'vst.variance.standardized', 'vst.variable', '_index']\n", "  - var index类型: object\n", "    gene_name: object\n", "    vst.mean: float64\n", "    vst.variance: float64\n", "    vst.variance.expected: float64\n", "    vst.variance.standardized: float64\n", "    vst.variable: bool\n", "    _index: object\n", "\n", "4. 数值一致性检查:\n", "  - 行和范围: [568.0, 89641.0]\n", "  - 行和均值: 6524.1\n", "  - 零行数: 0\n", "  - 行和是否整数样: True\n", "  - 最大值: 4888.000 (可能是counts数据)\n", "\n", "5. 关键操作测试:\n", "  ✓ 稀疏矩阵data转float32成功\n", "  ✓ 稀疏矩阵求和成功\n", "  ✓ NaN检测成功: 0个NaN\n", "\n", "6. 检测总结:\n", "  ✓ 未发现明显问题\n", "\n", "处理文件: Seurat_object_INS_Perturb_seq.RNA.counts.h5ad\n", "\n", "============================================================\n", "检测文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_INS_Perturb_seq.RNA.counts.h5ad\n", "============================================================\n", "✓ 文件读取成功\n", "  - 细胞数: 431457\n", "  - 基因数: 34025\n", "\n", "1. X矩阵检查:\n", "  - X类型: <class 'scipy.sparse._csr.csr_matrix'>\n", "  - X形状: (431457, 34025)\n", "  - 稀疏矩阵格式: csr\n", "  - data类型: float64\n", "  - indices类型: int32\n", "  - indptr类型: int32\n", "  - 包含NaN: False\n", "  - 包含Inf: False\n", "  - 包含负值: False\n", "  - 数值范围: [1.000, 7044.000]\n", "\n", "2. obs检查:\n", "  - obs形状: (431457, 17)\n", "  - obs列: ['orig.ident', 'nCount_RNA', 'nFeature_RNA', 'sample', 'bc1_well', 'bc2_well', 'bc3_well', 'percent.mito', 'cell_type', 'pathway', 'RNA_snn_res.0.9', 'seurat_clusters', 'sample_ID', 'Batch_info', 'guide', 'gene', 'mixscale_score']\n", "    orig.ident: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    nCount_RNA: float64\n", "    nFeature_RNA: int32\n", "    sample: category\n", "      分类数: 6, 使用数: 6\n", "      categories dtype: string\n", "    bc1_well: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    bc2_well: category\n", "      分类数: 95, 使用数: 95\n", "      categories dtype: string\n", "    bc3_well: category\n", "      分类数: 96, 使用数: 96\n", "      categories dtype: string\n", "    percent.mito: float64\n", "    cell_type: category\n", "      分类数: 6, 使用数: 6\n", "      categories dtype: string\n", "    pathway: category\n", "      分类数: 1, 使用数: 1\n", "      categories dtype: string\n", "    RNA_snn_res.0.9: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    seurat_clusters: category\n", "      分类数: 24, 使用数: 24\n", "      categories dtype: string\n", "    sample_ID: category\n", "      分类数: 16, 使用数: 16\n", "      categories dtype: string\n", "    Batch_info: category\n", "      分类数: 2, 使用数: 2\n", "      categories dtype: string\n", "    guide: category\n", "      分类数: 146, 使用数: 146\n", "      categories dtype: string\n", "    gene: category\n", "      分类数: 45, 使用数: 45\n", "      categories dtype: string\n", "    mixscale_score: float64\n", "\n", "3. var检查:\n", "  - var形状: (34025, 7)\n", "  - var列: ['gene_name', 'vst.mean', 'vst.variance', 'vst.variance.expected', 'vst.variance.standardized', 'vst.variable', '_index']\n", "  - var index类型: object\n", "    gene_name: object\n", "    vst.mean: float64\n", "    vst.variance: float64\n", "    vst.variance.expected: float64\n", "    vst.variance.standardized: float64\n", "    vst.variable: bool\n", "    _index: object\n", "\n", "4. 数值一致性检查:\n", "  - 行和范围: [554.0, 91046.0]\n", "  - 行和均值: 6481.3\n", "  - 零行数: 0\n", "  - 行和是否整数样: True\n", "  - 最大值: 7044.000 (可能是counts数据)\n", "\n", "5. 关键操作测试:\n", "  ✓ 稀疏矩阵data转float32成功\n", "  ✓ 稀疏矩阵求和成功\n", "  ✓ NaN检测成功: 0个NaN\n", "\n", "6. 检测总结:\n", "  ✓ 未发现明显问题\n", "\n", "处理文件: Se<PERSON><PERSON>_object_TNFA_Perturb_seq.RNA.counts.h5ad\n", "\n", "============================================================\n", "检测文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_TNFA_Perturb_seq.RNA.counts.h5ad\n", "============================================================\n", "✓ 文件读取成功\n", "  - 细胞数: 386631\n", "  - 基因数: 33525\n", "\n", "1. X矩阵检查:\n", "  - X类型: <class 'scipy.sparse._csr.csr_matrix'>\n", "  - X形状: (386631, 33525)\n", "  - 稀疏矩阵格式: csr\n", "  - data类型: float64\n", "  - indices类型: int32\n", "  - indptr类型: int32\n", "  - 包含NaN: False\n", "  - 包含Inf: False\n", "  - 包含负值: False\n", "  - 数值范围: [1.000, 5239.000]\n", "\n", "2. obs检查:\n", "  - obs形状: (386631, 15)\n", "  - obs列: ['orig.ident', 'nCount_RNA', 'nFeature_RNA', 'sample', 'cell_type', 'pathway', 'percent.mito', 'sample_ID', 'Batch_info', 'bc1_well', 'bc2_well', 'bc3_well', 'guide', 'gene', 'mixscale_score']\n", "    orig.ident: category\n", "      分类数: 32, 使用数: 32\n", "      categories dtype: string\n", "    nCount_RNA: float64\n", "    nFeature_RNA: int32\n", "    sample: category\n", "      分类数: 7, 使用数: 7\n", "      categories dtype: string\n", "    cell_type: category\n", "      分类数: 6, 使用数: 6\n", "      categories dtype: string\n", "    pathway: category\n", "      分类数: 1, 使用数: 1\n", "      categories dtype: string\n", "    percent.mito: float64\n", "    sample_ID: category\n", "      分类数: 16, 使用数: 16\n", "      categories dtype: string\n", "    Batch_info: category\n", "      分类数: 2, 使用数: 2\n", "      categories dtype: string\n", "    bc1_well: category\n", "      分类数: 25, 使用数: 25\n", "      categories dtype: string\n", "    bc2_well: category\n", "      分类数: 97, 使用数: 97\n", "      categories dtype: string\n", "    bc3_well: category\n", "      分类数: 97, 使用数: 97\n", "      categories dtype: string\n", "    guide: category\n", "      分类数: 179, 使用数: 179\n", "      categories dtype: string\n", "    gene: category\n", "      分类数: 56, 使用数: 56\n", "      categories dtype: string\n", "    mixscale_score: float64\n", "\n", "3. var检查:\n", "  - var形状: (33525, 7)\n", "  - var列: ['gene_name', 'vst.mean', 'vst.variance', 'vst.variance.expected', 'vst.variance.standardized', 'vst.variable', '_index']\n", "  - var index类型: object\n", "    gene_name: object\n", "    vst.mean: float64\n", "    vst.variance: float64\n", "    vst.variance.expected: float64\n", "    vst.variance.standardized: float64\n", "    vst.variable: bool\n", "    _index: object\n", "\n", "4. 数值一致性检查:\n", "  - 行和范围: [560.0, 105379.0]\n", "  - 行和均值: 5653.2\n", "  - 零行数: 0\n", "  - 行和是否整数样: True\n", "  - 最大值: 5239.000 (可能是counts数据)\n", "\n", "5. 关键操作测试:\n", "  ✓ 稀疏矩阵data转float32成功\n", "  ✓ 稀疏矩阵求和成功\n", "  ✓ NaN检测成功: 0个NaN\n", "\n", "6. 检测总结:\n", "  ✓ 未发现明显问题\n", "\n", "============================================================\n", "批处理总结\n", "============================================================\n", "\n", "Seurat_object_TGFB_Perturb_seq.RNA.counts.h5ad:\n", "  ✓ 原始文件无问题\n", "\n", "Seurat_object_IFNG_Perturb_seq.RNA.counts.h5ad:\n", "  ✓ 原始文件无问题\n", "\n", "Seurat_object_IFNB_Perturb_seq.RNA.counts.h5ad:\n", "  ✓ 原始文件无问题\n", "\n", "Seurat_object_INS_Perturb_seq.RNA.counts.h5ad:\n", "  ✓ 原始文件无问题\n", "\n", "Seurat_object_TNFA_Perturb_seq.RNA.counts.h5ad:\n", "  ✓ 原始文件无问题\n"]}], "source": ["# H5AD文件检测和修复工具\n", "# 用于检测从RDS转换的H5AD文件是否存在数据类型问题\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import anndata as ad\n", "import scipy.sparse as sp\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "def comprehensive_h5ad_check(h5ad_path):\n", "    \"\"\"\n", "    全面检测H5AD文件的数据完整性和类型问题\n", "    \"\"\"\n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"检测文件: {h5ad_path}\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    try:\n", "        # 读取文件\n", "        adata = ad.read_h5ad(h5ad_path)\n", "        print(f\"✓ 文件读取成功\")\n", "        print(f\"  - 细胞数: {adata.n_obs}\")\n", "        print(f\"  - 基因数: {adata.n_vars}\")\n", "        \n", "        issues = []\n", "        \n", "        # 1. 检查X矩阵\n", "        print(f\"\\n1. X矩阵检查:\")\n", "        X = adata.X\n", "        print(f\"  - X类型: {type(X)}\")\n", "        print(f\"  - X形状: {X.shape}\")\n", "        \n", "        if sp.is<PERSON><PERSON>e(X):\n", "            print(f\"  - 稀疏矩阵格式: {X.format}\")\n", "            print(f\"  - data类型: {X.data.dtype}\")\n", "            print(f\"  - indices类型: {X.indices.dtype}\")\n", "            print(f\"  - indptr类型: {X.indptr.dtype}\")\n", "            \n", "            # 检查data中的异常值\n", "            if len(X.data) > 0:\n", "                has_nan = np.isnan(X.data).any()\n", "                has_inf = np.isinf(X.data).any()\n", "                has_negative = (X.data < 0).any()\n", "                \n", "                print(f\"  - 包含NaN: {has_nan}\")\n", "                print(f\"  - 包含Inf: {has_inf}\")\n", "                print(f\"  - 包含负值: {has_negative}\")\n", "                print(f\"  - 数值范围: [{X.data.min():.3f}, {X.data.max():.3f}]\")\n", "                \n", "                if has_nan:\n", "                    issues.append(\"X.data包含NaN值\")\n", "                if has_inf:\n", "                    issues.append(\"X.data包含Inf值\")\n", "                    \n", "        else:\n", "            print(f\"  - 密集矩阵dtype: {X.dtype}\")\n", "            has_nan = np.isnan(X).any()\n", "            has_inf = np.isinf(X).any()\n", "            has_negative = (X < 0).any()\n", "            \n", "            print(f\"  - 包含NaN: {has_nan}\")\n", "            print(f\"  - 包含Inf: {has_inf}\")\n", "            print(f\"  - 包含负值: {has_negative}\")\n", "            print(f\"  - 数值范围: [{X.min():.3f}, {X.max():.3f}]\")\n", "            \n", "            if has_nan:\n", "                issues.append(\"X包含NaN值\")\n", "            if has_inf:\n", "                issues.append(\"X包含Inf值\")\n", "        \n", "        # 2. 检查obs\n", "        print(f\"\\n2. obs检查:\")\n", "        print(f\"  - obs形状: {adata.obs.shape}\")\n", "        print(f\"  - obs列: {list(adata.obs.columns)}\")\n", "        \n", "        for col in adata.obs.columns:\n", "            dtype = adata.obs[col].dtype\n", "            print(f\"    {col}: {dtype}\")\n", "            \n", "            # 检查分类数据的问题\n", "            if pd.api.types.is_categorical_dtype(adata.obs[col]):\n", "                cat_col = adata.obs[col]\n", "                n_categories = len(cat_col.cat.categories)\n", "                n_used = len(cat_col.unique())\n", "                print(f\"      分类数: {n_categories}, 使用数: {n_used}\")\n", "                \n", "                # 检查categories的类型\n", "                cat_dtype = cat_col.cat.categories.dtype\n", "                print(f\"      categories dtype: {cat_dtype}\")\n", "                \n", "                if n_categories > n_used * 3:\n", "                    issues.append(f\"obs[{col}]有过多未使用的categories\")\n", "            \n", "            # 检查是否有特殊值\n", "            if adata.obs[col].dtype == 'object':\n", "                unique_vals = adata.obs[col].unique()\n", "                print(f\"      唯一值数量: {len(unique_vals)}\")\n", "                if len(unique_vals) <= 10:\n", "                    print(f\"      唯一值: {unique_vals}\")\n", "        \n", "        # 3. 检查var\n", "        print(f\"\\n3. var检查:\")\n", "        print(f\"  - var形状: {adata.var.shape}\")\n", "        print(f\"  - var列: {list(adata.var.columns)}\")\n", "        print(f\"  - var index类型: {adata.var.index.dtype}\")\n", "        \n", "        for col in adata.var.columns:\n", "            dtype = adata.var[col].dtype\n", "            print(f\"    {col}: {dtype}\")\n", "            \n", "            if pd.api.types.is_categorical_dtype(adata.var[col]):\n", "                cat_col = adata.var[col]\n", "                n_categories = len(cat_col.cat.categories)\n", "                n_used = len(cat_col.unique())\n", "                print(f\"      分类数: {n_categories}, 使用数: {n_used}\")\n", "        \n", "        # 4. 数值一致性检查\n", "        print(f\"\\n4. 数值一致性检查:\")\n", "        \n", "        # 检查行和\n", "        if sp.is<PERSON><PERSON>e(X):\n", "            row_sums = np.asarray(X.sum(axis=1)).ravel()\n", "        else:\n", "            row_sums = X.sum(axis=1)\n", "        \n", "        print(f\"  - 行和范围: [{row_sums.min():.1f}, {row_sums.max():.1f}]\")\n", "        print(f\"  - 行和均值: {row_sums.mean():.1f}\")\n", "        print(f\"  - 零行数: {(row_sums == 0).sum()}\")\n", "        \n", "        # 检查是否像counts数据\n", "        sample_sums = row_sums[:min(100, len(row_sums))]\n", "        is_integer_like = np.allclose(sample_sums, np.round(sample_sums))\n", "        print(f\"  - 行和是否整数样: {is_integer_like}\")\n", "        \n", "        # 检查是否像log数据\n", "        if len(X.data) > 0 if sp.issparse(X) else X.size > 0:\n", "            max_val = X.data.max() if sp.issparse(X) else X.max()\n", "            print(f\"  - 最大值: {max_val:.3f} ({'可能是log数据' if max_val < 15 else '可能是counts数据'})\")\n", "        \n", "        # 5. 尝试关键操作\n", "        print(f\"\\n5. 关键操作测试:\")\n", "        \n", "        # 测试类型转换\n", "        try:\n", "            if sp.is<PERSON><PERSON>e(X):\n", "                test_data = X.data.astype(np.float32)\n", "                print(f\"  ✓ 稀疏矩阵data转float32成功\")\n", "            else:\n", "                test_X = X.astype(np.float32)\n", "                print(f\"  ✓ 密集矩阵转float32成功\")\n", "        except Exception as e:\n", "            print(f\"  ✗ 类型转换失败: {e}\")\n", "            issues.append(f\"类型转换失败: {e}\")\n", "        \n", "        # 测试数学运算\n", "        try:\n", "            if sp.is<PERSON><PERSON>e(X):\n", "                _ = X.sum()\n", "                print(f\"  ✓ 稀疏矩阵求和成功\")\n", "            else:\n", "                _ = X.sum()\n", "                print(f\"  ✓ 密集矩阵求和成功\")\n", "        except Exception as e:\n", "            print(f\"  ✗ 求和失败: {e}\")\n", "            issues.append(f\"求和失败: {e}\")\n", "        \n", "        # 测试NaN处理\n", "        try:\n", "            if sp.is<PERSON><PERSON>e(X):\n", "                nan_mask = np.isnan(X.data)\n", "                print(f\"  ✓ NaN检测成功: {nan_mask.sum()}个NaN\")\n", "            else:\n", "                nan_mask = np.isnan(X)\n", "                print(f\"  ✓ NaN检测成功: {nan_mask.sum()}个NaN\")\n", "        except Exception as e:\n", "            print(f\"  ✗ NaN检测失败: {e}\")\n", "            issues.append(f\"NaN检测失败: {e}\")\n", "        \n", "        # 6. 总结\n", "        print(f\"\\n6. 检测总结:\")\n", "        if issues:\n", "            print(f\"  发现 {len(issues)} 个问题:\")\n", "            for i, issue in enumerate(issues, 1):\n", "                print(f\"    {i}. {issue}\")\n", "        else:\n", "            print(f\"  ✓ 未发现明显问题\")\n", "        \n", "        return adata, issues\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ 文件读取失败: {e}\")\n", "        return None, [f\"文件读取失败: {e}\"]\n", "\n", "\n", "def fix_h5ad_issues(adata, output_path=None):\n", "    \"\"\"\n", "    修复检测到的H5AD文件问题\n", "    \"\"\"\n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"修复H5AD文件问题\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    adata_fixed = adata.copy()\n", "    \n", "    # 1. 修复X矩阵\n", "    print(\"1. 修复X矩阵...\")\n", "    X = adata_fixed.X\n", "    \n", "    if sp.is<PERSON><PERSON>e(X):\n", "        print(\"  处理稀疏矩阵...\")\n", "        X = X.tocsr()  # 确保CSR格式\n", "        \n", "        # 修复data类型\n", "        if X.data.dtype.kind in ['i', 'u']:  # 整数类型\n", "            print(f\"    转换data从 {X.data.dtype} 到 float32\")\n", "            X.data = X.data.astype(np.float32)\n", "        elif X.data.dtype != np.float32:\n", "            print(f\"    转换data从 {X.data.dtype} 到 float32\")\n", "            X.data = X.data.astype(np.float32)\n", "        \n", "        # 处理NaN和Inf\n", "        if len(X.data) > 0:\n", "            nan_mask = np.isnan(X.data)\n", "            inf_mask = np.isinf(X.data)\n", "            if nan_mask.any():\n", "                print(f\"    修复 {nan_mask.sum()} 个NaN值\")\n", "                X.data[nan_mask] = 0.0\n", "            if inf_mask.any():\n", "                print(f\"    修复 {inf_mask.sum()} 个Inf值\")\n", "                X.data[inf_mask] = 0.0\n", "                \n", "            # 清理零元素\n", "            X.eliminate_zeros()\n", "        \n", "        adata_fixed.X = X\n", "        \n", "    else:\n", "        print(\"  处理密集矩阵...\")\n", "        # 确保是float32\n", "        if X.dtype.kind in ['i', 'u']:  # 整数类型\n", "            print(f\"    转换从 {X.dtype} 到 float32\")\n", "            X = X.astype(np.float32)\n", "        elif X.dtype != np.float32:\n", "            print(f\"    转换从 {X.dtype} 到 float32\")\n", "            X = X.astype(np.float32)\n", "        \n", "        # 处理NaN和Inf\n", "        if X.size > 0:\n", "            nan_mask = np.isnan(X)\n", "            inf_mask = np.isinf(X)\n", "            if nan_mask.any():\n", "                print(f\"    修复 {nan_mask.sum()} 个NaN值\")\n", "                X[nan_mask] = 0.0\n", "            if inf_mask.any():\n", "                print(f\"    修复 {inf_mask.sum()} 个Inf值\")\n", "                X[inf_mask] = 0.0\n", "        \n", "        adata_fixed.X = X\n", "    \n", "    # 2. 修复obs\n", "    print(\"2. 修复obs...\")\n", "    for col in adata_fixed.obs.columns:\n", "        if pd.api.types.is_categorical_dtype(adata_fixed.obs[col]):\n", "            cat_col = adata_fixed.obs[col]\n", "            # 移除未使用的categories\n", "            adata_fixed.obs[col] = cat_col.cat.remove_unused_categories()\n", "            print(f\"    {col}: 清理未使用的categories\")\n", "    \n", "    # 3. 修复var\n", "    print(\"3. 修复var...\")\n", "    for col in adata_fixed.var.columns:\n", "        if pd.api.types.is_categorical_dtype(adata_fixed.var[col]):\n", "            cat_col = adata_fixed.var[col]\n", "            # 移除未使用的categories\n", "            adata_fixed.var[col] = cat_col.cat.remove_unused_categories()\n", "            print(f\"    {col}: 清理未使用的categories\")\n", "    \n", "    # 4. 确保索引唯一性\n", "    print(\"4. 确保索引唯一性...\")\n", "    adata_fixed.obs_names_make_unique()\n", "    adata_fixed.var_names_make_unique()\n", "    \n", "    # 5. 保存修复后的文件\n", "    if output_path:\n", "        print(f\"5. 保存修复后的文件到: {output_path}\")\n", "        adata_fixed.write_h5ad(output_path, compression='gzip')\n", "        print(\"  ✓ 保存成功\")\n", "    \n", "    return adata_fixed\n", "\n", "\n", "# 使用示例\n", "def batch_check_and_fix(input_dir, output_dir=None):\n", "    \"\"\"\n", "    批量检测和修复H5AD文件\n", "    \"\"\"\n", "    input_path = Path(input_dir)\n", "    h5ad_files = list(input_path.glob(\"*.h5ad\"))\n", "    \n", "    if not h5ad_files:\n", "        print(f\"在 {input_dir} 中未找到H5AD文件\")\n", "        return\n", "    \n", "    print(f\"找到 {len(h5ad_files)} 个H5AD文件\")\n", "    \n", "    if output_dir:\n", "        output_path = Path(output_dir)\n", "        output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    results = {}\n", "    \n", "    for h5ad_file in h5ad_files:\n", "        print(f\"\\n处理文件: {h5ad_file.name}\")\n", "        \n", "        # 检测\n", "        adata, issues = comprehensive_h5ad_check(h5ad_file)\n", "        results[h5ad_file.name] = {\"issues\": issues}\n", "        \n", "        # 如果有问题且提供了输出目录，尝试修复\n", "        if issues and adata is not None and output_dir:\n", "            output_file = output_path / f\"{h5ad_file.stem}_fixed.h5ad\"\n", "            try:\n", "                adata_fixed = fix_h5ad_issues(adata, output_file)\n", "                results[h5ad_file.name][\"fixed\"] = True\n", "                results[h5ad_file.name][\"output\"] = str(output_file)\n", "                \n", "                # 再次检测修复后的文件\n", "                print(f\"\\n验证修复后的文件...\")\n", "                _, remaining_issues = comprehensive_h5ad_check(output_file)\n", "                results[h5ad_file.name][\"remaining_issues\"] = remaining_issues\n", "                \n", "            except Exception as e:\n", "                print(f\"修复失败: {e}\")\n", "                results[h5ad_file.name][\"fixed\"] = False\n", "                results[h5ad_file.name][\"fix_error\"] = str(e)\n", "    \n", "    # 打印总结\n", "    print(f\"\\n{'='*60}\")\n", "    print(\"批处理总结\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    for filename, result in results.items():\n", "        print(f\"\\n{filename}:\")\n", "        if result[\"issues\"]:\n", "            print(f\"  原始问题: {len(result['issues'])} 个\")\n", "            for issue in result[\"issues\"]:\n", "                print(f\"    - {issue}\")\n", "        else:\n", "            print(f\"  ✓ 原始文件无问题\")\n", "        \n", "        if \"fixed\" in result:\n", "            if result[\"fixed\"]:\n", "                print(f\"  ✓ 修复成功: {result['output']}\")\n", "                remaining = result.get(\"remaining_issues\", [])\n", "                if remaining:\n", "                    print(f\"  剩余问题: {len(remaining)} 个\")\n", "                else:\n", "                    print(f\"  ✓ 完全修复\")\n", "            else:\n", "                print(f\"  ✗ 修复失败: {result['fix_error']}\")\n", "    \n", "    return results\n", "\n", "# 快速使用示例:\n", "# 检测单个文件\n", "# adata, issues = comprehensive_h5ad_check(\"/path/to/your/file.h5ad\")\n", "\n", "# 批量检测和修复\n", "results = batch_check_and_fix(\n", "    input_dir=\"/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang\",\n", "    output_dir=\"/data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/fixed_h5ad\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "jupyter_r_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}