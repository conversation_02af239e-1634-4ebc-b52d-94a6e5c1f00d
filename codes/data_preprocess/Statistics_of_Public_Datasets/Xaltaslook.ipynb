{"cells": [{"cell_type": "code", "execution_count": 2, "id": "3b93d385", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'min': 0.0, 'max': 5340.0, 'mean': 0.16988609468805063, 'std': 2.2133335802465797, 'zero_fraction': 0.918864381462114}\n"]}, {"ename": "TypeError", "evalue": "Accessing a group is done with bytes or str, not <class 'tuple'>", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 54\u001b[39m\n\u001b[32m     51\u001b[39m     selected_cells_expression_sum = np.zeros(num_cells_to_check)\n\u001b[32m     52\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m idx, cell \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(selected_cells):\n\u001b[32m     53\u001b[39m         \u001b[38;5;66;03m# 获取当前细胞的数据\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m54\u001b[39m         cell_data = \u001b[43mX\u001b[49m\u001b[43m[\u001b[49m\u001b[43m:\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcell\u001b[49m\u001b[43m]\u001b[49m\n\u001b[32m     55\u001b[39m         selected_cells_expression_sum[idx] = np.sum(cell_data)\n\u001b[32m     57\u001b[39m \u001b[38;5;66;03m# 输出选定细胞的表达值和\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/_objects.pyx:56\u001b[39m, in \u001b[36mh5py._objects.with_phil.wrapper\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/_objects.pyx:57\u001b[39m, in \u001b[36mh5py._objects.with_phil.wrapper\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/single_cell_preprocess/lib/python3.11/site-packages/h5py/_hl/group.py:362\u001b[39m, in \u001b[36mGroup.__getitem__\u001b[39m\u001b[34m(self, name)\u001b[39m\n\u001b[32m    360\u001b[39m     oid = h5o.open(\u001b[38;5;28mself\u001b[39m.id, \u001b[38;5;28mself\u001b[39m._e(name), lapl=\u001b[38;5;28mself\u001b[39m._lapl)\n\u001b[32m    361\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m362\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mAccessing a group is done with bytes or str, \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    363\u001b[39m                     \u001b[33m\"\u001b[39m\u001b[33mnot \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m\"\u001b[39m.format(\u001b[38;5;28mtype\u001b[39m(name)))\n\u001b[32m    365\u001b[39m otype = h5i.get_type(oid)\n\u001b[32m    366\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m otype == h5i.GROUP:\n", "\u001b[31mTypeError\u001b[39m: Accessing a group is done with bytes or str, not <class 'tuple'>"]}], "source": ["import h5py\n", "import numpy as np\n", "\n", "path = \"/data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad/Seurat_object_IFNG_Perturb_seq.RNA.counts.h5ad\"\n", "with h5py.File(path, \"r\") as f:\n", "    X = f[\"X\"]\n", "    # 取 n_obs / n_vars（从 obs/var 任意一列）\n", "    n_obs = next(v.shape[0] for v in f[\"obs\"].values() if isinstance(v, h5py.Dataset))\n", "    n_vars = next(v.shape[0] for v in f[\"var\"].values() if isinstance(v, h5py.Dataset))\n", "    N = n_obs * n_vars\n", "\n", "    # 打印 X 矩阵的形状\n", "    # print(\"X matrix shape:\", X.shape)\n", "    \n", "    if isinstance(X, h5py.Dataset):  # 稠密矩阵\n", "        m, M, s, s2, zc = np.inf, -np.inf, 0.0, 0.0, 0\n", "        for i in range(0, X.shape[0], 5000):\n", "            B = X[i:i+5000, :]\n", "            m = min(m, B.min()); M = max(M, B.max())\n", "            s += B.sum(); s2 += (B**2).sum(); zc += (B == 0).sum()\n", "        mean = s / N; std = (max(0.0, s2 / N - mean**2))**0.5\n", "        zero_frac = zc / N\n", "        if zero_frac > 0: m = 0.0\n", "    else:  # 稀疏矩阵（CSR/CSC）\n", "        d = X[\"data\"]; nnz = d.shape[0]\n", "        m, M, s, s2 = np.inf, -np.inf, 0.0, 0.0\n", "        for i in range(0, nnz, 5_000_000):\n", "            chunk = d[i:i+5_000_000]\n", "            if chunk.size == 0: break\n", "            m = min(m, chunk.min()); M = max(M, chunk.max())\n", "            s += chunk.sum(); s2 += (chunk**2).sum()\n", "        zero_frac = 1 - nnz / N\n", "        if zero_frac > 0: m = 0.0\n", "        mean = s / N; std = (max(0.0, s2 / N - mean**2))**0.5\n", "\n", "    # 打印基本统计信息\n", "    print(dict(min=float(m), max=float(M), mean=float(mean),\n", "               std=float(std), zero_fraction=float(zero_frac)))\n", "    \n", "    # 计算几个细胞（列）的表达值之和\n", "    num_cells_to_check = 5  # 选择5个细胞来检查\n", "    selected_cells = np.random.choice(n_vars, num_cells_to_check, replace=False)  # 随机选择5个细胞\n", "    \n", "    # 确保选择的列是升序的\n", "    selected_cells = np.sort(selected_cells)\n", "\n", "    # 稠密矩阵处理\n", "    if isinstance(X, h5py.Dataset):\n", "        selected_cells_expression_sum = X[:, selected_cells].sum(axis=0)  # 计算每个选定细胞的表达值和\n", "    else:  # 稀疏矩阵处理\n", "        selected_cells_expression_sum = np.zeros(num_cells_to_check)\n", "        for idx, cell in enumerate(selected_cells):\n", "            # 获取当前细胞的数据\n", "            cell_data = X[:, cell]\n", "            selected_cells_expression_sum[idx] = np.sum(cell_data)\n", "\n", "    # 输出选定细胞的表达值和\n", "    print(\"Selected cells' expression sum:\", selected_cells_expression_sum)\n", "\n", "    # 检查是否进行归一化\n", "    total_expression_per_cell = np.sum(X, axis=0)  # 计算每个细胞的总表达值\n", "    expected_normalization_factor = 10000  # 假设我们希望每个细胞的总表达值被归一化到10000\n", "\n", "    # 归一化检查\n", "    normalization_check = total_expression_per_cell / expected_normalization_factor\n", "    print(\"Normalization check (should be close to 1.0):\", normalization_check)\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "a156aa30", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "\"Unable to synchronously open object (object 'var' doesn't exist)\"", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 75\u001b[39m\n\u001b[32m     73\u001b[39m \u001b[38;5;66;03m# obs/var 里找任意一列来确定 n_obs/n_vars\u001b[39;00m\n\u001b[32m     74\u001b[39m n_obs = \u001b[38;5;28mnext\u001b[39m(v.shape[\u001b[32m0\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m v \u001b[38;5;129;01min\u001b[39;00m f[\u001b[33m\"\u001b[39m\u001b[33mobs\u001b[39m\u001b[33m\"\u001b[39m].values() \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(v, h5py.Dataset))\n\u001b[32m---> \u001b[39m\u001b[32m75\u001b[39m n_vars = \u001b[38;5;28mnext\u001b[39m(v.shape[\u001b[32m0\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m v \u001b[38;5;129;01min\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvar\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m.values() \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(v, h5py.Dataset))\n\u001b[32m     77\u001b[39m \u001b[38;5;66;03m# 判断稠密/稀疏 & 稀疏类型\u001b[39;00m\n\u001b[32m     78\u001b[39m is_dense = \u001b[38;5;28misinstance\u001b[39m(X, h5py.Dataset)  \u001b[38;5;66;03m# 稠密矩阵\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/_objects.pyx:56\u001b[39m, in \u001b[36mh5py._objects.with_phil.wrapper\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/_objects.pyx:57\u001b[39m, in \u001b[36mh5py._objects.with_phil.wrapper\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/single_cell_preprocess/lib/python3.11/site-packages/h5py/_hl/group.py:360\u001b[39m, in \u001b[36mGroup.__getitem__\u001b[39m\u001b[34m(self, name)\u001b[39m\n\u001b[32m    358\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mInvalid HDF5 object reference\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    359\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(name, (\u001b[38;5;28mbytes\u001b[39m, \u001b[38;5;28mstr\u001b[39m)):\n\u001b[32m--> \u001b[39m\u001b[32m360\u001b[39m     oid = \u001b[43mh5o\u001b[49m\u001b[43m.\u001b[49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_e\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlapl\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_lapl\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    361\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    362\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mAccessing a group is done with bytes or str, \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    363\u001b[39m                     \u001b[33m\"\u001b[39m\u001b[33mnot \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m\"\u001b[39m.format(\u001b[38;5;28mtype\u001b[39m(name)))\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/_objects.pyx:56\u001b[39m, in \u001b[36mh5py._objects.with_phil.wrapper\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/_objects.pyx:57\u001b[39m, in \u001b[36mh5py._objects.with_phil.wrapper\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mh5py/h5o.pyx:257\u001b[39m, in \u001b[36mh5py.h5o.open\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: \"Unable to synchronously open object (object 'var' doesn't exist)\""]}], "source": ["import h5py\n", "import numpy as np\n", "\n", "path = \"/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_TGFB_Perturb_seq.RNA.counts.h5ad\"\n", "\n", "def basic_stats_dense(dset, n_obs, n_vars, row_block=5000):\n", "    N = n_obs * n_vars\n", "    m, M, s, s2, zc = np.inf, -np.inf, 0.0, 0.0, 0\n", "    for i in range(0, n_obs, row_block):\n", "        B = dset[i:i+row_block, :]  # (block_rows, n_vars)\n", "        m = min(m, B.min()); M = max(M, B.max())\n", "        s += B.sum(); s2 += (B**2).sum(); zc += (B == 0).sum()\n", "    mean = s / N\n", "    std = float(max(0.0, s2 / N - mean**2) ** 0.5)\n", "    zero_frac = float(zc / N)\n", "    if zero_frac > 0: m = 0.0\n", "    return dict(min=float(m), max=float(M), mean=float(mean), std=std, zero_fraction=zero_frac)\n", "\n", "def row_sums_csr(Xgrp, n_obs):\n", "    # CSR: indptr 长度 = n_obs+1；某一行 i 的非零在 data[indptr[i]:indptr[i+1]]\n", "    indptr = Xgrp[\"indptr\"][:]  # 小，读全内存问题不大\n", "    data = Xgrp[\"data\"]\n", "    rs = np.empty(n_obs, dtype=np.float64)\n", "    for i in range(n_obs):\n", "        start, end = indptr[i], indptr[i+1]\n", "        if end > start:\n", "            rs[i] = data[start:end].sum()\n", "        else:\n", "            rs[i] = 0.0\n", "    return rs\n", "\n", "def row_sums_csc(Xgrp, n_obs, col_block=20000):\n", "    # CSC: 按列存；累计到每一行的总和：对每个列块，读取 indices/data 切片，用 bincount 叠加\n", "    indptr = Xgrp[\"indptr\"][:]      # 长度 n_vars+1\n", "    indices = Xgrp[\"indices\"]       # 行索引（按列拼接）\n", "    data = Xgrp[\"data\"]\n", "    rs = np.zeros(n_obs, dtype=np.float64)\n", "    n_vars = Xgrp[\"shape\"][1] if \"shape\" in Xgrp else None\n", "    if n_vars is None:\n", "        # 兼容某些写法：shape 是 Dataset\n", "        n_vars = int(np.array(Xgrp[\"shape\"])[1])\n", "    for j0 in range(0, n_vars, col_block):\n", "        j1 = min(j0 + col_block, n_vars)\n", "        start = indptr[j0]; end = indptr[j1]\n", "        if end > start:\n", "            idx = indices[start:end]     # 这些非零元素对应的行\n", "            val = data[start:end]\n", "            # 累加到对应行\n", "            rs += np.bincount(idx, weights=val, minlength=n_obs).astype(np.float64)\n", "    return rs\n", "\n", "def sparse_global_stats(Xgrp, n_obs, n_vars, data_block=5_000_000):\n", "    N = n_obs * n_vars\n", "    d = Xgrp[\"data\"]\n", "    nnz = d.shape[0]\n", "    m, M, s, s2 = np.inf, -np.inf, 0.0, 0.0\n", "    for i in range(0, nnz, data_block):\n", "        chunk = d[i:i+data_block]\n", "        if chunk.size == 0: break\n", "        m = min(m, chunk.min()); M = max(M, chunk.max())\n", "        s += chunk.sum(); s2 += (chunk**2).sum()\n", "    zero_frac = 1 - nnz / N\n", "    if zero_frac > 0: m = 0.0\n", "    mean = s / N\n", "    std = float(max(0.0, s2 / N - mean**2) ** 0.5)\n", "    return dict(min=float(m), max=float(M), mean=float(mean), std=std, zero_fraction=float(zero_frac))\n", "\n", "with h5py.File(path, \"r\") as f:\n", "    # 读形状\n", "    if \"X\" not in f:\n", "        raise RuntimeError(\"h5ad 缺少 X\")\n", "    X = f[\"X\"]\n", "    # obs/var 里找任意一列来确定 n_obs/n_vars\n", "    n_obs = next(v.shape[0] for v in f[\"obs\"].values() if isinstance(v, h5py.Dataset))\n", "    n_vars = next(v.shape[0] for v in f[\"var\"].values() if isinstance(v, h5py.Dataset))\n", "\n", "    # 判断稠密/稀疏 & 稀疏类型\n", "    is_dense = isinstance(X, h5py.Dataset)  # 稠密矩阵\n", "    if is_dense:\n", "        enc = \"dense\"\n", "        gstats = basic_stats_dense(X, n_obs, n_vars)\n", "    else:\n", "        enc = X.attrs.get(\"encoding-type\", \"\").decode() if isinstance(X.attrs.get(\"encoding-type\", b\"\"), (bytes, bytearray)) else X.attrs.get(\"encoding-type\", \"\")\n", "        if enc == \"csr_matrix\":\n", "            sparse_kind = \"csr\"\n", "        elif enc == \"csc_matrix\":\n", "            sparse_kind = \"csc\"\n", "        else:\n", "            # 有些文件没写 attrs，用 shape 判断：CSR: shape[0]==n_obs；CSC: 同理，但两者都成立……\n", "            # 默认按 CSR 先试；若 indptr 长度为 n_vars+1 则其实是 CSC\n", "            indptr_len = X[\"indptr\"].shape[0]\n", "            sparse_kind = \"csr\" if indptr_len == n_obs + 1 else \"csc\"\n", "        gstats = sparse_global_stats(X, n_obs, n_vars)\n", "    print({\"encoding\": enc, \"sparse_kind\": (None if is_dense else sparse_kind), **gstats})\n", "\n", "    # --- 抽查 5 个细胞（行）总表达 ---\n", "    num_cells_to_check = 5\n", "    selected_rows = np.sort(np.random.choice(n_obs, num_cells_to_check, replace=False))\n", "\n", "    if is_dense:\n", "        # 行和\n", "        selected_rows_sum = X[selected_rows, :].sum(axis=1)\n", "        # 所有细胞行和（用于归一化检查）\n", "        # 分块求和以省内存\n", "        row_sum_all = np.zeros(n_obs, dtype=np.float64)\n", "        block = 5000\n", "        for i in range(0, n_obs, block):\n", "            row_sum_all[i:i+block] = X[i:i+block, :].sum(axis=1)\n", "    else:\n", "        if sparse_kind == \"csr\":\n", "            row_sum_all = row_sums_csr(X, n_obs)\n", "            selected_rows_sum = row_sum_all[selected_rows]\n", "        else:  # CSC\n", "            row_sum_all = row_sums_csc(X, n_obs)\n", "            selected_rows_sum = row_sum_all[selected_rows]\n", "\n", "    print(\"Random 5 cells' (row) sums:\", selected_rows_sum.tolist())\n", "\n", "    # --- 归一化检查：是否接近某个目标（例如 1e4） ---\n", "    target = 10_000.0  # 你原先的假设\n", "    ratio = row_sum_all / target\n", "    # 打印总体分布的若干统计量\n", "    q = np.quantile(row_sum_all, [0.0, 0.25, 0.5, 0.75, 1.0])\n", "    print({\n", "        \"row_sum_min\": float(q[0]),\n", "        \"row_sum_Q1\": float(q[1]),\n", "        \"row_sum_median\": float(q[2]),\n", "        \"row_sum_Q3\": float(q[3]),\n", "        \"row_sum_max\": float(q[4]),\n", "        \"row_sum_mean\": float(row_sum_all.mean()),\n", "        \"row_sum_std\": float(row_sum_all.std()),\n", "        \"ratio_mean(=row_sum/target)\": float(ratio.mean()),\n", "        \"ratio_std\": float(ratio.std())\n", "    })\n", "\n", "    # 如果需要也可以设置一个“接近 1.0”的阈值判断是否规范化\n", "    # 例如：绝大多数（>95%）细胞的 ratio 在 [0.9, 1.1] 之间\n", "    within = np.mean((ratio >= 0.9) & (ratio <= 1.1))\n", "    print({\"fraction_within_[0.9,1.1]\": float(within)})\n"]}, {"cell_type": "code", "execution_count": null, "id": "d0537e78", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "id": "9c638f24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X matrix shape: (145473, 9624)\n", "{'min': 0.0, 'max': 6761.0, 'mean': 1.9340336322784424, 'std': 11.743929862976074, 'zero_fraction': 0.5681376708840041}\n", "Selected cells' expression sum: [ 28809. 121895.  54123.  30743. 244094.]\n", "Normalization check (should be close to 1.0): [   2.9499   27.525     2.5717 ... 2061.5247    2.9135    6.7008]\n"]}], "source": ["import h5py\n", "import numpy as np\n", "\n", "path = \"/data/vcc/raw_data_vcc/raw_data_vcc/Nadig_et_al_2025/GSE264667_hepg2_raw_singlecell_01.h5ad\"\n", "with h5py.File(path, \"r\") as f:\n", "    X = f[\"X\"]\n", "    # 取 n_obs / n_vars（从 obs/var 任意一列）\n", "    n_obs = next(v.shape[0] for v in f[\"obs\"].values() if isinstance(v, h5py.Dataset))\n", "    n_vars = next(v.shape[0] for v in f[\"var\"].values() if isinstance(v, h5py.Dataset))\n", "    N = n_obs * n_vars\n", "\n", "    # 打印 X 矩阵的形状\n", "    print(\"X matrix shape:\", X.shape)\n", "    \n", "    if isinstance(X, h5py.Dataset):  # 稠密矩阵\n", "        m, M, s, s2, zc = np.inf, -np.inf, 0.0, 0.0, 0\n", "        for i in range(0, X.shape[0], 5000):\n", "            B = X[i:i+5000, :]\n", "            m = min(m, B.min()); M = max(M, B.max())\n", "            s += B.sum(); s2 += (B**2).sum(); zc += (B == 0).sum()\n", "        mean = s / N; std = (max(0.0, s2 / N - mean**2))**0.5\n", "        zero_frac = zc / N\n", "        if zero_frac > 0: m = 0.0\n", "    else:  # 稀疏矩阵（CSR/CSC）\n", "        d = X[\"data\"]; nnz = d.shape[0]\n", "        m, M, s, s2 = np.inf, -np.inf, 0.0, 0.0\n", "        for i in range(0, nnz, 5_000_000):\n", "            chunk = d[i:i+5_000_000]\n", "            if chunk.size == 0: break\n", "            m = min(m, chunk.min()); M = max(M, chunk.max())\n", "            s += chunk.sum(); s2 += (chunk**2).sum()\n", "        zero_frac = 1 - nnz / N\n", "        if zero_frac > 0: m = 0.0\n", "        mean = s / N; std = (max(0.0, s2 / N - mean**2))**0.5\n", "\n", "    # 打印基本统计信息\n", "    print(dict(min=float(m), max=float(M), mean=float(mean),\n", "               std=float(std), zero_fraction=float(zero_frac)))\n", "    \n", "    # 计算几个细胞（列）的表达值之和\n", "    num_cells_to_check = 5  # 选择5个细胞来检查\n", "    selected_cells = np.random.choice(n_vars, num_cells_to_check, replace=False)  # 随机选择5个细胞\n", "    \n", "    # 确保选择的列是升序的\n", "    selected_cells = np.sort(selected_cells)\n", "\n", "    # 稠密矩阵处理\n", "    if isinstance(X, h5py.Dataset):\n", "        selected_cells_expression_sum = X[:, selected_cells].sum(axis=0)  # 计算每个选定细胞的表达值和\n", "    else:  # 稀疏矩阵处理\n", "        selected_cells_expression_sum = np.zeros(num_cells_to_check)\n", "        for idx, cell in enumerate(selected_cells):\n", "            # 获取当前细胞的数据\n", "            cell_data = X[:, cell]\n", "            selected_cells_expression_sum[idx] = np.sum(cell_data)\n", "\n", "    # 输出选定细胞的表达值和\n", "    print(\"Selected cells' expression sum:\", selected_cells_expression_sum)\n", "\n", "    # 检查是否进行归一化\n", "    total_expression_per_cell = np.sum(X, axis=0)  # 计算每个细胞的总表达值\n", "    expected_normalization_factor = 10000  # 假设我们希望每个细胞的总表达值被归一化到10000\n", "\n", "    # 归一化检查\n", "    normalization_check = total_expression_per_cell / expected_normalization_factor\n", "    print(\"Normalization check (should be close to 1.0):\", normalization_check)\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "id": "06985249", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始扫描文件夹: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang\n", "文件模式: *.h5ad\n", "采样设置: 1000 细胞, 1000 基因\n", "最大值阈值: 20.0\n", "--------------------------------------------------\n", "Seurat_object_IFNB_Perturb_seq.RNA.counts.h5ad\n", "  -> 判定: False\n", "  -> 原因: 采样最大值 vmax=86.00 超过阈值 20.0（不太像 log1p）\n", "  -> 采样统计: cells=1000/328542, genes=1000/34025, density=0.0908, vmin=1.000, vmax=86.000\n", "\n", "Seurat_object_IFNG_Perturb_seq.RNA.counts.h5ad\n", "  -> 判定: False\n", "  -> 原因: 采样最大值 vmax=344.00 超过阈值 20.0（不太像 log1p）\n", "  -> 采样统计: cells=1000/245240, genes=1000/33525, density=0.0762, vmin=1.000, vmax=344.000\n", "\n", "Seurat_object_INS_Perturb_seq.RNA.counts.h5ad\n", "  -> 判定: False\n", "  -> 原因: 采样最大值 vmax=49.00 超过阈值 20.0（不太像 log1p）\n", "  -> 采样统计: cells=1000/431457, genes=1000/34025, density=0.0879, vmin=1.000, vmax=49.000\n", "\n", "Seurat_object_TGFB_Perturb_seq.RNA.counts.h5ad\n", "  -> 判定: False\n", "  -> 原因: 采样最大值 vmax=276.00 超过阈值 20.0（不太像 log1p）\n", "  -> 采样统计: cells=1000/236606, genes=1000/33525, density=0.0708, vmin=1.000, vmax=276.000\n", "\n", "Seurat_object_TNFA_Perturb_seq.RNA.counts.h5ad\n", "  -> 判定: False\n", "  -> 原因: 采样最大值 vmax=173.00 超过阈值 20.0（不太像 log1p）\n", "  -> 采样统计: cells=1000/386631, genes=1000/33525, density=0.0775, vmin=1.000, vmax=173.000\n", "\n", "--------------------------------------------------\n", "扫描完成!\n", "\n", "汇总统计:\n", "总文件数: 5\n", "log1p格式文件: 0\n", "非log1p格式文件: 5\n", "错误文件: 0\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>文件名</th>\n", "      <th>判定结果</th>\n", "      <th>判定原因</th>\n", "      <th>总细胞数</th>\n", "      <th>总基因数</th>\n", "      <th>采样细胞数</th>\n", "      <th>采样基因数</th>\n", "      <th>采样密度</th>\n", "      <th>最小值</th>\n", "      <th>最大值</th>\n", "      <th>使用的layer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON><PERSON>_object_IFNB_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=86.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>328542</td>\n", "      <td>34025</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.090829</td>\n", "      <td>1.0</td>\n", "      <td>86.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Se<PERSON><PERSON>_object_IFNG_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=344.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>245240</td>\n", "      <td>33525</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.076197</td>\n", "      <td>1.0</td>\n", "      <td>344.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Se<PERSON><PERSON>_object_INS_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=49.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>431457</td>\n", "      <td>34025</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.087864</td>\n", "      <td>1.0</td>\n", "      <td>49.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON>_object_TGFB_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=276.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>236606</td>\n", "      <td>33525</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.070791</td>\n", "      <td>1.0</td>\n", "      <td>276.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON><PERSON>_object_TNFA_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=173.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>386631</td>\n", "      <td>33525</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.077467</td>\n", "      <td>1.0</td>\n", "      <td>173.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              文件名   判定结果  \\\n", "0  Seurat_object_IFNB_Perturb_seq.RNA.counts.h5ad  False   \n", "1  Seurat_object_IFNG_Perturb_seq.RNA.counts.h5ad  False   \n", "2   Seurat_object_INS_Perturb_seq.RNA.counts.h5ad  False   \n", "3  Seurat_object_TGFB_Perturb_seq.RNA.counts.h5ad  False   \n", "4  Seurat_object_TNFA_Perturb_seq.RNA.counts.h5ad  False   \n", "\n", "                                     判定原因    总细胞数   总基因数  采样细胞数  采样基因数  \\\n", "0   采样最大值 vmax=86.00 超过阈值 20.0（不太像 log1p）  328542  34025   1000   1000   \n", "1  采样最大值 vmax=344.00 超过阈值 20.0（不太像 log1p）  245240  33525   1000   1000   \n", "2   采样最大值 vmax=49.00 超过阈值 20.0（不太像 log1p）  431457  34025   1000   1000   \n", "3  采样最大值 vmax=276.00 超过阈值 20.0（不太像 log1p）  236606  33525   1000   1000   \n", "4  采样最大值 vmax=173.00 超过阈值 20.0（不太像 log1p）  386631  33525   1000   1000   \n", "\n", "       采样密度  最小值    最大值 使用的layer  \n", "0  0.090829  1.0   86.0     None  \n", "1  0.076197  1.0  344.0     None  \n", "2  0.087864  1.0   49.0     None  \n", "3  0.070791  1.0  276.0     None  \n", "4  0.077467  1.0  173.0     None  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>文件名</th>\n", "      <th>判定结果</th>\n", "      <th>判定原因</th>\n", "      <th>总细胞数</th>\n", "      <th>总基因数</th>\n", "      <th>采样细胞数</th>\n", "      <th>采样基因数</th>\n", "      <th>采样密度</th>\n", "      <th>最小值</th>\n", "      <th>最大值</th>\n", "      <th>使用的layer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON><PERSON>_object_IFNB_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=86.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>328542</td>\n", "      <td>34025</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.090829</td>\n", "      <td>1.0</td>\n", "      <td>86.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Se<PERSON><PERSON>_object_IFNG_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=344.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>245240</td>\n", "      <td>33525</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.076197</td>\n", "      <td>1.0</td>\n", "      <td>344.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Se<PERSON><PERSON>_object_INS_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=49.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>431457</td>\n", "      <td>34025</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.087864</td>\n", "      <td>1.0</td>\n", "      <td>49.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON>_object_TGFB_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=276.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>236606</td>\n", "      <td>33525</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.070791</td>\n", "      <td>1.0</td>\n", "      <td>276.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON><PERSON>_object_TNFA_Perturb_seq.RNA.counts.h5ad</td>\n", "      <td>False</td>\n", "      <td>采样最大值 vmax=173.00 超过阈值 20.0（不太像 log1p）</td>\n", "      <td>386631</td>\n", "      <td>33525</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0.077467</td>\n", "      <td>1.0</td>\n", "      <td>173.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              文件名   判定结果  \\\n", "0  Seurat_object_IFNB_Perturb_seq.RNA.counts.h5ad  False   \n", "1  Seurat_object_IFNG_Perturb_seq.RNA.counts.h5ad  False   \n", "2   Seurat_object_INS_Perturb_seq.RNA.counts.h5ad  False   \n", "3  Seurat_object_TGFB_Perturb_seq.RNA.counts.h5ad  False   \n", "4  Seurat_object_TNFA_Perturb_seq.RNA.counts.h5ad  False   \n", "\n", "                                     判定原因    总细胞数   总基因数  采样细胞数  采样基因数  \\\n", "0   采样最大值 vmax=86.00 超过阈值 20.0（不太像 log1p）  328542  34025   1000   1000   \n", "1  采样最大值 vmax=344.00 超过阈值 20.0（不太像 log1p）  245240  33525   1000   1000   \n", "2   采样最大值 vmax=49.00 超过阈值 20.0（不太像 log1p）  431457  34025   1000   1000   \n", "3  采样最大值 vmax=276.00 超过阈值 20.0（不太像 log1p）  236606  33525   1000   1000   \n", "4  采样最大值 vmax=173.00 超过阈值 20.0（不太像 log1p）  386631  33525   1000   1000   \n", "\n", "       采样密度  最小值    最大值 使用的layer  \n", "0  0.090829  1.0   86.0     None  \n", "1  0.076197  1.0  344.0     None  \n", "2  0.087864  1.0   49.0     None  \n", "3  0.070791  1.0  276.0     None  \n", "4  0.077467  1.0  173.0     None  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import glob\n", "import numpy as np\n", "import anndata as ad\n", "import scipy.sparse as sp\n", "import pandas as pd\n", "from IPython.display import display\n", "\n", "# ----------------- helpers -----------------\n", "def _ensure_csr(X):\n", "    return X if sp.isspmatrix_csr(X) else X.tocsr()\n", "\n", "def _get_X(adata, layer=None):\n", "    if layer is None:\n", "        return adata.X\n", "    if layer in adata.layers:\n", "        return adata.layers[layer]\n", "    raise KeyError(f\"layer '{layer}' 不存在。可用层：{list(adata.layers.keys())}\")\n", "\n", "# ----------------- verbose checker -----------------\n", "def is_log1p_matrix_verbose(adata,\n", "                            layer=None,\n", "                            sample_cells=1000,\n", "                            sample_genes=1000,\n", "                            vmax_threshold=50.0,\n", "                            rng_cells_seed=0,\n", "                            rng_genes_seed=1):\n", "    \"\"\"\n", "    返回: (is_lognorm: bool, reason: str, stats: dict)\n", "    判定逻辑优先顺序:\n", "      1) adata.uns['log1p'] 明示过 -> True\n", "      2) adata.uns['pp'] 含 log1p 或 recipe=='log1p' -> True\n", "      3) 采样子矩阵:\n", "         - 若有负值 -> False, reason='negative (可能 z-score)'\n", "         - 若 max > vmax_threshold -> False, reason='max>threshold'\n", "         - 否则 -> True, reason='heuristic: max<=threshold & no negative'\n", "    \"\"\"\n", "    stats = {}\n", "    # 1) 明确信号\n", "    try:\n", "        if isinstance(adata.uns.get(\"log1p\", None), dict) or (\"log1p\" in adata.uns):\n", "            return True, \"uns['log1p'] 存在（显式标注为 log1p）\", {\"path\": getattr(adata, \"filename\", None)}\n", "    except Exception:\n", "        pass\n", "\n", "    try:\n", "        pp = adata.uns.get(\"pp\", {})\n", "        if isinstance(pp, dict) and (\"log1p\" in pp or pp.get(\"recipe\") == \"log1p\"):\n", "            return True, \"uns['pp'] 指示为 log1p（或 recipe=='log1p'）\", {\"path\": getattr(adata, \"filename\", None)}\n", "    except Exception:\n", "        pass\n", "\n", "    # 2) 采样判断\n", "    X = _get_X(adata, layer)\n", "    n_cells, n_genes = adata.n_obs, adata.n_vars\n", "    sc = min(sample_cells, n_cells)\n", "    sg = min(sample_genes, n_genes)\n", "\n", "    # 固定随机种子，保证可复现\n", "    r_idx = np.arange(n_cells) if n_cells <= sc else np.random.default_rng(rng_cells_seed).choice(n_cells, size=sc, replace=False)\n", "    c_idx = np.arange(n_genes) if n_genes <= sg else np.random.default_rng(rng_genes_seed).choice(n_genes, size=sg, replace=False)\n", "    r_idx.sort(); c_idx.sort()\n", "\n", "    Xs = X[r_idx][:, c_idx]\n", "    total_slots = int(sc * sg)\n", "\n", "    if sp.is<PERSON>arse(Xs):\n", "        Xs = _ensure_csr(Xs)\n", "        vals = Xs.data\n", "        nnz = int(vals.size)\n", "        density = nnz / total_slots if total_slots > 0 else 0.0\n", "        vmin = float(vals.min()) if nnz > 0 else 0.0\n", "        vmax = float(vals.max()) if nnz > 0 else 0.0\n", "    else:\n", "        arr = np.asarray(Xs)\n", "        vals = arr.ravel()\n", "        nnz = int((vals != 0).sum())\n", "        density = nnz / total_slots if total_slots > 0 else 0.0\n", "        vmin = float(vals.min()) if vals.size else 0.0\n", "        vmax = float(vals.max()) if vals.size else 0.0\n", "\n", "    stats.update(dict(\n", "        n_cells=int(n_cells), n_genes=int(n_genes),\n", "        sample_cells=int(sc), sample_genes=int(sg),\n", "        sampled_total=int(total_slots), sampled_nnz=int(nnz), sampled_density=float(density),\n", "        vmin=float(vmin), vmax=float(vmax),\n", "        layer=layer\n", "    ))\n", "\n", "    if vals.size == 0:\n", "        return False, \"采样为空（可能全零或切片异常）\", stats\n", "\n", "    if vmin < 0:\n", "        return False, \"检测到负值（疑似 z-score/中心化数据）\", stats\n", "\n", "    if vmax > vmax_threshold:\n", "        return False, f\"采样最大值 vmax={vmax:.2f} 超过阈值 {vmax_threshold}（不太像 log1p）\", stats\n", "\n", "    return True, \"启发式判断：无负值且最大值不过大，判定为 log1p\", stats\n", "\n", "# ----------------- folder scanner -----------------\n", "def scan_folder(folder, layer=None, pattern=\"*.h5ad\", sample_cells=1000, sample_genes=1000, vmax_threshold=20.0):\n", "    \"\"\"\n", "    扫描文件夹中的h5ad文件，检测是否为log1p格式\n", "    \n", "    参数:\n", "    folder: 文件夹路径\n", "    layer: 使用的layer名（默认用.X）\n", "    pattern: 文件匹配模式\n", "    sample_cells: 采样细胞数上限\n", "    sample_genes: 采样基因数上限\n", "    vmax_threshold: 最大值阈值（>阈值视为非log1p）\n", "    \n", "    返回:\n", "    DataFrame: 包含每个文件的检测结果\n", "    \"\"\"\n", "    paths = sorted(glob.glob(os.path.join(folder, pattern)))\n", "    results = []\n", "    \n", "    if not paths:\n", "        print(f\"[WARN] 目录内没有匹配到 {pattern} ：{folder}\")\n", "        return pd.DataFrame()\n", "    \n", "    for p in paths:\n", "        try:\n", "            adata = ad.read_h5ad(p, backed=\"r\")  # 内存映射只读\n", "            try:\n", "                is_log, reason, info = is_log1p_matrix_verbose(\n", "                    adata, layer=layer,\n", "                    sample_cells=sample_cells, sample_genes=sample_genes,\n", "                    vmax_threshold=vmax_threshold\n", "                )\n", "                name = os.path.basename(p)\n", "                \n", "                # 收集结果\n", "                result = {\n", "                    '文件名': name,\n", "                    '判定结果': is_log,\n", "                    '判定原因': reason,\n", "                    '总细胞数': info.get('n_cells'),\n", "                    '总基因数': info.get('n_genes'),\n", "                    '采样细胞数': info.get('sample_cells'),\n", "                    '采样基因数': info.get('sample_genes'),\n", "                    '采样密度': info.get('sampled_density'),\n", "                    '最小值': info.get('vmin'),\n", "                    '最大值': info.get('vmax'),\n", "                    '使用的layer': info.get('layer')\n", "                }\n", "                results.append(result)\n", "                \n", "                # 打印结果\n", "                print(f\"{name}\\n  -> 判定: {is_log}\\n  -> 原因: {reason}\\n  -> 采样统计: \"\n", "                      f\"cells={info.get('sample_cells')}/{info.get('n_cells')}, \"\n", "                      f\"genes={info.get('sample_genes')}/{info.get('n_genes')}, \"\n", "                      f\"density={info.get('sampled_density'):.4f}, \"\n", "                      f\"vmin={info.get('vmin'):.3f}, vmax={info.get('vmax'):.3f}\\n\")\n", "            finally:\n", "                # backed 模式需关闭文件句柄\n", "                if hasattr(adata, \"file\") and hasattr(adata.file, \"close\"):\n", "                    adata.file.close()\n", "        except Exception as e:\n", "            print(f\"{os.path.basename(p)}\\n  -> 判定: ERROR\\n  -> 原因: {e}\\n\")\n", "            results.append({\n", "                '文件名': os.path.basename(p),\n", "                '判定结果': 'ERROR',\n", "                '判定原因': str(e),\n", "                '总细胞数': None,\n", "                '总基因数': None,\n", "                '采样细胞数': None,\n", "                '采样基因数': None,\n", "                '采样密度': None,\n", "                '最小值': None,\n", "                '最大值': None,\n", "                '使用的layer': layer\n", "            })\n", "    \n", "    return pd.DataFrame(results)\n", "\n", "# ----------------- 在<PERSON><PERSON><PERSON>中使用的函数 -----------------\n", "def check_h5ad_files(folder_path, layer=None, pattern=\"*.h5ad\", \n", "                     sample_cells=1000, sample_genes=1000, vmax_threshold=20.0):\n", "    \"\"\"\n", "    在Jupyter Notebook中检查h5ad文件是否为log1p格式\n", "    \n", "    参数:\n", "    folder_path: 文件夹路径\n", "    layer: 使用的layer名（默认用.X）\n", "    pattern: 文件匹配模式\n", "    sample_cells: 采样细胞数上限\n", "    sample_genes: 采样基因数上限\n", "    vmax_threshold: 最大值阈值（>阈值视为非log1p）\n", "    \n", "    返回:\n", "    DataFrame: 包含每个文件的检测结果\n", "    \"\"\"\n", "    print(f\"开始扫描文件夹: {folder_path}\")\n", "    print(f\"文件模式: {pattern}\")\n", "    print(f\"采样设置: {sample_cells} 细胞, {sample_genes} 基因\")\n", "    print(f\"最大值阈值: {vmax_threshold}\")\n", "    print(\"-\" * 50)\n", "    \n", "    # 执行扫描\n", "    results_df = scan_folder(\n", "        folder_path, \n", "        layer=layer, \n", "        pattern=pattern,\n", "        sample_cells=sample_cells,\n", "        sample_genes=sample_genes,\n", "        vmax_threshold=vmax_threshold\n", "    )\n", "    \n", "    print(\"-\" * 50)\n", "    print(\"扫描完成!\")\n", "    \n", "    # 显示汇总统计\n", "    if not results_df.empty:\n", "        print(\"\\n汇总统计:\")\n", "        total_files = len(results_df)\n", "        log1p_files = len(results_df[results_df['判定结果'] == True])\n", "        non_log1p_files = len(results_df[results_df['判定结果'] == False])\n", "        error_files = len(results_df[results_df['判定结果'] == 'ERROR'])\n", "        \n", "        print(f\"总文件数: {total_files}\")\n", "        print(f\"log1p格式文件: {log1p_files}\")\n", "        print(f\"非log1p格式文件: {non_log1p_files}\")\n", "        print(f\"错误文件: {error_files}\")\n", "    \n", "    return results_df\n", "\n", "\n", "# 设置参数\n", "folder_path = \"/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang\"\n", "layer = None  # 使用.X，如果要使用特定layer，设置为layer名称\n", "\n", "# 执行检查\n", "results = check_h5ad_files(folder_path, layer=layer)\n", "\n", "# 显示结果表格\n", "display(results)\n", "\n", "# 筛选出非log1p格式的文件\n", "non_log1p_files = results[results['判定结果'] == False]\n", "display(non_log1p_files)"]}, {"cell_type": "code", "execution_count": 1, "id": "fe70ea2d", "metadata": {}, "outputs": [], "source": ["import scanpy as sc"]}, {"cell_type": "code", "execution_count": 6, "id": "86613dcf", "metadata": {}, "outputs": [], "source": ["a= sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_train.h5',backed='r')"]}, {"cell_type": "code", "execution_count": 9, "id": "20983363", "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n", "\n", "# 读入（backed='r' 是只读，不能直接改）\n", "a = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_public/competition_train.h5', backed='r')\n", "\n", "# 拉到内存\n", "adata = a.to_memory()           # 等价于 a[:, :].to_memory()\n", "\n", "# 把 var 的索引复制成一列\n", "adata.var[\"gene_name\"] = adata.var_names.astype(str)\n", "\n", "\n", "adata.write_h5ad('/data/ioz_whr_wsx/datasets/VCC/raw_data_in_vcc/200perturb_vccfortest/competition_train.with_gene_name.h5ad', compression='lzf')\n"]}, {"cell_type": "code", "execution_count": 8, "id": "671e198e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND4L</th>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND4</th>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND6</th>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-CYB</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18080 rows × 0 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [<PERSON><PERSON>11, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>1, <PERSON><PERSON>M1, <PERSON>ES4, <PERSON><PERSON>15, <PERSON>R<PERSON>, <PERSON>NF223, <PERSON>1orf159, T<PERSON><PERSON>10, TNF<PERSON>F18, TNFRSF4, <PERSON>F4, B3<PERSON>LT6, <PERSON>1<PERSON><PERSON><PERSON>12, <PERSON>BE2J2, <PERSON>NN1<PERSON>, <PERSON><PERSON>3, <PERSON><PERSON><PERSON>1, <PERSON><PERSON>11, <PERSON><PERSON>, TAS1R3, DVL1, <PERSON><PERSON><PERSON>8, AUR<PERSON>IP1, <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>, TMEM88B, VWA1, ATAD3C, ATAD3B, ATAD3A, TMEM240, SSU72, FNDC10, MIB2, MMP23B, CDK11B, CDK11A, NADK, GNB1, CALML6, TMEM52, <PERSON>AP74, <PERSON>BR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>P20, <PERSON><PERSON>, RER1, <PERSON>EX10, P<PERSON>H2, PANK4, HES5, TNFRSF14, PRXL2B, MMEL1, ACTRT2, <PERSON>DM16, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>F6, <PERSON><PERSON>G1<PERSON>, <PERSON>AP73, TP73, <PERSON><PERSON><PERSON>, <PERSON><PERSON>1, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>104, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>174, <PERSON><PERSON>1, <PERSON><PERSON><PERSON>4, <PERSON><PERSON><PERSON><PERSON>, <PERSON>D5, <PERSON><PERSON>207, <PERSON><PERSON><PERSON>, <PERSON><PERSON>3, <PERSON><PERSON><PERSON>3, ACOT7, HES2, ESPN, TNFRSF25, PLEKHG5, NOL9, TAS1R1, ZBTB48, KLHL21, PHF13, THAP3, DNAJC11, CAMTA1, VAMP3, PER3, UTS2, TNFRSF9, ERR<PERSON>1, SLC45A1, <PERSON>ERE, <PERSON>NO1, CA6, ...]\n", "\n", "[18080 rows x 0 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["a.var"]}], "metadata": {"kernelspec": {"display_name": "single_cell_preprocess", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}