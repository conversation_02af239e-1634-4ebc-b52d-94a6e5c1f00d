import scanpy as sc
import pandas as pd



a = sc.read_h5ad('/data/vcc/compass_loaded/colab_like/rpe1_Training.h5ad',backed='r')

a.var

import anndata as ad
b= ad.read_h5ad('/data/vcc/compass_loaded/compass_cell_load_filtered_log1p/Nadig/GSE264667_hepg2_raw_singlecell_01.aligned.cellload.filtered.h5ad',backed='r')

anndata.version

b


a

a.obs

a.var

f=a.X.toarray()

import numpy as np

sum(np.expm1(f[0]))

non_targeting_rows = a.obs[a.obs['gene'].str.contains('non-targeting', case=False, na=False)]
non_targeting_rows

a.var

a.X

a.obs['']

import scanpy as sc
import pandas as pd

b = sc.read_h5ad('/data/ioz_cbmi/HEK293T_filtered_dual_guide_cells.h5ad', backed="r")

b

b.var


import scanpy as sc
import pandas as pd

a = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed_filtered.h5ad', backed="r")

a.var



import scanpy as sc
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from scipy.sparse import issparse, csr_matrix
import os
import json

def analyze_h5ad_changes(file1_path, file2_path, output_dir):
    os.makedirs(output_dir, exist_ok=True)

    # 读取文件
    adata_original = sc.read_h5ad(file1_path, backed="r")
    adata_filtered = sc.read_h5ad(file2_path, backed="r")

    print(f"原始文件形状: {adata_original.shape}")
    print(f"过滤后文件形状: {adata_filtered.shape}")

    # 获取 gene_id 列进行比较
    original_var = adata_original.var[['gene_id', 'gene_name']].copy()
    filtered_var = adata_filtered.var[['gene_id', 'gene_name']].copy()

    # 重置索引，使用 gene_id 作为唯一标识
    original_var.reset_index(drop=True, inplace=True)
    filtered_var.reset_index(drop=True, inplace=True)

    # 找出被删除的基因
    deleted_var = original_var[~original_var['gene_id'].isin(filtered_var['gene_id'])]
    print(f"\n实际被删除的基因数量: {len(deleted_var)}")
    print(f"删除比例: {len(deleted_var) / len(original_var):.2%}")

    # 保存被删除基因的详细信息
    deleted_var.to_csv(f"{output_dir}/deleted_genes_details.csv", index=False)
    print(f"\n被删除基因的详细信息已保存到: {output_dir}/deleted_genes_details.csv")

    # 分析被删除基因的特点
    print("\n=== 被删除基因分析 ===")

    # 1. 基因名与 gene_id 相同的比例
    same_name_id = deleted_var['gene_name'] == deleted_var['gene_id']
    print(f"基因名与 gene_id 相同的比例: {same_name_id.mean():.2%}")

    # 2. 是否是有效的 Ensembl ID 格式
    def is_ensg(x):
        return isinstance(x, str) and x.startswith('ENSG') and len(x) > 10

    is_ensg_vec = deleted_var['gene_id'].apply(is_ensg)
    print(f"有效 Ensembl ID 格式的比例: {is_ensg_vec.mean():.2%}")

    # 3. 基因类型分布（如果有此信息）
    if 'gene_biotype' in deleted_var.columns:
        biotype_counts = deleted_var['gene_biotype'].value_counts()
        print("\n被删除基因的生物类型分布:")
        print(biotype_counts.head(10))

        plt.figure(figsize=(12, 6))
        biotype_counts.head(10).plot(kind='bar')
        plt.title('Top 10 Biotypes of Deleted Genes')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f"{output_dir}/biotype_distribution.png")
        plt.close()

    # 4. 基因名前缀分析
    print("\n被删除基因中最常见的前缀:")
    gene_prefixes = deleted_var['gene_name'].str.extract(r'^([A-Za-z]+)')[0].value_counts()
    print(gene_prefixes.head(10))

    # 5. 表达量分析（如果有 mean 列）
    if 'mean' in deleted_var.columns:
        plt.figure(figsize=(10, 6))
        plt.hist(deleted_var['mean'].dropna(), bins=50, alpha=0.7, label='Deleted Genes')

        # 比较保留基因的表达量
        kept_var = filtered_var
        if 'mean' in kept_var.columns:
            plt.hist(kept_var['mean'].dropna(), bins=50, alpha=0.7, label='Kept Genes')

        plt.xlabel('Mean Expression')
        plt.ylabel('Frequency')
        plt.title('Expression Distribution: Deleted vs Kept Genes')
        plt.legend()
        plt.savefig(f"{output_dir}/expression_comparison.png")
        plt.close()

    # 6. 稀疏度分析
    print("\n计算被删除基因的稀疏度...")
    sample_size = min(1000, adata_original.n_obs)
    sampled_cells = np.random.choice(adata_original.n_obs, sample_size, replace=False)

    # 获取被删除基因的列索引
    original_gene_ids = original_var['gene_id'].tolist()
    deleted_gene_ids = deleted_var['gene_id'].tolist()
    deleted_gene_indices = [original_gene_ids.index(gid) for gid in deleted_gene_ids]

    # 分块处理以减少内存使用
    chunk_size = 100
    nnz_total = 0
    total_elements = 0

    for i in tqdm(range(0, len(sampled_cells), chunk_size), desc="处理细胞块"):
        chunk_cells = sampled_cells[i:i + chunk_size]
        chunk_data = adata_original.X[chunk_cells][:, deleted_gene_indices]

        if issparse(chunk_data):
            nnz_total += chunk_data.nnz
        else:
            nnz_total += np.count_nonzero(chunk_data)

        total_elements += chunk_data.size

    sparsity = 1.0 - (nnz_total / total_elements) if total_elements > 0 else 0
    print(f"被删除基因的稀疏度: {sparsity:.2%}")

    # 生成总结报告
    report = {
        "原始基因数量": int(len(original_var)),
        "过滤后基因数量": int(len(filtered_var)),
        "删除基因数量": int(len(deleted_var)),
        "删除比例": float(len(deleted_var) / len(original_var)),
        "基因名与gene_id相同的比例": float(same_name_id.mean()),
        "有效Ensembl ID比例": float(is_ensg_vec.mean()),
        "被删除基因稀疏度": float(sparsity)
    }

    with open(f"{output_dir}/analysis_report.json", 'w') as f:
        json.dump(report, f, indent=2)

    print("\n分析完成!")
    return report

# 使用示例
output_dir = "/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/analysis"
report = analyze_h5ad_changes(
    "/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed.h5ad",
    "/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed_filtered.h5ad",
    output_dir
)


import scanpy as sc
import numpy as np
import scipy.sparse as sp
import h5py

def check_sparsity(file_path):
    # 读取h5ad文件
    adata = sc.read_h5ad(file_path)
    
    # 获取表达矩阵
    X = adata.X
    
    # 检查矩阵类型并计算稀疏性
    if hasattr(X, 'nnz'):  # 标准稀疏矩阵
        total_elements = X.shape[0] * X.shape[1]
        non_zero_elements = X.nnz
        matrix_type = "标准稀疏矩阵"
    elif hasattr(X, 'shape') and hasattr(X, 'indices'):  # HDF5支持的稀疏矩阵
        # 对于HDF5稀疏矩阵，我们需要特殊处理
        total_elements = X.shape[0] * X.shape[1]
        # 尝试获取非零元素数量
        try:
            non_zero_elements = len(X.indices)
            matrix_type = "HDF5稀疏矩阵"
        except:
            # 如果无法直接获取，可能需要转换为标准稀疏矩阵
            X = X.to_memory()
            non_zero_elements = X.nnz
            matrix_type = "转换后的稀疏矩阵"
    else:
        # 尝试转换为numpy数组
        try:
            X_dense = np.array(X)
            total_elements = X_dense.size
            non_zero_elements = np.count_nonzero(X_dense)
            matrix_type = "密集矩阵"
        except:
            print(f"无法处理矩阵类型: {type(X)}")
            return None
    
    # 计算稀疏度
    sparsity = 1 - (non_zero_elements / total_elements)
    
    # 打印结果
    print(f"文件: {file_path}")
    print(f"矩阵类型: {matrix_type}")
    print(f"矩阵形状: {X.shape}")
    print(f"总元素数: {total_elements}")
    print(f"非零元素数: {non_zero_elements}")
    print(f"稀疏度: {sparsity:.4%}")
    print(f"非零元素比例: {(1-sparsity):.4%}")
    print("-" * 50)
    
    return sparsity

# 检查两个文件
file1 = "/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed.h5ad"
file2 = "/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed_filtered.h5ad"

try:
    sparsity1 = check_sparsity(file1)
    sparsity2 = check_sparsity(file2)
    
    if sparsity1 is not None and sparsity2 is not None:
        print(f"稀疏度差异: {abs(sparsity1 - sparsity2):.4%}")
except Exception as e:
    print(f"处理文件时出错: {e}")
    print("尝试使用备用方法...")
    
    # 备用方法：直接检查HDF5文件
    def check_h5_sparsity(file_path):
        try:
            with h5py.File(file_path, 'r') as f:
                if 'X' in f:
                    X = f['X']
                    shape = X.attrs['shape']
                    total_elements = shape[0] * shape[1]
                    
                    # 尝试获取非零元素
                    if 'data' in X:
                        non_zero_elements = len(X['data'])
                    else:
                        # 如果不能直接获取，可能需要估算
                        print("无法直接获取非零元素数量")
                        return None
                    
                    sparsity = 1 - (non_zero_elements / total_elements)
                    
                    print(f"文件: {file_path}")
                    print(f"矩阵形状: {shape}")
                    print(f"总元素数: {total_elements}")
                    print(f"非零元素数: {non_zero_elements}")
                    print(f"稀疏度: {sparsity:.4%}")
                    print("-" * 50)
                    
                    return sparsity
                else:
                    print(f"文件中未找到X矩阵: {file_path}")
                    return None
        except Exception as e:
            print(f"读取HDF5文件时出错: {e}")
            return None
    
    sparsity1 = check_h5_sparsity(file1)
    sparsity2 = check_h5_sparsity(file2)

a.X


a.obs.columns

a.var

ensg_count = a.var.index.str.startswith('ENSG').sum()
print(f"Number of entries in a.var starting with 'ENSG': {ensg_count}")

b=sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/aligned18000datas/K562_gwps_raw_bulk_01.aligned.h5ad', backed="r")

ensg_count = b.var.index.str.startswith('ENSG').sum()
print(f"Number of entries in a.var starting with 'ENSG': {ensg_count}")

b.var

import scanpy as sc
import pandas as pd

a = sc.read_h5ad('/data/ioz_cbmi/HEK293T_filtered_dual_guide_cells.h5ad', backed="r")
a.var
print("DDX11L2、MIR1302-2HG等内容是a.var的行索引。", a.var.index)







a.var_names

a.obs


b = sc.read_h5ad('/data/ioz_cbmi/HCT116_filtered_dual_guide_cells.h5ad', backed="r")
b.var


b.obs


c = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/aligned18000datas_merged/K562_essential_raw_singlecell_01.aligned.h5ad', backed="r")
c.var
c.var.index


c.obs

d = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Replogle_et_al_2022/rpe1_raw_bulk_01.h5ad', backed="r")
d.var





d.obs

e = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/aligned18000datas_merged/K562_essential_raw_singlecell_01.aligned.h5ad',backed='r')
e.var




e.obs


f = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Replogle_et_al_2022/K562_essential_raw_singlecell_01.h5ad', backed="r")
f.var



f.obs


g = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Replogle_et_al_2022/K562_essential_raw_singlecell_01.h5ad', backed="r")
g.var




g.obs


h = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Replogle_et_al_2022/K562_essential_raw_bulk_01.h5ad', backed="r")
h.var


h.obs

i = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Nadig_et_al_2025/GSE264667_jurkat_raw_singlecell_01.h5ad', backed="r")
i.var


i.obs

j = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Nadig_et_al_2025/GSE264667_hepg2_raw_singlecell_01.h5ad', backed="r")
j.var


j.obs