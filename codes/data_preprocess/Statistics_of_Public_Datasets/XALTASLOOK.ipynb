{"cells": [{"cell_type": "code", "execution_count": 1, "id": "53f2f685", "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n", "import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 2, "id": "28ec26a9", "metadata": {}, "outputs": [], "source": ["\n", "a = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p/Replogle/K562_essential_raw_singlecell_01.aligned.cellload.filtered.h5ad',backed='r')"]}, {"cell_type": "code", "execution_count": 3, "id": "6ede6340", "metadata": {}, "outputs": [{"data": {"text/plain": ["AnnData object with n_obs × n_vars = 171268 × 18080 backed at '/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p/Replogle/K562_essential_raw_singlecell_01.aligned.cellload.filtered.h5ad'\n", "    obs: 'gem_group', 'gene', 'gene_id', 'transcript', 'gene_transcript', 'sgID_AB', 'mitopercent', 'UMI_count', 'z_gemgroup_UMI', 'core_scale_factor', 'core_adjusted_UMI_count', 'cell_residual_ratio', 'cell_target_expr', 'cell_ctrl_mean'\n", "    var: 'gene_name', 'gene_id'\n", "    uns: '__h5sparse_format__', 'cellload_preproc', 'log1p', 'pp'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 4, "id": "43264027", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>core_scale_factor</th>\n", "      <th>core_adjusted_UMI_count</th>\n", "      <th>cell_residual_ratio</th>\n", "      <th>cell_target_expr</th>\n", "      <th>cell_ctrl_mean</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAAATCCA-27</th>\n", "      <td>27</td>\n", "      <td>NAF1</td>\n", "      <td>ENSG00000145414</td>\n", "      <td>P1P2</td>\n", "      <td>5449_NAF1_P1P2_ENSG00000145414</td>\n", "      <td>NAF1_+_164087918.23-P1P2|NAF1_-_164087674.23-P1P2</td>\n", "      <td>0.112083</td>\n", "      <td>11438.0</td>\n", "      <td>0.013047</td>\n", "      <td>0.813253</td>\n", "      <td>14064.512695</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.604291</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAACTTCC-31</th>\n", "      <td>31</td>\n", "      <td>BUB1</td>\n", "      <td>ENSG00000169679</td>\n", "      <td>P1P2</td>\n", "      <td>935_BUB1_P1P2_ENSG00000169679</td>\n", "      <td>BUB1_-_111435363.23-P1P2|BUB1_-_111435372.23-P1P2</td>\n", "      <td>0.179895</td>\n", "      <td>5342.0</td>\n", "      <td>-1.522247</td>\n", "      <td>0.844107</td>\n", "      <td>6328.584473</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.287946</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAGCCAC-34</th>\n", "      <td>34</td>\n", "      <td>UBL5</td>\n", "      <td>ENSG00000198258</td>\n", "      <td>P1P2</td>\n", "      <td>9534_UBL5_P1P2_ENSG00000198258</td>\n", "      <td>UBL5_-_9938639.23-P1P2|UBL5_+_9938801.23-P1P2</td>\n", "      <td>0.105287</td>\n", "      <td>17305.0</td>\n", "      <td>0.384157</td>\n", "      <td>1.091537</td>\n", "      <td>15853.792969</td>\n", "      <td>0.168391</td>\n", "      <td>0.887548</td>\n", "      <td>5.270747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAATAGTC-43</th>\n", "      <td>43</td>\n", "      <td>C9orf16</td>\n", "      <td>ENSG00000171159</td>\n", "      <td>P1P2</td>\n", "      <td>1131_C9orf16_P1P2_ENSG00000171159</td>\n", "      <td>C9orf16_+_130922603.23-P1P2|C9orf16_+_13092264...</td>\n", "      <td>0.099359</td>\n", "      <td>30244.0</td>\n", "      <td>3.721912</td>\n", "      <td>0.948277</td>\n", "      <td>31893.619141</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>4.274510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGACAGCGT-28</th>\n", "      <td>28</td>\n", "      <td>TIMM9</td>\n", "      <td>ENSG00000100575</td>\n", "      <td>P1P2</td>\n", "      <td>8927_TIMM9_P1P2_ENSG00000100575</td>\n", "      <td>TIMM9_-_58893843.23-P1P2|TIMM9_-_58893848.23-P1P2</td>\n", "      <td>0.137623</td>\n", "      <td>8407.0</td>\n", "      <td>-0.975371</td>\n", "      <td>0.868942</td>\n", "      <td>9674.979492</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.933983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGGTGCG-19</th>\n", "      <td>19</td>\n", "      <td>RTCB</td>\n", "      <td>ENSG00000100220</td>\n", "      <td>P1P2</td>\n", "      <td>7583_RTCB_P1P2_ENSG00000100220</td>\n", "      <td>RTCB_+_32808194.23-P1P2|RTCB_+_32808188.23-P1P2</td>\n", "      <td>0.091941</td>\n", "      <td>17979.0</td>\n", "      <td>0.464728</td>\n", "      <td>1.123384</td>\n", "      <td>16004.317383</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.380027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCGTC-45</th>\n", "      <td>45</td>\n", "      <td>ATP6V1D</td>\n", "      <td>ENSG00000100554</td>\n", "      <td>P1P2</td>\n", "      <td>682_ATP6V1D_P1P2_ENSG00000100554</td>\n", "      <td>ATP6V1D_+_67826485.23-P1P2|ATP6V1D_+_67826497....</td>\n", "      <td>0.100272</td>\n", "      <td>18350.0</td>\n", "      <td>0.428227</td>\n", "      <td>1.115052</td>\n", "      <td>16456.638672</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.586924</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCTCG-27</th>\n", "      <td>27</td>\n", "      <td>CNOT3</td>\n", "      <td>ENSG00000088038</td>\n", "      <td>P1P2</td>\n", "      <td>1718_CNOT3_P1P2_ENSG00000088038</td>\n", "      <td>CNOT3_+_54641532.23-P1P2|CNOT3_-_54641691.23-P1P2</td>\n", "      <td>0.093876</td>\n", "      <td>8671.0</td>\n", "      <td>-0.633593</td>\n", "      <td>0.813253</td>\n", "      <td>10662.125000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.507016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTGCGG-44</th>\n", "      <td>44</td>\n", "      <td>METTL3</td>\n", "      <td>ENSG00000165819</td>\n", "      <td>P1P2</td>\n", "      <td>5004_METTL3_P1P2_ENSG00000165819</td>\n", "      <td>METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...</td>\n", "      <td>0.107983</td>\n", "      <td>20568.0</td>\n", "      <td>1.054624</td>\n", "      <td>0.973352</td>\n", "      <td>21131.095703</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.727288</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTTACAC-25</th>\n", "      <td>25</td>\n", "      <td>SEC61B</td>\n", "      <td>ENSG00000106803</td>\n", "      <td>P1P2</td>\n", "      <td>7752_SEC61B_P1P2_ENSG00000106803</td>\n", "      <td>SEC61B_+_101984577.23-P1P2|SEC61B_-_101984591....</td>\n", "      <td>0.095009</td>\n", "      <td>13504.0</td>\n", "      <td>0.341625</td>\n", "      <td>0.855594</td>\n", "      <td>15783.187500</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>4.742945</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>171268 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                     gem_group     gene          gene_id transcript  \\\n", "index                                                                 \n", "AAACCCAAGAAATCCA-27         27     NAF1  ENSG00000145414       P1P2   \n", "AAACCCAAGAACTTCC-31         31     BUB1  ENSG00000169679       P1P2   \n", "AAACCCAAGAAGCCAC-34         34     UBL5  ENSG00000198258       P1P2   \n", "AAACCCAAGAATAGTC-43         43  C9orf16  ENSG00000171159       P1P2   \n", "AAACCCAAGACAGCGT-28         28    TIMM9  ENSG00000100575       P1P2   \n", "...                        ...      ...              ...        ...   \n", "TTTGTTGTCTGGTGCG-19         19     RTCB  ENSG00000100220       P1P2   \n", "TTTGTTGTCTGTCGTC-45         45  ATP6V1D  ENSG00000100554       P1P2   \n", "TTTGTTGTCTGTCTCG-27         27    CNOT3  ENSG00000088038       P1P2   \n", "TTTGTTGTCTGTGCGG-44         44   METTL3  ENSG00000165819       P1P2   \n", "TTTGTTGTCTTTACAC-25         25   SEC61B  ENSG00000106803       P1P2   \n", "\n", "                                       gene_transcript  \\\n", "index                                                    \n", "AAACCCAAGAAATCCA-27     5449_NAF1_P1P2_ENSG00000145414   \n", "AAACCCAAGAACTTCC-31      935_BUB1_P1P2_ENSG00000169679   \n", "AAACCCAAGAAGCCAC-34     9534_UBL5_P1P2_ENSG00000198258   \n", "AAACCCAAGAATAGTC-43  1131_C9orf16_P1P2_ENSG00000171159   \n", "AAACCCAAGACAGCGT-28    8927_TIMM9_P1P2_ENSG00000100575   \n", "...                                                ...   \n", "TTTGTTGTCTGGTGCG-19     7583_RTCB_P1P2_ENSG00000100220   \n", "TTTGTTGTCTGTCGTC-45   682_ATP6V1D_P1P2_ENSG00000100554   \n", "TTTGTTGTCTGTCTCG-27    1718_CNOT3_P1P2_ENSG00000088038   \n", "TTTGTTGTCTGTGCGG-44   5004_METTL3_P1P2_ENSG00000165819   \n", "TTTGTTGTCTTTACAC-25   7752_SEC61B_P1P2_ENSG00000106803   \n", "\n", "                                                               sgID_AB  \\\n", "index                                                                    \n", "AAACCCAAGAAATCCA-27  NAF1_+_164087918.23-P1P2|NAF1_-_164087674.23-P1P2   \n", "AAACCCAAGAACTTCC-31  BUB1_-_111435363.23-P1P2|BUB1_-_111435372.23-P1P2   \n", "AAACCCAAGAAGCCAC-34      UBL5_-_9938639.23-P1P2|UBL5_+_9938801.23-P1P2   \n", "AAACCCAAGAATAGTC-43  C9orf16_+_130922603.23-P1P2|C9orf16_+_13092264...   \n", "AAACCCAAGACAGCGT-28  TIMM9_-_58893843.23-P1P2|TIMM9_-_58893848.23-P1P2   \n", "...                                                                ...   \n", "TTTGTTGTCTGGTGCG-19    RTCB_+_32808194.23-P1P2|RTCB_+_32808188.23-P1P2   \n", "TTTGTTGTCTGTCGTC-45  ATP6V1D_+_67826485.23-P1P2|ATP6V1D_+_67826497....   \n", "TTTGTTGTCTGTCTCG-27  CNOT3_+_54641532.23-P1P2|CNOT3_-_54641691.23-P1P2   \n", "TTTGTTGTCTGTGCGG-44  METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...   \n", "TTTGTTGTCTTTACAC-25  SEC61B_+_101984577.23-P1P2|SEC61B_-_101984591....   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \\\n", "index                                                         \n", "AAACCCAAGAAATCCA-27     0.112083    11438.0        0.013047   \n", "AAACCCAAGAACTTCC-31     0.179895     5342.0       -1.522247   \n", "AAACCCAAGAAGCCAC-34     0.105287    17305.0        0.384157   \n", "AAACCCAAGAATAGTC-43     0.099359    30244.0        3.721912   \n", "AAACCCAAGACAGCGT-28     0.137623     8407.0       -0.975371   \n", "...                          ...        ...             ...   \n", "TTTGTTGTCTGGTGCG-19     0.091941    17979.0        0.464728   \n", "TTTGTTGTCTGTCGTC-45     0.100272    18350.0        0.428227   \n", "TTTGTTGTCTGTCTCG-27     0.093876     8671.0       -0.633593   \n", "TTTGTTGTCTGTGCGG-44     0.107983    20568.0        1.054624   \n", "TTTGTTGTCTTTACAC-25     0.095009    13504.0        0.341625   \n", "\n", "                     core_scale_factor  core_adjusted_UMI_count  \\\n", "index                                                             \n", "AAACCCAAGAAATCCA-27           0.813253             14064.512695   \n", "AAACCCAAGAACTTCC-31           0.844107              6328.584473   \n", "AAACCCAAGAAGCCAC-34           1.091537             15853.792969   \n", "AAACCCAAGAATAGTC-43           0.948277             31893.619141   \n", "AAACCCAAGACAGCGT-28           0.868942              9674.979492   \n", "...                                ...                      ...   \n", "TTTGTTGTCTGGTGCG-19           1.123384             16004.317383   \n", "TTTGTTGTCTGTCGTC-45           1.115052             16456.638672   \n", "TTTGTTGTCTGTCTCG-27           0.813253             10662.125000   \n", "TTTGTTGTCTGTGCGG-44           0.973352             21131.095703   \n", "TTTGTTGTCTTTACAC-25           0.855594             15783.187500   \n", "\n", "                     cell_residual_ratio  cell_target_expr  cell_ctrl_mean  \n", "index                                                                       \n", "AAACCCAAGAAATCCA-27             0.000000          0.000000        0.604291  \n", "AAACCCAAGAACTTCC-31             0.000000          0.000000        1.287946  \n", "AAACCCAAGAAGCCAC-34             0.168391          0.887548        5.270747  \n", "AAACCCAAGAATAGTC-43             0.000000          0.000000        4.274510  \n", "AAACCCAAGACAGCGT-28             0.000000          0.000000        0.933983  \n", "...                                  ...               ...             ...  \n", "TTTGTTGTCTGGTGCG-19             0.000000          0.000000        1.380027  \n", "TTTGTTGTCTGTCGTC-45             0.000000          0.000000        0.586924  \n", "TTTGTTGTCTGTCTCG-27             0.000000          0.000000        0.507016  \n", "TTTGTTGTCTGTGCGG-44             0.000000          0.000000        0.727288  \n", "TTTGTTGTCTTTACAC-25             0.000000          0.000000        4.742945  \n", "\n", "[171268 rows x 14 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["a.obs"]}, {"cell_type": "code", "execution_count": 7, "id": "2e58ba04", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_id</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "      <td>ENSG00000187634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "      <td>ENSG00000188976</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "      <td>ENSG00000187961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "      <td>ENSG00000187583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "      <td>ENSG00000187642</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND4L</th>\n", "      <td>ENSG00000212907</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND4</th>\n", "      <td>ENSG00000198886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND5</th>\n", "      <td>ENSG00000198786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND6</th>\n", "      <td>ENSG00000198695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-CYB</th>\n", "      <td>ENSG00000198727</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18080 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                 gene_id\n", "index                   \n", "SAMD11   ENSG00000187634\n", "NOC2L    ENSG00000188976\n", "KLHL17   ENSG00000187961\n", "PLEKHN1  ENSG00000187583\n", "PERM1    ENSG00000187642\n", "...                  ...\n", "MT-ND4L  ENSG00000212907\n", "MT-ND4   ENSG00000198886\n", "MT-ND5   ENSG00000198786\n", "MT-ND6   ENSG00000198695\n", "MT-CYB   ENSG00000198727\n", "\n", "[18080 rows x 1 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["a.var"]}, {"cell_type": "code", "execution_count": 8, "id": "69bb44cb", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'CSRDataset' object has no attribute 'toarray'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m f=\u001b[43ma\u001b[49m\u001b[43m.\u001b[49m\u001b[43mX\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtoarray\u001b[49m()\n", "\u001b[31mAttributeError\u001b[39m: 'CSRDataset' object has no attribute 'toarray'"]}], "source": ["f=a.X.toarray()"]}, {"cell_type": "code", "execution_count": 5, "id": "86e4583c", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 9, "id": "2b55843f", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float32(9999.956)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(np.expm1(f[0]))"]}, {"cell_type": "code", "execution_count": 22, "id": "03b8f092", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>orig.ident</th>\n", "      <th>nCount_RNA</th>\n", "      <th>nFeature_RNA</th>\n", "      <th>sample</th>\n", "      <th>bc1_well</th>\n", "      <th>bc2_well</th>\n", "      <th>bc3_well</th>\n", "      <th>percent.mito</th>\n", "      <th>cell_type</th>\n", "      <th>pathway</th>\n", "      <th>RNA_snn_res.0.9</th>\n", "      <th>seurat_clusters</th>\n", "      <th>sample_ID</th>\n", "      <th>Batch_info</th>\n", "      <th>guide</th>\n", "      <th>gene</th>\n", "      <th>mixscale_score</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>79_19_64_1_1_1_1_1_1_1_1_1</th>\n", "      <td>79</td>\n", "      <td>6328.0</td>\n", "      <td>2896</td>\n", "      <td>A549_INS</td>\n", "      <td>G7</td>\n", "      <td>B7</td>\n", "      <td>F4</td>\n", "      <td>9.702908</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>NTg11</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80_14_20_1_1_1_1_1_1_1_1_1</th>\n", "      <td>80</td>\n", "      <td>6174.0</td>\n", "      <td>2987</td>\n", "      <td>A549_INS</td>\n", "      <td>G8</td>\n", "      <td>B2</td>\n", "      <td>B8</td>\n", "      <td>0.988014</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>NTg8</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79_58_05_1_1_1_1_1_1_1_1_1</th>\n", "      <td>79</td>\n", "      <td>5950.0</td>\n", "      <td>2977</td>\n", "      <td>A549_INS</td>\n", "      <td>G7</td>\n", "      <td>E10</td>\n", "      <td>A5</td>\n", "      <td>2.352941</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>NTg9</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77_41_21_1_1_1_1_1_1_1_1_1</th>\n", "      <td>77</td>\n", "      <td>5089.0</td>\n", "      <td>2603</td>\n", "      <td>A549_INS</td>\n", "      <td>G5</td>\n", "      <td>D5</td>\n", "      <td>B9</td>\n", "      <td>2.023973</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>NTg1</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79_33_39_1_1_1_1_1_1_1_1_1</th>\n", "      <td>79</td>\n", "      <td>4634.0</td>\n", "      <td>2472</td>\n", "      <td>A549_INS</td>\n", "      <td>G7</td>\n", "      <td>C9</td>\n", "      <td>D3</td>\n", "      <td>11.760898</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>NTg14</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83_66_11_2_2</th>\n", "      <td>83</td>\n", "      <td>4472.0</td>\n", "      <td>2492</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G11</td>\n", "      <td>F6</td>\n", "      <td>A11</td>\n", "      <td>0.715564</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>NTg4</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81_05_14_2_2</th>\n", "      <td>81</td>\n", "      <td>4116.0</td>\n", "      <td>2224</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G9</td>\n", "      <td>A5</td>\n", "      <td>B2</td>\n", "      <td>1.020408</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>NTg4</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82_37_17_2_2</th>\n", "      <td>82</td>\n", "      <td>3985.0</td>\n", "      <td>2298</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G10</td>\n", "      <td>D1</td>\n", "      <td>B5</td>\n", "      <td>0.627353</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>NTg4</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84_84_11_2_2</th>\n", "      <td>84</td>\n", "      <td>3454.0</td>\n", "      <td>1931</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G12</td>\n", "      <td>G12</td>\n", "      <td>A11</td>\n", "      <td>0.897510</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>NTg9</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83_83_45_2_2</th>\n", "      <td>83</td>\n", "      <td>3444.0</td>\n", "      <td>2036</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G11</td>\n", "      <td>G11</td>\n", "      <td>D9</td>\n", "      <td>4.819977</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>NTg12</td>\n", "      <td>non-targeting</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>26001 rows × 17 columns</p>\n", "</div>"], "text/plain": ["                           orig.ident  nCount_RNA  nFeature_RNA    sample  \\\n", "index                                                                       \n", "79_19_64_1_1_1_1_1_1_1_1_1         79      6328.0          2896  A549_INS   \n", "80_14_20_1_1_1_1_1_1_1_1_1         80      6174.0          2987  A549_INS   \n", "79_58_05_1_1_1_1_1_1_1_1_1         79      5950.0          2977  A549_INS   \n", "77_41_21_1_1_1_1_1_1_1_1_1         77      5089.0          2603  A549_INS   \n", "79_33_39_1_1_1_1_1_1_1_1_1         79      4634.0          2472  A549_INS   \n", "...                               ...         ...           ...       ...   \n", "83_66_11_2_2                       83      4472.0          2492  MCF7_INS   \n", "81_05_14_2_2                       81      4116.0          2224  MCF7_INS   \n", "82_37_17_2_2                       82      3985.0          2298  MCF7_INS   \n", "84_84_11_2_2                       84      3454.0          1931  MCF7_INS   \n", "83_83_45_2_2                       83      3444.0          2036  MCF7_INS   \n", "\n", "                           bc1_well bc2_well bc3_well  percent.mito cell_type  \\\n", "index                                                                           \n", "79_19_64_1_1_1_1_1_1_1_1_1       G7       B7       F4      9.702908      A549   \n", "80_14_20_1_1_1_1_1_1_1_1_1       G8       B2       B8      0.988014      A549   \n", "79_58_05_1_1_1_1_1_1_1_1_1       G7      E10       A5      2.352941      A549   \n", "77_41_21_1_1_1_1_1_1_1_1_1       G5       D5       B9      2.023973      A549   \n", "79_33_39_1_1_1_1_1_1_1_1_1       G7       C9       D3     11.760898      A549   \n", "...                             ...      ...      ...           ...       ...   \n", "83_66_11_2_2                    G11       F6      A11      0.715564      MCF7   \n", "81_05_14_2_2                     G9       A5       B2      1.020408      MCF7   \n", "82_37_17_2_2                    G10       D1       B5      0.627353      MCF7   \n", "84_84_11_2_2                    G12      G12      A11      0.897510      MCF7   \n", "83_83_45_2_2                    G11      G11       D9      4.819977      MCF7   \n", "\n", "                           pathway RNA_snn_res.0.9 seurat_clusters  sample_ID  \\\n", "index                                                                           \n", "79_19_64_1_1_1_1_1_1_1_1_1     INS               3               3   sample_1   \n", "80_14_20_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "79_58_05_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "77_41_21_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "79_33_39_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "...                            ...             ...             ...        ...   \n", "83_66_11_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "81_05_14_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "82_37_17_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "84_84_11_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "83_83_45_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "\n", "                           Batch_info  guide           gene  mixscale_score  \n", "index                                                                        \n", "79_19_64_1_1_1_1_1_1_1_1_1       Rep1  NTg11  non-targeting             0.0  \n", "80_14_20_1_1_1_1_1_1_1_1_1       Rep1   NTg8  non-targeting             0.0  \n", "79_58_05_1_1_1_1_1_1_1_1_1       Rep1   NTg9  non-targeting             0.0  \n", "77_41_21_1_1_1_1_1_1_1_1_1       Rep1   NTg1  non-targeting             0.0  \n", "79_33_39_1_1_1_1_1_1_1_1_1       Rep1  NTg14  non-targeting             0.0  \n", "...                               ...    ...            ...             ...  \n", "83_66_11_2_2                     Rep2   NTg4  non-targeting             0.0  \n", "81_05_14_2_2                     Rep2   NTg4  non-targeting             0.0  \n", "82_37_17_2_2                     Rep2   NTg4  non-targeting             0.0  \n", "84_84_11_2_2                     Rep2   NTg9  non-targeting             0.0  \n", "83_83_45_2_2                     Rep2  NTg12  non-targeting             0.0  \n", "\n", "[26001 rows x 17 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["non_targeting_rows = a.obs[a.obs['gene'].str.contains('non-targeting', case=False, na=False)]\n", "non_targeting_rows"]}, {"cell_type": "code", "execution_count": 20, "id": "d329d5cf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>gene_id</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "      <td>SAMD11</td>\n", "      <td>ENSG00000187634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "      <td>NOC2L</td>\n", "      <td>ENSG00000188976</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "      <td>KLHL17</td>\n", "      <td>ENSG00000187961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "      <td>PLEKHN1</td>\n", "      <td>ENSG00000187583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "      <td>PERM1</td>\n", "      <td>ENSG00000187642</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND4L</th>\n", "      <td>MT-ND4L</td>\n", "      <td>ENSG00000212907</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND4</th>\n", "      <td>MT-ND4</td>\n", "      <td>ENSG00000198886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND5</th>\n", "      <td>MT-ND5</td>\n", "      <td>ENSG00000198786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND6</th>\n", "      <td>MT-ND6</td>\n", "      <td>ENSG00000198695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-CYB</th>\n", "      <td>MT-CYB</td>\n", "      <td>ENSG00000198727</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18080 rows × 2 columns</p>\n", "</div>"], "text/plain": ["        gene_name          gene_id\n", "index                             \n", "SAMD11     SAMD11  ENSG00000187634\n", "NOC2L       NOC2L  ENSG00000188976\n", "KLHL17     KLHL17  ENSG00000187961\n", "PLEKHN1   PLEKHN1  ENSG00000187583\n", "PERM1       PERM1  ENSG00000187642\n", "...           ...              ...\n", "MT-ND4L   MT-ND4L  ENSG00000212907\n", "MT-ND4     MT-ND4  ENSG00000198886\n", "MT-ND5     MT-ND5  ENSG00000198786\n", "MT-ND6     MT-ND6  ENSG00000198695\n", "MT-CYB     MT-CYB  ENSG00000198727\n", "\n", "[18080 rows x 2 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["a.var"]}, {"cell_type": "code", "execution_count": 14, "id": "98f5c79b", "metadata": {}, "outputs": [{"data": {"text/plain": ["CSRDataset: backend hdf5, shape (431457, 18080), data_dtype float32"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["a.X"]}, {"cell_type": "code", "execution_count": null, "id": "96fa4f6e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>orig.ident</th>\n", "      <th>nCount_RNA</th>\n", "      <th>nFeature_RNA</th>\n", "      <th>sample</th>\n", "      <th>bc1_well</th>\n", "      <th>bc2_well</th>\n", "      <th>bc3_well</th>\n", "      <th>percent.mito</th>\n", "      <th>cell_type</th>\n", "      <th>pathway</th>\n", "      <th>RNA_snn_res.0.9</th>\n", "      <th>seurat_clusters</th>\n", "      <th>sample_ID</th>\n", "      <th>Batch_info</th>\n", "      <th>guide</th>\n", "      <th>gene</th>\n", "      <th>mixscale_score</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>77_33_14_1_1_1_1_1_1_1_1_1</th>\n", "      <td>77</td>\n", "      <td>9826.0</td>\n", "      <td>3785</td>\n", "      <td>A549_INS</td>\n", "      <td>G5</td>\n", "      <td>C9</td>\n", "      <td>B2</td>\n", "      <td>16.283330</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>EIF2B1g1</td>\n", "      <td>EIF2B1</td>\n", "      <td>0.733807</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79_60_10_1_1_1_1_1_1_1_1_1</th>\n", "      <td>79</td>\n", "      <td>9610.0</td>\n", "      <td>4197</td>\n", "      <td>A549_INS</td>\n", "      <td>G7</td>\n", "      <td>E12</td>\n", "      <td>A10</td>\n", "      <td>6.670135</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>MTORg1</td>\n", "      <td>MTOR</td>\n", "      <td>-0.081975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77_74_82_1_1_1_1_1_1_1_1_1</th>\n", "      <td>77</td>\n", "      <td>9367.0</td>\n", "      <td>4134</td>\n", "      <td>A549_INS</td>\n", "      <td>G5</td>\n", "      <td>G2</td>\n", "      <td>G10</td>\n", "      <td>3.619088</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>PTENg3</td>\n", "      <td>PTEN</td>\n", "      <td>0.854904</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80_91_37_1_1_1_1_1_1_1_1_1</th>\n", "      <td>80</td>\n", "      <td>9342.0</td>\n", "      <td>3949</td>\n", "      <td>A549_INS</td>\n", "      <td>G8</td>\n", "      <td>H7</td>\n", "      <td>D1</td>\n", "      <td>0.824235</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>FOXO3g3</td>\n", "      <td>FOXO3</td>\n", "      <td>0.138456</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77_33_30_1_1_1_1_1_1_1_1_1</th>\n", "      <td>77</td>\n", "      <td>8208.0</td>\n", "      <td>3476</td>\n", "      <td>A549_INS</td>\n", "      <td>G5</td>\n", "      <td>C9</td>\n", "      <td>C6</td>\n", "      <td>14.766082</td>\n", "      <td>A549</td>\n", "      <td>INS</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>sample_1</td>\n", "      <td>Rep1</td>\n", "      <td>SREBF1g3</td>\n", "      <td>SREBF1</td>\n", "      <td>1.246439</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82_83_54_2_2</th>\n", "      <td>82</td>\n", "      <td>2295.0</td>\n", "      <td>1403</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G10</td>\n", "      <td>G11</td>\n", "      <td>E6</td>\n", "      <td>1.655773</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>PIK3CAg3</td>\n", "      <td>PIK3CA</td>\n", "      <td>0.247833</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81_04_45_2_2</th>\n", "      <td>81</td>\n", "      <td>1888.0</td>\n", "      <td>1364</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G9</td>\n", "      <td>A4</td>\n", "      <td>D9</td>\n", "      <td>1.112288</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>TSC2g3</td>\n", "      <td>TSC2</td>\n", "      <td>-2.462007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82_92_06_2_2</th>\n", "      <td>82</td>\n", "      <td>1877.0</td>\n", "      <td>1254</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G10</td>\n", "      <td>H8</td>\n", "      <td>A6</td>\n", "      <td>2.290890</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>FOXO3g2</td>\n", "      <td>FOXO3</td>\n", "      <td>-0.183276</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84_83_92_2_2</th>\n", "      <td>84</td>\n", "      <td>1809.0</td>\n", "      <td>1222</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G12</td>\n", "      <td>G11</td>\n", "      <td>H8</td>\n", "      <td>0.386954</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>XBP1g1</td>\n", "      <td>XBP1</td>\n", "      <td>-1.220815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84_92_02_2_2</th>\n", "      <td>84</td>\n", "      <td>1747.0</td>\n", "      <td>1228</td>\n", "      <td>MCF7_INS</td>\n", "      <td>G12</td>\n", "      <td>H8</td>\n", "      <td>A2</td>\n", "      <td>1.659989</td>\n", "      <td>MCF7</td>\n", "      <td>INS</td>\n", "      <td>NA_character_</td>\n", "      <td>NA_character_</td>\n", "      <td>sample_16</td>\n", "      <td>Rep2</td>\n", "      <td>PIK3CAg1</td>\n", "      <td>PIK3CA</td>\n", "      <td>0.866642</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>431457 rows × 17 columns</p>\n", "</div>"], "text/plain": ["                           orig.ident  nCount_RNA  nFeature_RNA    sample  \\\n", "index                                                                       \n", "77_33_14_1_1_1_1_1_1_1_1_1         77      9826.0          3785  A549_INS   \n", "79_60_10_1_1_1_1_1_1_1_1_1         79      9610.0          4197  A549_INS   \n", "77_74_82_1_1_1_1_1_1_1_1_1         77      9367.0          4134  A549_INS   \n", "80_91_37_1_1_1_1_1_1_1_1_1         80      9342.0          3949  A549_INS   \n", "77_33_30_1_1_1_1_1_1_1_1_1         77      8208.0          3476  A549_INS   \n", "...                               ...         ...           ...       ...   \n", "82_83_54_2_2                       82      2295.0          1403  MCF7_INS   \n", "81_04_45_2_2                       81      1888.0          1364  MCF7_INS   \n", "82_92_06_2_2                       82      1877.0          1254  MCF7_INS   \n", "84_83_92_2_2                       84      1809.0          1222  MCF7_INS   \n", "84_92_02_2_2                       84      1747.0          1228  MCF7_INS   \n", "\n", "                           bc1_well bc2_well bc3_well  percent.mito cell_type  \\\n", "index                                                                           \n", "77_33_14_1_1_1_1_1_1_1_1_1       G5       C9       B2     16.283330      A549   \n", "79_60_10_1_1_1_1_1_1_1_1_1       G7      E12      A10      6.670135      A549   \n", "77_74_82_1_1_1_1_1_1_1_1_1       G5       G2      G10      3.619088      A549   \n", "80_91_37_1_1_1_1_1_1_1_1_1       G8       H7       D1      0.824235      A549   \n", "77_33_30_1_1_1_1_1_1_1_1_1       G5       C9       C6     14.766082      A549   \n", "...                             ...      ...      ...           ...       ...   \n", "82_83_54_2_2                    G10      G11       E6      1.655773      MCF7   \n", "81_04_45_2_2                     G9       A4       D9      1.112288      MCF7   \n", "82_92_06_2_2                    G10       H8       A6      2.290890      MCF7   \n", "84_83_92_2_2                    G12      G11       H8      0.386954      MCF7   \n", "84_92_02_2_2                    G12       H8       A2      1.659989      MCF7   \n", "\n", "                           pathway RNA_snn_res.0.9 seurat_clusters  sample_ID  \\\n", "index                                                                           \n", "77_33_14_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "79_60_10_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "77_74_82_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "80_91_37_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "77_33_30_1_1_1_1_1_1_1_1_1     INS              14              14   sample_1   \n", "...                            ...             ...             ...        ...   \n", "82_83_54_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "81_04_45_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "82_92_06_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "84_83_92_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "84_92_02_2_2                   INS   NA_character_   NA_character_  sample_16   \n", "\n", "                           Batch_info     guide    gene  mixscale_score  \n", "index                                                                    \n", "77_33_14_1_1_1_1_1_1_1_1_1       Rep1  EIF2B1g1  EIF2B1        0.733807  \n", "79_60_10_1_1_1_1_1_1_1_1_1       Rep1    MTORg1    MTOR       -0.081975  \n", "77_74_82_1_1_1_1_1_1_1_1_1       Rep1    PTENg3    PTEN        0.854904  \n", "80_91_37_1_1_1_1_1_1_1_1_1       Rep1   FOXO3g3   FOXO3        0.138456  \n", "77_33_30_1_1_1_1_1_1_1_1_1       Rep1  SREBF1g3  SREBF1        1.246439  \n", "...                               ...       ...     ...             ...  \n", "82_83_54_2_2                     Rep2  PIK3CAg3  PIK3CA        0.247833  \n", "81_04_45_2_2                     Rep2    TSC2g3    TSC2       -2.462007  \n", "82_92_06_2_2                     Rep2   FOXO3g2   FOXO3       -0.183276  \n", "84_83_92_2_2                     Rep2    XBP1g1    XBP1       -1.220815  \n", "84_92_02_2_2                     Rep2  PIK3CAg1  PIK3CA        0.866642  \n", "\n", "[431457 rows x 17 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["a.obs['']"]}, {"cell_type": "code", "execution_count": 29, "id": "29f498ff", "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n", "import pandas as pd\n", "\n", "b = sc.read_h5ad('/data/ioz_cbmi/HEK293T_filtered_dual_guide_cells.h5ad', backed=\"r\")"]}, {"cell_type": "code", "execution_count": 30, "id": "a34d2d9f", "metadata": {}, "outputs": [{"data": {"text/plain": ["AnnData object with n_obs × n_vars = 4534299 × 38606 backed at '/data/ioz_cbmi/HEK293T_filtered_dual_guide_cells.h5ad'\n", "    obs: 'sample', 'num_features', 'guide_target', 'gene_target', 'n_genes_by_counts', 'total_counts', 'total_counts_mt', 'pct_counts_mt', 'pass_guide_filter'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["b"]}, {"cell_type": "code", "execution_count": 32, "id": "fe43a6ef", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>DDX11L2</th>\n", "    </tr>\n", "    <tr>\n", "      <th>MIR1302-2HG</th>\n", "    </tr>\n", "    <tr>\n", "      <th>FAM138A</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000290826</th>\n", "    </tr>\n", "    <tr>\n", "      <th>OR4F5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000277836</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278633</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000276017</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278817</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000277196</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>38606 rows × 0 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [DDX11L2, <PERSON>R1302-2<PERSON><PERSON>, <PERSON><PERSON>138<PERSON>, ENSG00000290826, <PERSON><PERSON>F5, <PERSON>NSG00000238009, <PERSON>NSG00000239945, ENSG00000239906, <PERSON>NSG00000241860, ENSG00000241599, <PERSON><PERSON><PERSON>00000286448, ENSG00000236601, ENSG00000290385, <PERSON><PERSON><PERSON>29, <PERSON>NSG00000235146, <PERSON><PERSON><PERSON>16, <PERSON>NSG00000291215, <PERSON>NSG00000229905, LINC01409, ENSG00000290784, FAM87B, LINC00115, LIN<PERSON>01128, ENSG00000288531, <PERSON><PERSON>41<PERSON>, ENSG00000272438, <PERSON>NSG00000230699, <PERSON>NSG00000241180, LINC02593, <PERSON><PERSON>11, NOC2<PERSON>, <PERSON><PERSON><PERSON>17, <PERSON><PERSON><PERSON>HN1, <PERSON><PERSON>M1, <PERSON>NSG00000272512, <PERSON>ES4, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>00000224969, <PERSON><PERSON><PERSON>, <PERSON>NSG00000242590, <PERSON><PERSON><PERSON><PERSON><PERSON>0291156, <PERSON><PERSON><PERSON><PERSON>000273443, <PERSON><PERSON>223, <PERSON><PERSON><PERSON>159, <PERSON><PERSON><PERSON>00000285812, <PERSON><PERSON><PERSON>01342, ENSG00000272141, TTLL10-AS1, TTLL10, TNFRSF18, TNFRSF4, SDF4, B3GALT6, C1QTNF12, ENSG00000260179, UBE2J2, LINC01786, SCNN1D, ACAP3, <PERSON>US<PERSON>1, IN<PERSON>11, <PERSON>NSG00000240731, CPTP, TAS1R3, DVL1, M<PERSON>RA8, AURKAIP1, CCNL2, MRPL20-AS1, MRPL20, MRPL20-DT, ANKRD65, ANKRD65-AS1, TMEM88B, LINC01770, VWA1, ATAD3C, ATAD3B, ENSG00000290916, ATAD3A, TMEM240, SSU72, ENSG00000215014, FNDC10, ENSG00000286989, ENSG00000272106, MIB2, MMP23B, CDK11B, ENSG00000272004, SLC35E2B, CDK11A, ENSG00000290854, NADK, GNB1, GNB1-DT, CALML6, TMEM52, CFAP74, ENSG00000233542, ...]\n", "\n", "[38606 rows x 0 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["b.var\n"]}, {"cell_type": "code", "execution_count": 37, "id": "861737ae", "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n", "import pandas as pd\n", "\n", "a = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed_filtered.h5ad', backed=\"r\")"]}, {"cell_type": "code", "execution_count": 38, "id": "eb3ff75e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_index</th>\n", "      <th>gene_id</th>\n", "      <th>gene_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ENSG00000243485</td>\n", "      <td>ENSG00000243485</td>\n", "      <td>MIR1302-2HG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ENSG00000237613</td>\n", "      <td>ENSG00000237613</td>\n", "      <td>FAM138A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ENSG00000186092</td>\n", "      <td>ENSG00000186092</td>\n", "      <td>OR4F5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ENSG00000284733</td>\n", "      <td>ENSG00000284733</td>\n", "      <td>OR4F29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ENSG00000284662</td>\n", "      <td>ENSG00000284662</td>\n", "      <td>OR4F16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25319</th>\n", "      <td>ENSG00000198886</td>\n", "      <td>ENSG00000198886</td>\n", "      <td>MT-ND4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25320</th>\n", "      <td>ENSG00000198786</td>\n", "      <td>ENSG00000198786</td>\n", "      <td>MT-ND5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25321</th>\n", "      <td>ENSG00000198695</td>\n", "      <td>ENSG00000198695</td>\n", "      <td>MT-ND6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25322</th>\n", "      <td>ENSG00000198727</td>\n", "      <td>ENSG00000198727</td>\n", "      <td>MT-CYB</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25323</th>\n", "      <td>ENSG00000274847</td>\n", "      <td>ENSG00000274847</td>\n", "      <td>MAFIP</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>25324 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                _index          gene_id    gene_name\n", "0      ENSG00000243485  ENSG00000243485  MIR1302-2HG\n", "1      ENSG00000237613  ENSG00000237613      FAM138A\n", "2      ENSG00000186092  ENSG00000186092        OR4F5\n", "3      ENSG00000284733  ENSG00000284733       OR4F29\n", "4      ENSG00000284662  ENSG00000284662       OR4F16\n", "...                ...              ...          ...\n", "25319  ENSG00000198886  ENSG00000198886       MT-ND4\n", "25320  ENSG00000198786  ENSG00000198786       MT-ND5\n", "25321  ENSG00000198695  ENSG00000198695       MT-ND6\n", "25322  ENSG00000198727  ENSG00000198727       MT-CYB\n", "25323  ENSG00000274847  ENSG00000274847        MAFIP\n", "\n", "[25324 rows x 3 columns]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["a.var"]}, {"cell_type": "code", "execution_count": null, "id": "8db214ab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 39, "id": "6f022574", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/jupyter_r_env/lib/python3.10/site-packages/anndata/_core/anndata.py:1758: UserWarning: Variable names are not unique. To make them unique, call `.var_names_make_unique`.\n", "  utils.warn_names_duplicates(\"var\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["原始文件形状: (4534299, 38606)\n", "过滤后文件形状: (4534299, 25324)\n", "\n", "实际被删除的基因数量: 13282\n", "删除比例: 34.40%\n", "\n", "被删除基因的详细信息已保存到: /data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/analysis/deleted_genes_details.csv\n", "\n", "=== 被删除基因分析 ===\n", "基因名与 gene_id 相同的比例: 100.00%\n", "有效 Ensembl ID 格式的比例: 96.62%\n", "\n", "被删除基因中最常见的前缀:\n", "0\n", "ENSG     12833\n", "LINC        63\n", "FAM         30\n", "C           24\n", "ZNF          9\n", "GOLGA        7\n", "SPATA        6\n", "OR           6\n", "CYP          6\n", "TEX          5\n", "Name: count, dtype: int64\n", "\n", "计算被删除基因的稀疏度...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["处理细胞块: 100%|██████████| 10/10 [00:02<00:00,  4.92it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["被删除基因的稀疏度: 0.00%\n", "\n", "分析完成!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import scanpy as sc\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "from scipy.sparse import issparse, csr_matrix\n", "import os\n", "import json\n", "\n", "def analyze_h5ad_changes(file1_path, file2_path, output_dir):\n", "    os.makedirs(output_dir, exist_ok=True)\n", "\n", "    # 读取文件\n", "    adata_original = sc.read_h5ad(file1_path, backed=\"r\")\n", "    adata_filtered = sc.read_h5ad(file2_path, backed=\"r\")\n", "\n", "    print(f\"原始文件形状: {adata_original.shape}\")\n", "    print(f\"过滤后文件形状: {adata_filtered.shape}\")\n", "\n", "    # 获取 gene_id 列进行比较\n", "    original_var = adata_original.var[['gene_id', 'gene_name']].copy()\n", "    filtered_var = adata_filtered.var[['gene_id', 'gene_name']].copy()\n", "\n", "    # 重置索引，使用 gene_id 作为唯一标识\n", "    original_var.reset_index(drop=True, inplace=True)\n", "    filtered_var.reset_index(drop=True, inplace=True)\n", "\n", "    # 找出被删除的基因\n", "    deleted_var = original_var[~original_var['gene_id'].isin(filtered_var['gene_id'])]\n", "    print(f\"\\n实际被删除的基因数量: {len(deleted_var)}\")\n", "    print(f\"删除比例: {len(deleted_var) / len(original_var):.2%}\")\n", "\n", "    # 保存被删除基因的详细信息\n", "    deleted_var.to_csv(f\"{output_dir}/deleted_genes_details.csv\", index=False)\n", "    print(f\"\\n被删除基因的详细信息已保存到: {output_dir}/deleted_genes_details.csv\")\n", "\n", "    # 分析被删除基因的特点\n", "    print(\"\\n=== 被删除基因分析 ===\")\n", "\n", "    # 1. 基因名与 gene_id 相同的比例\n", "    same_name_id = deleted_var['gene_name'] == deleted_var['gene_id']\n", "    print(f\"基因名与 gene_id 相同的比例: {same_name_id.mean():.2%}\")\n", "\n", "    # 2. 是否是有效的 Ensembl ID 格式\n", "    def is_ensg(x):\n", "        return isinstance(x, str) and x.startswith('ENSG') and len(x) > 10\n", "\n", "    is_ensg_vec = deleted_var['gene_id'].apply(is_ensg)\n", "    print(f\"有效 Ensembl ID 格式的比例: {is_ensg_vec.mean():.2%}\")\n", "\n", "    # 3. 基因类型分布（如果有此信息）\n", "    if 'gene_biotype' in deleted_var.columns:\n", "        biotype_counts = deleted_var['gene_biotype'].value_counts()\n", "        print(\"\\n被删除基因的生物类型分布:\")\n", "        print(biotype_counts.head(10))\n", "\n", "        plt.figure(figsize=(12, 6))\n", "        biotype_counts.head(10).plot(kind='bar')\n", "        plt.title('Top 10 Biotypes of Deleted Genes')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"{output_dir}/biotype_distribution.png\")\n", "        plt.close()\n", "\n", "    # 4. 基因名前缀分析\n", "    print(\"\\n被删除基因中最常见的前缀:\")\n", "    gene_prefixes = deleted_var['gene_name'].str.extract(r'^([A-Za-z]+)')[0].value_counts()\n", "    print(gene_prefixes.head(10))\n", "\n", "    # 5. 表达量分析（如果有 mean 列）\n", "    if 'mean' in deleted_var.columns:\n", "        plt.figure(figsize=(10, 6))\n", "        plt.hist(deleted_var['mean'].dropna(), bins=50, alpha=0.7, label='Deleted Genes')\n", "\n", "        # 比较保留基因的表达量\n", "        kept_var = filtered_var\n", "        if 'mean' in kept_var.columns:\n", "            plt.hist(kept_var['mean'].dropna(), bins=50, alpha=0.7, label='Kept Genes')\n", "\n", "        plt.xlabel('Mean Expression')\n", "        plt.ylabel('Frequency')\n", "        plt.title('Expression Distribution: Deleted vs Kept Genes')\n", "        plt.legend()\n", "        plt.savefig(f\"{output_dir}/expression_comparison.png\")\n", "        plt.close()\n", "\n", "    # 6. 稀疏度分析\n", "    print(\"\\n计算被删除基因的稀疏度...\")\n", "    sample_size = min(1000, adata_original.n_obs)\n", "    sampled_cells = np.random.choice(adata_original.n_obs, sample_size, replace=False)\n", "\n", "    # 获取被删除基因的列索引\n", "    original_gene_ids = original_var['gene_id'].tolist()\n", "    deleted_gene_ids = deleted_var['gene_id'].tolist()\n", "    deleted_gene_indices = [original_gene_ids.index(gid) for gid in deleted_gene_ids]\n", "\n", "    # 分块处理以减少内存使用\n", "    chunk_size = 100\n", "    nnz_total = 0\n", "    total_elements = 0\n", "\n", "    for i in tqdm(range(0, len(sampled_cells), chunk_size), desc=\"处理细胞块\"):\n", "        chunk_cells = sampled_cells[i:i + chunk_size]\n", "        chunk_data = adata_original.X[chunk_cells][:, deleted_gene_indices]\n", "\n", "        if issparse(chunk_data):\n", "            nnz_total += chunk_data.nnz\n", "        else:\n", "            nnz_total += np.count_nonzero(chunk_data)\n", "\n", "        total_elements += chunk_data.size\n", "\n", "    sparsity = 1.0 - (nnz_total / total_elements) if total_elements > 0 else 0\n", "    print(f\"被删除基因的稀疏度: {sparsity:.2%}\")\n", "\n", "    # 生成总结报告\n", "    report = {\n", "        \"原始基因数量\": int(len(original_var)),\n", "        \"过滤后基因数量\": int(len(filtered_var)),\n", "        \"删除基因数量\": int(len(deleted_var)),\n", "        \"删除比例\": float(len(deleted_var) / len(original_var)),\n", "        \"基因名与gene_id相同的比例\": float(same_name_id.mean()),\n", "        \"有效Ensembl ID比例\": float(is_ensg_vec.mean()),\n", "        \"被删除基因稀疏度\": float(sparsity)\n", "    }\n", "\n", "    with open(f\"{output_dir}/analysis_report.json\", 'w') as f:\n", "        json.dump(report, f, indent=2)\n", "\n", "    print(\"\\n分析完成!\")\n", "    return report\n", "\n", "# 使用示例\n", "output_dir = \"/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/analysis\"\n", "report = analyze_h5ad_changes(\n", "    \"/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed.h5ad\",\n", "    \"/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed_filtered.h5ad\",\n", "    output_dir\n", ")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a0338b89", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/jupyter_r_env/lib/python3.10/site-packages/anndata/_core/anndata.py:1758: UserWarning: Variable names are not unique. To make them unique, call `.var_names_make_unique`.\n", "  utils.warn_names_duplicates(\"var\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["文件: /data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed.h5ad\n", "矩阵类型: 标准稀疏矩阵\n", "矩阵形状: (4534299, 38606)\n", "总元素数: 175051147194\n", "非零元素数: 29136391388\n", "稀疏度: 83.3555%\n", "非零元素比例: 16.6445%\n", "--------------------------------------------------\n", "文件: /data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed_filtered.h5ad\n", "矩阵类型: 标准稀疏矩阵\n", "矩阵形状: (4534299, 25324)\n", "总元素数: 114826587876\n", "非零元素数: 28168602975\n", "稀疏度: 75.4686%\n", "非零元素比例: 24.5314%\n", "--------------------------------------------------\n", "稀疏度差异: 7.8869%\n"]}], "source": ["import scanpy as sc\n", "import numpy as np\n", "import scipy.sparse as sp\n", "import h5py\n", "\n", "def check_sparsity(file_path):\n", "    # 读取h5ad文件\n", "    adata = sc.read_h5ad(file_path)\n", "    \n", "    # 获取表达矩阵\n", "    X = adata.X\n", "    \n", "    # 检查矩阵类型并计算稀疏性\n", "    if hasattr(X, 'nnz'):  # 标准稀疏矩阵\n", "        total_elements = X.shape[0] * X.shape[1]\n", "        non_zero_elements = X.nnz\n", "        matrix_type = \"标准稀疏矩阵\"\n", "    <PERSON><PERSON> hasattr(X, 'shape') and hasattr(X, 'indices'):  # HDF5支持的稀疏矩阵\n", "        # 对于HDF5稀疏矩阵，我们需要特殊处理\n", "        total_elements = X.shape[0] * X.shape[1]\n", "        # 尝试获取非零元素数量\n", "        try:\n", "            non_zero_elements = len(X.indices)\n", "            matrix_type = \"HDF5稀疏矩阵\"\n", "        except:\n", "            # 如果无法直接获取，可能需要转换为标准稀疏矩阵\n", "            X = X.to_memory()\n", "            non_zero_elements = X.nnz\n", "            matrix_type = \"转换后的稀疏矩阵\"\n", "    else:\n", "        # 尝试转换为numpy数组\n", "        try:\n", "            X_dense = np.array(X)\n", "            total_elements = X_dense.size\n", "            non_zero_elements = np.count_nonzero(X_dense)\n", "            matrix_type = \"密集矩阵\"\n", "        except:\n", "            print(f\"无法处理矩阵类型: {type(X)}\")\n", "            return None\n", "    \n", "    # 计算稀疏度\n", "    sparsity = 1 - (non_zero_elements / total_elements)\n", "    \n", "    # 打印结果\n", "    print(f\"文件: {file_path}\")\n", "    print(f\"矩阵类型: {matrix_type}\")\n", "    print(f\"矩阵形状: {X.shape}\")\n", "    print(f\"总元素数: {total_elements}\")\n", "    print(f\"非零元素数: {non_zero_elements}\")\n", "    print(f\"稀疏度: {sparsity:.4%}\")\n", "    print(f\"非零元素比例: {(1-sparsity):.4%}\")\n", "    print(\"-\" * 50)\n", "    \n", "    return sparsity\n", "\n", "# 检查两个文件\n", "file1 = \"/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed.h5ad\"\n", "file2 = \"/data/ioz_whr_wsx/datasets/VCC/X_atlas/index_transfered_and_deleted/HEK293T_filtered_dual_guide_cells_ensembl_indexed_filtered.h5ad\"\n", "\n", "try:\n", "    sparsity1 = check_sparsity(file1)\n", "    sparsity2 = check_sparsity(file2)\n", "    \n", "    if sparsity1 is not None and sparsity2 is not None:\n", "        print(f\"稀疏度差异: {abs(sparsity1 - sparsity2):.4%}\")\n", "except Exception as e:\n", "    print(f\"处理文件时出错: {e}\")\n", "    print(\"尝试使用备用方法...\")\n", "    \n", "    # 备用方法：直接检查HDF5文件\n", "    def check_h5_sparsity(file_path):\n", "        try:\n", "            with h5py.File(file_path, 'r') as f:\n", "                if 'X' in f:\n", "                    X = f['X']\n", "                    shape = X.attrs['shape']\n", "                    total_elements = shape[0] * shape[1]\n", "                    \n", "                    # 尝试获取非零元素\n", "                    if 'data' in X:\n", "                        non_zero_elements = len(X['data'])\n", "                    else:\n", "                        # 如果不能直接获取，可能需要估算\n", "                        print(\"无法直接获取非零元素数量\")\n", "                        return None\n", "                    \n", "                    sparsity = 1 - (non_zero_elements / total_elements)\n", "                    \n", "                    print(f\"文件: {file_path}\")\n", "                    print(f\"矩阵形状: {shape}\")\n", "                    print(f\"总元素数: {total_elements}\")\n", "                    print(f\"非零元素数: {non_zero_elements}\")\n", "                    print(f\"稀疏度: {sparsity:.4%}\")\n", "                    print(\"-\" * 50)\n", "                    \n", "                    return sparsity\n", "                else:\n", "                    print(f\"文件中未找到X矩阵: {file_path}\")\n", "                    return None\n", "        except Exception as e:\n", "            print(f\"读取HDF5文件时出错: {e}\")\n", "            return None\n", "    \n", "    sparsity1 = check_h5_sparsity(file1)\n", "    sparsity2 = check_h5_sparsity(file2)"]}, {"cell_type": "code", "execution_count": 13, "id": "69a581b9", "metadata": {}, "outputs": [{"data": {"text/plain": ["CSRDataset: backend hdf5, shape (4534299, 25324), data_dtype float32"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["a.X\n"]}, {"cell_type": "code", "execution_count": 3, "id": "8084f51f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['orig.ident', 'nCount_RNA', 'nFeature_RNA', 'sample', 'cell_type',\n", "       'pathway', 'percent.mito', 'sample_ID', 'Batch_info', 'bc1_well',\n", "       'bc2_well', 'bc3_well', 'guide', 'gene', 'mixscale_score'],\n", "      dtype='object')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["a.obs.columns"]}, {"cell_type": "code", "execution_count": 4, "id": "0c2888ed", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>vst.mean</th>\n", "      <th>vst.variance</th>\n", "      <th>vst.variance.expected</th>\n", "      <th>vst.variance.standardized</th>\n", "      <th>vst.variable</th>\n", "      <th>_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>TSPAN6</th>\n", "      <td>TSPAN6</td>\n", "      <td>0.024910</td>\n", "      <td>0.026516</td>\n", "      <td>0.029564</td>\n", "      <td>0.896919</td>\n", "      <td>False</td>\n", "      <td>TSPAN6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TNMD</th>\n", "      <td>TNMD</td>\n", "      <td>0.000497</td>\n", "      <td>0.000538</td>\n", "      <td>0.000515</td>\n", "      <td>1.043887</td>\n", "      <td>False</td>\n", "      <td>TNMD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DPM1</th>\n", "      <td>DPM1</td>\n", "      <td>0.395474</td>\n", "      <td>0.590072</td>\n", "      <td>0.701894</td>\n", "      <td>0.840684</td>\n", "      <td>False</td>\n", "      <td>DPM1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SCYL3</th>\n", "      <td>SCYL3</td>\n", "      <td>0.218052</td>\n", "      <td>0.282625</td>\n", "      <td>0.330508</td>\n", "      <td>0.855123</td>\n", "      <td>False</td>\n", "      <td>SCYL3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>C1orf112</th>\n", "      <td>C1orf112</td>\n", "      <td>0.479967</td>\n", "      <td>0.879581</td>\n", "      <td>0.908653</td>\n", "      <td>0.968006</td>\n", "      <td>False</td>\n", "      <td>C1orf112</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AC068620.2</th>\n", "      <td>AC068620.2</td>\n", "      <td>0.000004</td>\n", "      <td>0.000004</td>\n", "      <td>0.000004</td>\n", "      <td>0.999399</td>\n", "      <td>False</td>\n", "      <td>AC068620.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AC006023.1</th>\n", "      <td>AC006023.1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>False</td>\n", "      <td>AC006023.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AL356121.2</th>\n", "      <td>AL356121.2</td>\n", "      <td>0.000004</td>\n", "      <td>0.000004</td>\n", "      <td>0.000004</td>\n", "      <td>0.999399</td>\n", "      <td>False</td>\n", "      <td>AL356121.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TAF9BP2</th>\n", "      <td>TAF9BP2</td>\n", "      <td>0.000004</td>\n", "      <td>0.000004</td>\n", "      <td>0.000004</td>\n", "      <td>0.999399</td>\n", "      <td>False</td>\n", "      <td>TAF9BP2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AC016065.2</th>\n", "      <td>AC016065.2</td>\n", "      <td>0.000012</td>\n", "      <td>0.000012</td>\n", "      <td>0.000012</td>\n", "      <td>0.986816</td>\n", "      <td>False</td>\n", "      <td>AC016065.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>33525 rows × 7 columns</p>\n", "</div>"], "text/plain": ["             gene_name  vst.mean  vst.variance  vst.variance.expected  \\\n", "TSPAN6          TSPAN6  0.024910      0.026516               0.029564   \n", "TNMD              TNMD  0.000497      0.000538               0.000515   \n", "DPM1              DPM1  0.395474      0.590072               0.701894   \n", "SCYL3            SCYL3  0.218052      0.282625               0.330508   \n", "C1orf112      C1orf112  0.479967      0.879581               0.908653   \n", "...                ...       ...           ...                    ...   \n", "AC068620.2  AC068620.2  0.000004      0.000004               0.000004   \n", "AC006023.1  AC006023.1  0.000000      0.000000               0.000000   \n", "AL356121.2  AL356121.2  0.000004      0.000004               0.000004   \n", "TAF9BP2        TAF9BP2  0.000004      0.000004               0.000004   \n", "AC016065.2  AC016065.2  0.000012      0.000012               0.000012   \n", "\n", "            vst.variance.standardized  vst.variable      _index  \n", "TSPAN6                       0.896919         False      TSPAN6  \n", "TNMD                         1.043887         False        TNMD  \n", "DPM1                         0.840684         False        DPM1  \n", "SCYL3                        0.855123         False       SCYL3  \n", "C1orf112                     0.968006         False    C1orf112  \n", "...                               ...           ...         ...  \n", "AC068620.2                   0.999399         False  AC068620.2  \n", "AC006023.1                   0.000000         False  AC006023.1  \n", "AL356121.2                   0.999399         False  AL356121.2  \n", "TAF9BP2                      0.999399         False     TAF9BP2  \n", "AC016065.2                   0.986816         False  AC016065.2  \n", "\n", "[33525 rows x 7 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["a.var"]}, {"cell_type": "code", "execution_count": 5, "id": "f6a8915b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of entries in a.var starting with 'ENSG': 0\n"]}], "source": ["ensg_count = a.var.index.str.startswith('ENSG').sum()\n", "print(f\"Number of entries in a.var starting with 'ENSG': {ensg_count}\")"]}, {"cell_type": "code", "execution_count": 1, "id": "a949eeec", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'sc' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m b\u001b[38;5;241m=\u001b[39m\u001b[43msc\u001b[49m\u001b[38;5;241m.\u001b[39mread_h5ad(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m/data/ioz_whr_wsx/datasets/VCC/aligned18000datas/K562_gwps_raw_bulk_01.aligned.h5ad\u001b[39m\u001b[38;5;124m'\u001b[39m, backed\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'sc' is not defined"]}], "source": ["b=sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/aligned18000datas/K562_gwps_raw_bulk_01.aligned.h5ad', backed=\"r\")"]}, {"cell_type": "code", "execution_count": 7, "id": "6ca606ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of entries in a.var starting with 'ENSG': 12962\n"]}], "source": ["ensg_count = b.var.index.str.startswith('ENSG').sum()\n", "print(f\"Number of entries in a.var starting with 'ENSG': {ensg_count}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "bfca8426", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>mt</th>\n", "      <th>n_cells_by_counts</th>\n", "      <th>mean_counts</th>\n", "      <th>pct_dropout_by_counts</th>\n", "      <th>total_counts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>DDX11L2</th>\n", "      <td>False</td>\n", "      <td>2719</td>\n", "      <td>2.777434e-04</td>\n", "      <td>99.972499</td>\n", "      <td>2746.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MIR1302-2HG</th>\n", "      <td>False</td>\n", "      <td>2620</td>\n", "      <td>2.673255e-04</td>\n", "      <td>99.973500</td>\n", "      <td>2643.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FAM138A</th>\n", "      <td>False</td>\n", "      <td>3</td>\n", "      <td>3.034341e-07</td>\n", "      <td>99.999970</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000290826</th>\n", "      <td>False</td>\n", "      <td>764</td>\n", "      <td>8.698444e-05</td>\n", "      <td>99.992273</td>\n", "      <td>860.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OR4F5</th>\n", "      <td>False</td>\n", "      <td>263</td>\n", "      <td>2.700564e-05</td>\n", "      <td>99.997340</td>\n", "      <td>267.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000277836</th>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>100.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278633</th>\n", "      <td>False</td>\n", "      <td>106</td>\n", "      <td>1.092363e-05</td>\n", "      <td>99.998928</td>\n", "      <td>108.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000276017</th>\n", "      <td>False</td>\n", "      <td>663</td>\n", "      <td>6.756467e-05</td>\n", "      <td>99.993294</td>\n", "      <td>668.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278817</th>\n", "      <td>False</td>\n", "      <td>35369</td>\n", "      <td>3.616227e-03</td>\n", "      <td>99.642261</td>\n", "      <td>35753.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000277196</th>\n", "      <td>False</td>\n", "      <td>48372</td>\n", "      <td>5.025274e-03</td>\n", "      <td>99.510743</td>\n", "      <td>49684.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>38606 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                    mt  n_cells_by_counts   mean_counts  \\\n", "DDX11L2          False               2719  2.777434e-04   \n", "MIR1302-2HG      False               2620  2.673255e-04   \n", "FAM138A          False                  3  3.034341e-07   \n", "ENSG00000290826  False                764  8.698444e-05   \n", "OR4F5            False                263  2.700564e-05   \n", "...                ...                ...           ...   \n", "ENSG00000277836  False                  0  0.000000e+00   \n", "ENSG00000278633  False                106  1.092363e-05   \n", "ENSG00000276017  False                663  6.756467e-05   \n", "ENSG00000278817  False              35369  3.616227e-03   \n", "ENSG00000277196  False              48372  5.025274e-03   \n", "\n", "                 pct_dropout_by_counts  total_counts  \n", "DDX11L2                      99.972499        2746.0  \n", "MIR1302-2HG                  99.973500        2643.0  \n", "FAM138A                      99.999970           3.0  \n", "ENSG00000290826              99.992273         860.0  \n", "OR4F5                        99.997340         267.0  \n", "...                                ...           ...  \n", "ENSG00000277836             100.000000           0.0  \n", "ENSG00000278633              99.998928         108.0  \n", "ENSG00000276017              99.993294         668.0  \n", "ENSG00000278817              99.642261       35753.0  \n", "ENSG00000277196              99.510743       49684.0  \n", "\n", "[38606 rows x 5 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["b.var"]}, {"cell_type": "code", "execution_count": 23, "id": "0299b81e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DDX11L2、MIR1302-2HG等内容是a.var的行索引。 Index(['DDX11L2', 'MIR1302-2HG', 'FAM138A', 'ENSG00000290826', 'OR4F5',\n", "       'ENSG00000238009', 'ENSG00000239945', 'ENSG00000239906',\n", "       'ENSG00000241860', 'ENSG00000241599',\n", "       ...\n", "       'ENSG00000275249', 'ENSG00000274792', 'ENSG00000274175',\n", "       'ENSG00000275869', 'ENSG00000273554', 'ENSG00000277836',\n", "       'ENSG00000278633', 'ENSG00000276017', 'ENSG00000278817',\n", "       'ENSG00000277196'],\n", "      dtype='object', length=38606)\n"]}], "source": ["import scanpy as sc\n", "import pandas as pd\n", "\n", "a = sc.read_h5ad('/data/ioz_cbmi/HEK293T_filtered_dual_guide_cells.h5ad', backed=\"r\")\n", "a.var\n", "print(\"DDX11L2、MIR1302-2HG等内容是a.var的行索引。\", a.var.index)\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 25, "id": "b4d67bad", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['DDX11L2', 'MIR1302-2HG', 'FAM138A', 'ENSG00000290826', 'OR4F5',\n", "       'ENSG00000238009', 'ENSG00000239945', 'ENSG00000239906',\n", "       'ENSG00000241860', 'ENSG00000241599',\n", "       ...\n", "       'ENSG00000275249', 'ENSG00000274792', 'ENSG00000274175',\n", "       'ENSG00000275869', 'ENSG00000273554', 'ENSG00000277836',\n", "       'ENSG00000278633', 'ENSG00000276017', 'ENSG00000278817',\n", "       'ENSG00000277196'],\n", "      dtype='object', length=38606)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["a.var_names"]}, {"cell_type": "code", "execution_count": 2, "id": "b03d9ee8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sample</th>\n", "      <th>num_features</th>\n", "      <th>guide_target</th>\n", "      <th>gene_target</th>\n", "      <th>n_genes_by_counts</th>\n", "      <th>total_counts</th>\n", "      <th>total_counts_mt</th>\n", "      <th>pct_counts_mt</th>\n", "      <th>pass_guide_filter</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCAAAGAAGTAGC-HEK293T_Batch1</th>\n", "      <td>HEK293T_Batch1</td>\n", "      <td>2</td>\n", "      <td>FAM180A_P1P2-1|FAM180A_P1P2-2</td>\n", "      <td>FAM180A</td>\n", "      <td>6856</td>\n", "      <td>24243.0</td>\n", "      <td>647.0</td>\n", "      <td>2.668812</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAAAGAGTACGG-HEK293T_Batch1</th>\n", "      <td>HEK293T_Batch1</td>\n", "      <td>2</td>\n", "      <td>non-targeting_03016|non-targeting_03214</td>\n", "      <td>Non-Targeting</td>\n", "      <td>6636</td>\n", "      <td>20847.0</td>\n", "      <td>886.0</td>\n", "      <td>4.250012</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAAAGAGTGGGC-HEK293T_Batch1</th>\n", "      <td>HEK293T_Batch1</td>\n", "      <td>2</td>\n", "      <td>PCDHGC4_P1P2-1|PCDHGC4_P1P2-2</td>\n", "      <td>PCDHGC4</td>\n", "      <td>5353</td>\n", "      <td>14035.0</td>\n", "      <td>610.0</td>\n", "      <td>4.346277</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAAAGAGTGTAA-HEK293T_Batch1</th>\n", "      <td>HEK293T_Batch1</td>\n", "      <td>2</td>\n", "      <td>USP30_P1P2-1|USP30_P1P2-2</td>\n", "      <td>USP30</td>\n", "      <td>6358</td>\n", "      <td>20939.0</td>\n", "      <td>589.0</td>\n", "      <td>2.812933</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAAAGATACGGG-HEK293T_Batch1</th>\n", "      <td>HEK293T_Batch1</td>\n", "      <td>2</td>\n", "      <td>KLRD1_P1P2-1|KLRD1_P1P2-2</td>\n", "      <td>KLRD1</td>\n", "      <td>5527</td>\n", "      <td>14083.0</td>\n", "      <td>476.0</td>\n", "      <td>3.379961</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTCCGTGGGATGG-HEK293T_Batch223</th>\n", "      <td>HEK293T_Batch223</td>\n", "      <td>2</td>\n", "      <td>non-targeting_00183|non-targeting_01885</td>\n", "      <td>Non-Targeting</td>\n", "      <td>8112</td>\n", "      <td>29296.0</td>\n", "      <td>522.0</td>\n", "      <td>1.781813</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTCTTCAACAGCC-HEK293T_Batch223</th>\n", "      <td>HEK293T_Batch223</td>\n", "      <td>2</td>\n", "      <td>AKAP13_P1P2-1|AKAP13_P1P2-2</td>\n", "      <td>AKAP13</td>\n", "      <td>10568</td>\n", "      <td>88551.0</td>\n", "      <td>3035.0</td>\n", "      <td>3.427403</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTCTTCAGTACTG-HEK293T_Batch223</th>\n", "      <td>HEK293T_Batch223</td>\n", "      <td>2</td>\n", "      <td>TTF2_P1P2-1|TTF2_P1P2-2</td>\n", "      <td>TTF2</td>\n", "      <td>10735</td>\n", "      <td>81286.0</td>\n", "      <td>1808.0</td>\n", "      <td>2.224245</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTCTTCCGCCGTT-HEK293T_Batch223</th>\n", "      <td>HEK293T_Batch223</td>\n", "      <td>2</td>\n", "      <td>MMP7_P1P2-1|MMP7_P1P2-2</td>\n", "      <td>MMP7</td>\n", "      <td>9248</td>\n", "      <td>40733.0</td>\n", "      <td>1767.0</td>\n", "      <td>4.338006</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTGCAGGCTTGCG-HEK293T_Batch223</th>\n", "      <td>HEK293T_Batch223</td>\n", "      <td>2</td>\n", "      <td>AAK1_P1P2-1|AAK1_P1P2-2</td>\n", "      <td>AAK1</td>\n", "      <td>8682</td>\n", "      <td>36045.0</td>\n", "      <td>945.0</td>\n", "      <td>2.621723</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4534299 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                                             sample  num_features  \\\n", "AAACCAAAGAAGTAGC-HEK293T_Batch1      HEK293T_Batch1             2   \n", "AAACCAAAGAGTACGG-HEK293T_Batch1      HEK293T_Batch1             2   \n", "AAACCAAAGAGTGGGC-HEK293T_Batch1      HEK293T_Batch1             2   \n", "AAACCAAAGAGTGTAA-HEK293T_Batch1      HEK293T_Batch1             2   \n", "AAACCAAAGATACGGG-HEK293T_Batch1      HEK293T_Batch1             2   \n", "...                                             ...           ...   \n", "GTTGTCCGTGGGATGG-HEK293T_Batch223  HEK293T_Batch223             2   \n", "GTTGTCTTCAACAGCC-HEK293T_Batch223  HEK293T_Batch223             2   \n", "GTTGTCTTCAGTACTG-HEK293T_Batch223  HEK293T_Batch223             2   \n", "GTTGTCTTCCGCCGTT-HEK293T_Batch223  HEK293T_Batch223             2   \n", "GTTGTGCAGGCTTGCG-HEK293T_Batch223  HEK293T_Batch223             2   \n", "\n", "                                                              guide_target  \\\n", "AAACCAAAGAAGTAGC-HEK293T_Batch1              FAM180A_P1P2-1|FAM180A_P1P2-2   \n", "AAACCAAAGAGTACGG-HEK293T_Batch1    non-targeting_03016|non-targeting_03214   \n", "AAACCAAAGAGTGGGC-HEK293T_Batch1              PCDHGC4_P1P2-1|PCDHGC4_P1P2-2   \n", "AAACCAAAGAGTGTAA-HEK293T_Batch1                  USP30_P1P2-1|USP30_P1P2-2   \n", "AAACCAAAGATACGGG-HEK293T_Batch1                  KLRD1_P1P2-1|KLRD1_P1P2-2   \n", "...                                                                    ...   \n", "GTTGTCCGTGGGATGG-HEK293T_Batch223  non-targeting_00183|non-targeting_01885   \n", "GTTGTCTTCAACAGCC-HEK293T_Batch223              AKAP13_P1P2-1|AKAP13_P1P2-2   \n", "GTTGTCTTCAGTACTG-HEK293T_Batch223                  TTF2_P1P2-1|TTF2_P1P2-2   \n", "GTTGTCTTCCGCCGTT-HEK293T_Batch223                  MMP7_P1P2-1|MMP7_P1P2-2   \n", "GTTGTGCAGGCTTGCG-HEK293T_Batch223                  AAK1_P1P2-1|AAK1_P1P2-2   \n", "\n", "                                     gene_target  n_genes_by_counts  \\\n", "AAACCAAAGAAGTAGC-HEK293T_Batch1          FAM180A               6856   \n", "AAACCAAAGAGTACGG-HEK293T_Batch1    Non-Targeting               6636   \n", "AAACCAAAGAGTGGGC-HEK293T_Batch1          PCDHGC4               5353   \n", "AAACCAAAGAGTGTAA-HEK293T_Batch1            USP30               6358   \n", "AAACCAAAGATACGGG-HEK293T_Batch1            KLRD1               5527   \n", "...                                          ...                ...   \n", "GTTGTCCGTGGGATGG-HEK293T_Batch223  Non-Targeting               8112   \n", "GTTGTCTTCAACAGCC-HEK293T_Batch223         AKAP13              10568   \n", "GTTGTCTTCAGTACTG-HEK293T_Batch223           TTF2              10735   \n", "GTTGTCTTCCGCCGTT-HEK293T_Batch223           MMP7               9248   \n", "GTTGTGCAGGCTTGCG-HEK293T_Batch223           AAK1               8682   \n", "\n", "                                   total_counts  total_counts_mt  \\\n", "AAACCAAAGAAGTAGC-HEK293T_Batch1         24243.0            647.0   \n", "AAACCAAAGAGTACGG-HEK293T_Batch1         20847.0            886.0   \n", "AAACCAAAGAGTGGGC-HEK293T_Batch1         14035.0            610.0   \n", "AAACCAAAGAGTGTAA-HEK293T_Batch1         20939.0            589.0   \n", "AAACCAAAGATACGGG-HEK293T_Batch1         14083.0            476.0   \n", "...                                         ...              ...   \n", "GTTGTCCGTGGGATGG-HEK293T_Batch223       29296.0            522.0   \n", "GTTGTCTTCAACAGCC-HEK293T_Batch223       88551.0           3035.0   \n", "GTTGTCTTCAGTACTG-HEK293T_Batch223       81286.0           1808.0   \n", "GTTGTCTTCCGCCGTT-HEK293T_Batch223       40733.0           1767.0   \n", "GTTGTGCAGGCTTGCG-HEK293T_Batch223       36045.0            945.0   \n", "\n", "                                   pct_counts_mt  pass_guide_filter  \n", "AAACCAAAGAAGTAGC-HEK293T_Batch1         2.668812               True  \n", "AAACCAAAGAGTACGG-HEK293T_Batch1         4.250012               True  \n", "AAACCAAAGAGTGGGC-HEK293T_Batch1         4.346277               True  \n", "AAACCAAAGAGTGTAA-HEK293T_Batch1         2.812933               True  \n", "AAACCAAAGATACGGG-HEK293T_Batch1         3.379961               True  \n", "...                                          ...                ...  \n", "GTTGTCCGTGGGATGG-HEK293T_Batch223       1.781813               True  \n", "GTTGTCTTCAACAGCC-HEK293T_Batch223       3.427403               True  \n", "GTTGTCTTCAGTACTG-HEK293T_Batch223       2.224245               True  \n", "GTTGTCTTCCGCCGTT-HEK293T_Batch223       4.338006               True  \n", "GTTGTGCAGGCTTGCG-HEK293T_Batch223       2.621723               True  \n", "\n", "[4534299 rows x 9 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["a.obs\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b2f82cc0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>mt</th>\n", "      <th>n_cells_by_counts</th>\n", "      <th>mean_counts</th>\n", "      <th>pct_dropout_by_counts</th>\n", "      <th>total_counts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>DDX11L2</th>\n", "      <td>False</td>\n", "      <td>2719</td>\n", "      <td>2.777434e-04</td>\n", "      <td>99.972499</td>\n", "      <td>2746.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MIR1302-2HG</th>\n", "      <td>False</td>\n", "      <td>2620</td>\n", "      <td>2.673255e-04</td>\n", "      <td>99.973500</td>\n", "      <td>2643.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FAM138A</th>\n", "      <td>False</td>\n", "      <td>3</td>\n", "      <td>3.034341e-07</td>\n", "      <td>99.999970</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000290826</th>\n", "      <td>False</td>\n", "      <td>764</td>\n", "      <td>8.698444e-05</td>\n", "      <td>99.992273</td>\n", "      <td>860.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OR4F5</th>\n", "      <td>False</td>\n", "      <td>263</td>\n", "      <td>2.700564e-05</td>\n", "      <td>99.997340</td>\n", "      <td>267.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000277836</th>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>100.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278633</th>\n", "      <td>False</td>\n", "      <td>106</td>\n", "      <td>1.092363e-05</td>\n", "      <td>99.998928</td>\n", "      <td>108.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000276017</th>\n", "      <td>False</td>\n", "      <td>663</td>\n", "      <td>6.756467e-05</td>\n", "      <td>99.993294</td>\n", "      <td>668.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278817</th>\n", "      <td>False</td>\n", "      <td>35369</td>\n", "      <td>3.616227e-03</td>\n", "      <td>99.642261</td>\n", "      <td>35753.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000277196</th>\n", "      <td>False</td>\n", "      <td>48372</td>\n", "      <td>5.025274e-03</td>\n", "      <td>99.510743</td>\n", "      <td>49684.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>38606 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                    mt  n_cells_by_counts   mean_counts  \\\n", "DDX11L2          False               2719  2.777434e-04   \n", "MIR1302-2HG      False               2620  2.673255e-04   \n", "FAM138A          False                  3  3.034341e-07   \n", "ENSG00000290826  False                764  8.698444e-05   \n", "OR4F5            False                263  2.700564e-05   \n", "...                ...                ...           ...   \n", "ENSG00000277836  False                  0  0.000000e+00   \n", "ENSG00000278633  False                106  1.092363e-05   \n", "ENSG00000276017  False                663  6.756467e-05   \n", "ENSG00000278817  False              35369  3.616227e-03   \n", "ENSG00000277196  False              48372  5.025274e-03   \n", "\n", "                 pct_dropout_by_counts  total_counts  \n", "DDX11L2                      99.972499        2746.0  \n", "MIR1302-2HG                  99.973500        2643.0  \n", "FAM138A                      99.999970           3.0  \n", "ENSG00000290826              99.992273         860.0  \n", "OR4F5                        99.997340         267.0  \n", "...                                ...           ...  \n", "ENSG00000277836             100.000000           0.0  \n", "ENSG00000278633              99.998928         108.0  \n", "ENSG00000276017              99.993294         668.0  \n", "ENSG00000278817              99.642261       35753.0  \n", "ENSG00000277196              99.510743       49684.0  \n", "\n", "[38606 rows x 5 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["b = sc.read_h5ad('/data/ioz_cbmi/HCT116_filtered_dual_guide_cells.h5ad', backed=\"r\")\n", "b.var\n"]}, {"cell_type": "code", "execution_count": 4, "id": "3a09c962", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sample</th>\n", "      <th>num_features</th>\n", "      <th>guide_target</th>\n", "      <th>gene_target</th>\n", "      <th>n_genes_by_counts</th>\n", "      <th>total_counts</th>\n", "      <th>total_counts_mt</th>\n", "      <th>pct_counts_mt</th>\n", "      <th>pass_guide_filter</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCAAAGACATGTT-HCT116_Batch1</th>\n", "      <td>HCT116_Batch1</td>\n", "      <td>2</td>\n", "      <td>ST14_P1P2-1|ST14_P1P2-2</td>\n", "      <td>ST14</td>\n", "      <td>4883</td>\n", "      <td>19136.0</td>\n", "      <td>1179.0</td>\n", "      <td>6.161162</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAAAGACCCAAC-HCT116_Batch1</th>\n", "      <td>HCT116_Batch1</td>\n", "      <td>2</td>\n", "      <td>SIGLEC5_P1P2-1|SIGLEC5_P1P2-2</td>\n", "      <td>SIGLEC5</td>\n", "      <td>8130</td>\n", "      <td>47916.0</td>\n", "      <td>1562.0</td>\n", "      <td>3.259871</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAAAGAGGTACG-HCT116_Batch1</th>\n", "      <td>HCT116_Batch1</td>\n", "      <td>2</td>\n", "      <td>VSNL1_P1P2-1|VSNL1_P1P2-2</td>\n", "      <td>VSNL1</td>\n", "      <td>6531</td>\n", "      <td>28435.0</td>\n", "      <td>1042.0</td>\n", "      <td>3.664498</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAAAGCGATTAT-HCT116_Batch1</th>\n", "      <td>HCT116_Batch1</td>\n", "      <td>2</td>\n", "      <td>KCNK7_P1P2-1|KCNK7_P1P2-2</td>\n", "      <td>KCNK7</td>\n", "      <td>5931</td>\n", "      <td>26080.0</td>\n", "      <td>1087.0</td>\n", "      <td>4.167945</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCAAAGGCTTAAT-HCT116_Batch1</th>\n", "      <td>HCT116_Batch1</td>\n", "      <td>2</td>\n", "      <td>APOA4_P1P2-1|APOA4_P1P2-2</td>\n", "      <td>APOA4</td>\n", "      <td>7157</td>\n", "      <td>38366.0</td>\n", "      <td>955.0</td>\n", "      <td>2.489183</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTGCAGGGAATCG-HCT116_Batch109</th>\n", "      <td>HCT116_Batch109</td>\n", "      <td>2</td>\n", "      <td>ACP6_P1P2-1|ACP6_P1P2-2</td>\n", "      <td>ACP6</td>\n", "      <td>5107</td>\n", "      <td>17882.0</td>\n", "      <td>767.0</td>\n", "      <td>4.289229</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTGGGTCACGCGG-HCT116_Batch109</th>\n", "      <td>HCT116_Batch109</td>\n", "      <td>2</td>\n", "      <td>RNF39_P1P2-1|RNF39_P1P2-2</td>\n", "      <td>RNF39</td>\n", "      <td>3643</td>\n", "      <td>8155.0</td>\n", "      <td>664.0</td>\n", "      <td>8.142244</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTGGGTCTGACTC-HCT116_Batch109</th>\n", "      <td>HCT116_Batch109</td>\n", "      <td>2</td>\n", "      <td>AFP_P2-1|AFP_P2-2</td>\n", "      <td>AFP</td>\n", "      <td>2831</td>\n", "      <td>6334.0</td>\n", "      <td>1553.0</td>\n", "      <td>24.518473</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTGGGTCTTGATT-HCT116_Batch109</th>\n", "      <td>HCT116_Batch109</td>\n", "      <td>2</td>\n", "      <td>METTL9_P1P2-1|METTL9_P1P2-2</td>\n", "      <td>METTL9</td>\n", "      <td>3868</td>\n", "      <td>8766.0</td>\n", "      <td>868.0</td>\n", "      <td>9.901894</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGTGGGTGTAGTTA-HCT116_Batch109</th>\n", "      <td>HCT116_Batch109</td>\n", "      <td>2</td>\n", "      <td>SLC5A10_P1P2-1|SLC5A10_P1P2-2</td>\n", "      <td>SLC5A10</td>\n", "      <td>4095</td>\n", "      <td>9686.0</td>\n", "      <td>738.0</td>\n", "      <td>7.619245</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3409169 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                                           sample  num_features  \\\n", "AAACCAAAGACATGTT-HCT116_Batch1      HCT116_Batch1             2   \n", "AAACCAAAGACCCAAC-HCT116_Batch1      HCT116_Batch1             2   \n", "AAACCAAAGAGGTACG-HCT116_Batch1      HCT116_Batch1             2   \n", "AAACCAAAGCGATTAT-HCT116_Batch1      HCT116_Batch1             2   \n", "AAACCAAAGGCTTAAT-HCT116_Batch1      HCT116_Batch1             2   \n", "...                                           ...           ...   \n", "GTTGTGCAGGGAATCG-HCT116_Batch109  HCT116_Batch109             2   \n", "GTTGTGGGTCACGCGG-HCT116_Batch109  HCT116_Batch109             2   \n", "GTTGTGGGTCTGACTC-HCT116_Batch109  HCT116_Batch109             2   \n", "GTTGTGGGTCTTGATT-HCT116_Batch109  HCT116_Batch109             2   \n", "GTTGTGGGTGTAGTTA-HCT116_Batch109  HCT116_Batch109             2   \n", "\n", "                                                   guide_target gene_target  \\\n", "AAACCAAAGACATGTT-HCT116_Batch1          ST14_P1P2-1|ST14_P1P2-2        ST14   \n", "AAACCAAAGACCCAAC-HCT116_Batch1    SIGLEC5_P1P2-1|SIGLEC5_P1P2-2     SIGLEC5   \n", "AAACCAAAGAGGTACG-HCT116_Batch1        VSNL1_P1P2-1|VSNL1_P1P2-2       VSNL1   \n", "AAACCAAAGCGATTAT-HCT116_Batch1        KCNK7_P1P2-1|KCNK7_P1P2-2       KCNK7   \n", "AAACCAAAGGCTTAAT-HCT116_Batch1        APOA4_P1P2-1|APOA4_P1P2-2       APOA4   \n", "...                                                         ...         ...   \n", "GTTGTGCAGGGAATCG-HCT116_Batch109        ACP6_P1P2-1|ACP6_P1P2-2        ACP6   \n", "GTTGTGGGTCACGCGG-HCT116_Batch109      RNF39_P1P2-1|RNF39_P1P2-2       RNF39   \n", "GTTGTGGGTCTGACTC-HCT116_Batch109              AFP_P2-1|AFP_P2-2         AFP   \n", "GTTGTGGGTCTTGATT-HCT116_Batch109    METTL9_P1P2-1|METTL9_P1P2-2      METTL9   \n", "GTTGTGGGTGTAGTTA-HCT116_Batch109  SLC5A10_P1P2-1|SLC5A10_P1P2-2     SLC5A10   \n", "\n", "                                  n_genes_by_counts  total_counts  \\\n", "AAACCAAAGACATGTT-HCT116_Batch1                 4883       19136.0   \n", "AAACCAAAGACCCAAC-HCT116_Batch1                 8130       47916.0   \n", "AAACCAAAGAGGTACG-HCT116_Batch1                 6531       28435.0   \n", "AAACCAAAGCGATTAT-HCT116_Batch1                 5931       26080.0   \n", "AAACCAAAGGCTTAAT-HCT116_Batch1                 7157       38366.0   \n", "...                                             ...           ...   \n", "GTTGTGCAGGGAATCG-HCT116_Batch109               5107       17882.0   \n", "GTTGTGGGTCACGCGG-HCT116_Batch109               3643        8155.0   \n", "GTTGTGGGTCTGACTC-HCT116_Batch109               2831        6334.0   \n", "GTTGTGGGTCTTGATT-HCT116_Batch109               3868        8766.0   \n", "GTTGTGGGTGTAGTTA-HCT116_Batch109               4095        9686.0   \n", "\n", "                                  total_counts_mt  pct_counts_mt  \\\n", "AAACCAAAGACATGTT-HCT116_Batch1             1179.0       6.161162   \n", "AAACCAAAGACCCAAC-HCT116_Batch1             1562.0       3.259871   \n", "AAACCAAAGAGGTACG-HCT116_Batch1             1042.0       3.664498   \n", "AAACCAAAGCGATTAT-HCT116_Batch1             1087.0       4.167945   \n", "AAACCAAAGGCTTAAT-HCT116_Batch1              955.0       2.489183   \n", "...                                           ...            ...   \n", "GTTGTGCAGGGAATCG-HCT116_Batch109            767.0       4.289229   \n", "GTTGTGGGTCACGCGG-HCT116_Batch109            664.0       8.142244   \n", "GTTGTGGGTCTGACTC-HCT116_Batch109           1553.0      24.518473   \n", "GTTGTGGGTCTTGATT-HCT116_Batch109            868.0       9.901894   \n", "GTTGTGGGTGTAGTTA-HCT116_Batch109            738.0       7.619245   \n", "\n", "                                  pass_guide_filter  \n", "AAACCAAAGACATGTT-HCT116_Batch1                 True  \n", "AAACCAAAGACCCAAC-HCT116_Batch1                 True  \n", "AAACCAAAGAGGTACG-HCT116_Batch1                 True  \n", "AAACCAAAGCGATTAT-HCT116_Batch1                 True  \n", "AAACCAAAGGCTTAAT-HCT116_Batch1                 True  \n", "...                                             ...  \n", "GTTGTGCAGGGAATCG-HCT116_Batch109               True  \n", "GTTGTGGGTCACGCGG-HCT116_Batch109               True  \n", "GTTGTGGGTCTGACTC-HCT116_Batch109               True  \n", "GTTGTGGGTCTTGATT-HCT116_Batch109               True  \n", "GTTGTGGGTGTAGTTA-HCT116_Batch109               True  \n", "\n", "[3409169 rows x 9 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["b.obs"]}, {"cell_type": "code", "execution_count": null, "id": "0bb73cac", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['ENSG00000188976', 'ENSG00000187583', 'ENSG00000188290',\n", "       'ENSG00000187608', 'ENSG00000188157', 'ENSG00000078808',\n", "       'ENSG00000176022', 'ENSG00000160087', 'ENSG00000131584',\n", "       'ENSG00000169972',\n", "       ...\n", "       'ENSG00000198712', 'ENSG00000228253', 'ENSG00000198899',\n", "       'ENSG00000198938', 'ENSG00000198840', 'ENSG00000212907',\n", "       'ENSG00000198886', 'ENSG00000198786', 'ENSG00000198695',\n", "       'ENSG00000198727'],\n", "      dtype='object', name='gene_id', length=8749)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "c = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/aligned18000datas_merged/K562_essential_raw_singlecell_01.aligned.h5ad', backed=\"r\")\n", "c.var\n", "c.var.index\n"]}, {"cell_type": "code", "execution_count": 6, "id": "ed4a4b76", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>core_scale_factor</th>\n", "      <th>core_adjusted_UMI_count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAAACTAC-53</th>\n", "      <td>53</td>\n", "      <td>MRPS31</td>\n", "      <td>ENSG00000102738</td>\n", "      <td>P1P2</td>\n", "      <td>5261_MRPS31_P1P2_ENSG00000102738</td>\n", "      <td>MRPS31_-_41345123.23-P1P2|MRPS31_+_41345107.23...</td>\n", "      <td>0.051790</td>\n", "      <td>38405.0</td>\n", "      <td>0.065533</td>\n", "      <td>2.985979</td>\n", "      <td>12861.780273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAGCCAC-51</th>\n", "      <td>51</td>\n", "      <td>LRRC37A3</td>\n", "      <td>ENSG00000176809</td>\n", "      <td>P1P2</td>\n", "      <td>4661_LRRC37A3_P1P2_ENSG00000176809</td>\n", "      <td>LRRC37A3_+_62915581.23-P1P2|LRRC37A3_-_6291539...</td>\n", "      <td>0.048614</td>\n", "      <td>12774.0</td>\n", "      <td>1.087875</td>\n", "      <td>0.721063</td>\n", "      <td>17715.509766</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAGCGAA-32</th>\n", "      <td>32</td>\n", "      <td>SRCAP</td>\n", "      <td>ENSG00000080603</td>\n", "      <td>P1P2</td>\n", "      <td>8397_SRCAP_P1P2_ENSG00000080603</td>\n", "      <td>SRCAP_-_30710672.23-P1P2|SRCAP_-_30710513.23-P1P2</td>\n", "      <td>0.049437</td>\n", "      <td>15353.0</td>\n", "      <td>0.571514</td>\n", "      <td>0.989524</td>\n", "      <td>15515.543945</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAATACAC-44</th>\n", "      <td>44</td>\n", "      <td>WBP1</td>\n", "      <td>ENSG00000239779</td>\n", "      <td>P1P2</td>\n", "      <td>9793_WBP1_P1P2_ENSG00000239779</td>\n", "      <td>WBP1_-_74685599.23-P1P2|WBP1_-_74685547.23-P1P2</td>\n", "      <td>0.059373</td>\n", "      <td>18729.0</td>\n", "      <td>0.281205</td>\n", "      <td>1.321505</td>\n", "      <td>14172.480469</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAATCGAT-43</th>\n", "      <td>43</td>\n", "      <td>RRP12</td>\n", "      <td>ENSG00000052749</td>\n", "      <td>P1P2</td>\n", "      <td>7561_RRP12_P1P2_ENSG00000052749</td>\n", "      <td>RRP12_+_99161057.23-P1P2|RRP12_-_99161036.23-P1P2</td>\n", "      <td>0.059395</td>\n", "      <td>11634.0</td>\n", "      <td>0.633577</td>\n", "      <td>0.735945</td>\n", "      <td>15808.244141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGCACCT-44</th>\n", "      <td>44</td>\n", "      <td>MAX</td>\n", "      <td>ENSG00000125952</td>\n", "      <td>P1P2</td>\n", "      <td>4871_MAX_P1P2_ENSG00000125952</td>\n", "      <td>MAX_+_65569008.23-P1P2|MAX_-_65568906.23-P1P2</td>\n", "      <td>0.079629</td>\n", "      <td>22228.0</td>\n", "      <td>0.767826</td>\n", "      <td>1.321505</td>\n", "      <td>16820.220703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGGGCAC-32</th>\n", "      <td>32</td>\n", "      <td>ATP6V0C</td>\n", "      <td>ENSG00000185883</td>\n", "      <td>P1P2</td>\n", "      <td>675_ATP6V0C_P1P2_ENSG00000185883</td>\n", "      <td>ATP6V0C_+_2564168.23-P1P2|ATP6V0C_-_2563995.23...</td>\n", "      <td>0.049527</td>\n", "      <td>12377.0</td>\n", "      <td>-0.004215</td>\n", "      <td>0.989524</td>\n", "      <td>12508.036133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCCCA-37</th>\n", "      <td>37</td>\n", "      <td>MLST8</td>\n", "      <td>ENSG00000167965</td>\n", "      <td>P1P2</td>\n", "      <td>5115_MLST8_P1P2_ENSG00000167965</td>\n", "      <td>MLST8_+_2255502.23-P1P2|MLST8_-_2255521.23-P1P2</td>\n", "      <td>0.061164</td>\n", "      <td>8093.0</td>\n", "      <td>-0.616659</td>\n", "      <td>0.908722</td>\n", "      <td>8905.912109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTGGTCC-17</th>\n", "      <td>17</td>\n", "      <td>CDK6</td>\n", "      <td>ENSG00000105810</td>\n", "      <td>P1</td>\n", "      <td>1440_CDK6_P1_ENSG00000105810</td>\n", "      <td>CDK6_-_92465893.23-P1|CDK6_+_92465903.23-P1</td>\n", "      <td>0.048416</td>\n", "      <td>9625.0</td>\n", "      <td>-0.493887</td>\n", "      <td>0.954016</td>\n", "      <td>10088.931641</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTTCCGG-22</th>\n", "      <td>22</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>non-targeting</td>\n", "      <td>11270_non-targeting_non-targeting_non-targeting</td>\n", "      <td>non-targeting_03462|non-targeting_00434</td>\n", "      <td>0.048590</td>\n", "      <td>18296.0</td>\n", "      <td>1.378922</td>\n", "      <td>1.007874</td>\n", "      <td>18153.058594</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>247914 rows × 11 columns</p>\n", "</div>"], "text/plain": ["                     gem_group           gene          gene_id     transcript  \\\n", "cell_barcode                                                                    \n", "AAACCCAAGAAACTAC-53         53         MRPS31  ENSG00000102738           P1P2   \n", "AAACCCAAGAAGCCAC-51         51       LRRC37A3  ENSG00000176809           P1P2   \n", "AAACCCAAGAAGCGAA-32         32          SRCAP  ENSG00000080603           P1P2   \n", "AAACCCAAGAATACAC-44         44           WBP1  ENSG00000239779           P1P2   \n", "AAACCCAAGAATCGAT-43         43          RRP12  ENSG00000052749           P1P2   \n", "...                        ...            ...              ...            ...   \n", "TTTGTTGTCTGCACCT-44         44            MAX  ENSG00000125952           P1P2   \n", "TTTGTTGTCTGGGCAC-32         32        ATP6V0C  ENSG00000185883           P1P2   \n", "TTTGTTGTCTGTCCCA-37         37          MLST8  ENSG00000167965           P1P2   \n", "TTTGTTGTCTTGGTCC-17         17           CDK6  ENSG00000105810             P1   \n", "TTTGTTGTCTTTCCGG-22         22  non-targeting    non-targeting  non-targeting   \n", "\n", "                                                     gene_transcript  \\\n", "cell_barcode                                                           \n", "AAACCCAAGAAACTAC-53                 5261_MRPS31_P1P2_ENSG00000102738   \n", "AAACCCAAGAAGCCAC-51               4661_LRRC37A3_P1P2_ENSG00000176809   \n", "AAACCCAAGAAGCGAA-32                  8397_SRCAP_P1P2_ENSG00000080603   \n", "AAACCCAAGAATACAC-44                   9793_WBP1_P1P2_ENSG00000239779   \n", "AAACCCAAGAATCGAT-43                  7561_RRP12_P1P2_ENSG00000052749   \n", "...                                                              ...   \n", "TTTGTTGTCTGCACCT-44                    4871_MAX_P1P2_ENSG00000125952   \n", "TTTGTTGTCTGGGCAC-32                 675_ATP6V0C_P1P2_ENSG00000185883   \n", "TTTGTTGTCTGTCCCA-37                  5115_MLST8_P1P2_ENSG00000167965   \n", "TTTGTTGTCTTGGTCC-17                     1440_CDK6_P1_ENSG00000105810   \n", "TTTGTTGTCTTTCCGG-22  11270_non-targeting_non-targeting_non-targeting   \n", "\n", "                                                               sgID_AB  \\\n", "cell_barcode                                                             \n", "AAACCCAAGAAACTAC-53  MRPS31_-_41345123.23-P1P2|MRPS31_+_41345107.23...   \n", "AAACCCAAGAAGCCAC-51  LRRC37A3_+_62915581.23-P1P2|LRRC37A3_-_6291539...   \n", "AAACCCAAGAAGCGAA-32  SRCAP_-_30710672.23-P1P2|SRCAP_-_30710513.23-P1P2   \n", "AAACCCAAGAATACAC-44    WBP1_-_74685599.23-P1P2|WBP1_-_74685547.23-P1P2   \n", "AAACCCAAGAATCGAT-43  RRP12_+_99161057.23-P1P2|RRP12_-_99161036.23-P1P2   \n", "...                                                                ...   \n", "TTTGTTGTCTGCACCT-44      MAX_+_65569008.23-P1P2|MAX_-_65568906.23-P1P2   \n", "TTTGTTGTCTGGGCAC-32  ATP6V0C_+_2564168.23-P1P2|ATP6V0C_-_2563995.23...   \n", "TTTGTTGTCTGTCCCA-37    MLST8_+_2255502.23-P1P2|MLST8_-_2255521.23-P1P2   \n", "TTTGTTGTCTTGGTCC-17        CDK6_-_92465893.23-P1|CDK6_+_92465903.23-P1   \n", "TTTGTTGTCTTTCCGG-22            non-targeting_03462|non-targeting_00434   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \\\n", "cell_barcode                                                  \n", "AAACCCAAGAAACTAC-53     0.051790    38405.0        0.065533   \n", "AAACCCAAGAAGCCAC-51     0.048614    12774.0        1.087875   \n", "AAACCCAAGAAGCGAA-32     0.049437    15353.0        0.571514   \n", "AAACCCAAGAATACAC-44     0.059373    18729.0        0.281205   \n", "AAACCCAAGAATCGAT-43     0.059395    11634.0        0.633577   \n", "...                          ...        ...             ...   \n", "TTTGTTGTCTGCACCT-44     0.079629    22228.0        0.767826   \n", "TTTGTTGTCTGGGCAC-32     0.049527    12377.0       -0.004215   \n", "TTTGTTGTCTGTCCCA-37     0.061164     8093.0       -0.616659   \n", "TTTGTTGTCTTGGTCC-17     0.048416     9625.0       -0.493887   \n", "TTTGTTGTCTTTCCGG-22     0.048590    18296.0        1.378922   \n", "\n", "                     core_scale_factor  core_adjusted_UMI_count  \n", "cell_barcode                                                     \n", "AAACCCAAGAAACTAC-53           2.985979             12861.780273  \n", "AAACCCAAGAAGCCAC-51           0.721063             17715.509766  \n", "AAACCCAAGAAGCGAA-32           0.989524             15515.543945  \n", "AAACCCAAGAATACAC-44           1.321505             14172.480469  \n", "AAACCCAAGAATCGAT-43           0.735945             15808.244141  \n", "...                                ...                      ...  \n", "TTTGTTGTCTGCACCT-44           1.321505             16820.220703  \n", "TTTGTTGTCTGGGCAC-32           0.989524             12508.036133  \n", "TTTGTTGTCTGTCCCA-37           0.908722              8905.912109  \n", "TTTGTTGTCTTGGTCC-17           0.954016             10088.931641  \n", "TTTGTTGTCTTTCCGG-22           1.007874             18153.058594  \n", "\n", "[247914 rows x 11 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["c.obs"]}, {"cell_type": "code", "execution_count": 7, "id": "05af1042", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>cv</th>\n", "      <th>in_matrix</th>\n", "      <th>gini</th>\n", "      <th>clean_mean</th>\n", "      <th>clean_std</th>\n", "      <th>clean_cv</th>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ENSG00000188976</th>\n", "      <td>NOC2L</td>\n", "      <td>0.925306</td>\n", "      <td>0.216197</td>\n", "      <td>0.233649</td>\n", "      <td>True</td>\n", "      <td>0.124345</td>\n", "      <td>0.985649</td>\n", "      <td>0.225734</td>\n", "      <td>0.229020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000187583</th>\n", "      <td>PLEKHN1</td>\n", "      <td>0.126289</td>\n", "      <td>0.059157</td>\n", "      <td>0.468425</td>\n", "      <td>True</td>\n", "      <td>0.250009</td>\n", "      <td>0.132876</td>\n", "      <td>0.063824</td>\n", "      <td>0.480323</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188290</th>\n", "      <td>HES4</td>\n", "      <td>0.680438</td>\n", "      <td>0.365879</td>\n", "      <td>0.537711</td>\n", "      <td>True</td>\n", "      <td>0.223568</td>\n", "      <td>0.729217</td>\n", "      <td>0.399879</td>\n", "      <td>0.548367</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000187608</th>\n", "      <td>ISG15</td>\n", "      <td>0.462525</td>\n", "      <td>0.235287</td>\n", "      <td>0.508702</td>\n", "      <td>True</td>\n", "      <td>0.224943</td>\n", "      <td>0.500009</td>\n", "      <td>0.260917</td>\n", "      <td>0.521824</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188157</th>\n", "      <td>AGRN</td>\n", "      <td>0.368164</td>\n", "      <td>0.154146</td>\n", "      <td>0.418689</td>\n", "      <td>True</td>\n", "      <td>0.212531</td>\n", "      <td>0.379996</td>\n", "      <td>0.162956</td>\n", "      <td>0.428837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000212907</th>\n", "      <td>MT-ND4L</td>\n", "      <td>3.700753</td>\n", "      <td>1.091458</td>\n", "      <td>0.294929</td>\n", "      <td>True</td>\n", "      <td>0.152265</td>\n", "      <td>3.710612</td>\n", "      <td>1.087930</td>\n", "      <td>0.293194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198886</th>\n", "      <td>MT-ND4</td>\n", "      <td>82.065681</td>\n", "      <td>19.408978</td>\n", "      <td>0.236505</td>\n", "      <td>True</td>\n", "      <td>0.125400</td>\n", "      <td>81.838829</td>\n", "      <td>18.903275</td>\n", "      <td>0.230982</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198786</th>\n", "      <td>MT-ND5</td>\n", "      <td>24.636215</td>\n", "      <td>5.965447</td>\n", "      <td>0.242141</td>\n", "      <td>True</td>\n", "      <td>0.127631</td>\n", "      <td>24.750563</td>\n", "      <td>5.950906</td>\n", "      <td>0.240435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198695</th>\n", "      <td>MT-ND6</td>\n", "      <td>3.701308</td>\n", "      <td>1.252693</td>\n", "      <td>0.338446</td>\n", "      <td>True</td>\n", "      <td>0.138177</td>\n", "      <td>3.453148</td>\n", "      <td>1.241829</td>\n", "      <td>0.359622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198727</th>\n", "      <td>MT-CYB</td>\n", "      <td>97.152924</td>\n", "      <td>25.826433</td>\n", "      <td>0.265833</td>\n", "      <td>True</td>\n", "      <td>0.139148</td>\n", "      <td>95.895744</td>\n", "      <td>24.754992</td>\n", "      <td>0.258145</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8749 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                gene_name       mean        std        cv  in_matrix  \\\n", "gene_id                                                                \n", "ENSG00000188976     NOC2L   0.925306   0.216197  0.233649       True   \n", "ENSG00000187583   PLEKHN1   0.126289   0.059157  0.468425       True   \n", "ENSG00000188290      HES4   0.680438   0.365879  0.537711       True   \n", "ENSG00000187608     ISG15   0.462525   0.235287  0.508702       True   \n", "ENSG00000188157      AGRN   0.368164   0.154146  0.418689       True   \n", "...                   ...        ...        ...       ...        ...   \n", "ENSG00000212907   MT-ND4L   3.700753   1.091458  0.294929       True   \n", "ENSG00000198886    MT-ND4  82.065681  19.408978  0.236505       True   \n", "ENSG00000198786    MT-ND5  24.636215   5.965447  0.242141       True   \n", "ENSG00000198695    MT-ND6   3.701308   1.252693  0.338446       True   \n", "ENSG00000198727    MT-CYB  97.152924  25.826433  0.265833       True   \n", "\n", "                     gini  clean_mean  clean_std  clean_cv  \n", "gene_id                                                     \n", "ENSG00000188976  0.124345    0.985649   0.225734  0.229020  \n", "ENSG00000187583  0.250009    0.132876   0.063824  0.480323  \n", "ENSG00000188290  0.223568    0.729217   0.399879  0.548367  \n", "ENSG00000187608  0.224943    0.500009   0.260917  0.521824  \n", "ENSG00000188157  0.212531    0.379996   0.162956  0.428837  \n", "...                   ...         ...        ...       ...  \n", "ENSG00000212907  0.152265    3.710612   1.087930  0.293194  \n", "ENSG00000198886  0.125400   81.838829  18.903275  0.230982  \n", "ENSG00000198786  0.127631   24.750563   5.950906  0.240435  \n", "ENSG00000198695  0.138177    3.453148   1.241829  0.359622  \n", "ENSG00000198727  0.139148   95.895744  24.754992  0.258145  \n", "\n", "[8749 rows x 9 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["d = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Replogle_et_al_2022/rpe1_raw_bulk_01.h5ad', backed=\"r\")\n", "d.var\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "6c2e1230", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>UMI_count_unfiltered</th>\n", "      <th>num_cells_unfiltered</th>\n", "      <th>num_cells_filtered</th>\n", "      <th>control_expr</th>\n", "      <th>fold_expr</th>\n", "      <th>pct_expr</th>\n", "      <th>core_control</th>\n", "      <th>mean_leverage_score</th>\n", "      <th>std_leverage_score</th>\n", "      <th>energy_test_p_value</th>\n", "      <th>and<PERSON>_darling_counts</th>\n", "      <th>mann_whitney_counts</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>mitopercent</th>\n", "      <th>TE_ratio</th>\n", "      <th>cnv_score_z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_transcript</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10005_ZBTB4_P1_ENSG00000174282</th>\n", "      <td>10778.880859</td>\n", "      <td>59</td>\n", "      <td>40.0</td>\n", "      <td>0.308079</td>\n", "      <td>1.103848</td>\n", "      <td>0.103848</td>\n", "      <td>False</td>\n", "      <td>1.501334</td>\n", "      <td>1.543915</td>\n", "      <td>0.000100</td>\n", "      <td>267.0</td>\n", "      <td>210</td>\n", "      <td>0.038201</td>\n", "      <td>0.078558</td>\n", "      <td>0.006893</td>\n", "      <td>-0.289561</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10020_ZC3H13_P1P2_ENSG00000123200</th>\n", "      <td>9102.114258</td>\n", "      <td>122</td>\n", "      <td>112.0</td>\n", "      <td>1.299198</td>\n", "      <td>0.123951</td>\n", "      <td>-0.876049</td>\n", "      <td>False</td>\n", "      <td>2.813979</td>\n", "      <td>1.268223</td>\n", "      <td>0.000100</td>\n", "      <td>3708.0</td>\n", "      <td>2351</td>\n", "      <td>-0.554576</td>\n", "      <td>0.073170</td>\n", "      <td>0.008532</td>\n", "      <td>2.276640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10023_ZC3H18_P1P2_ENSG00000158545</th>\n", "      <td>11299.639648</td>\n", "      <td>75</td>\n", "      <td>66.0</td>\n", "      <td>0.643801</td>\n", "      <td>0.158253</td>\n", "      <td>-0.841747</td>\n", "      <td>False</td>\n", "      <td>1.688321</td>\n", "      <td>1.027714</td>\n", "      <td>0.000100</td>\n", "      <td>1546.0</td>\n", "      <td>1139</td>\n", "      <td>-0.237008</td>\n", "      <td>0.070744</td>\n", "      <td>0.005621</td>\n", "      <td>3.470345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10024_ZC3H3_P1P2_ENSG00000014164</th>\n", "      <td>12613.722656</td>\n", "      <td>108</td>\n", "      <td>102.0</td>\n", "      <td>0.074273</td>\n", "      <td>0.117984</td>\n", "      <td>-0.882016</td>\n", "      <td>False</td>\n", "      <td>0.664183</td>\n", "      <td>0.806218</td>\n", "      <td>0.000100</td>\n", "      <td>247.0</td>\n", "      <td>223</td>\n", "      <td>0.141950</td>\n", "      <td>0.065022</td>\n", "      <td>0.007394</td>\n", "      <td>0.135278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10025_ZC3H4_P1_ENSG00000130749</th>\n", "      <td>10286.013672</td>\n", "      <td>75</td>\n", "      <td>71.0</td>\n", "      <td>0.199062</td>\n", "      <td>0.153057</td>\n", "      <td>-0.846943</td>\n", "      <td>False</td>\n", "      <td>0.637746</td>\n", "      <td>0.679740</td>\n", "      <td>0.000100</td>\n", "      <td>607.0</td>\n", "      <td>506</td>\n", "      <td>-0.265628</td>\n", "      <td>0.066755</td>\n", "      <td>0.007916</td>\n", "      <td>0.027606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9974_ZBTB17_P1P2_ENSG00000116809</th>\n", "      <td>12093.723633</td>\n", "      <td>98</td>\n", "      <td>96.0</td>\n", "      <td>0.096402</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>False</td>\n", "      <td>0.834752</td>\n", "      <td>1.360602</td>\n", "      <td>0.000100</td>\n", "      <td>194.0</td>\n", "      <td>182</td>\n", "      <td>0.103167</td>\n", "      <td>0.060264</td>\n", "      <td>0.005730</td>\n", "      <td>-0.134610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9975_ZBTB17_P1P2_ENSG00000116809</th>\n", "      <td>14159.051758</td>\n", "      <td>77</td>\n", "      <td>74.0</td>\n", "      <td>0.096402</td>\n", "      <td>0.224506</td>\n", "      <td>-0.775494</td>\n", "      <td>False</td>\n", "      <td>1.074533</td>\n", "      <td>1.128362</td>\n", "      <td>0.000100</td>\n", "      <td>737.0</td>\n", "      <td>715</td>\n", "      <td>0.335319</td>\n", "      <td>0.064054</td>\n", "      <td>0.006274</td>\n", "      <td>8.018181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>997_C16orf86_P1P2_ENSG00000159761</th>\n", "      <td>12500.812500</td>\n", "      <td>197</td>\n", "      <td>181.0</td>\n", "      <td>0.004480</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>False</td>\n", "      <td>0.293434</td>\n", "      <td>0.904588</td>\n", "      <td>0.266473</td>\n", "      <td>7.0</td>\n", "      <td>5</td>\n", "      <td>0.065645</td>\n", "      <td>0.062063</td>\n", "      <td>0.005162</td>\n", "      <td>-0.408595</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9998_ZBTB44_P1P2_ENSG00000196323</th>\n", "      <td>13648.458984</td>\n", "      <td>85</td>\n", "      <td>79.0</td>\n", "      <td>0.250069</td>\n", "      <td>0.124216</td>\n", "      <td>-0.875784</td>\n", "      <td>False</td>\n", "      <td>1.216549</td>\n", "      <td>1.335359</td>\n", "      <td>0.000100</td>\n", "      <td>457.0</td>\n", "      <td>413</td>\n", "      <td>0.511878</td>\n", "      <td>0.067535</td>\n", "      <td>0.006007</td>\n", "      <td>0.419711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9_AAR2_P1P2_ENSG00000131043</th>\n", "      <td>12127.533203</td>\n", "      <td>15</td>\n", "      <td>11.0</td>\n", "      <td>0.132044</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>False</td>\n", "      <td>0.981240</td>\n", "      <td>1.127972</td>\n", "      <td>0.000100</td>\n", "      <td>53.0</td>\n", "      <td>9</td>\n", "      <td>0.993411</td>\n", "      <td>0.065566</td>\n", "      <td>0.005467</td>\n", "      <td>4.165301</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2679 rows × 16 columns</p>\n", "</div>"], "text/plain": ["                                   UMI_count_unfiltered  num_cells_unfiltered  \\\n", "gene_transcript                                                                 \n", "10005_ZBTB4_P1_ENSG00000174282             10778.880859                    59   \n", "10020_ZC3H13_P1P2_ENSG00000123200           9102.114258                   122   \n", "10023_ZC3H18_P1P2_ENSG00000158545          11299.639648                    75   \n", "10024_ZC3H3_P1P2_ENSG00000014164           12613.722656                   108   \n", "10025_ZC3H4_P1_ENSG00000130749             10286.013672                    75   \n", "...                                                 ...                   ...   \n", "9974_ZBTB17_P1P2_ENSG00000116809           12093.723633                    98   \n", "9975_ZBTB17_P1P2_ENSG00000116809           14159.051758                    77   \n", "997_C16orf86_P1P2_ENSG00000159761          12500.812500                   197   \n", "9998_ZBTB44_P1P2_ENSG00000196323           13648.458984                    85   \n", "9_AAR2_P1P2_ENSG00000131043                12127.533203                    15   \n", "\n", "                                   num_cells_filtered  control_expr  \\\n", "gene_transcript                                                       \n", "10005_ZBTB4_P1_ENSG00000174282                   40.0      0.308079   \n", "10020_ZC3H13_P1P2_ENSG00000123200               112.0      1.299198   \n", "10023_ZC3H18_P1P2_ENSG00000158545                66.0      0.643801   \n", "10024_ZC3H3_P1P2_ENSG00000014164                102.0      0.074273   \n", "10025_ZC3H4_P1_ENSG00000130749                   71.0      0.199062   \n", "...                                               ...           ...   \n", "9974_ZBTB17_P1P2_ENSG00000116809                 96.0      0.096402   \n", "9975_ZBTB17_P1P2_ENSG00000116809                 74.0      0.096402   \n", "997_C16orf86_P1P2_ENSG00000159761               181.0      0.004480   \n", "9998_ZBTB44_P1P2_ENSG00000196323                 79.0      0.250069   \n", "9_AAR2_P1P2_ENSG00000131043                      11.0      0.132044   \n", "\n", "                                   fold_expr  pct_expr  core_control  \\\n", "gene_transcript                                                        \n", "10005_ZBTB4_P1_ENSG00000174282      1.103848  0.103848         False   \n", "10020_ZC3H13_P1P2_ENSG00000123200   0.123951 -0.876049         False   \n", "10023_ZC3H18_P1P2_ENSG00000158545   0.158253 -0.841747         False   \n", "10024_ZC3H3_P1P2_ENSG00000014164    0.117984 -0.882016         False   \n", "10025_ZC3H4_P1_ENSG00000130749      0.153057 -0.846943         False   \n", "...                                      ...       ...           ...   \n", "9974_ZBTB17_P1P2_ENSG00000116809    0.000000 -1.000000         False   \n", "9975_ZBTB17_P1P2_ENSG00000116809    0.224506 -0.775494         False   \n", "997_C16orf86_P1P2_ENSG00000159761   0.000000 -1.000000         False   \n", "9998_ZBTB44_P1P2_ENSG00000196323    0.124216 -0.875784         False   \n", "9_AAR2_P1P2_ENSG00000131043         0.000000 -1.000000         False   \n", "\n", "                                   mean_leverage_score  std_leverage_score  \\\n", "gene_transcript                                                              \n", "10005_ZBTB4_P1_ENSG00000174282                1.501334            1.543915   \n", "10020_ZC3H13_P1P2_ENSG00000123200             2.813979            1.268223   \n", "10023_ZC3H18_P1P2_ENSG00000158545             1.688321            1.027714   \n", "10024_ZC3H3_P1P2_ENSG00000014164              0.664183            0.806218   \n", "10025_ZC3H4_P1_ENSG00000130749                0.637746            0.679740   \n", "...                                                ...                 ...   \n", "9974_ZBTB17_P1P2_ENSG00000116809              0.834752            1.360602   \n", "9975_ZBTB17_P1P2_ENSG00000116809              1.074533            1.128362   \n", "997_C16orf86_P1P2_ENSG00000159761             0.293434            0.904588   \n", "9998_ZBTB44_P1P2_ENSG00000196323              1.216549            1.335359   \n", "9_AAR2_P1P2_ENSG00000131043                   0.981240            1.127972   \n", "\n", "                                   energy_test_p_value  \\\n", "gene_transcript                                          \n", "10005_ZBTB4_P1_ENSG00000174282                0.000100   \n", "10020_ZC3H13_P1P2_ENSG00000123200             0.000100   \n", "10023_ZC3H18_P1P2_ENSG00000158545             0.000100   \n", "10024_ZC3H3_P1P2_ENSG00000014164              0.000100   \n", "10025_ZC3H4_P1_ENSG00000130749                0.000100   \n", "...                                                ...   \n", "9974_ZBTB17_P1P2_ENSG00000116809              0.000100   \n", "9975_ZBTB17_P1P2_ENSG00000116809              0.000100   \n", "997_C16orf86_P1P2_ENSG00000159761             0.266473   \n", "9998_ZBTB44_P1P2_ENSG00000196323              0.000100   \n", "9_AAR2_P1P2_ENSG00000131043                   0.000100   \n", "\n", "                                   and<PERSON>_darling_counts  \\\n", "gene_transcript                                              \n", "10005_ZBTB4_P1_ENSG00000174282                       267.0   \n", "10020_ZC3H13_P1P2_ENSG00000123200                   3708.0   \n", "10023_ZC3H18_P1P2_ENSG00000158545                   1546.0   \n", "10024_ZC3H3_P1P2_ENSG00000014164                     247.0   \n", "10025_ZC3H4_P1_ENSG00000130749                       607.0   \n", "...                                                    ...   \n", "9974_ZBTB17_P1P2_ENSG00000116809                     194.0   \n", "9975_ZBTB17_P1P2_ENSG00000116809                     737.0   \n", "997_C16orf86_P1P2_ENSG00000159761                      7.0   \n", "9998_ZBTB44_P1P2_ENSG00000196323                     457.0   \n", "9_AAR2_P1P2_ENSG00000131043                           53.0   \n", "\n", "                                   mann_whitney_counts  z_gemgroup_UMI  \\\n", "gene_transcript                                                          \n", "10005_ZBTB4_P1_ENSG00000174282                     210        0.038201   \n", "10020_ZC3H13_P1P2_ENSG00000123200                 2351       -0.554576   \n", "10023_ZC3H18_P1P2_ENSG00000158545                 1139       -0.237008   \n", "10024_ZC3H3_P1P2_ENSG00000014164                   223        0.141950   \n", "10025_ZC3H4_P1_ENSG00000130749                     506       -0.265628   \n", "...                                                ...             ...   \n", "9974_ZBTB17_P1P2_ENSG00000116809                   182        0.103167   \n", "9975_ZBTB17_P1P2_ENSG00000116809                   715        0.335319   \n", "997_C16orf86_P1P2_ENSG00000159761                    5        0.065645   \n", "9998_ZBTB44_P1P2_ENSG00000196323                   413        0.511878   \n", "9_AAR2_P1P2_ENSG00000131043                          9        0.993411   \n", "\n", "                                   mitopercent  TE_ratio  cnv_score_z  \n", "gene_transcript                                                        \n", "10005_ZBTB4_P1_ENSG00000174282        0.078558  0.006893    -0.289561  \n", "10020_ZC3H13_P1P2_ENSG00000123200     0.073170  0.008532     2.276640  \n", "10023_ZC3H18_P1P2_ENSG00000158545     0.070744  0.005621     3.470345  \n", "10024_ZC3H3_P1P2_ENSG00000014164      0.065022  0.007394     0.135278  \n", "10025_ZC3H4_P1_ENSG00000130749        0.066755  0.007916     0.027606  \n", "...                                        ...       ...          ...  \n", "9974_ZBTB17_P1P2_ENSG00000116809      0.060264  0.005730    -0.134610  \n", "9975_ZBTB17_P1P2_ENSG00000116809      0.064054  0.006274     8.018181  \n", "997_C16orf86_P1P2_ENSG00000159761     0.062063  0.005162    -0.408595  \n", "9998_ZBTB44_P1P2_ENSG00000196323      0.067535  0.006007     0.419711  \n", "9_AAR2_P1P2_ENSG00000131043           0.065566  0.005467     4.165301  \n", "\n", "[2679 rows x 16 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["d.obs"]}, {"cell_type": "code", "execution_count": 2, "id": "a74077cd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>gene_id</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SAMD11</th>\n", "      <td>SAMD11</td>\n", "      <td>ENSG00000187634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NOC2L</th>\n", "      <td>NOC2L</td>\n", "      <td>ENSG00000188976</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KLHL17</th>\n", "      <td>KLHL17</td>\n", "      <td>ENSG00000187961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PLEKHN1</th>\n", "      <td>PLEKHN1</td>\n", "      <td>ENSG00000187583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PERM1</th>\n", "      <td>PERM1</td>\n", "      <td>ENSG00000187642</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND4L</th>\n", "      <td>MT-ND4L</td>\n", "      <td>ENSG00000212907</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND4</th>\n", "      <td>MT-ND4</td>\n", "      <td>ENSG00000198886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND5</th>\n", "      <td>MT-ND5</td>\n", "      <td>ENSG00000198786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-ND6</th>\n", "      <td>MT-ND6</td>\n", "      <td>ENSG00000198695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MT-CYB</th>\n", "      <td>MT-CYB</td>\n", "      <td>ENSG00000198727</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18080 rows × 2 columns</p>\n", "</div>"], "text/plain": ["        gene_name          gene_id\n", "index                             \n", "SAMD11     SAMD11  ENSG00000187634\n", "NOC2L       NOC2L  ENSG00000188976\n", "KLHL17     KLHL17  ENSG00000187961\n", "PLEKHN1   PLEKHN1  ENSG00000187583\n", "PERM1       PERM1  ENSG00000187642\n", "...           ...              ...\n", "MT-ND4L   MT-ND4L  ENSG00000212907\n", "MT-ND4     MT-ND4  ENSG00000198886\n", "MT-ND5     MT-ND5  ENSG00000198786\n", "MT-ND6     MT-ND6  ENSG00000198695\n", "MT-CYB     MT-CYB  ENSG00000198727\n", "\n", "[18080 rows x 2 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["e = sc.read_h5ad('/data/ioz_whr_wsx/datasets/VCC/aligned18000datas_merged/K562_essential_raw_singlecell_01.aligned.h5ad',backed='r')\n", "e.var\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "92bcbba4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>core_scale_factor</th>\n", "      <th>core_adjusted_UMI_count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAAATCCA-27</th>\n", "      <td>27</td>\n", "      <td>NAF1</td>\n", "      <td>ENSG00000145414</td>\n", "      <td>P1P2</td>\n", "      <td>5449_NAF1_P1P2_ENSG00000145414</td>\n", "      <td>NAF1_+_164087918.23-P1P2|NAF1_-_164087674.23-P1P2</td>\n", "      <td>0.112083</td>\n", "      <td>11438.0</td>\n", "      <td>0.013047</td>\n", "      <td>0.813253</td>\n", "      <td>14064.512695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAACTTCC-31</th>\n", "      <td>31</td>\n", "      <td>BUB1</td>\n", "      <td>ENSG00000169679</td>\n", "      <td>P1P2</td>\n", "      <td>935_BUB1_P1P2_ENSG00000169679</td>\n", "      <td>BUB1_-_111435363.23-P1P2|BUB1_-_111435372.23-P1P2</td>\n", "      <td>0.179895</td>\n", "      <td>5342.0</td>\n", "      <td>-1.522247</td>\n", "      <td>0.844107</td>\n", "      <td>6328.584473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAGCCAC-34</th>\n", "      <td>34</td>\n", "      <td>UBL5</td>\n", "      <td>ENSG00000198258</td>\n", "      <td>P1P2</td>\n", "      <td>9534_UBL5_P1P2_ENSG00000198258</td>\n", "      <td>UBL5_-_9938639.23-P1P2|UBL5_+_9938801.23-P1P2</td>\n", "      <td>0.105287</td>\n", "      <td>17305.0</td>\n", "      <td>0.384157</td>\n", "      <td>1.091537</td>\n", "      <td>15853.792969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAATAGTC-43</th>\n", "      <td>43</td>\n", "      <td>C9orf16</td>\n", "      <td>ENSG00000171159</td>\n", "      <td>P1P2</td>\n", "      <td>1131_C9orf16_P1P2_ENSG00000171159</td>\n", "      <td>C9orf16_+_130922603.23-P1P2|C9orf16_+_13092264...</td>\n", "      <td>0.099359</td>\n", "      <td>30244.0</td>\n", "      <td>3.721912</td>\n", "      <td>0.948277</td>\n", "      <td>31893.619141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGACAGCGT-28</th>\n", "      <td>28</td>\n", "      <td>TIMM9</td>\n", "      <td>ENSG00000100575</td>\n", "      <td>P1P2</td>\n", "      <td>8927_TIMM9_P1P2_ENSG00000100575</td>\n", "      <td>TIMM9_-_58893843.23-P1P2|TIMM9_-_58893848.23-P1P2</td>\n", "      <td>0.137623</td>\n", "      <td>8407.0</td>\n", "      <td>-0.975371</td>\n", "      <td>0.868942</td>\n", "      <td>9674.979492</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCGTC-45</th>\n", "      <td>45</td>\n", "      <td>ATP6V1D</td>\n", "      <td>ENSG00000100554</td>\n", "      <td>P1P2</td>\n", "      <td>682_ATP6V1D_P1P2_ENSG00000100554</td>\n", "      <td>ATP6V1D_+_67826485.23-P1P2|ATP6V1D_+_67826497....</td>\n", "      <td>0.100272</td>\n", "      <td>18350.0</td>\n", "      <td>0.428227</td>\n", "      <td>1.115052</td>\n", "      <td>16456.638672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCTCG-27</th>\n", "      <td>27</td>\n", "      <td>CNOT3</td>\n", "      <td>ENSG00000088038</td>\n", "      <td>P1P2</td>\n", "      <td>1718_CNOT3_P1P2_ENSG00000088038</td>\n", "      <td>CNOT3_+_54641532.23-P1P2|CNOT3_-_54641691.23-P1P2</td>\n", "      <td>0.093876</td>\n", "      <td>8671.0</td>\n", "      <td>-0.633593</td>\n", "      <td>0.813253</td>\n", "      <td>10662.125000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTGCGG-44</th>\n", "      <td>44</td>\n", "      <td>METTL3</td>\n", "      <td>ENSG00000165819</td>\n", "      <td>P1P2</td>\n", "      <td>5004_METTL3_P1P2_ENSG00000165819</td>\n", "      <td>METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...</td>\n", "      <td>0.107983</td>\n", "      <td>20568.0</td>\n", "      <td>1.054624</td>\n", "      <td>0.973352</td>\n", "      <td>21131.095703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTGCAGA-14</th>\n", "      <td>14</td>\n", "      <td>RPL5</td>\n", "      <td>ENSG00000122406</td>\n", "      <td>P1P2</td>\n", "      <td>7475_RPL5_P1P2_ENSG00000122406</td>\n", "      <td>RPL5_+_93297664.23-P1P2|RPL5_-_93297968.23-P1P2</td>\n", "      <td>0.128225</td>\n", "      <td>23568.0</td>\n", "      <td>1.676254</td>\n", "      <td>1.050055</td>\n", "      <td>22444.542969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTTACAC-25</th>\n", "      <td>25</td>\n", "      <td>SEC61B</td>\n", "      <td>ENSG00000106803</td>\n", "      <td>P1P2</td>\n", "      <td>7752_SEC61B_P1P2_ENSG00000106803</td>\n", "      <td>SEC61B_+_101984577.23-P1P2|SEC61B_-_101984591....</td>\n", "      <td>0.095009</td>\n", "      <td>13504.0</td>\n", "      <td>0.341625</td>\n", "      <td>0.855594</td>\n", "      <td>15783.187500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>310385 rows × 11 columns</p>\n", "</div>"], "text/plain": ["                     gem_group     gene          gene_id transcript  \\\n", "index                                                                 \n", "AAACCCAAGAAATCCA-27         27     NAF1  ENSG00000145414       P1P2   \n", "AAACCCAAGAACTTCC-31         31     BUB1  ENSG00000169679       P1P2   \n", "AAACCCAAGAAGCCAC-34         34     UBL5  ENSG00000198258       P1P2   \n", "AAACCCAAGAATAGTC-43         43  C9orf16  ENSG00000171159       P1P2   \n", "AAACCCAAGACAGCGT-28         28    TIMM9  ENSG00000100575       P1P2   \n", "...                        ...      ...              ...        ...   \n", "TTTGTTGTCTGTCGTC-45         45  ATP6V1D  ENSG00000100554       P1P2   \n", "TTTGTTGTCTGTCTCG-27         27    CNOT3  ENSG00000088038       P1P2   \n", "TTTGTTGTCTGTGCGG-44         44   METTL3  ENSG00000165819       P1P2   \n", "TTTGTTGTCTTGCAGA-14         14     RPL5  ENSG00000122406       P1P2   \n", "TTTGTTGTCTTTACAC-25         25   SEC61B  ENSG00000106803       P1P2   \n", "\n", "                                       gene_transcript  \\\n", "index                                                    \n", "AAACCCAAGAAATCCA-27     5449_NAF1_P1P2_ENSG00000145414   \n", "AAACCCAAGAACTTCC-31      935_BUB1_P1P2_ENSG00000169679   \n", "AAACCCAAGAAGCCAC-34     9534_UBL5_P1P2_ENSG00000198258   \n", "AAACCCAAGAATAGTC-43  1131_C9orf16_P1P2_ENSG00000171159   \n", "AAACCCAAGACAGCGT-28    8927_TIMM9_P1P2_ENSG00000100575   \n", "...                                                ...   \n", "TTTGTTGTCTGTCGTC-45   682_ATP6V1D_P1P2_ENSG00000100554   \n", "TTTGTTGTCTGTCTCG-27    1718_CNOT3_P1P2_ENSG00000088038   \n", "TTTGTTGTCTGTGCGG-44   5004_METTL3_P1P2_ENSG00000165819   \n", "TTTGTTGTCTTGCAGA-14     7475_RPL5_P1P2_ENSG00000122406   \n", "TTTGTTGTCTTTACAC-25   7752_SEC61B_P1P2_ENSG00000106803   \n", "\n", "                                                               sgID_AB  \\\n", "index                                                                    \n", "AAACCCAAGAAATCCA-27  NAF1_+_164087918.23-P1P2|NAF1_-_164087674.23-P1P2   \n", "AAACCCAAGAACTTCC-31  BUB1_-_111435363.23-P1P2|BUB1_-_111435372.23-P1P2   \n", "AAACCCAAGAAGCCAC-34      UBL5_-_9938639.23-P1P2|UBL5_+_9938801.23-P1P2   \n", "AAACCCAAGAATAGTC-43  C9orf16_+_130922603.23-P1P2|C9orf16_+_13092264...   \n", "AAACCCAAGACAGCGT-28  TIMM9_-_58893843.23-P1P2|TIMM9_-_58893848.23-P1P2   \n", "...                                                                ...   \n", "TTTGTTGTCTGTCGTC-45  ATP6V1D_+_67826485.23-P1P2|ATP6V1D_+_67826497....   \n", "TTTGTTGTCTGTCTCG-27  CNOT3_+_54641532.23-P1P2|CNOT3_-_54641691.23-P1P2   \n", "TTTGTTGTCTGTGCGG-44  METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...   \n", "TTTGTTGTCTTGCAGA-14    RPL5_+_93297664.23-P1P2|RPL5_-_93297968.23-P1P2   \n", "TTTGTTGTCTTTACAC-25  SEC61B_+_101984577.23-P1P2|SEC61B_-_101984591....   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \\\n", "index                                                         \n", "AAACCCAAGAAATCCA-27     0.112083    11438.0        0.013047   \n", "AAACCCAAGAACTTCC-31     0.179895     5342.0       -1.522247   \n", "AAACCCAAGAAGCCAC-34     0.105287    17305.0        0.384157   \n", "AAACCCAAGAATAGTC-43     0.099359    30244.0        3.721912   \n", "AAACCCAAGACAGCGT-28     0.137623     8407.0       -0.975371   \n", "...                          ...        ...             ...   \n", "TTTGTTGTCTGTCGTC-45     0.100272    18350.0        0.428227   \n", "TTTGTTGTCTGTCTCG-27     0.093876     8671.0       -0.633593   \n", "TTTGTTGTCTGTGCGG-44     0.107983    20568.0        1.054624   \n", "TTTGTTGTCTTGCAGA-14     0.128225    23568.0        1.676254   \n", "TTTGTTGTCTTTACAC-25     0.095009    13504.0        0.341625   \n", "\n", "                     core_scale_factor  core_adjusted_UMI_count  \n", "index                                                            \n", "AAACCCAAGAAATCCA-27           0.813253             14064.512695  \n", "AAACCCAAGAACTTCC-31           0.844107              6328.584473  \n", "AAACCCAAGAAGCCAC-34           1.091537             15853.792969  \n", "AAACCCAAGAATAGTC-43           0.948277             31893.619141  \n", "AAACCCAAGACAGCGT-28           0.868942              9674.979492  \n", "...                                ...                      ...  \n", "TTTGTTGTCTGTCGTC-45           1.115052             16456.638672  \n", "TTTGTTGTCTGTCTCG-27           0.813253             10662.125000  \n", "TTTGTTGTCTGTGCGG-44           0.973352             21131.095703  \n", "TTTGTTGTCTTGCAGA-14           1.050055             22444.542969  \n", "TTTGTTGTCTTTACAC-25           0.855594             15783.187500  \n", "\n", "[310385 rows x 11 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["e.obs\n"]}, {"cell_type": "code", "execution_count": 5, "id": "82d689ef", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>class</th>\n", "      <th>strand</th>\n", "      <th>length</th>\n", "      <th>in_matrix</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>cv</th>\n", "      <th>fano</th>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ENSG00000237491</th>\n", "      <td>LINC01409</td>\n", "      <td>chr1</td>\n", "      <td>778747</td>\n", "      <td>810065</td>\n", "      <td>gene_version10</td>\n", "      <td>+</td>\n", "      <td>31318</td>\n", "      <td>True</td>\n", "      <td>0.137594</td>\n", "      <td>0.380048</td>\n", "      <td>2.762105</td>\n", "      <td>1.049733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000228794</th>\n", "      <td>LINC01128</td>\n", "      <td>chr1</td>\n", "      <td>825138</td>\n", "      <td>868202</td>\n", "      <td>gene_version9</td>\n", "      <td>+</td>\n", "      <td>43064</td>\n", "      <td>True</td>\n", "      <td>0.256720</td>\n", "      <td>0.520162</td>\n", "      <td>2.026184</td>\n", "      <td>1.053944</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188976</th>\n", "      <td>NOC2L</td>\n", "      <td>chr1</td>\n", "      <td>944203</td>\n", "      <td>959309</td>\n", "      <td>gene_version11</td>\n", "      <td>-</td>\n", "      <td>15106</td>\n", "      <td>True</td>\n", "      <td>1.975144</td>\n", "      <td>1.707837</td>\n", "      <td>0.864665</td>\n", "      <td>1.476706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000187961</th>\n", "      <td>KLHL17</td>\n", "      <td>chr1</td>\n", "      <td>960584</td>\n", "      <td>965719</td>\n", "      <td>gene_version14</td>\n", "      <td>+</td>\n", "      <td>5135</td>\n", "      <td>True</td>\n", "      <td>0.119593</td>\n", "      <td>0.353702</td>\n", "      <td>2.957540</td>\n", "      <td>1.046089</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188290</th>\n", "      <td>HES4</td>\n", "      <td>chr1</td>\n", "      <td>998962</td>\n", "      <td>1000172</td>\n", "      <td>gene_version10</td>\n", "      <td>-</td>\n", "      <td>1210</td>\n", "      <td>True</td>\n", "      <td>0.249577</td>\n", "      <td>0.561933</td>\n", "      <td>2.251540</td>\n", "      <td>1.265214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278704</th>\n", "      <td>BX004987.1</td>\n", "      <td>GL000009.2</td>\n", "      <td>56140</td>\n", "      <td>58376</td>\n", "      <td>gene_version1</td>\n", "      <td>-</td>\n", "      <td>2236</td>\n", "      <td>True</td>\n", "      <td>0.241213</td>\n", "      <td>0.507266</td>\n", "      <td>2.102976</td>\n", "      <td>1.066768</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000274847</th>\n", "      <td>MAFIP</td>\n", "      <td>GL000194.1</td>\n", "      <td>53594</td>\n", "      <td>115055</td>\n", "      <td>gene_version1</td>\n", "      <td>-</td>\n", "      <td>61461</td>\n", "      <td>True</td>\n", "      <td>0.127525</td>\n", "      <td>0.361556</td>\n", "      <td>2.835168</td>\n", "      <td>1.025072</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278384</th>\n", "      <td>AL354822.1</td>\n", "      <td>GL000218.1</td>\n", "      <td>51867</td>\n", "      <td>54893</td>\n", "      <td>gene_version1</td>\n", "      <td>-</td>\n", "      <td>3026</td>\n", "      <td>True</td>\n", "      <td>0.248814</td>\n", "      <td>0.516552</td>\n", "      <td>2.076062</td>\n", "      <td>1.072394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000271254</th>\n", "      <td>AC240274.1</td>\n", "      <td>KI270711.1</td>\n", "      <td>4612</td>\n", "      <td>29626</td>\n", "      <td>gene_version6</td>\n", "      <td>-</td>\n", "      <td>25014</td>\n", "      <td>True</td>\n", "      <td>0.224144</td>\n", "      <td>0.505202</td>\n", "      <td>2.253916</td>\n", "      <td>1.138683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000276345</th>\n", "      <td>AC004556.3</td>\n", "      <td>KI270721.1</td>\n", "      <td>2585</td>\n", "      <td>11802</td>\n", "      <td>gene_version1</td>\n", "      <td>+</td>\n", "      <td>9217</td>\n", "      <td>True</td>\n", "      <td>0.343644</td>\n", "      <td>0.623236</td>\n", "      <td>1.813608</td>\n", "      <td>1.130305</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8563 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                  gene_name         chr   start      end           class  \\\n", "gene_id                                                                    \n", "ENSG00000237491   LINC01409        chr1  778747   810065  gene_version10   \n", "ENSG00000228794   LINC01128        chr1  825138   868202   gene_version9   \n", "ENSG00000188976       NOC2L        chr1  944203   959309  gene_version11   \n", "ENSG00000187961      KLHL17        chr1  960584   965719  gene_version14   \n", "ENSG00000188290        HES4        chr1  998962  1000172  gene_version10   \n", "...                     ...         ...     ...      ...             ...   \n", "ENSG00000278704  BX004987.1  GL000009.2   56140    58376   gene_version1   \n", "ENSG00000274847       MAFIP  GL000194.1   53594   115055   gene_version1   \n", "ENSG00000278384  AL354822.1  GL000218.1   51867    54893   gene_version1   \n", "ENSG00000271254  AC240274.1  KI270711.1    4612    29626   gene_version6   \n", "ENSG00000276345  AC004556.3  KI270721.1    2585    11802   gene_version1   \n", "\n", "                strand  length  in_matrix      mean       std        cv  \\\n", "gene_id                                                                   \n", "ENSG00000237491      +   31318       True  0.137594  0.380048  2.762105   \n", "ENSG00000228794      +   43064       True  0.256720  0.520162  2.026184   \n", "ENSG00000188976      -   15106       True  1.975144  1.707837  0.864665   \n", "ENSG00000187961      +    5135       True  0.119593  0.353702  2.957540   \n", "ENSG00000188290      -    1210       True  0.249577  0.561933  2.251540   \n", "...                ...     ...        ...       ...       ...       ...   \n", "ENSG00000278704      -    2236       True  0.241213  0.507266  2.102976   \n", "ENSG00000274847      -   61461       True  0.127525  0.361556  2.835168   \n", "ENSG00000278384      -    3026       True  0.248814  0.516552  2.076062   \n", "ENSG00000271254      -   25014       True  0.224144  0.505202  2.253916   \n", "ENSG00000276345      +    9217       True  0.343644  0.623236  1.813608   \n", "\n", "                     fano  \n", "gene_id                    \n", "ENSG00000237491  1.049733  \n", "ENSG00000228794  1.053944  \n", "ENSG00000188976  1.476706  \n", "ENSG00000187961  1.046089  \n", "ENSG00000188290  1.265214  \n", "...                   ...  \n", "ENSG00000278704  1.066768  \n", "ENSG00000274847  1.025072  \n", "ENSG00000278384  1.072394  \n", "ENSG00000271254  1.138683  \n", "ENSG00000276345  1.130305  \n", "\n", "[8563 rows x 12 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["f = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Replogle_et_al_2022/K562_essential_raw_singlecell_01.h5ad', backed=\"r\")\n", "f.var\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "20336ec7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>core_scale_factor</th>\n", "      <th>core_adjusted_UMI_count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAAATCCA-27</th>\n", "      <td>27</td>\n", "      <td>NAF1</td>\n", "      <td>ENSG00000145414</td>\n", "      <td>P1P2</td>\n", "      <td>5449_NAF1_P1P2_ENSG00000145414</td>\n", "      <td>NAF1_+_164087918.23-P1P2|NAF1_-_164087674.23-P1P2</td>\n", "      <td>0.112083</td>\n", "      <td>11438.0</td>\n", "      <td>0.013047</td>\n", "      <td>0.813253</td>\n", "      <td>14064.512695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAACTTCC-31</th>\n", "      <td>31</td>\n", "      <td>BUB1</td>\n", "      <td>ENSG00000169679</td>\n", "      <td>P1P2</td>\n", "      <td>935_BUB1_P1P2_ENSG00000169679</td>\n", "      <td>BUB1_-_111435363.23-P1P2|BUB1_-_111435372.23-P1P2</td>\n", "      <td>0.179895</td>\n", "      <td>5342.0</td>\n", "      <td>-1.522247</td>\n", "      <td>0.844107</td>\n", "      <td>6328.584473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAGCCAC-34</th>\n", "      <td>34</td>\n", "      <td>UBL5</td>\n", "      <td>ENSG00000198258</td>\n", "      <td>P1P2</td>\n", "      <td>9534_UBL5_P1P2_ENSG00000198258</td>\n", "      <td>UBL5_-_9938639.23-P1P2|UBL5_+_9938801.23-P1P2</td>\n", "      <td>0.105287</td>\n", "      <td>17305.0</td>\n", "      <td>0.384157</td>\n", "      <td>1.091537</td>\n", "      <td>15853.792969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAATAGTC-43</th>\n", "      <td>43</td>\n", "      <td>C9orf16</td>\n", "      <td>ENSG00000171159</td>\n", "      <td>P1P2</td>\n", "      <td>1131_C9orf16_P1P2_ENSG00000171159</td>\n", "      <td>C9orf16_+_130922603.23-P1P2|C9orf16_+_13092264...</td>\n", "      <td>0.099359</td>\n", "      <td>30244.0</td>\n", "      <td>3.721912</td>\n", "      <td>0.948277</td>\n", "      <td>31893.619141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGACAGCGT-28</th>\n", "      <td>28</td>\n", "      <td>TIMM9</td>\n", "      <td>ENSG00000100575</td>\n", "      <td>P1P2</td>\n", "      <td>8927_TIMM9_P1P2_ENSG00000100575</td>\n", "      <td>TIMM9_-_58893843.23-P1P2|TIMM9_-_58893848.23-P1P2</td>\n", "      <td>0.137623</td>\n", "      <td>8407.0</td>\n", "      <td>-0.975371</td>\n", "      <td>0.868942</td>\n", "      <td>9674.979492</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCGTC-45</th>\n", "      <td>45</td>\n", "      <td>ATP6V1D</td>\n", "      <td>ENSG00000100554</td>\n", "      <td>P1P2</td>\n", "      <td>682_ATP6V1D_P1P2_ENSG00000100554</td>\n", "      <td>ATP6V1D_+_67826485.23-P1P2|ATP6V1D_+_67826497....</td>\n", "      <td>0.100272</td>\n", "      <td>18350.0</td>\n", "      <td>0.428227</td>\n", "      <td>1.115052</td>\n", "      <td>16456.638672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCTCG-27</th>\n", "      <td>27</td>\n", "      <td>CNOT3</td>\n", "      <td>ENSG00000088038</td>\n", "      <td>P1P2</td>\n", "      <td>1718_CNOT3_P1P2_ENSG00000088038</td>\n", "      <td>CNOT3_+_54641532.23-P1P2|CNOT3_-_54641691.23-P1P2</td>\n", "      <td>0.093876</td>\n", "      <td>8671.0</td>\n", "      <td>-0.633593</td>\n", "      <td>0.813253</td>\n", "      <td>10662.125000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTGCGG-44</th>\n", "      <td>44</td>\n", "      <td>METTL3</td>\n", "      <td>ENSG00000165819</td>\n", "      <td>P1P2</td>\n", "      <td>5004_METTL3_P1P2_ENSG00000165819</td>\n", "      <td>METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...</td>\n", "      <td>0.107983</td>\n", "      <td>20568.0</td>\n", "      <td>1.054624</td>\n", "      <td>0.973352</td>\n", "      <td>21131.095703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTGCAGA-14</th>\n", "      <td>14</td>\n", "      <td>RPL5</td>\n", "      <td>ENSG00000122406</td>\n", "      <td>P1P2</td>\n", "      <td>7475_RPL5_P1P2_ENSG00000122406</td>\n", "      <td>RPL5_+_93297664.23-P1P2|RPL5_-_93297968.23-P1P2</td>\n", "      <td>0.128225</td>\n", "      <td>23568.0</td>\n", "      <td>1.676254</td>\n", "      <td>1.050055</td>\n", "      <td>22444.542969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTTACAC-25</th>\n", "      <td>25</td>\n", "      <td>SEC61B</td>\n", "      <td>ENSG00000106803</td>\n", "      <td>P1P2</td>\n", "      <td>7752_SEC61B_P1P2_ENSG00000106803</td>\n", "      <td>SEC61B_+_101984577.23-P1P2|SEC61B_-_101984591....</td>\n", "      <td>0.095009</td>\n", "      <td>13504.0</td>\n", "      <td>0.341625</td>\n", "      <td>0.855594</td>\n", "      <td>15783.187500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>310385 rows × 11 columns</p>\n", "</div>"], "text/plain": ["                     gem_group     gene          gene_id transcript  \\\n", "cell_barcode                                                          \n", "AAACCCAAGAAATCCA-27         27     NAF1  ENSG00000145414       P1P2   \n", "AAACCCAAGAACTTCC-31         31     BUB1  ENSG00000169679       P1P2   \n", "AAACCCAAGAAGCCAC-34         34     UBL5  ENSG00000198258       P1P2   \n", "AAACCCAAGAATAGTC-43         43  C9orf16  ENSG00000171159       P1P2   \n", "AAACCCAAGACAGCGT-28         28    TIMM9  ENSG00000100575       P1P2   \n", "...                        ...      ...              ...        ...   \n", "TTTGTTGTCTGTCGTC-45         45  ATP6V1D  ENSG00000100554       P1P2   \n", "TTTGTTGTCTGTCTCG-27         27    CNOT3  ENSG00000088038       P1P2   \n", "TTTGTTGTCTGTGCGG-44         44   METTL3  ENSG00000165819       P1P2   \n", "TTTGTTGTCTTGCAGA-14         14     RPL5  ENSG00000122406       P1P2   \n", "TTTGTTGTCTTTACAC-25         25   SEC61B  ENSG00000106803       P1P2   \n", "\n", "                                       gene_transcript  \\\n", "cell_barcode                                             \n", "AAACCCAAGAAATCCA-27     5449_NAF1_P1P2_ENSG00000145414   \n", "AAACCCAAGAACTTCC-31      935_BUB1_P1P2_ENSG00000169679   \n", "AAACCCAAGAAGCCAC-34     9534_UBL5_P1P2_ENSG00000198258   \n", "AAACCCAAGAATAGTC-43  1131_C9orf16_P1P2_ENSG00000171159   \n", "AAACCCAAGACAGCGT-28    8927_TIMM9_P1P2_ENSG00000100575   \n", "...                                                ...   \n", "TTTGTTGTCTGTCGTC-45   682_ATP6V1D_P1P2_ENSG00000100554   \n", "TTTGTTGTCTGTCTCG-27    1718_CNOT3_P1P2_ENSG00000088038   \n", "TTTGTTGTCTGTGCGG-44   5004_METTL3_P1P2_ENSG00000165819   \n", "TTTGTTGTCTTGCAGA-14     7475_RPL5_P1P2_ENSG00000122406   \n", "TTTGTTGTCTTTACAC-25   7752_SEC61B_P1P2_ENSG00000106803   \n", "\n", "                                                               sgID_AB  \\\n", "cell_barcode                                                             \n", "AAACCCAAGAAATCCA-27  NAF1_+_164087918.23-P1P2|NAF1_-_164087674.23-P1P2   \n", "AAACCCAAGAACTTCC-31  BUB1_-_111435363.23-P1P2|BUB1_-_111435372.23-P1P2   \n", "AAACCCAAGAAGCCAC-34      UBL5_-_9938639.23-P1P2|UBL5_+_9938801.23-P1P2   \n", "AAACCCAAGAATAGTC-43  C9orf16_+_130922603.23-P1P2|C9orf16_+_13092264...   \n", "AAACCCAAGACAGCGT-28  TIMM9_-_58893843.23-P1P2|TIMM9_-_58893848.23-P1P2   \n", "...                                                                ...   \n", "TTTGTTGTCTGTCGTC-45  ATP6V1D_+_67826485.23-P1P2|ATP6V1D_+_67826497....   \n", "TTTGTTGTCTGTCTCG-27  CNOT3_+_54641532.23-P1P2|CNOT3_-_54641691.23-P1P2   \n", "TTTGTTGTCTGTGCGG-44  METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...   \n", "TTTGTTGTCTTGCAGA-14    RPL5_+_93297664.23-P1P2|RPL5_-_93297968.23-P1P2   \n", "TTTGTTGTCTTTACAC-25  SEC61B_+_101984577.23-P1P2|SEC61B_-_101984591....   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \\\n", "cell_barcode                                                  \n", "AAACCCAAGAAATCCA-27     0.112083    11438.0        0.013047   \n", "AAACCCAAGAACTTCC-31     0.179895     5342.0       -1.522247   \n", "AAACCCAAGAAGCCAC-34     0.105287    17305.0        0.384157   \n", "AAACCCAAGAATAGTC-43     0.099359    30244.0        3.721912   \n", "AAACCCAAGACAGCGT-28     0.137623     8407.0       -0.975371   \n", "...                          ...        ...             ...   \n", "TTTGTTGTCTGTCGTC-45     0.100272    18350.0        0.428227   \n", "TTTGTTGTCTGTCTCG-27     0.093876     8671.0       -0.633593   \n", "TTTGTTGTCTGTGCGG-44     0.107983    20568.0        1.054624   \n", "TTTGTTGTCTTGCAGA-14     0.128225    23568.0        1.676254   \n", "TTTGTTGTCTTTACAC-25     0.095009    13504.0        0.341625   \n", "\n", "                     core_scale_factor  core_adjusted_UMI_count  \n", "cell_barcode                                                     \n", "AAACCCAAGAAATCCA-27           0.813253             14064.512695  \n", "AAACCCAAGAACTTCC-31           0.844107              6328.584473  \n", "AAACCCAAGAAGCCAC-34           1.091537             15853.792969  \n", "AAACCCAAGAATAGTC-43           0.948277             31893.619141  \n", "AAACCCAAGACAGCGT-28           0.868942              9674.979492  \n", "...                                ...                      ...  \n", "TTTGTTGTCTGTCGTC-45           1.115052             16456.638672  \n", "TTTGTTGTCTGTCTCG-27           0.813253             10662.125000  \n", "TTTGTTGTCTGTGCGG-44           0.973352             21131.095703  \n", "TTTGTTGTCTTGCAGA-14           1.050055             22444.542969  \n", "TTTGTTGTCTTTACAC-25           0.855594             15783.187500  \n", "\n", "[310385 rows x 11 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["f.obs"]}, {"cell_type": "code", "execution_count": 13, "id": "32259880", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>class</th>\n", "      <th>strand</th>\n", "      <th>length</th>\n", "      <th>in_matrix</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>cv</th>\n", "      <th>fano</th>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ENSG00000237491</th>\n", "      <td>LINC01409</td>\n", "      <td>chr1</td>\n", "      <td>778747</td>\n", "      <td>810065</td>\n", "      <td>gene_version10</td>\n", "      <td>+</td>\n", "      <td>31318</td>\n", "      <td>True</td>\n", "      <td>0.137594</td>\n", "      <td>0.380048</td>\n", "      <td>2.762105</td>\n", "      <td>1.049733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000228794</th>\n", "      <td>LINC01128</td>\n", "      <td>chr1</td>\n", "      <td>825138</td>\n", "      <td>868202</td>\n", "      <td>gene_version9</td>\n", "      <td>+</td>\n", "      <td>43064</td>\n", "      <td>True</td>\n", "      <td>0.256720</td>\n", "      <td>0.520162</td>\n", "      <td>2.026184</td>\n", "      <td>1.053944</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188976</th>\n", "      <td>NOC2L</td>\n", "      <td>chr1</td>\n", "      <td>944203</td>\n", "      <td>959309</td>\n", "      <td>gene_version11</td>\n", "      <td>-</td>\n", "      <td>15106</td>\n", "      <td>True</td>\n", "      <td>1.975144</td>\n", "      <td>1.707837</td>\n", "      <td>0.864665</td>\n", "      <td>1.476706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000187961</th>\n", "      <td>KLHL17</td>\n", "      <td>chr1</td>\n", "      <td>960584</td>\n", "      <td>965719</td>\n", "      <td>gene_version14</td>\n", "      <td>+</td>\n", "      <td>5135</td>\n", "      <td>True</td>\n", "      <td>0.119593</td>\n", "      <td>0.353702</td>\n", "      <td>2.957540</td>\n", "      <td>1.046089</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188290</th>\n", "      <td>HES4</td>\n", "      <td>chr1</td>\n", "      <td>998962</td>\n", "      <td>1000172</td>\n", "      <td>gene_version10</td>\n", "      <td>-</td>\n", "      <td>1210</td>\n", "      <td>True</td>\n", "      <td>0.249577</td>\n", "      <td>0.561933</td>\n", "      <td>2.251540</td>\n", "      <td>1.265214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278704</th>\n", "      <td>BX004987.1</td>\n", "      <td>GL000009.2</td>\n", "      <td>56140</td>\n", "      <td>58376</td>\n", "      <td>gene_version1</td>\n", "      <td>-</td>\n", "      <td>2236</td>\n", "      <td>True</td>\n", "      <td>0.241213</td>\n", "      <td>0.507266</td>\n", "      <td>2.102976</td>\n", "      <td>1.066768</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000274847</th>\n", "      <td>MAFIP</td>\n", "      <td>GL000194.1</td>\n", "      <td>53594</td>\n", "      <td>115055</td>\n", "      <td>gene_version1</td>\n", "      <td>-</td>\n", "      <td>61461</td>\n", "      <td>True</td>\n", "      <td>0.127525</td>\n", "      <td>0.361556</td>\n", "      <td>2.835168</td>\n", "      <td>1.025072</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278384</th>\n", "      <td>AL354822.1</td>\n", "      <td>GL000218.1</td>\n", "      <td>51867</td>\n", "      <td>54893</td>\n", "      <td>gene_version1</td>\n", "      <td>-</td>\n", "      <td>3026</td>\n", "      <td>True</td>\n", "      <td>0.248814</td>\n", "      <td>0.516552</td>\n", "      <td>2.076062</td>\n", "      <td>1.072394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000271254</th>\n", "      <td>AC240274.1</td>\n", "      <td>KI270711.1</td>\n", "      <td>4612</td>\n", "      <td>29626</td>\n", "      <td>gene_version6</td>\n", "      <td>-</td>\n", "      <td>25014</td>\n", "      <td>True</td>\n", "      <td>0.224144</td>\n", "      <td>0.505202</td>\n", "      <td>2.253916</td>\n", "      <td>1.138683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000276345</th>\n", "      <td>AC004556.3</td>\n", "      <td>KI270721.1</td>\n", "      <td>2585</td>\n", "      <td>11802</td>\n", "      <td>gene_version1</td>\n", "      <td>+</td>\n", "      <td>9217</td>\n", "      <td>True</td>\n", "      <td>0.343644</td>\n", "      <td>0.623236</td>\n", "      <td>1.813608</td>\n", "      <td>1.130305</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8563 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                  gene_name         chr   start      end           class  \\\n", "gene_id                                                                    \n", "ENSG00000237491   LINC01409        chr1  778747   810065  gene_version10   \n", "ENSG00000228794   LINC01128        chr1  825138   868202   gene_version9   \n", "ENSG00000188976       NOC2L        chr1  944203   959309  gene_version11   \n", "ENSG00000187961      KLHL17        chr1  960584   965719  gene_version14   \n", "ENSG00000188290        HES4        chr1  998962  1000172  gene_version10   \n", "...                     ...         ...     ...      ...             ...   \n", "ENSG00000278704  BX004987.1  GL000009.2   56140    58376   gene_version1   \n", "ENSG00000274847       MAFIP  GL000194.1   53594   115055   gene_version1   \n", "ENSG00000278384  AL354822.1  GL000218.1   51867    54893   gene_version1   \n", "ENSG00000271254  AC240274.1  KI270711.1    4612    29626   gene_version6   \n", "ENSG00000276345  AC004556.3  KI270721.1    2585    11802   gene_version1   \n", "\n", "                strand  length  in_matrix      mean       std        cv  \\\n", "gene_id                                                                   \n", "ENSG00000237491      +   31318       True  0.137594  0.380048  2.762105   \n", "ENSG00000228794      +   43064       True  0.256720  0.520162  2.026184   \n", "ENSG00000188976      -   15106       True  1.975144  1.707837  0.864665   \n", "ENSG00000187961      +    5135       True  0.119593  0.353702  2.957540   \n", "ENSG00000188290      -    1210       True  0.249577  0.561933  2.251540   \n", "...                ...     ...        ...       ...       ...       ...   \n", "ENSG00000278704      -    2236       True  0.241213  0.507266  2.102976   \n", "ENSG00000274847      -   61461       True  0.127525  0.361556  2.835168   \n", "ENSG00000278384      -    3026       True  0.248814  0.516552  2.076062   \n", "ENSG00000271254      -   25014       True  0.224144  0.505202  2.253916   \n", "ENSG00000276345      +    9217       True  0.343644  0.623236  1.813608   \n", "\n", "                     fano  \n", "gene_id                    \n", "ENSG00000237491  1.049733  \n", "ENSG00000228794  1.053944  \n", "ENSG00000188976  1.476706  \n", "ENSG00000187961  1.046089  \n", "ENSG00000188290  1.265214  \n", "...                   ...  \n", "ENSG00000278704  1.066768  \n", "ENSG00000274847  1.025072  \n", "ENSG00000278384  1.072394  \n", "ENSG00000271254  1.138683  \n", "ENSG00000276345  1.130305  \n", "\n", "[8563 rows x 12 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "g = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Replogle_et_al_2022/K562_essential_raw_singlecell_01.h5ad', backed=\"r\")\n", "g.var\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "2c4746e6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>core_scale_factor</th>\n", "      <th>core_adjusted_UMI_count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAAATCCA-27</th>\n", "      <td>27</td>\n", "      <td>NAF1</td>\n", "      <td>ENSG00000145414</td>\n", "      <td>P1P2</td>\n", "      <td>5449_NAF1_P1P2_ENSG00000145414</td>\n", "      <td>NAF1_+_164087918.23-P1P2|NAF1_-_164087674.23-P1P2</td>\n", "      <td>0.112083</td>\n", "      <td>11438.0</td>\n", "      <td>0.013047</td>\n", "      <td>0.813253</td>\n", "      <td>14064.512695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAACTTCC-31</th>\n", "      <td>31</td>\n", "      <td>BUB1</td>\n", "      <td>ENSG00000169679</td>\n", "      <td>P1P2</td>\n", "      <td>935_BUB1_P1P2_ENSG00000169679</td>\n", "      <td>BUB1_-_111435363.23-P1P2|BUB1_-_111435372.23-P1P2</td>\n", "      <td>0.179895</td>\n", "      <td>5342.0</td>\n", "      <td>-1.522247</td>\n", "      <td>0.844107</td>\n", "      <td>6328.584473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAGCCAC-34</th>\n", "      <td>34</td>\n", "      <td>UBL5</td>\n", "      <td>ENSG00000198258</td>\n", "      <td>P1P2</td>\n", "      <td>9534_UBL5_P1P2_ENSG00000198258</td>\n", "      <td>UBL5_-_9938639.23-P1P2|UBL5_+_9938801.23-P1P2</td>\n", "      <td>0.105287</td>\n", "      <td>17305.0</td>\n", "      <td>0.384157</td>\n", "      <td>1.091537</td>\n", "      <td>15853.792969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAATAGTC-43</th>\n", "      <td>43</td>\n", "      <td>C9orf16</td>\n", "      <td>ENSG00000171159</td>\n", "      <td>P1P2</td>\n", "      <td>1131_C9orf16_P1P2_ENSG00000171159</td>\n", "      <td>C9orf16_+_130922603.23-P1P2|C9orf16_+_13092264...</td>\n", "      <td>0.099359</td>\n", "      <td>30244.0</td>\n", "      <td>3.721912</td>\n", "      <td>0.948277</td>\n", "      <td>31893.619141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGACAGCGT-28</th>\n", "      <td>28</td>\n", "      <td>TIMM9</td>\n", "      <td>ENSG00000100575</td>\n", "      <td>P1P2</td>\n", "      <td>8927_TIMM9_P1P2_ENSG00000100575</td>\n", "      <td>TIMM9_-_58893843.23-P1P2|TIMM9_-_58893848.23-P1P2</td>\n", "      <td>0.137623</td>\n", "      <td>8407.0</td>\n", "      <td>-0.975371</td>\n", "      <td>0.868942</td>\n", "      <td>9674.979492</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCGTC-45</th>\n", "      <td>45</td>\n", "      <td>ATP6V1D</td>\n", "      <td>ENSG00000100554</td>\n", "      <td>P1P2</td>\n", "      <td>682_ATP6V1D_P1P2_ENSG00000100554</td>\n", "      <td>ATP6V1D_+_67826485.23-P1P2|ATP6V1D_+_67826497....</td>\n", "      <td>0.100272</td>\n", "      <td>18350.0</td>\n", "      <td>0.428227</td>\n", "      <td>1.115052</td>\n", "      <td>16456.638672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTCTCG-27</th>\n", "      <td>27</td>\n", "      <td>CNOT3</td>\n", "      <td>ENSG00000088038</td>\n", "      <td>P1P2</td>\n", "      <td>1718_CNOT3_P1P2_ENSG00000088038</td>\n", "      <td>CNOT3_+_54641532.23-P1P2|CNOT3_-_54641691.23-P1P2</td>\n", "      <td>0.093876</td>\n", "      <td>8671.0</td>\n", "      <td>-0.633593</td>\n", "      <td>0.813253</td>\n", "      <td>10662.125000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTGCGG-44</th>\n", "      <td>44</td>\n", "      <td>METTL3</td>\n", "      <td>ENSG00000165819</td>\n", "      <td>P1P2</td>\n", "      <td>5004_METTL3_P1P2_ENSG00000165819</td>\n", "      <td>METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...</td>\n", "      <td>0.107983</td>\n", "      <td>20568.0</td>\n", "      <td>1.054624</td>\n", "      <td>0.973352</td>\n", "      <td>21131.095703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTGCAGA-14</th>\n", "      <td>14</td>\n", "      <td>RPL5</td>\n", "      <td>ENSG00000122406</td>\n", "      <td>P1P2</td>\n", "      <td>7475_RPL5_P1P2_ENSG00000122406</td>\n", "      <td>RPL5_+_93297664.23-P1P2|RPL5_-_93297968.23-P1P2</td>\n", "      <td>0.128225</td>\n", "      <td>23568.0</td>\n", "      <td>1.676254</td>\n", "      <td>1.050055</td>\n", "      <td>22444.542969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTTACAC-25</th>\n", "      <td>25</td>\n", "      <td>SEC61B</td>\n", "      <td>ENSG00000106803</td>\n", "      <td>P1P2</td>\n", "      <td>7752_SEC61B_P1P2_ENSG00000106803</td>\n", "      <td>SEC61B_+_101984577.23-P1P2|SEC61B_-_101984591....</td>\n", "      <td>0.095009</td>\n", "      <td>13504.0</td>\n", "      <td>0.341625</td>\n", "      <td>0.855594</td>\n", "      <td>15783.187500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>310385 rows × 11 columns</p>\n", "</div>"], "text/plain": ["                     gem_group     gene          gene_id transcript  \\\n", "cell_barcode                                                          \n", "AAACCCAAGAAATCCA-27         27     NAF1  ENSG00000145414       P1P2   \n", "AAACCCAAGAACTTCC-31         31     BUB1  ENSG00000169679       P1P2   \n", "AAACCCAAGAAGCCAC-34         34     UBL5  ENSG00000198258       P1P2   \n", "AAACCCAAGAATAGTC-43         43  C9orf16  ENSG00000171159       P1P2   \n", "AAACCCAAGACAGCGT-28         28    TIMM9  ENSG00000100575       P1P2   \n", "...                        ...      ...              ...        ...   \n", "TTTGTTGTCTGTCGTC-45         45  ATP6V1D  ENSG00000100554       P1P2   \n", "TTTGTTGTCTGTCTCG-27         27    CNOT3  ENSG00000088038       P1P2   \n", "TTTGTTGTCTGTGCGG-44         44   METTL3  ENSG00000165819       P1P2   \n", "TTTGTTGTCTTGCAGA-14         14     RPL5  ENSG00000122406       P1P2   \n", "TTTGTTGTCTTTACAC-25         25   SEC61B  ENSG00000106803       P1P2   \n", "\n", "                                       gene_transcript  \\\n", "cell_barcode                                             \n", "AAACCCAAGAAATCCA-27     5449_NAF1_P1P2_ENSG00000145414   \n", "AAACCCAAGAACTTCC-31      935_BUB1_P1P2_ENSG00000169679   \n", "AAACCCAAGAAGCCAC-34     9534_UBL5_P1P2_ENSG00000198258   \n", "AAACCCAAGAATAGTC-43  1131_C9orf16_P1P2_ENSG00000171159   \n", "AAACCCAAGACAGCGT-28    8927_TIMM9_P1P2_ENSG00000100575   \n", "...                                                ...   \n", "TTTGTTGTCTGTCGTC-45   682_ATP6V1D_P1P2_ENSG00000100554   \n", "TTTGTTGTCTGTCTCG-27    1718_CNOT3_P1P2_ENSG00000088038   \n", "TTTGTTGTCTGTGCGG-44   5004_METTL3_P1P2_ENSG00000165819   \n", "TTTGTTGTCTTGCAGA-14     7475_RPL5_P1P2_ENSG00000122406   \n", "TTTGTTGTCTTTACAC-25   7752_SEC61B_P1P2_ENSG00000106803   \n", "\n", "                                                               sgID_AB  \\\n", "cell_barcode                                                             \n", "AAACCCAAGAAATCCA-27  NAF1_+_164087918.23-P1P2|NAF1_-_164087674.23-P1P2   \n", "AAACCCAAGAACTTCC-31  BUB1_-_111435363.23-P1P2|BUB1_-_111435372.23-P1P2   \n", "AAACCCAAGAAGCCAC-34      UBL5_-_9938639.23-P1P2|UBL5_+_9938801.23-P1P2   \n", "AAACCCAAGAATAGTC-43  C9orf16_+_130922603.23-P1P2|C9orf16_+_13092264...   \n", "AAACCCAAGACAGCGT-28  TIMM9_-_58893843.23-P1P2|TIMM9_-_58893848.23-P1P2   \n", "...                                                                ...   \n", "TTTGTTGTCTGTCGTC-45  ATP6V1D_+_67826485.23-P1P2|ATP6V1D_+_67826497....   \n", "TTTGTTGTCTGTCTCG-27  CNOT3_+_54641532.23-P1P2|CNOT3_-_54641691.23-P1P2   \n", "TTTGTTGTCTGTGCGG-44  METTL3_+_21979431.23-P1P2|METTL3_-_21979084.23...   \n", "TTTGTTGTCTTGCAGA-14    RPL5_+_93297664.23-P1P2|RPL5_-_93297968.23-P1P2   \n", "TTTGTTGTCTTTACAC-25  SEC61B_+_101984577.23-P1P2|SEC61B_-_101984591....   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \\\n", "cell_barcode                                                  \n", "AAACCCAAGAAATCCA-27     0.112083    11438.0        0.013047   \n", "AAACCCAAGAACTTCC-31     0.179895     5342.0       -1.522247   \n", "AAACCCAAGAAGCCAC-34     0.105287    17305.0        0.384157   \n", "AAACCCAAGAATAGTC-43     0.099359    30244.0        3.721912   \n", "AAACCCAAGACAGCGT-28     0.137623     8407.0       -0.975371   \n", "...                          ...        ...             ...   \n", "TTTGTTGTCTGTCGTC-45     0.100272    18350.0        0.428227   \n", "TTTGTTGTCTGTCTCG-27     0.093876     8671.0       -0.633593   \n", "TTTGTTGTCTGTGCGG-44     0.107983    20568.0        1.054624   \n", "TTTGTTGTCTTGCAGA-14     0.128225    23568.0        1.676254   \n", "TTTGTTGTCTTTACAC-25     0.095009    13504.0        0.341625   \n", "\n", "                     core_scale_factor  core_adjusted_UMI_count  \n", "cell_barcode                                                     \n", "AAACCCAAGAAATCCA-27           0.813253             14064.512695  \n", "AAACCCAAGAACTTCC-31           0.844107              6328.584473  \n", "AAACCCAAGAAGCCAC-34           1.091537             15853.792969  \n", "AAACCCAAGAATAGTC-43           0.948277             31893.619141  \n", "AAACCCAAGACAGCGT-28           0.868942              9674.979492  \n", "...                                ...                      ...  \n", "TTTGTTGTCTGTCGTC-45           1.115052             16456.638672  \n", "TTTGTTGTCTGTCTCG-27           0.813253             10662.125000  \n", "TTTGTTGTCTGTGCGG-44           0.973352             21131.095703  \n", "TTTGTTGTCTTGCAGA-14           1.050055             22444.542969  \n", "TTTGTTGTCTTTACAC-25           0.855594             15783.187500  \n", "\n", "[310385 rows x 11 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["g.obs"]}, {"cell_type": "code", "execution_count": 15, "id": "ae592182", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>cv</th>\n", "      <th>in_matrix</th>\n", "      <th>gini</th>\n", "      <th>clean_mean</th>\n", "      <th>clean_std</th>\n", "      <th>clean_cv</th>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ENSG00000237491</th>\n", "      <td>LINC01409</td>\n", "      <td>0.130014</td>\n", "      <td>0.043734</td>\n", "      <td>0.336381</td>\n", "      <td>True</td>\n", "      <td>0.179845</td>\n", "      <td>0.137393</td>\n", "      <td>0.046048</td>\n", "      <td>0.335153</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000228794</th>\n", "      <td>LINC01128</td>\n", "      <td>0.243296</td>\n", "      <td>0.064490</td>\n", "      <td>0.265067</td>\n", "      <td>True</td>\n", "      <td>0.142173</td>\n", "      <td>0.259054</td>\n", "      <td>0.067663</td>\n", "      <td>0.261195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188976</th>\n", "      <td>NOC2L</td>\n", "      <td>1.887061</td>\n", "      <td>0.327900</td>\n", "      <td>0.173762</td>\n", "      <td>True</td>\n", "      <td>0.094459</td>\n", "      <td>2.005543</td>\n", "      <td>0.326936</td>\n", "      <td>0.163016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000187961</th>\n", "      <td>KLHL17</td>\n", "      <td>0.114511</td>\n", "      <td>0.043057</td>\n", "      <td>0.376007</td>\n", "      <td>True</td>\n", "      <td>0.196132</td>\n", "      <td>0.120919</td>\n", "      <td>0.043987</td>\n", "      <td>0.363770</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188290</th>\n", "      <td>HES4</td>\n", "      <td>0.237708</td>\n", "      <td>0.084139</td>\n", "      <td>0.353960</td>\n", "      <td>True</td>\n", "      <td>0.182887</td>\n", "      <td>0.251248</td>\n", "      <td>0.088138</td>\n", "      <td>0.350802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278704</th>\n", "      <td>BX004987.1</td>\n", "      <td>0.227898</td>\n", "      <td>0.058213</td>\n", "      <td>0.255434</td>\n", "      <td>True</td>\n", "      <td>0.139306</td>\n", "      <td>0.240996</td>\n", "      <td>0.060908</td>\n", "      <td>0.252734</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000274847</th>\n", "      <td>MAFIP</td>\n", "      <td>0.121706</td>\n", "      <td>0.042970</td>\n", "      <td>0.353064</td>\n", "      <td>True</td>\n", "      <td>0.186434</td>\n", "      <td>0.128235</td>\n", "      <td>0.044821</td>\n", "      <td>0.349526</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000278384</th>\n", "      <td>AL354822.1</td>\n", "      <td>0.233260</td>\n", "      <td>0.057251</td>\n", "      <td>0.245438</td>\n", "      <td>True</td>\n", "      <td>0.134192</td>\n", "      <td>0.246777</td>\n", "      <td>0.060972</td>\n", "      <td>0.247074</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000271254</th>\n", "      <td>AC240274.1</td>\n", "      <td>0.213767</td>\n", "      <td>0.068033</td>\n", "      <td>0.318256</td>\n", "      <td>True</td>\n", "      <td>0.174092</td>\n", "      <td>0.228042</td>\n", "      <td>0.070520</td>\n", "      <td>0.309240</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000276345</th>\n", "      <td>AC004556.3</td>\n", "      <td>0.326259</td>\n", "      <td>0.083567</td>\n", "      <td>0.256137</td>\n", "      <td>True</td>\n", "      <td>0.140527</td>\n", "      <td>0.348605</td>\n", "      <td>0.085357</td>\n", "      <td>0.244852</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8563 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                  gene_name      mean       std        cv  in_matrix  \\\n", "gene_id                                                                \n", "ENSG00000237491   LINC01409  0.130014  0.043734  0.336381       True   \n", "ENSG00000228794   LINC01128  0.243296  0.064490  0.265067       True   \n", "ENSG00000188976       NOC2L  1.887061  0.327900  0.173762       True   \n", "ENSG00000187961      KLHL17  0.114511  0.043057  0.376007       True   \n", "ENSG00000188290        HES4  0.237708  0.084139  0.353960       True   \n", "...                     ...       ...       ...       ...        ...   \n", "ENSG00000278704  BX004987.1  0.227898  0.058213  0.255434       True   \n", "ENSG00000274847       MAFIP  0.121706  0.042970  0.353064       True   \n", "ENSG00000278384  AL354822.1  0.233260  0.057251  0.245438       True   \n", "ENSG00000271254  AC240274.1  0.213767  0.068033  0.318256       True   \n", "ENSG00000276345  AC004556.3  0.326259  0.083567  0.256137       True   \n", "\n", "                     gini  clean_mean  clean_std  clean_cv  \n", "gene_id                                                     \n", "ENSG00000237491  0.179845    0.137393   0.046048  0.335153  \n", "ENSG00000228794  0.142173    0.259054   0.067663  0.261195  \n", "ENSG00000188976  0.094459    2.005543   0.326936  0.163016  \n", "ENSG00000187961  0.196132    0.120919   0.043987  0.363770  \n", "ENSG00000188290  0.182887    0.251248   0.088138  0.350802  \n", "...                   ...         ...        ...       ...  \n", "ENSG00000278704  0.139306    0.240996   0.060908  0.252734  \n", "ENSG00000274847  0.186434    0.128235   0.044821  0.349526  \n", "ENSG00000278384  0.134192    0.246777   0.060972  0.247074  \n", "ENSG00000271254  0.174092    0.228042   0.070520  0.309240  \n", "ENSG00000276345  0.140527    0.348605   0.085357  0.244852  \n", "\n", "[8563 rows x 9 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "h = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Replogle_et_al_2022/K562_essential_raw_bulk_01.h5ad', backed=\"r\")\n", "h.var\n"]}, {"cell_type": "code", "execution_count": 16, "id": "20731b65", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>UMI_count_unfiltered</th>\n", "      <th>num_cells_unfiltered</th>\n", "      <th>num_cells_filtered</th>\n", "      <th>control_expr</th>\n", "      <th>fold_expr</th>\n", "      <th>pct_expr</th>\n", "      <th>core_control</th>\n", "      <th>mean_leverage_score</th>\n", "      <th>std_leverage_score</th>\n", "      <th>energy_test_p_value</th>\n", "      <th>and<PERSON>_darling_counts</th>\n", "      <th>mann_whitney_counts</th>\n", "      <th>z_gemgroup_UMI</th>\n", "      <th>mitopercent</th>\n", "      <th>TE_ratio</th>\n", "      <th>cnv_score_z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_transcript</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10023_ZC3H18_P1P2_ENSG00000158545</th>\n", "      <td>14663.544922</td>\n", "      <td>33</td>\n", "      <td>32.0</td>\n", "      <td>1.364413</td>\n", "      <td>0.235040</td>\n", "      <td>-0.764960</td>\n", "      <td>False</td>\n", "      <td>0.457316</td>\n", "      <td>0.827434</td>\n", "      <td>0.000200</td>\n", "      <td>99</td>\n", "      <td>84</td>\n", "      <td>0.245197</td>\n", "      <td>0.124813</td>\n", "      <td>0.014071</td>\n", "      <td>-0.572571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10029_ZC3H8_P1P2_ENSG00000144161</th>\n", "      <td>13993.951172</td>\n", "      <td>21</td>\n", "      <td>21.0</td>\n", "      <td>0.611213</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>False</td>\n", "      <td>0.586959</td>\n", "      <td>0.840791</td>\n", "      <td>0.000200</td>\n", "      <td>11</td>\n", "      <td>6</td>\n", "      <td>-0.068740</td>\n", "      <td>0.114389</td>\n", "      <td>0.015762</td>\n", "      <td>0.594484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10040_ZCCHC9_P1P2_ENSG00000131732</th>\n", "      <td>12458.108398</td>\n", "      <td>55</td>\n", "      <td>54.0</td>\n", "      <td>0.686704</td>\n", "      <td>0.232753</td>\n", "      <td>-0.767247</td>\n", "      <td>False</td>\n", "      <td>1.120659</td>\n", "      <td>0.927980</td>\n", "      <td>0.000100</td>\n", "      <td>457</td>\n", "      <td>308</td>\n", "      <td>-0.228909</td>\n", "      <td>0.094516</td>\n", "      <td>0.015937</td>\n", "      <td>2.824967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10041_ZCRB1_P1P2_ENSG00000139168</th>\n", "      <td>13984.878906</td>\n", "      <td>149</td>\n", "      <td>146.0</td>\n", "      <td>0.924870</td>\n", "      <td>0.057199</td>\n", "      <td>-0.942801</td>\n", "      <td>False</td>\n", "      <td>0.268099</td>\n", "      <td>0.792387</td>\n", "      <td>0.000100</td>\n", "      <td>291</td>\n", "      <td>293</td>\n", "      <td>0.061719</td>\n", "      <td>0.121195</td>\n", "      <td>0.014642</td>\n", "      <td>-0.999272</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1004_C17orf58_P1P2_ENSG00000186665</th>\n", "      <td>13321.516602</td>\n", "      <td>60</td>\n", "      <td>58.0</td>\n", "      <td>0.713534</td>\n", "      <td>0.168724</td>\n", "      <td>-0.831276</td>\n", "      <td>False</td>\n", "      <td>0.337023</td>\n", "      <td>1.024798</td>\n", "      <td>0.205079</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0.038637</td>\n", "      <td>0.101197</td>\n", "      <td>0.013686</td>\n", "      <td>1.830636</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9946_YTHDC1_P1P2_ENSG00000083896</th>\n", "      <td>10837.282227</td>\n", "      <td>297</td>\n", "      <td>237.0</td>\n", "      <td>1.100145</td>\n", "      <td>0.524860</td>\n", "      <td>-0.475140</td>\n", "      <td>False</td>\n", "      <td>0.775323</td>\n", "      <td>1.124331</td>\n", "      <td>0.000100</td>\n", "      <td>914</td>\n", "      <td>675</td>\n", "      <td>-0.123780</td>\n", "      <td>0.117485</td>\n", "      <td>0.015646</td>\n", "      <td>2.588443</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9958_YY1_P1P2_ENSG00000100811</th>\n", "      <td>13071.000000</td>\n", "      <td>86</td>\n", "      <td>82.0</td>\n", "      <td>1.787731</td>\n", "      <td>0.742124</td>\n", "      <td>-0.257876</td>\n", "      <td>False</td>\n", "      <td>0.345290</td>\n", "      <td>0.890078</td>\n", "      <td>0.013999</td>\n", "      <td>32</td>\n", "      <td>15</td>\n", "      <td>-0.072767</td>\n", "      <td>0.101909</td>\n", "      <td>0.012779</td>\n", "      <td>0.511820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9969_ZBTB11_P1P2_ENSG00000066422</th>\n", "      <td>14092.860352</td>\n", "      <td>150</td>\n", "      <td>146.0</td>\n", "      <td>0.822979</td>\n", "      <td>0.357662</td>\n", "      <td>-0.642338</td>\n", "      <td>False</td>\n", "      <td>0.292767</td>\n", "      <td>1.102415</td>\n", "      <td>0.179782</td>\n", "      <td>35</td>\n", "      <td>33</td>\n", "      <td>0.080599</td>\n", "      <td>0.104848</td>\n", "      <td>0.013659</td>\n", "      <td>0.044934</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9975_ZBTB17_P1P2_ENSG00000116809</th>\n", "      <td>12124.895508</td>\n", "      <td>172</td>\n", "      <td>160.0</td>\n", "      <td>0.138009</td>\n", "      <td>0.144780</td>\n", "      <td>-0.855220</td>\n", "      <td>False</td>\n", "      <td>0.594186</td>\n", "      <td>1.040095</td>\n", "      <td>0.000100</td>\n", "      <td>420</td>\n", "      <td>307</td>\n", "      <td>-0.194360</td>\n", "      <td>0.104817</td>\n", "      <td>0.015217</td>\n", "      <td>0.758179</td>\n", "    </tr>\n", "    <tr>\n", "      <th>997_C16orf86_P1P2_ENSG00000159761</th>\n", "      <td>14110.484375</td>\n", "      <td>188</td>\n", "      <td>184.0</td>\n", "      <td>0.016937</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>False</td>\n", "      <td>0.267521</td>\n", "      <td>0.965351</td>\n", "      <td>0.814419</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.153926</td>\n", "      <td>0.106475</td>\n", "      <td>0.013555</td>\n", "      <td>-0.212223</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2285 rows × 16 columns</p>\n", "</div>"], "text/plain": ["                                    UMI_count_unfiltered  \\\n", "gene_transcript                                            \n", "10023_ZC3H18_P1P2_ENSG00000158545           14663.544922   \n", "10029_ZC3H8_P1P2_ENSG00000144161            13993.951172   \n", "10040_ZCCHC9_P1P2_ENSG00000131732           12458.108398   \n", "10041_ZCRB1_P1P2_ENSG00000139168            13984.878906   \n", "1004_C17orf58_P1P2_ENSG00000186665          13321.516602   \n", "...                                                  ...   \n", "9946_YTHDC1_P1P2_ENSG00000083896            10837.282227   \n", "9958_YY1_P1P2_ENSG00000100811               13071.000000   \n", "9969_ZBTB11_P1P2_ENSG00000066422            14092.860352   \n", "9975_ZBTB17_P1P2_ENSG00000116809            12124.895508   \n", "997_C16orf86_P1P2_ENSG00000159761           14110.484375   \n", "\n", "                                    num_cells_unfiltered  num_cells_filtered  \\\n", "gene_transcript                                                                \n", "10023_ZC3H18_P1P2_ENSG00000158545                     33                32.0   \n", "10029_ZC3H8_P1P2_ENSG00000144161                      21                21.0   \n", "10040_ZCCHC9_P1P2_ENSG00000131732                     55                54.0   \n", "10041_ZCRB1_P1P2_ENSG00000139168                     149               146.0   \n", "1004_C17orf58_P1P2_ENSG00000186665                    60                58.0   \n", "...                                                  ...                 ...   \n", "9946_YTHDC1_P1P2_ENSG00000083896                     297               237.0   \n", "9958_YY1_P1P2_ENSG00000100811                         86                82.0   \n", "9969_ZBTB11_P1P2_ENSG00000066422                     150               146.0   \n", "9975_ZBTB17_P1P2_ENSG00000116809                     172               160.0   \n", "997_C16orf86_P1P2_ENSG00000159761                    188               184.0   \n", "\n", "                                    control_expr  fold_expr  pct_expr  \\\n", "gene_transcript                                                         \n", "10023_ZC3H18_P1P2_ENSG00000158545       1.364413   0.235040 -0.764960   \n", "10029_ZC3H8_P1P2_ENSG00000144161        0.611213   0.000000 -1.000000   \n", "10040_ZCCHC9_P1P2_ENSG00000131732       0.686704   0.232753 -0.767247   \n", "10041_ZCRB1_P1P2_ENSG00000139168        0.924870   0.057199 -0.942801   \n", "1004_C17orf58_P1P2_ENSG00000186665      0.713534   0.168724 -0.831276   \n", "...                                          ...        ...       ...   \n", "9946_YTHDC1_P1P2_ENSG00000083896        1.100145   0.524860 -0.475140   \n", "9958_YY1_P1P2_ENSG00000100811           1.787731   0.742124 -0.257876   \n", "9969_ZBTB11_P1P2_ENSG00000066422        0.822979   0.357662 -0.642338   \n", "9975_ZBTB17_P1P2_ENSG00000116809        0.138009   0.144780 -0.855220   \n", "997_C16orf86_P1P2_ENSG00000159761       0.016937   0.000000 -1.000000   \n", "\n", "                                    core_control  mean_leverage_score  \\\n", "gene_transcript                                                         \n", "10023_ZC3H18_P1P2_ENSG00000158545          False             0.457316   \n", "10029_ZC3H8_P1P2_ENSG00000144161           False             0.586959   \n", "10040_ZCCHC9_P1P2_ENSG00000131732          False             1.120659   \n", "10041_ZCRB1_P1P2_ENSG00000139168           False             0.268099   \n", "1004_C17orf58_P1P2_ENSG00000186665         False             0.337023   \n", "...                                          ...                  ...   \n", "9946_YTHDC1_P1P2_ENSG00000083896           False             0.775323   \n", "9958_YY1_P1P2_ENSG00000100811              False             0.345290   \n", "9969_ZBTB11_P1P2_ENSG00000066422           False             0.292767   \n", "9975_ZBTB17_P1P2_ENSG00000116809           False             0.594186   \n", "997_C16orf86_P1P2_ENSG00000159761          False             0.267521   \n", "\n", "                                    std_leverage_score  energy_test_p_value  \\\n", "gene_transcript                                                               \n", "10023_ZC3H18_P1P2_ENSG00000158545             0.827434             0.000200   \n", "10029_ZC3H8_P1P2_ENSG00000144161              0.840791             0.000200   \n", "10040_ZCCHC9_P1P2_ENSG00000131732             0.927980             0.000100   \n", "10041_ZCRB1_P1P2_ENSG00000139168              0.792387             0.000100   \n", "1004_C17orf58_P1P2_ENSG00000186665            1.024798             0.205079   \n", "...                                                ...                  ...   \n", "9946_YTHDC1_P1P2_ENSG00000083896              1.124331             0.000100   \n", "9958_YY1_P1P2_ENSG00000100811                 0.890078             0.013999   \n", "9969_ZBTB11_P1P2_ENSG00000066422              1.102415             0.179782   \n", "9975_ZBTB17_P1P2_ENSG00000116809              1.040095             0.000100   \n", "997_C16orf86_P1P2_ENSG00000159761             0.965351             0.814419   \n", "\n", "                                    and<PERSON>_darling_counts  \\\n", "gene_transcript                                               \n", "10023_ZC3H18_P1P2_ENSG00000158545                        99   \n", "10029_ZC3H8_P1P2_ENSG00000144161                         11   \n", "10040_ZCCHC9_P1P2_ENSG00000131732                       457   \n", "10041_ZCRB1_P1P2_ENSG00000139168                        291   \n", "1004_C17orf58_P1P2_ENSG00000186665                        3   \n", "...                                                     ...   \n", "9946_YTHDC1_P1P2_ENSG00000083896                        914   \n", "9958_YY1_P1P2_ENSG00000100811                            32   \n", "9969_ZBTB11_P1P2_ENSG00000066422                         35   \n", "9975_ZBTB17_P1P2_ENSG00000116809                        420   \n", "997_C16orf86_P1P2_ENSG00000159761                         0   \n", "\n", "                                    mann_whitney_counts  z_gemgroup_UMI  \\\n", "gene_transcript                                                           \n", "10023_ZC3H18_P1P2_ENSG00000158545                    84        0.245197   \n", "10029_ZC3H8_P1P2_ENSG00000144161                      6       -0.068740   \n", "10040_ZCCHC9_P1P2_ENSG00000131732                   308       -0.228909   \n", "10041_ZCRB1_P1P2_ENSG00000139168                    293        0.061719   \n", "1004_C17orf58_P1P2_ENSG00000186665                    3        0.038637   \n", "...                                                 ...             ...   \n", "9946_YTHDC1_P1P2_ENSG00000083896                    675       -0.123780   \n", "9958_YY1_P1P2_ENSG00000100811                        15       -0.072767   \n", "9969_ZBTB11_P1P2_ENSG00000066422                     33        0.080599   \n", "9975_ZBTB17_P1P2_ENSG00000116809                    307       -0.194360   \n", "997_C16orf86_P1P2_ENSG00000159761                     0        0.153926   \n", "\n", "                                    mitopercent  TE_ratio  cnv_score_z  \n", "gene_transcript                                                         \n", "10023_ZC3H18_P1P2_ENSG00000158545      0.124813  0.014071    -0.572571  \n", "10029_ZC3H8_P1P2_ENSG00000144161       0.114389  0.015762     0.594484  \n", "10040_ZCCHC9_P1P2_ENSG00000131732      0.094516  0.015937     2.824967  \n", "10041_ZCRB1_P1P2_ENSG00000139168       0.121195  0.014642    -0.999272  \n", "1004_C17orf58_P1P2_ENSG00000186665     0.101197  0.013686     1.830636  \n", "...                                         ...       ...          ...  \n", "9946_YTHDC1_P1P2_ENSG00000083896       0.117485  0.015646     2.588443  \n", "9958_YY1_P1P2_ENSG00000100811          0.101909  0.012779     0.511820  \n", "9969_ZBTB11_P1P2_ENSG00000066422       0.104848  0.013659     0.044934  \n", "9975_ZBTB17_P1P2_ENSG00000116809       0.104817  0.015217     0.758179  \n", "997_C16orf86_P1P2_ENSG00000159761      0.106475  0.013555    -0.212223  \n", "\n", "[2285 rows x 16 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["h.obs"]}, {"cell_type": "code", "execution_count": 17, "id": "a70961bb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>class</th>\n", "      <th>strand</th>\n", "      <th>length</th>\n", "      <th>in_matrix</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>cv</th>\n", "      <th>fano</th>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ENSG00000237491</th>\n", "      <td>LINC01409</td>\n", "      <td>chr1</td>\n", "      <td>778747</td>\n", "      <td>810065</td>\n", "      <td>gene_version10</td>\n", "      <td>+</td>\n", "      <td>31318</td>\n", "      <td>True</td>\n", "      <td>0.155002</td>\n", "      <td>0.417462</td>\n", "      <td>2.693264</td>\n", "      <td>1.124335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000228794</th>\n", "      <td>LINC01128</td>\n", "      <td>chr1</td>\n", "      <td>825138</td>\n", "      <td>868202</td>\n", "      <td>gene_version9</td>\n", "      <td>+</td>\n", "      <td>43064</td>\n", "      <td>True</td>\n", "      <td>0.287165</td>\n", "      <td>0.572515</td>\n", "      <td>1.993682</td>\n", "      <td>1.141413</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188976</th>\n", "      <td>NOC2L</td>\n", "      <td>chr1</td>\n", "      <td>944203</td>\n", "      <td>959309</td>\n", "      <td>gene_version11</td>\n", "      <td>-</td>\n", "      <td>15106</td>\n", "      <td>True</td>\n", "      <td>0.939766</td>\n", "      <td>1.160949</td>\n", "      <td>1.235359</td>\n", "      <td>1.434190</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188290</th>\n", "      <td>HES4</td>\n", "      <td>chr1</td>\n", "      <td>998962</td>\n", "      <td>1000172</td>\n", "      <td>gene_version10</td>\n", "      <td>-</td>\n", "      <td>1210</td>\n", "      <td>True</td>\n", "      <td>2.612689</td>\n", "      <td>3.524809</td>\n", "      <td>1.349112</td>\n", "      <td>4.755362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000187608</th>\n", "      <td>ISG15</td>\n", "      <td>chr1</td>\n", "      <td>1001138</td>\n", "      <td>1014540</td>\n", "      <td>gene_version10</td>\n", "      <td>+</td>\n", "      <td>13402</td>\n", "      <td>True</td>\n", "      <td>3.091917</td>\n", "      <td>2.869021</td>\n", "      <td>0.927910</td>\n", "      <td>2.662194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198786</th>\n", "      <td>MT-ND5</td>\n", "      <td>chrM</td>\n", "      <td>12337</td>\n", "      <td>14148</td>\n", "      <td>gene_version2</td>\n", "      <td>+</td>\n", "      <td>1811</td>\n", "      <td>True</td>\n", "      <td>13.657433</td>\n", "      <td>11.270500</td>\n", "      <td>0.825228</td>\n", "      <td>9.300736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198695</th>\n", "      <td>MT-ND6</td>\n", "      <td>chrM</td>\n", "      <td>14149</td>\n", "      <td>14673</td>\n", "      <td>gene_version2</td>\n", "      <td>-</td>\n", "      <td>524</td>\n", "      <td>True</td>\n", "      <td>8.794392</td>\n", "      <td>8.041733</td>\n", "      <td>0.914416</td>\n", "      <td>7.353489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198727</th>\n", "      <td>MT-CYB</td>\n", "      <td>chrM</td>\n", "      <td>14747</td>\n", "      <td>15887</td>\n", "      <td>gene_version2</td>\n", "      <td>+</td>\n", "      <td>1140</td>\n", "      <td>True</td>\n", "      <td>85.718658</td>\n", "      <td>66.273308</td>\n", "      <td>0.773149</td>\n", "      <td>51.239151</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000271254</th>\n", "      <td>AC240274.1</td>\n", "      <td>KI270711.1</td>\n", "      <td>4612</td>\n", "      <td>29626</td>\n", "      <td>gene_version6</td>\n", "      <td>-</td>\n", "      <td>25014</td>\n", "      <td>True</td>\n", "      <td>0.292539</td>\n", "      <td>0.621219</td>\n", "      <td>2.123542</td>\n", "      <td>1.319184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000276345</th>\n", "      <td>AC004556.3</td>\n", "      <td>KI270721.1</td>\n", "      <td>2585</td>\n", "      <td>11802</td>\n", "      <td>gene_version1</td>\n", "      <td>+</td>\n", "      <td>9217</td>\n", "      <td>True</td>\n", "      <td>0.651829</td>\n", "      <td>0.938046</td>\n", "      <td>1.439099</td>\n", "      <td>1.349941</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8882 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                  gene_name         chr    start      end           class  \\\n", "gene_id                                                                     \n", "ENSG00000237491   LINC01409        chr1   778747   810065  gene_version10   \n", "ENSG00000228794   LINC01128        chr1   825138   868202   gene_version9   \n", "ENSG00000188976       NOC2L        chr1   944203   959309  gene_version11   \n", "ENSG00000188290        HES4        chr1   998962  1000172  gene_version10   \n", "ENSG00000187608       ISG15        chr1  1001138  1014540  gene_version10   \n", "...                     ...         ...      ...      ...             ...   \n", "ENSG00000198786      MT-ND5        chrM    12337    14148   gene_version2   \n", "ENSG00000198695      MT-ND6        chrM    14149    14673   gene_version2   \n", "ENSG00000198727      MT-CYB        chrM    14747    15887   gene_version2   \n", "ENSG00000271254  AC240274.1  KI270711.1     4612    29626   gene_version6   \n", "ENSG00000276345  AC004556.3  KI270721.1     2585    11802   gene_version1   \n", "\n", "                strand  length  in_matrix       mean        std        cv  \\\n", "gene_id                                                                     \n", "ENSG00000237491      +   31318       True   0.155002   0.417462  2.693264   \n", "ENSG00000228794      +   43064       True   0.287165   0.572515  1.993682   \n", "ENSG00000188976      -   15106       True   0.939766   1.160949  1.235359   \n", "ENSG00000188290      -    1210       True   2.612689   3.524809  1.349112   \n", "ENSG00000187608      +   13402       True   3.091917   2.869021  0.927910   \n", "...                ...     ...        ...        ...        ...       ...   \n", "ENSG00000198786      +    1811       True  13.657433  11.270500  0.825228   \n", "ENSG00000198695      -     524       True   8.794392   8.041733  0.914416   \n", "ENSG00000198727      +    1140       True  85.718658  66.273308  0.773149   \n", "ENSG00000271254      -   25014       True   0.292539   0.621219  2.123542   \n", "ENSG00000276345      +    9217       True   0.651829   0.938046  1.439099   \n", "\n", "                      fano  \n", "gene_id                     \n", "ENSG00000237491   1.124335  \n", "ENSG00000228794   1.141413  \n", "ENSG00000188976   1.434190  \n", "ENSG00000188290   4.755362  \n", "ENSG00000187608   2.662194  \n", "...                    ...  \n", "ENSG00000198786   9.300736  \n", "ENSG00000198695   7.353489  \n", "ENSG00000198727  51.239151  \n", "ENSG00000271254   1.319184  \n", "ENSG00000276345   1.349941  \n", "\n", "[8882 rows x 12 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["i = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Nadig_et_al_2025/GSE264667_jurkat_raw_singlecell_01.h5ad', backed=\"r\")\n", "i.var\n"]}, {"cell_type": "code", "execution_count": 18, "id": "404abd41", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAAACTGT-27</th>\n", "      <td>27</td>\n", "      <td>NELFE</td>\n", "      <td>ENSG00000204356</td>\n", "      <td>P1P2</td>\n", "      <td>5601_NELFE_P1P2_ENSG00000204356</td>\n", "      <td>NELFE_+_31926720.23-P1P2|NELFE_-_31926676.23-P1P2</td>\n", "      <td>0.063665</td>\n", "      <td>13194.0</td>\n", "      <td>0.106271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAATCCA-12</th>\n", "      <td>12</td>\n", "      <td>EMC7</td>\n", "      <td>ENSG00000134153</td>\n", "      <td>P1P2</td>\n", "      <td>2616_EMC7_P1P2_ENSG00000134153</td>\n", "      <td>EMC7_+_34394068.23-P1P2|EMC7_-_34393868.23-P1P2</td>\n", "      <td>0.049182</td>\n", "      <td>9719.0</td>\n", "      <td>-0.054858</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAATTCG-56</th>\n", "      <td>56</td>\n", "      <td>TAF1D</td>\n", "      <td>ENSG00000166012</td>\n", "      <td>P2</td>\n", "      <td>8659_TAF1D_P2_ENSG00000166012</td>\n", "      <td>TAF1D_-_93471390.23-P2|TAF1D_+_93471338.23-P2</td>\n", "      <td>0.055632</td>\n", "      <td>11576.0</td>\n", "      <td>-0.138458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAAGCCAC-26</th>\n", "      <td>26</td>\n", "      <td>EIF2B2</td>\n", "      <td>ENSG00000119718</td>\n", "      <td>P1P2</td>\n", "      <td>2536_EIF2B2_P1P2_ENSG00000119718</td>\n", "      <td>EIF2B2_-_75469671.23-P1P2|EIF2B2_-_75469856.23...</td>\n", "      <td>0.044284</td>\n", "      <td>12849.0</td>\n", "      <td>-0.422243</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGACAACTA-5</th>\n", "      <td>5</td>\n", "      <td>RPP30</td>\n", "      <td>ENSG00000148688</td>\n", "      <td>P1P2</td>\n", "      <td>7491_RPP30_P1P2_ENSG00000148688</td>\n", "      <td>RPP30_+_92631924.23-P1P2|RPP30_-_92631746.23-P1P2</td>\n", "      <td>0.072090</td>\n", "      <td>11555.0</td>\n", "      <td>-1.806991</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTGTAAGC-21</th>\n", "      <td>21</td>\n", "      <td>UHRF1</td>\n", "      <td>ENSG00000276043</td>\n", "      <td>P1P2</td>\n", "      <td>9583_UHRF1_P1P2_ENSG00000276043</td>\n", "      <td>UHRF1_+_4910126.23-P1P2|UHRF1_+_4909924.23-P1P2</td>\n", "      <td>0.065960</td>\n", "      <td>5003.0</td>\n", "      <td>-1.069798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTAGCTT-9</th>\n", "      <td>9</td>\n", "      <td>EIF1AX</td>\n", "      <td>ENSG00000173674</td>\n", "      <td>P1P2</td>\n", "      <td>2527_EIF1AX_P1P2_ENSG00000173674</td>\n", "      <td>EIF1AX_+_20159985.23-P1P2|EIF1AX_-_20159725.23...</td>\n", "      <td>0.063649</td>\n", "      <td>12726.0</td>\n", "      <td>0.727394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTCCTAA-56</th>\n", "      <td>56</td>\n", "      <td>MCM3</td>\n", "      <td>ENSG00000112118</td>\n", "      <td>P1P2</td>\n", "      <td>4900_MCM3_P1P2_ENSG00000112118</td>\n", "      <td>MCM3_-_52149354.23-P1P2|MCM3_-_52149307.23-P1P2</td>\n", "      <td>0.030737</td>\n", "      <td>9630.0</td>\n", "      <td>-0.521912</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTCGTAT-29</th>\n", "      <td>29</td>\n", "      <td>MED20</td>\n", "      <td>ENSG00000124641</td>\n", "      <td>P1P2</td>\n", "      <td>4943_MED20_P1P2_ENSG00000124641</td>\n", "      <td>MED20_+_41888837.23-P1P2|MED20_+_41888805.23-P1P2</td>\n", "      <td>0.056258</td>\n", "      <td>5777.0</td>\n", "      <td>-1.199783</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTGGGCG-42</th>\n", "      <td>42</td>\n", "      <td>RPF1</td>\n", "      <td>ENSG00000117133</td>\n", "      <td>P1P2</td>\n", "      <td>7426_RPF1_P1P2_ENSG00000117133</td>\n", "      <td>RPF1_-_84945270.23-P1P2|RPF1_+_84945350.23-P1P2</td>\n", "      <td>0.041012</td>\n", "      <td>7632.0</td>\n", "      <td>-0.841916</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>262956 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                     gem_group    gene          gene_id transcript  \\\n", "cell_barcode                                                         \n", "AAACCCAAGAAACTGT-27         27   NELFE  ENSG00000204356       P1P2   \n", "AAACCCAAGAAATCCA-12         12    EMC7  ENSG00000134153       P1P2   \n", "AAACCCAAGAAATTCG-56         56   TAF1D  ENSG00000166012         P2   \n", "AAACCCAAGAAGCCAC-26         26  EIF2B2  ENSG00000119718       P1P2   \n", "AAACCCAAGACAACTA-5           5   RPP30  ENSG00000148688       P1P2   \n", "...                        ...     ...              ...        ...   \n", "TTTGTTGTCTGTAAGC-21         21   UHRF1  ENSG00000276043       P1P2   \n", "TTTGTTGTCTTAGCTT-9           9  EIF1AX  ENSG00000173674       P1P2   \n", "TTTGTTGTCTTCCTAA-56         56    MCM3  ENSG00000112118       P1P2   \n", "TTTGTTGTCTTCGTAT-29         29   MED20  ENSG00000124641       P1P2   \n", "TTTGTTGTCTTGGGCG-42         42    RPF1  ENSG00000117133       P1P2   \n", "\n", "                                      gene_transcript  \\\n", "cell_barcode                                            \n", "AAACCCAAGAAACTGT-27   5601_NELFE_P1P2_ENSG00000204356   \n", "AAACCCAAGAAATCCA-12    2616_EMC7_P1P2_ENSG00000134153   \n", "AAACCCAAGAAATTCG-56     8659_TAF1D_P2_ENSG00000166012   \n", "AAACCCAAGAAGCCAC-26  2536_EIF2B2_P1P2_ENSG00000119718   \n", "AAACCCAAGACAACTA-5    7491_RPP30_P1P2_ENSG00000148688   \n", "...                                               ...   \n", "TTTGTTGTCTGTAAGC-21   9583_UHRF1_P1P2_ENSG00000276043   \n", "TTTGTTGTCTTAGCTT-9   2527_EIF1AX_P1P2_ENSG00000173674   \n", "TTTGTTGTCTTCCTAA-56    4900_MCM3_P1P2_ENSG00000112118   \n", "TTTGTTGTCTTCGTAT-29   4943_MED20_P1P2_ENSG00000124641   \n", "TTTGTTGTCTTGGGCG-42    7426_RPF1_P1P2_ENSG00000117133   \n", "\n", "                                                               sgID_AB  \\\n", "cell_barcode                                                             \n", "AAACCCAAGAAACTGT-27  NELFE_+_31926720.23-P1P2|NELFE_-_31926676.23-P1P2   \n", "AAACCCAAGAAATCCA-12    EMC7_+_34394068.23-P1P2|EMC7_-_34393868.23-P1P2   \n", "AAACCCAAGAAATTCG-56      TAF1D_-_93471390.23-P2|TAF1D_+_93471338.23-P2   \n", "AAACCCAAGAAGCCAC-26  EIF2B2_-_75469671.23-P1P2|EIF2B2_-_75469856.23...   \n", "AAACCCAAGACAACTA-5   RPP30_+_92631924.23-P1P2|RPP30_-_92631746.23-P1P2   \n", "...                                                                ...   \n", "TTTGTTGTCTGTAAGC-21    UHRF1_+_4910126.23-P1P2|UHRF1_+_4909924.23-P1P2   \n", "TTTGTTGTCTTAGCTT-9   EIF1AX_+_20159985.23-P1P2|EIF1AX_-_20159725.23...   \n", "TTTGTTGTCTTCCTAA-56    MCM3_-_52149354.23-P1P2|MCM3_-_52149307.23-P1P2   \n", "TTTGTTGTCTTCGTAT-29  MED20_+_41888837.23-P1P2|MED20_+_41888805.23-P1P2   \n", "TTTGTTGTCTTGGGCG-42    RPF1_-_84945270.23-P1P2|RPF1_+_84945350.23-P1P2   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \n", "cell_barcode                                                 \n", "AAACCCAAGAAACTGT-27     0.063665    13194.0        0.106271  \n", "AAACCCAAGAAATCCA-12     0.049182     9719.0       -0.054858  \n", "AAACCCAAGAAATTCG-56     0.055632    11576.0       -0.138458  \n", "AAACCCAAGAAGCCAC-26     0.044284    12849.0       -0.422243  \n", "AAACCCAAGACAACTA-5      0.072090    11555.0       -1.806991  \n", "...                          ...        ...             ...  \n", "TTTGTTGTCTGTAAGC-21     0.065960     5003.0       -1.069798  \n", "TTTGTTGTCTTAGCTT-9      0.063649    12726.0        0.727394  \n", "TTTGTTGTCTTCCTAA-56     0.030737     9630.0       -0.521912  \n", "TTTGTTGTCTTCGTAT-29     0.056258     5777.0       -1.199783  \n", "TTTGTTGTCTTGGGCG-42     0.041012     7632.0       -0.841916  \n", "\n", "[262956 rows x 9 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["i.obs"]}, {"cell_type": "code", "execution_count": 19, "id": "7de67555", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene_name</th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>class</th>\n", "      <th>strand</th>\n", "      <th>length</th>\n", "      <th>in_matrix</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>cv</th>\n", "      <th>fano</th>\n", "    </tr>\n", "    <tr>\n", "      <th>gene_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ENSG00000228794</th>\n", "      <td>LINC01128</td>\n", "      <td>chr1</td>\n", "      <td>825138</td>\n", "      <td>868202</td>\n", "      <td>gene_version9</td>\n", "      <td>+</td>\n", "      <td>43064</td>\n", "      <td>True</td>\n", "      <td>0.187369</td>\n", "      <td>0.462334</td>\n", "      <td>2.467510</td>\n", "      <td>1.140814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188976</th>\n", "      <td>NOC2L</td>\n", "      <td>chr1</td>\n", "      <td>944203</td>\n", "      <td>959309</td>\n", "      <td>gene_version11</td>\n", "      <td>-</td>\n", "      <td>15106</td>\n", "      <td>True</td>\n", "      <td>1.742970</td>\n", "      <td>1.967864</td>\n", "      <td>1.129029</td>\n", "      <td>2.221775</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000187583</th>\n", "      <td>PLEKHN1</td>\n", "      <td>chr1</td>\n", "      <td>966482</td>\n", "      <td>975865</td>\n", "      <td>gene_version11</td>\n", "      <td>+</td>\n", "      <td>9383</td>\n", "      <td>True</td>\n", "      <td>0.164274</td>\n", "      <td>0.444831</td>\n", "      <td>2.707855</td>\n", "      <td>1.204539</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000188290</th>\n", "      <td>HES4</td>\n", "      <td>chr1</td>\n", "      <td>998962</td>\n", "      <td>1000172</td>\n", "      <td>gene_version10</td>\n", "      <td>-</td>\n", "      <td>1210</td>\n", "      <td>True</td>\n", "      <td>2.179681</td>\n", "      <td>2.977747</td>\n", "      <td>1.366139</td>\n", "      <td>4.068017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000187608</th>\n", "      <td>ISG15</td>\n", "      <td>chr1</td>\n", "      <td>1001138</td>\n", "      <td>1014540</td>\n", "      <td>gene_version10</td>\n", "      <td>+</td>\n", "      <td>13402</td>\n", "      <td>True</td>\n", "      <td>1.701508</td>\n", "      <td>4.689206</td>\n", "      <td>2.755911</td>\n", "      <td>12.923033</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198786</th>\n", "      <td>MT-ND5</td>\n", "      <td>chrM</td>\n", "      <td>12337</td>\n", "      <td>14148</td>\n", "      <td>gene_version2</td>\n", "      <td>+</td>\n", "      <td>1811</td>\n", "      <td>True</td>\n", "      <td>36.606468</td>\n", "      <td>28.708439</td>\n", "      <td>0.784245</td>\n", "      <td>22.514448</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198695</th>\n", "      <td>MT-ND6</td>\n", "      <td>chrM</td>\n", "      <td>14149</td>\n", "      <td>14673</td>\n", "      <td>gene_version2</td>\n", "      <td>-</td>\n", "      <td>524</td>\n", "      <td>True</td>\n", "      <td>4.300245</td>\n", "      <td>4.671066</td>\n", "      <td>1.086233</td>\n", "      <td>5.073864</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000198727</th>\n", "      <td>MT-CYB</td>\n", "      <td>chrM</td>\n", "      <td>14747</td>\n", "      <td>15887</td>\n", "      <td>gene_version2</td>\n", "      <td>+</td>\n", "      <td>1140</td>\n", "      <td>True</td>\n", "      <td>143.382690</td>\n", "      <td>102.631416</td>\n", "      <td>0.715787</td>\n", "      <td>73.462204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000276256</th>\n", "      <td>AC011043.1</td>\n", "      <td>GL000195.1</td>\n", "      <td>42939</td>\n", "      <td>49164</td>\n", "      <td>gene_version1</td>\n", "      <td>-</td>\n", "      <td>6225</td>\n", "      <td>True</td>\n", "      <td>0.186037</td>\n", "      <td>0.463681</td>\n", "      <td>2.492413</td>\n", "      <td>1.155684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ENSG00000271254</th>\n", "      <td>AC240274.1</td>\n", "      <td>KI270711.1</td>\n", "      <td>4612</td>\n", "      <td>29626</td>\n", "      <td>gene_version6</td>\n", "      <td>-</td>\n", "      <td>25014</td>\n", "      <td>True</td>\n", "      <td>0.423658</td>\n", "      <td>0.849215</td>\n", "      <td>2.004482</td>\n", "      <td>1.702236</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9624 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                  gene_name         chr    start      end           class  \\\n", "gene_id                                                                     \n", "ENSG00000228794   LINC01128        chr1   825138   868202   gene_version9   \n", "ENSG00000188976       NOC2L        chr1   944203   959309  gene_version11   \n", "ENSG00000187583     PLEKHN1        chr1   966482   975865  gene_version11   \n", "ENSG00000188290        HES4        chr1   998962  1000172  gene_version10   \n", "ENSG00000187608       ISG15        chr1  1001138  1014540  gene_version10   \n", "...                     ...         ...      ...      ...             ...   \n", "ENSG00000198786      MT-ND5        chrM    12337    14148   gene_version2   \n", "ENSG00000198695      MT-ND6        chrM    14149    14673   gene_version2   \n", "ENSG00000198727      MT-CYB        chrM    14747    15887   gene_version2   \n", "ENSG00000276256  AC011043.1  GL000195.1    42939    49164   gene_version1   \n", "ENSG00000271254  AC240274.1  KI270711.1     4612    29626   gene_version6   \n", "\n", "                strand  length  in_matrix        mean         std        cv  \\\n", "gene_id                                                                       \n", "ENSG00000228794      +   43064       True    0.187369    0.462334  2.467510   \n", "ENSG00000188976      -   15106       True    1.742970    1.967864  1.129029   \n", "ENSG00000187583      +    9383       True    0.164274    0.444831  2.707855   \n", "ENSG00000188290      -    1210       True    2.179681    2.977747  1.366139   \n", "ENSG00000187608      +   13402       True    1.701508    4.689206  2.755911   \n", "...                ...     ...        ...         ...         ...       ...   \n", "ENSG00000198786      +    1811       True   36.606468   28.708439  0.784245   \n", "ENSG00000198695      -     524       True    4.300245    4.671066  1.086233   \n", "ENSG00000198727      +    1140       True  143.382690  102.631416  0.715787   \n", "ENSG00000276256      -    6225       True    0.186037    0.463681  2.492413   \n", "ENSG00000271254      -   25014       True    0.423658    0.849215  2.004482   \n", "\n", "                      fano  \n", "gene_id                     \n", "ENSG00000228794   1.140814  \n", "ENSG00000188976   2.221775  \n", "ENSG00000187583   1.204539  \n", "ENSG00000188290   4.068017  \n", "ENSG00000187608  12.923033  \n", "...                    ...  \n", "ENSG00000198786  22.514448  \n", "ENSG00000198695   5.073864  \n", "ENSG00000198727  73.462204  \n", "ENSG00000276256   1.155684  \n", "ENSG00000271254   1.702236  \n", "\n", "[9624 rows x 12 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["j = sc.read_h5ad('/data/vcc/raw_data_vcc/raw_data_vcc/Nadig_et_al_2025/GSE264667_hepg2_raw_singlecell_01.h5ad', backed=\"r\")\n", "j.var\n"]}, {"cell_type": "code", "execution_count": 20, "id": "6985e5d6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gem_group</th>\n", "      <th>gene</th>\n", "      <th>gene_id</th>\n", "      <th>transcript</th>\n", "      <th>gene_transcript</th>\n", "      <th>sgID_AB</th>\n", "      <th>mitopercent</th>\n", "      <th>UMI_count</th>\n", "      <th>z_gemgroup_UMI</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cell_barcode</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAACCCAAGAATAGTC-3</th>\n", "      <td>3</td>\n", "      <td>KIAA1143</td>\n", "      <td>ENSG00000163807</td>\n", "      <td>P1P2</td>\n", "      <td>4360_KIAA1143_P1P2_ENSG00000163807</td>\n", "      <td>KIAA1143_+_44803075.23-P1P2|KIAA1143_+_4480308...</td>\n", "      <td>0.114029</td>\n", "      <td>11234.0</td>\n", "      <td>-0.611091</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGACAGCTG-12</th>\n", "      <td>12</td>\n", "      <td>FEN1</td>\n", "      <td>ENSG00000168496</td>\n", "      <td>P1P2</td>\n", "      <td>3057_FEN1_P1P2_ENSG00000168496</td>\n", "      <td>FEN1_-_61560380.23-P1P2|FEN1_+_61560617.23-P1P2</td>\n", "      <td>0.095229</td>\n", "      <td>11068.0</td>\n", "      <td>-0.070171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGACCCTTA-53</th>\n", "      <td>53</td>\n", "      <td>RNPS1</td>\n", "      <td>ENSG00000205937</td>\n", "      <td>P1P2</td>\n", "      <td>7407_RNPS1_P1P2_ENSG00000205937</td>\n", "      <td>RNPS1_+_2318108.23-P1P2|RNPS1_+_2318045.23-P1P2</td>\n", "      <td>0.086603</td>\n", "      <td>16743.0</td>\n", "      <td>0.208552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGACGCCCT-39</th>\n", "      <td>39</td>\n", "      <td>PHF10</td>\n", "      <td>ENSG00000130024</td>\n", "      <td>P1P2</td>\n", "      <td>6279_PHF10_P1P2_ENSG00000130024</td>\n", "      <td>PHF10_-_170124315.23-P1P2|PHF10_+_170124573.23...</td>\n", "      <td>0.084000</td>\n", "      <td>21488.0</td>\n", "      <td>0.091101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAACCCAAGAGGCCAT-24</th>\n", "      <td>24</td>\n", "      <td>HSF1</td>\n", "      <td>ENSG00000185122</td>\n", "      <td>P1P2</td>\n", "      <td>3959_HSF1_P1P2_ENSG00000185122</td>\n", "      <td>HSF1_+_145515304.23-P1P2|HSF1_-_145515300.23-P1P2</td>\n", "      <td>0.099000</td>\n", "      <td>19293.0</td>\n", "      <td>0.041390</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTACGGA-22</th>\n", "      <td>22</td>\n", "      <td>ADAT3</td>\n", "      <td>ENSG00000213638</td>\n", "      <td>P1</td>\n", "      <td>141_ADAT3_P1_ENSG00000213638</td>\n", "      <td>ADAT3_-_1905438.23-P1|ADAT3_+_1905424.23-P1</td>\n", "      <td>0.093179</td>\n", "      <td>16259.0</td>\n", "      <td>-0.275738</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTCGATT-35</th>\n", "      <td>35</td>\n", "      <td>DHX16</td>\n", "      <td>ENSG00000204560</td>\n", "      <td>P1P2</td>\n", "      <td>2204_DHX16_P1P2_ENSG00000204560</td>\n", "      <td>DHX16_+_30640731.23-P1P2|DHX16_-_30640796.23-P1P2</td>\n", "      <td>0.026757</td>\n", "      <td>35692.0</td>\n", "      <td>-0.256314</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTGATTC-31</th>\n", "      <td>31</td>\n", "      <td>EBNA1BP2</td>\n", "      <td>ENSG00000117395</td>\n", "      <td>P1P2</td>\n", "      <td>2456_EBNA1BP2_P1P2_ENSG00000117395</td>\n", "      <td>EBNA1BP2_-_43637779.23-P1P2|EBNA1BP2_+_4363789...</td>\n", "      <td>0.066386</td>\n", "      <td>25457.0</td>\n", "      <td>0.024672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTGGGCG-39</th>\n", "      <td>39</td>\n", "      <td>RPL8</td>\n", "      <td>ENSG00000161016</td>\n", "      <td>P1P2</td>\n", "      <td>7480_RPL8_P1P2_ENSG00000161016</td>\n", "      <td>RPL8_+_146017745.23-P1P2|RPL8_-_146017783.23-P1P2</td>\n", "      <td>0.074004</td>\n", "      <td>30012.0</td>\n", "      <td>0.854286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TTTGTTGTCTTGGTCC-9</th>\n", "      <td>9</td>\n", "      <td>MBIP</td>\n", "      <td>ENSG00000151332</td>\n", "      <td>P1P2</td>\n", "      <td>4879_MBIP_P1P2_ENSG00000151332</td>\n", "      <td>MBIP_+_36789486.23-P1P2|MBIP_+_36789822.23-P1P2</td>\n", "      <td>0.147744</td>\n", "      <td>28705.0</td>\n", "      <td>1.426755</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>145473 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                     gem_group      gene          gene_id transcript  \\\n", "cell_barcode                                                           \n", "AAACCCAAGAATAGTC-3           3  KIAA1143  ENSG00000163807       P1P2   \n", "AAACCCAAGACAGCTG-12         12      FEN1  ENSG00000168496       P1P2   \n", "AAACCCAAGACCCTTA-53         53     RNPS1  ENSG00000205937       P1P2   \n", "AAACCCAAGACGCCCT-39         39     PHF10  ENSG00000130024       P1P2   \n", "AAACCCAAGAGGCCAT-24         24      HSF1  ENSG00000185122       P1P2   \n", "...                        ...       ...              ...        ...   \n", "TTTGTTGTCTTACGGA-22         22     ADAT3  ENSG00000213638         P1   \n", "TTTGTTGTCTTCGATT-35         35     DHX16  ENSG00000204560       P1P2   \n", "TTTGTTGTCTTGATTC-31         31  EBNA1BP2  ENSG00000117395       P1P2   \n", "TTTGTTGTCTTGGGCG-39         39      RPL8  ENSG00000161016       P1P2   \n", "TTTGTTGTCTTGGTCC-9           9      MBIP  ENSG00000151332       P1P2   \n", "\n", "                                        gene_transcript  \\\n", "cell_barcode                                              \n", "AAACCCAAGAATAGTC-3   4360_KIAA1143_P1P2_ENSG00000163807   \n", "AAACCCAAGACAGCTG-12      3057_FEN1_P1P2_ENSG00000168496   \n", "AAACCCAAGACCCTTA-53     7407_RNPS1_P1P2_ENSG00000205937   \n", "AAACCCAAGACGCCCT-39     6279_PHF10_P1P2_ENSG00000130024   \n", "AAACCCAAGAGGCCAT-24      3959_HSF1_P1P2_ENSG00000185122   \n", "...                                                 ...   \n", "TTTGTTGTCTTACGGA-22        141_ADAT3_P1_ENSG00000213638   \n", "TTTGTTGTCTTCGATT-35     2204_DHX16_P1P2_ENSG00000204560   \n", "TTTGTTGTCTTGATTC-31  2456_EBNA1BP2_P1P2_ENSG00000117395   \n", "TTTGTTGTCTTGGGCG-39      7480_RPL8_P1P2_ENSG00000161016   \n", "TTTGTTGTCTTGGTCC-9       4879_MBIP_P1P2_ENSG00000151332   \n", "\n", "                                                               sgID_AB  \\\n", "cell_barcode                                                             \n", "AAACCCAAGAATAGTC-3   KIAA1143_+_44803075.23-P1P2|KIAA1143_+_4480308...   \n", "AAACCCAAGACAGCTG-12    FEN1_-_61560380.23-P1P2|FEN1_+_61560617.23-P1P2   \n", "AAACCCAAGACCCTTA-53    RNPS1_+_2318108.23-P1P2|RNPS1_+_2318045.23-P1P2   \n", "AAACCCAAGACGCCCT-39  PHF10_-_170124315.23-P1P2|PHF10_+_170124573.23...   \n", "AAACCCAAGAGGCCAT-24  HSF1_+_145515304.23-P1P2|HSF1_-_145515300.23-P1P2   \n", "...                                                                ...   \n", "TTTGTTGTCTTACGGA-22        ADAT3_-_1905438.23-P1|ADAT3_+_1905424.23-P1   \n", "TTTGTTGTCTTCGATT-35  DHX16_+_30640731.23-P1P2|DHX16_-_30640796.23-P1P2   \n", "TTTGTTGTCTTGATTC-31  EBNA1BP2_-_43637779.23-P1P2|EBNA1BP2_+_4363789...   \n", "TTTGTTGTCTTGGGCG-39  RPL8_+_146017745.23-P1P2|RPL8_-_146017783.23-P1P2   \n", "TTTGTTGTCTTGGTCC-9     MBIP_+_36789486.23-P1P2|MBIP_+_36789822.23-P1P2   \n", "\n", "                     mitopercent  UMI_count  z_gemgroup_UMI  \n", "cell_barcode                                                 \n", "AAACCCAAGAATAGTC-3      0.114029    11234.0       -0.611091  \n", "AAACCCAAGACAGCTG-12     0.095229    11068.0       -0.070171  \n", "AAACCCAAGACCCTTA-53     0.086603    16743.0        0.208552  \n", "AAACCCAAGACGCCCT-39     0.084000    21488.0        0.091101  \n", "AAACCCAAGAGGCCAT-24     0.099000    19293.0        0.041390  \n", "...                          ...        ...             ...  \n", "TTTGTTGTCTTACGGA-22     0.093179    16259.0       -0.275738  \n", "TTTGTTGTCTTCGATT-35     0.026757    35692.0       -0.256314  \n", "TTTGTTGTCTTGATTC-31     0.066386    25457.0        0.024672  \n", "TTTGTTGTCTTGGGCG-39     0.074004    30012.0        0.854286  \n", "TTTGTTGTCTTGGTCC-9      0.147744    28705.0        1.426755  \n", "\n", "[145473 rows x 9 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["j.obs"]}], "metadata": {"kernelspec": {"display_name": "state", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}