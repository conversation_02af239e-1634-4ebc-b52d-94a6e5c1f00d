{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "    WARNING: The R package \"reticulate\" only fixed recently\n", "    an issue that caused a segfault when used with rpy2:\n", "    https://github.com/rstudio/reticulate/pull/1188\n", "    Make sure that you use a version of that package that includes\n", "    the fix.\n", "    \n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Bulk_RNAseq_Seurat_object_IFNG_and_TGFB_stim.rds\n", "[STAT] cells=9, genes=60649, nnz=143247, zero_frac=0.7376\n", "[OBS cols head] {'orig.ident': 'category', 'nCount_RNA': 'float64', 'nFeature_RNA': 'int32', 'sample': 'category', 'stim': 'category', 'replicate': 'category'}\n", "[VAR idx head] ['DDX11L1', 'WASH7P', 'MIR6859-1', 'MIR1302-2HG', 'MIR1302-2']\n", "[OK] wrote /data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad/Bulk_RNAseq_Seurat_object_IFNG_and_TGFB_stim.RNA.counts.h5ad\n", "\n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/HClust_Pathway_celltype_specific_genelist.rds\n", "[SKIP] [1] \"not <PERSON><PERSON><PERSON>\"\n", "\n", "\n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Pathway_Exclusive_genelist.rds\n", "[SKIP] [1] \"not <PERSON><PERSON><PERSON>\"\n", "\n", "\n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Pathway_genelist.rds\n", "[SKIP] [1] \"not <PERSON><PERSON><PERSON>\"\n", "\n", "\n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_IFNB_Perturb_seq.rds\n", "[STAT] cells=328542, genes=34025, nnz=980979021, zero_frac=0.9122\n", "[OBS cols head] {'orig.ident': 'category', 'nCount_RNA': 'float64', 'nFeature_RNA': 'int32', 'sample': 'category', 'bc1_well': 'category', 'bc2_well': 'category', 'bc3_well': 'category', 'percent.mito': 'float64', 'cell_type': 'category', 'pathway': 'category'}\n", "[VAR idx head] ['TSPAN6', 'TNMD', 'DPM1', 'SCYL3', 'C1orf112']\n", "[OK] wrote /data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad/Seurat_object_IFNB_Perturb_seq.RNA.counts.h5ad\n", "\n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_IFNG_Perturb_seq.rds\n", "[STAT] cells=245240, genes=33525, nnz=667070362, zero_frac=0.9189\n", "[OBS cols head] {'orig.ident': 'category', 'nCount_RNA': 'float64', 'nFeature_RNA': 'int32', 'sample': 'category', 'cell_type': 'category', 'pathway': 'category', 'percent.mito': 'float64', 'sample_ID': 'category', 'Batch_info': 'category', 'bc1_well': 'category'}\n", "[VAR idx head] ['TSPAN6', 'TNMD', 'DPM1', 'SCYL3', 'C1orf112']\n", "[OK] wrote /data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad/Seurat_object_IFNG_Perturb_seq.RNA.counts.h5ad\n", "\n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_INS_Perturb_seq.rds\n", "[STAT] cells=431457, genes=34025, nnz=1280241271, zero_frac=0.9128\n", "[OBS cols head] {'orig.ident': 'category', 'nCount_RNA': 'float64', 'nFeature_RNA': 'int32', 'sample': 'category', 'bc1_well': 'category', 'bc2_well': 'category', 'bc3_well': 'category', 'percent.mito': 'float64', 'cell_type': 'category', 'pathway': 'category'}\n", "[VAR idx head] ['TSPAN6', 'TNMD', 'DPM1', 'SCYL3', 'C1orf112']\n", "[OK] wrote /data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad/Seurat_object_INS_Perturb_seq.RNA.counts.h5ad\n", "\n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_TGFB_Perturb_seq.rds\n", "[STAT] cells=236606, genes=33525, nnz=605806920, zero_frac=0.9236\n", "[OBS cols head] {'orig.ident': 'category', 'nCount_RNA': 'float64', 'nFeature_RNA': 'int32', 'sample': 'category', 'cell_type': 'category', 'pathway': 'category', 'percent.mito': 'float64', 'sample_ID': 'category', 'Batch_info': 'category', 'bc1_well': 'category'}\n", "[VAR idx head] ['TSPAN6', 'TNMD', 'DPM1', 'SCYL3', 'C1orf112']\n", "[OK] wrote /data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad/Seurat_object_TGFB_Perturb_seq.RNA.counts.h5ad\n", "\n", "[INFO] /data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_TNFA_Perturb_seq.rds\n", "[STAT] cells=386631, genes=33525, nnz=1066935493, zero_frac=0.9177\n", "[OBS cols head] {'orig.ident': 'category', 'nCount_RNA': 'float64', 'nFeature_RNA': 'int32', 'sample': 'category', 'cell_type': 'category', 'pathway': 'category', 'percent.mito': 'float64', 'sample_ID': 'category', 'Batch_info': 'category', 'bc1_well': 'category'}\n", "[VAR idx head] ['TSPAN6', 'TNMD', 'DPM1', 'SCYL3', 'C1orf112']\n", "[OK] wrote /data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad/Seurat_object_TNFA_Perturb_seq.RNA.counts.h5ad\n", "\n", "[<PERSON><PERSON><PERSON>] tried 9, wrote 6. Summary -> /data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad/_summary.json\n"]}], "source": ["# Minimal Seurat RDS -> H5AD (counts->X, keep obs/var)\n", "# - No zellkonverter / SeuratDisk / scCustomize\n", "# - rpy2 + <PERSON><PERSON><PERSON> + <PERSON> to fetch counts; AnnData写h5ad\n", "# - 关闭HDF5文件锁 + 安全写入（临时文件+原子替换+重试）\n", "\n", "import os, json, time\n", "from uuid import uuid4\n", "from pathlib import Path\n", "\n", "# ★ 关键：在导入 anndata/h5py 之前关闭 HDF5 文件锁\n", "os.environ.setdefault(\"HDF5_USE_FILE_LOCKING\", \"FALSE\")\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import scipy.sparse as sp\n", "import anndata as ad\n", "\n", "import rpy2.robjects as ro\n", "from rpy2.robjects.packages import importr\n", "from rpy2.robjects.conversion import localconverter\n", "from rpy2.robjects import default_converter\n", "from rpy2.robjects import pandas2ri\n", "\n", "# --------- CONFIG ---------\n", "INPUT_DIR = \"/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025\"   # <-- 改成你的\n", "OUTPUT_DIR = \"/data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad\"  # <-- 改成你的\n", "# --------------------------\n", "\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "\n", "# 允许可空字符串写入（anndata>=0.11支持；对旧版这里不报错）\n", "try:\n", "    ad.settings.allow_write_nullable_strings = True\n", "except Exception:\n", "    pass\n", "\n", "# R端：提取 counts（优先v5 layer='counts'，否则slot），以及 obs/meta.features\n", "ro.r(r\"\"\"\n", "suppressMessages(library(Seurat))\n", "suppressMessages(library(Matrix))\n", "\n", "conv_pick <- function(path) {\n", "  obj <- tryCatch(readRDS(path), error=function(e)\n", "    return(list(ok=FALSE, reason=paste(\"readRDS error:\", e$message)))\n", "  )\n", "  if (is.list(obj) && isFALSE(obj$ok)) return(obj)\n", "  if (!inherits(obj, \"<PERSON><PERSON><PERSON>\")) return(list(ok=FALSE, reason=\"not <PERSON><PERSON><PERSON>\"))\n", "\n", "  assays <- names(obj@assays)\n", "  if (length(assays) == 0) return(list(ok=FALSE, reason=\"no assays\"))\n", "  prefer <- if (\"RNA\" %in% assays) \"RNA\" else assays[[1L]]\n", "\n", "  m <- tryCatch({ GetAssayData(object=obj, assay=prefer, layer=\"counts\") }, error=function(e) NULL)\n", "  if (is.null(m)) {\n", "    a <- obj@assays[[prefer]]\n", "    s <- slotNames(a)\n", "    if (!(\"counts\" %in% s)) return(list(ok=FALSE, reason=sprintf(\"assay '%s' has no counts\", prefer)))\n", "    m <- a@counts\n", "  }\n", "  if (!inherits(m, \"dgCMatrix\")) m <- as(m, \"dgCMatrix\")\n", "\n", "  feats <- rownames(m); if (is.null(feats)) feats <- as.character(seq_len(nrow(m)))\n", "  cells <- colnames(m); if (is.null(cells)) cells <- as.character(seq_len(ncol(m)))\n", "\n", "  obs <- <EMAIL>\n", "  mf  <- tryCatch(obj@assays[[prefer]]@meta.features, error=function(e) NULL)\n", "\n", "  list(ok=TRUE, assay=prefer, i=m@i, p=m@p, x=m@x, dim=m@Dim,\n", "       features=feats, cells=cells, obs=obs, var=mf)\n", "}\n", "\"\"\")\n", "conv_pick = ro.globalenv[\"conv_pick\"]\n", "\n", "def rget(rlist, key):\n", "    return rlist.rx2(key) if key in rlist.names else None\n", "\n", "def coerce_frame(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"将 DataFrame dtype 转为 AnnData 友好类型，避免 StringArray 写入问题。\"\"\"\n", "    if df is None:\n", "        return pd.DataFrame()\n", "    df = df.copy()\n", "    df.replace({\"NA_character_\": pd.NA, \"NaN\": pd.NA}, inplace=True)\n", "    for c in df.columns:\n", "        s = df[c]\n", "        if pd.api.types.is_integer_dtype(s) and getattr(s, \"isna\", lambda: False)().any():\n", "            df[c] = s.astype(\"float64\")\n", "        elif pd.api.types.is_integer_dtype(s) or pd.api.types.is_float_dtype(s) or pd.api.types.is_bool_dtype(s):\n", "            df[c] = s\n", "        else:\n", "            df[c] = s.astype(\"string\").astype(\"category\")\n", "    df.index = df.index.astype(str)\n", "    return df\n", "\n", "# ★ 安全写入：临时文件 + 原子替换 + 重试 + 清理\n", "def safe_write_h5ad(adata: ad.AnnData, out_path: Path, compression=\"gzip\", max_tries=5, sleep_base=0.6):\n", "    out_path = Path(out_path)\n", "    out_path.parent.mkdir(parents=True, exist_ok=True)\n", "    last_err = None\n", "    for k in range(max_tries):\n", "        tmp = out_path.with_suffix(out_path.suffix + f\".tmp-{uuid4().hex}\")\n", "        try:\n", "            adata.write_h5ad(tmp, compression=compression)\n", "            # 原子替换到目标路径\n", "            os.replace(tmp, out_path)\n", "            return\n", "        except Exception as e:\n", "            last_err = e\n", "            # 清理临时文件\n", "            try:\n", "                if tmp.exists():\n", "                    tmp.unlink()\n", "            except Exception:\n", "                pass\n", "            # 针对 HDF5 锁或偶发 I/O，等待后重试\n", "            time.sleep(sleep_base * (k + 1))\n", "    # 多次失败后抛出最后一个错误\n", "    raise last_err\n", "\n", "def convert_one(path: Path):\n", "    print(f\"\\n[INFO] {path}\")\n", "    res = conv_pick(str(path))\n", "\n", "    ok = bool(np.array(rget(res, \"ok\"))[0])\n", "    if not ok:\n", "        print(f\"[SKIP] {str(rget(res,'reason'))}\")\n", "        return None\n", "\n", "    assay = str(np.array(rget(res, \"assay\")).tolist()[0])\n", "    i = np.array(rget(res, \"i\"), dtype=np.int32)\n", "    p = np.array(rget(res, \"p\"), dtype=np.int32)\n", "    x = np.array(rget(res, \"x\"), dtype=float)\n", "    dim = tuple(np.array(rget(res, \"dim\"), dtype=np.int32).tolist())\n", "    features = list(map(str, list(rget(res, \"features\"))))\n", "    cells = list(map(str, list(rget(res, \"cells\"))))\n", "\n", "    # counts: genes x cells (CSC) -> cells x genes (CSR)\n", "    X = sp.csc_matrix((x, i, p), shape=dim).T.tocsr()\n", "\n", "    # R -> pandas\n", "    with localconverter(default_converter + pandas2ri.converter):\n", "        obs_r = rget(res, \"obs\")\n", "        var_r = rget(res, \"var\")\n", "        obs = ro.conversion.rpy2py(obs_r) if obs_r is not None else pd.DataFrame(index=cells)\n", "        var = ro.conversion.rpy2py(var_r) if var_r is not None else pd.DataFrame(index=features)\n", "\n", "    # 对齐索引（严格按 counts 的 cells/features 顺序）\n", "    obs.index = obs.index.astype(str)\n", "    var.index = var.index.astype(str)\n", "    obs = obs.reindex(cells)\n", "    var = var.reindex(features)\n", "\n", "    # 严检断言，确保顺序一致\n", "    assert list(obs.index) == list(cells), \"obs.index order != cells (after reindex)\"\n", "    assert list(var.index) == list(features), \"var.index order != features (after reindex)\"\n", "\n", "    # dtype 安全\n", "    obs = coerce_frame(obs)\n", "    var = coerce_frame(var)\n", "\n", "    # 构建 AnnData (X=counts)\n", "    adata = ad.AnnData(X=X, obs=obs, var=var)\n", "    adata.obs_names_make_unique()\n", "    adata.var_names_make_unique()\n", "\n", "    # 统计\n", "    nnz = int(X.nnz)\n", "    total = adata.n_obs * adata.n_vars\n", "    zero_frac = 1 - (nnz / max(1, total))\n", "    cell_sums = np.asarray(X.sum(axis=1)).ravel()\n", "    gene_sums = np.asarray(X.sum(axis=0)).ravel()\n", "\n", "    stat = {\n", "        \"file\": str(path),\n", "        \"assay\": assay,\n", "        \"cells\": int(adata.n_obs),\n", "        \"genes\": int(adata.n_vars),\n", "        \"nnz\": nnz,\n", "        \"zero_frac\": float(round(zero_frac, 6)),\n", "        \"per_cell_sum\": {\n", "            \"mean\": float(np.mean(cell_sums)), \"median\": float(np.median(cell_sums)),\n", "            \"min\": float(np.min(cell_sums)), \"max\": float(np.max(cell_sums))\n", "        },\n", "        \"per_gene_sum\": {\n", "            \"mean\": float(np.mean(gene_sums)), \"median\": float(np.median(gene_sums)),\n", "            \"min\": float(np.min(gene_sums)), \"max\": float(np.max(gene_sums))\n", "        },\n", "        \"obs_cols\": {c: str(adata.obs[c].dtype) for c in adata.obs.columns[:10]},\n", "        \"var_cols\": {c: str(adata.var[c].dtype) for c in adata.var.columns[:10]},\n", "    }\n", "    print(f\"[STAT] cells={stat['cells']}, genes={stat['genes']}, nnz={stat['nnz']}, zero_frac={stat['zero_frac']:.4f}\")\n", "    print(\"[OBS cols head]\", stat[\"obs_cols\"])\n", "    print(\"[VAR idx head]\", adata.var.index[:5].tolist())\n", "\n", "    out_name = f\"{path.stem}.{assay}.counts.h5ad\"\n", "    out_path = Path(OUTPUT_DIR) / out_name\n", "\n", "    # ★ 用安全写入替代直接 write_h5ad\n", "    safe_write_h5ad(adata, out_path, compression=\"gzip\")\n", "    print(f\"[<PERSON>] wrote {out_path}\")\n", "    return stat\n", "\n", "# 批处理\n", "paths = sorted(Path(INPUT_DIR).glob(\"*.rds\"))\n", "if not paths:\n", "    raise SystemExit(f\"No .rds files in {INPUT_DIR}\")\n", "\n", "summary = {\"output_dir\": OUTPUT_DIR, \"files\": []}\n", "done = 0\n", "for p in paths:\n", "    try:\n", "        st = convert_one(p)\n", "        if st is not None:\n", "            summary[\"files\"].append(st); done += 1\n", "    except Exception as e:\n", "        print(f\"[ERROR] {p} -> {e}\")\n", "\n", "sum_path = Path(OUTPUT_DIR) / \"_summary.json\"\n", "with open(sum_path, \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(summary, f, ensure_ascii=False, indent=2)\n", "print(f\"\\n[DONE] tried {len(paths)}, wrote {done}. Summary -> {sum_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d5b4c7fc", "metadata": {}, "outputs": [], "source": ["# Minimal Seurat RDS -> H5AD (counts->X, keep obs/var)\n", "# - No zellkonverter / SeuratDisk / scCustomize (avoids network or reticulate issues)\n", "# - Uses rpy2 + Seurat + Matrix to fetch counts, obs (meta.data), var (meta.features)\n", "# - Coerces obs/var dtypes to AnnData-safe forms\n", "# - Prints stats; writes _summary.json\n", "\n", "import os, json\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import scipy.sparse as sp\n", "import anndata as ad\n", "\n", "import rpy2.robjects as ro\n", "from rpy2.robjects.packages import importr\n", "from rpy2.robjects.conversion import localconverter\n", "from rpy2.robjects import default_converter\n", "from rpy2.robjects import pandas2ri\n", "\n", "# --------- CONFIG ---------\n", "INPUT_FILE = \"/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_TGFB_Perturb_seq.rds\"  # 指定要处理的单个文件\n", "OUTPUT_DIR = \"/data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad\"  # 输出目录\n", "# --------------------------\n", "\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "\n", "# Prefer to avoid StringArray pitfalls in older anndata; still allow newer behavior.\n", "try:\n", "    ad.settings.allow_write_nullable_strings = True\n", "except Exception:\n", "    pass\n", "\n", "# R-side minimal extractor: get counts (v5 layer='counts' or slot), obs, var\n", "ro.r(r\"\"\"\n", "suppressMessages(library(Seurat))\n", "suppressMessages(library(Matrix))\n", "\n", "conv_pick <- function(path) {\n", "  obj <- tryCatch(readRDS(path), error=function(e)\n", "    return(list(ok=FALSE, reason=paste(\"readRDS error:\", e$message)))\n", "  )\n", "  if (is.list(obj) && isFALSE(obj$ok)) return(obj)\n", "  if (!inherits(obj, \"<PERSON><PERSON><PERSON>\")) return(list(ok=FALSE, reason=\"not <PERSON><PERSON><PERSON>\"))\n", "\n", "  assays <- names(obj@assays)\n", "  if (length(assays) == 0) return(list(ok=FALSE, reason=\"no assays\"))\n", "  prefer <- if (\"RNA\" %in% assays) \"RNA\" else assays[[1L]]\n", "\n", "  # Prefer Seurat v5 layer='counts'; fallback to slot 'counts'\n", "  m <- tryCatch({ GetAssayData(object=obj, assay=prefer, layer=\"counts\") }, error=function(e) NULL)\n", "  if (is.null(m)) {\n", "    a <- obj@assays[[prefer]]\n", "    s <- slotNames(a)\n", "    if (!(\"counts\" %in% s)) return(list(ok=FALSE, reason=sprintf(\"assay '%s' has no counts\", prefer)))\n", "    m <- a@counts\n", "  }\n", "  if (!inherits(m, \"dgCMatrix\")) m <- as(m, \"dgCMatrix\")\n", "\n", "  feats <- rownames(m); if (is.null(feats)) feats <- as.character(seq_len(nrow(m)))\n", "  cells <- colnames(m); if (is.null(cells)) cells <- as.character(seq_len(ncol(m)))\n", "\n", "  obs <- <EMAIL>\n", "  mf  <- tryCatch(obj@assays[[prefer]]@meta.features, error=function(e) NULL)\n", "\n", "  list(ok=TRUE, assay=prefer, i=m@i, p=m@p, x=m@x, dim=m@Dim,\n", "       features=feats, cells=cells, obs=obs, var=mf)\n", "}\n", "\"\"\")\n", "conv_pick = ro.globalenv[\"conv_pick\"]\n", "\n", "def rget(rlist, key):\n", "    return rlist.rx2(key) if key in rlist.names else None\n", "\n", "def coerce_frame(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Coerce DataFrame dtypes to AnnData-friendly types (avoid StringArray issues).\"\"\"\n", "    if df is None:\n", "        return pd.DataFrame()\n", "    df = df.copy()\n", "    df.replace({\"NA_character_\": pd.NA, \"NaN\": pd.NA}, inplace=True)\n", "    for c in df.columns:\n", "        s = df[c]\n", "        # Keep numeric/bool; handle pandas nullable ints by promoting to float\n", "        if pd.api.types.is_integer_dtype(s) and getattr(s, \"isna\", lambda: False)().any():\n", "            df[c] = s.astype(\"float64\")\n", "        elif pd.api.types.is_integer_dtype(s) or pd.api.types.is_float_dtype(s) or pd.api.types.is_bool_dtype(s):\n", "            df[c] = s\n", "        else:\n", "            # object/string/mixed -> category (safe to write)\n", "            df[c] = s.astype(\"string\").astype(\"category\")\n", "    df.index = df.index.astype(str)\n", "    return df\n", "\n", "def convert_one(path: Path):\n", "    print(f\"\\n[INFO] {path}\")\n", "    res = conv_pick(str(path))\n", "\n", "    ok = bool(np.array(rget(res, \"ok\"))[0])\n", "    if not ok:\n", "        reason = str(rget(res, \"reason\")) if rget(res, \"reason\") is not None else \"unknown reason\"\n", "        print(f\"[SKIP] {reason}\")\n", "        return None\n", "\n", "    assay = str(np.array(rget(res, \"assay\")).tolist()[0])\n", "    i = np.array(rget(res, \"i\"), dtype=np.int32)\n", "    p = np.array(rget(res, \"p\"), dtype=np.int32)\n", "    x = np.array(rget(res, \"x\"), dtype=float)\n", "    dim = tuple(np.array(rget(res, \"dim\"), dtype=np.int32).tolist())\n", "    features = list(map(str, list(rget(res, \"features\"))))\n", "    cells = list(map(str, list(rget(res, \"cells\"))))\n", "\n", "    # Build counts matrix X (cells x genes) in CSR\n", "    X = sp.csc_matrix((x, i, p), shape=dim).T.tocsr()\n", "\n", "    # R -> pandas for obs/var\n", "    with localconverter(default_converter + pandas2ri.converter):\n", "        obs_r = rget(res, \"obs\")\n", "        var_r = rget(res, \"var\")\n", "        obs = ro.conversion.rpy2py(obs_r) if obs_r is not None else pd.DataFrame(index=cells)\n", "        var = ro.conversion.rpy2py(var_r) if var_r is not None else pd.DataFrame(index=features)\n", "\n", "    # Align indices\n", "    obs.index = obs.index.astype(str)\n", "    var.index = var.index.astype(str)\n", "    if set(obs.index) != set(cells): obs = obs.reindex(cells)\n", "    else: obs = obs.loc[cells]\n", "    if set(var.index) != set(features): var = var.reindex(features)\n", "    else: var = var.loc[features]\n", "\n", "    # Coerce to safe dtypes\n", "    obs = coerce_frame(obs)\n", "    var = coerce_frame(var)\n", "\n", "    # Build AnnData (X=counts)\n", "    adata = ad.AnnData(X=X, obs=obs, var=var)\n", "    adata.obs_names_make_unique()\n", "    adata.var_names_make_unique()\n", "\n", "    # Stats\n", "    nnz = int(X.nnz)\n", "    total = adata.n_obs * adata.n_vars\n", "    zero_frac = 1 - (nnz / max(1, total))\n", "    cell_sums = np.asarray(X.sum(axis=1)).ravel()\n", "    gene_sums = np.asarray(X.sum(axis=0)).ravel()\n", "\n", "    stat = {\n", "        \"file\": str(path),\n", "        \"assay\": assay,\n", "        \"cells\": int(adata.n_obs),\n", "        \"genes\": int(adata.n_vars),\n", "        \"nnz\": nnz,\n", "        \"zero_frac\": float(round(zero_frac, 6)),\n", "        \"per_cell_sum\": {\n", "            \"mean\": float(np.mean(cell_sums)), \"median\": float(np.median(cell_sums)),\n", "            \"min\": float(np.min(cell_sums)), \"max\": float(np.max(cell_sums))\n", "        },\n", "        \"per_gene_sum\": {\n", "            \"mean\": float(np.mean(gene_sums)), \"median\": float(np.median(gene_sums)),\n", "            \"min\": float(np.min(gene_sums)), \"max\": float(np.max(gene_sums))\n", "        },\n", "        \"obs_cols\": {c: str(adata.obs[c].dtype) for c in adata.obs.columns[:10]},\n", "        \"var_cols\": {c: str(adata.var[c].dtype) for c in adata.var.columns[:10]},\n", "    }\n", "    print(f\"[STAT] cells={stat['cells']}, genes={stat['genes']}, nnz={stat['nnz']}, zero_frac={stat['zero_frac']:.4f}\")\n", "    print(\"[OBS cols head]\", stat[\"obs_cols\"])\n", "    print(\"[VAR idx head]\", adata.var.index[:5].tolist())\n", "\n", "    out_name = f\"{path.stem}.{assay}.counts.h5ad\"\n", "    out_path = Path(OUTPUT_DIR) / out_name\n", "    adata.write_h5ad(out_path, compression=\"gzip\")\n", "    print(f\"[<PERSON>] wrote {out_path}\")\n", "    return stat\n", "\n", "# 处理单个文件\n", "path = Path(INPUT_FILE)\n", "if not path.exists():\n", "    raise SystemExit(f\"File not found: {INPUT_FILE}\")\n", "\n", "summary = {\"output_dir\": OUTPUT_DIR, \"files\": []}\n", "try:\n", "    st = convert_one(path)\n", "    if st is not None:\n", "        summary[\"files\"].append(st)\n", "        print(f\"\\n[DONE] Successfully processed {path.name}\")\n", "    else:\n", "        print(f\"\\n[FAILED] Could not process {path.name}\")\n", "except Exception as e:\n", "    print(f\"[ERROR] {path} -> {e}\")\n", "\n", "# 保存摘要信息\n", "sum_path = Path(OUTPUT_DIR) / f\"{path.stem}_summary.json\"\n", "with open(sum_path, \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(summary, f, ensure_ascii=False, indent=2)\n", "print(f\"Summary -> {sum_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "5827ed74", "metadata": {}, "outputs": [], "source": ["import rpy2.robjects as ro\n", "from rpy2.robjects.packages import importr\n", "\n", "# 加载必要的 R 包\n", "ro.r('library(Seurat)')\n", "ro.r('library(Matrix)')\n", "\n", "def inspect_rds(file_path):\n", "    \"\"\"检查 RDS 文件的结构\"\"\"\n", "    ro.r(f'''\n", "    obj <- readRDS(\"{file_path}\")\n", "    cat(\"File:\", \"{file_path}\", \"\\\\n\")\n", "    cat(\"Class:\", class(obj), \"\\\\n\")\n", "    \n", "    if(inherits(obj, \"<PERSON><PERSON><PERSON>\")) {{\n", "        cat(\"Assays:\", names(obj@assays), \"\\\\n\")\n", "        cat(\"Meta.data columns:\", colnames(<EMAIL>), \"\\\\n\")\n", "        cat(\"Meta.data dimensions:\", dim(<EMAIL>), \"\\\\n\")\n", "        \n", "        # 检查是否有 meta.features\n", "        assay_name <- ifelse(\"RNA\" %in% names(obj@assays), \"RNA\", names(obj@assays)[1])\n", "        assay_obj <- obj@assays[[assay_name]]\n", "        if(\"meta.features\" %in% slotNames(assay_obj)) {{\n", "            cat(\"Meta.features columns:\", colnames(<EMAIL>), \"\\\\n\")\n", "            cat(\"Meta.features dimensions:\", dim(<EMAIL>), \"\\\\n\")\n", "        }} else {{\n", "            cat(\"No meta.features found\\\\n\")\n", "        }}\n", "        \n", "        # 检查数据类型\n", "        cat(\"Meta.data column types:\\\\n\")\n", "        for(col in colnames(<EMAIL>)) {{\n", "            cat(\"  \", col, \":\", class(<EMAIL>[[col]]), \"\\\\n\")\n", "        }}\n", "    }}\n", "    ''')\n", "    \n", "# 检查两个文件\n", "print(\"=== INS 文件 ===\")\n", "inspect_rds(\"/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_INS_Perturb_seq.rds\")\n", "\n", "print(\"\\n=== TGFB 文件 ===\")\n", "inspect_rds(\"/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_TGFB_Perturb_seq.rds\")"]}], "metadata": {"kernelspec": {"display_name": "jupyter_r_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}