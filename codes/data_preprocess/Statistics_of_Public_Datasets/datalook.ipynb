{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧬 扰动基因提取 & 基因 ID 映射\n", "从 Replogle、<PERSON><PERSON><PERSON>、<PERSON> 三组数据提取扰动基因，输出 gene name 和 gene id，如果没 id 则补充 GTF 映射。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ .h5ad 文件读取成功！\n", "🔹 obs (细胞元数据):\n", "✅ .h5ad 文件读取成功！\n", "🔹 obs (细胞元数据):\n"]}], "source": ["# 📦 导入所需库\n", "import scanpy as sc\n", "import anndata\n", "import pandas as pd\n", "\n", "# 📍 读取 .h5ad 文件\n", "h5ad_path = \"/public/home/<USER>/whr/VCC/ST_others/Nadig_et_al_2025/GSE264667_jurkat_raw_singlecell_01.h5ad\"\n", "adata = anndata.read_h5ad(h5ad_path)\n", "\n", "print(\"✅ .h5ad 文件读取成功！\")\n", "print(\"🔹 obs (细胞元数据):\")\n", "jurkat=adata.obs['gene']\n", "\n", "# 📍 读取 .h5ad 文件\n", "h5ad_path = \"/public/home/<USER>/whr/VCC/ST_others/Nadig_et_al_2025/GSE264667_hepg2_raw_singlecell_01.h5ad\"\n", "adata = anndata.read_h5ad(h5ad_path)\n", "\n", "print(\"✅ .h5ad 文件读取成功！\")\n", "print(\"🔹 obs (细胞元数据):\")\n", "hepg2=adata.obs['gene']\n", "\n", "# print(\"🔹 var (基因元数据):\")\n", "# display(adata.var)\n", "\n", "# 检查 jurkat 和 hepg2 是否相同\n", "jurkat = sorted(set(jurkat))\n", "hepg2 = sorted(set(hepg2))\n", "\n", "if jurkat == hepg2 :\n", "    print('yesyes')\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["仅在 jurkat 中的元素: {'INCENP'}\n", "仅在 hepg2 中的元素: {'CDCA8'}\n"]}], "source": ["# 找出 jurkat 和 hepg2 的差异\n", "jurkat_set = set(jurkat)\n", "hepg2_set = set(hepg2)\n", "\n", "# 仅在 jurkat 中的元素\n", "only_in_jurkat = jurkat_set - hepg2_set\n", "\n", "# 仅在 hepg2 中的元素\n", "only_in_hepg2 = hepg2_set - jurkat_set\n", "\n", "# 打印结果\n", "print(\"仅在 jurkat 中的元素:\", only_in_jurkat)\n", "print(\"仅在 hepg2 中的元素:\", only_in_hepg2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["2394"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["len(jurkat)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["2394"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["len(hepg2)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 📦 R 与 Python 的接口\n", "%load_ext rpy2.ipython\n", "\n", "# 加载 R 所需包并读取 .rds 文件"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["%%R\n", "\n", "\n", "lookobj <- readRDS(\"/public/home/<USER>/whr/VCC/ST_others/Jiang_et_al_2025/Pathway_Exclusive_genelist.rds\")  # 替换成你自己的路径\n", "\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <span>ListVector with 8 elements.</span>\n", "        <table>\n", "        <tbody>\n", "        \n", "          <tr>\n", "            <th>\n", "            [no name]\n", "            </th>\n", "            <td>\n", "            ['CIITA' 'ICAM1' 'NEAT1' 'GBP5' 'CD74' 'AC005515.1' 'IL18BP' 'AC005515.2'\n", " 'DENND1A' 'GSTK1' 'DENND4A' 'AC016831.7' 'VMP1' 'SLC12A7' 'SERPING1'\n", " 'CXCL9' 'NUCB1' 'PCYT1A' 'SBNO2' 'AL157871.4' 'ASCC3' 'MMP25-AS1'\n", " 'HAPLN3' 'C5orf15' 'CCDC68' 'SLC30A7' 'FNDC3B' 'LPGAT1' 'RAB27A' 'PHC3'\n", " 'KARS' 'CEP162' 'IFNAR2' 'SCN9A' 'VPS54' 'HK1' 'CDCP1' 'ZEB1' 'HSD17B12'\n", " 'KIF2A' 'SNTB2' 'GBP1P1' 'ZC3H7B' 'TRIB2' 'DOCK4' 'RALA' 'RARRES1'\n", " 'ATP13A3' 'ATP11C' 'IL6ST' 'HLA-DMA' 'ADGRE5' 'TBC1D32' 'NRG1' 'RARS'\n", " 'MIR29B2CHG' 'FAM241A' 'LY75' 'BCL6' 'HTR7' 'GUK1' 'GALK2' 'MED15'\n", " 'FAM208B' 'TAPBPL' 'EPB41' 'SCML1' 'PLSCR4' 'SLC2A13' 'ARF3' 'RARRES3'\n", " 'UBXN4' 'SEMA3F' 'ZNF280D' 'NMT1' 'NUP210' 'SUSD6' 'ENO1' 'SERPINB2'\n", " 'NNMT' 'DTNBP1' 'MAN1A1' 'ZFYVE28' 'MAP3K14' 'CLIC2' 'COQ8B' 'CIR1'\n", " 'MYO1B' 'CALM3' 'UBE2E2' 'XPO6' 'TANC1' 'LMTK2' 'LYPD6B' 'CLUAP1' 'LARP1'\n", " 'FNBP4' 'ESR2' 'FAM117B' 'LARS2' 'CSNK1G1' 'VPS9D1' 'ELMSAN1' 'FLNB'\n", " 'TMEM51' 'SOAT1' 'MFHAS1' 'HS3ST3A1' 'CLIP1' 'KIF18A' 'MIR4435-2HG'\n", " 'GNA13' 'RMDN3' 'LINC01169' 'TAOK3' 'TNFAIP2' 'ERI1' 'RHBDD1' 'ERLIN1'\n", " 'RMC1' 'NUMB' 'GCLM' 'GON4L' 'UTP4' 'VPS13B' 'GBP6' 'FAS' 'STT3B'\n", " 'SLC37A1' 'MITD1' 'CDC42EP4' 'KREMEN1' 'PPARGC1B' 'CENPA' 'TENM3'\n", " 'MIR3681HG' 'EDEM2' 'CDH11' 'USP9X' 'CAPS2' 'AC073610.2' 'TEP1' 'ZFP36'\n", " 'AK4' 'AC004917.1' 'NHEJ1' 'MTAP' 'TCF4' 'RAPH1' 'RNF152' 'TVP23C'\n", " 'ZNF462' 'MFSD1' 'EGLN3' 'SPAG17' 'AC022335.1' 'KDSR' 'SCYL3' 'PDIA6'\n", " 'CDRT4' 'C18orf25' 'TUSC3' 'OAF' 'DCLK1' 'POLQ' 'STK38L' 'TFPI' 'RAB23'\n", " 'HYOU1' 'RAB2B' 'ANXA7' 'EIF4A3' 'SNX16' 'DDX23' 'ACSL3' 'TPGS2' 'EHD1'\n", " 'DNMT3B' 'USO1' 'SPATA13' 'ITPKC' 'SLC28A3' 'USP43' 'RPS16' 'ATR' 'SEPT6'\n", " 'MLLT6' 'ESR1' 'CLIC5' 'MFSD14A' 'SMC4' 'ME2' 'MYO10' 'ATP10D' 'RASAL2'\n", " 'CMC2' 'CLCN3' 'ZNF761']\n", "            </td>\n", "          </tr>\n", "        \n", "          <tr>\n", "            <th>\n", "            [no name]\n", "            </th>\n", "            <td>\n", "            ['IFIT1' 'RSAD2' 'OASL' 'CMPK2' 'PLEKHA4' 'IFI27' 'USP18' 'PPM1K'\n", " 'C19orf66' 'BLZF1' 'RRBP1' 'HSH2D' 'LAMP3' 'ISG15' 'LY6E' 'HERC5'\n", " 'AC004551.1' 'ANKIB1' 'IFITM2' 'BST2' 'ZBP1' 'CCND3' 'C17orf67' 'LGALS9'\n", " 'MVB12A' 'ECE1' 'LINC01285' 'EHD4' 'ZNF107' 'MASTL' 'SIDT1' 'RASGRP3'\n", " 'AC009948.1' 'AC108010.1' 'TMEM123' 'SLC15A3' 'UBA7' 'IFIT5' 'TXNIP'\n", " 'GCA' 'LNPEP' 'GABRE' 'RBMS2' 'CNP' 'DSP' 'RAD51B' 'SMARCA5' 'MYH14'\n", " 'AGRN' 'CHMP5' 'TRIM21' 'SCLT1' 'PARP10' 'GTPBP2' 'CYTH1' 'PHACTR4'\n", " 'RUBCN' 'HIST1H2AC' 'DCP1A' 'SH3BGRL' 'LINC02432' 'ST14' 'AC090114.3'\n", " 'CD164' 'IFITM3' 'RBBP6' 'GCNT2' 'DAPP1' 'GMPR' 'WHAMM' 'NT5C2'\n", " 'AC104389.4' 'C1GALT1' 'DAPK1' 'PRKCE' 'IRF7' 'STX17' 'BRCA2' 'PHTF1'\n", " 'LIMCH1' 'RAPGEF5' 'BAG1' 'CPEB2' 'TMEM50A' 'SYNPO2' 'RAB8B' 'ENDOD1'\n", " 'GPR180' 'CCSER1' 'MXD1' 'SHISA5' 'SPATA6L' 'USP28' 'ARHGAP26' 'RIPK1'\n", " 'ST3GAL2' 'FAM122C' 'CD55' 'FRS2' 'RABGAP1L' 'SEMA4B' 'RNF43' 'NRIP1'\n", " 'TMEM63A' 'B3GNT2' 'C9orf3' 'NADK' 'ETS2' 'ELOVL6' 'NCOA2' 'L2HGDH'\n", " 'BIRC2' 'FBXL7' 'ARID4B' 'WHRN' 'PCMTD1' 'KDM2A' 'CLIC4' 'TTC6' 'SMURF1'\n", " 'ARHGAP21' 'SSBP3' 'TBK1' 'DBF4B' 'IGFL2-AS1' 'FBXO32' 'LAMB1' 'LIN52'\n", " 'GPT2' 'SEC24D' 'CAB39' 'CUEDC1' 'GTF2B' 'LIPG' 'TACSTD2' 'KCNMB4' 'GSN'\n", " 'ABTB2' 'NKAPP1' 'CUL4B' 'GLI3' 'AKAP7' 'IRF6' 'DISC1' 'MYD88' 'VEGFA'\n", " 'CRTC3' 'SLC44A3-AS1' 'ANO5' 'BACE2' 'GRINA' 'FOXO3' 'NID1' 'AL390728.4'\n", " 'TARID' 'FAM221A' 'GPC5' 'POR' 'NRCAM' 'RELL1' 'SREBF1' 'TM9SF2' 'PCDH1'\n", " 'AC098829.1' 'ANKRD33B' 'SAP130' 'SOS1' 'BCL2L11' 'TMX1' 'ATP10A'\n", " 'PLEKHA7' 'TCF20' 'DUSP16' 'NME7' 'PSEN1' 'IRAK2' 'CDK18' 'MED25' 'MTF2'\n", " 'TOR1B' 'TET2' 'DROSHA' 'TENT2' 'MIER1' 'KIAA1551' 'OSMR-AS1' 'PGGHG'\n", " 'MITF' 'SLC16A3' 'STAG3L5P' 'MED13' 'IFI27L1' 'PDLIM1' 'CATSPERE'\n", " 'EXOSC9' 'NR2F2-AS1' 'CDC42EP3' 'CPEB4' 'TOR1AIP1' 'PRDX6' 'RORA' 'SFI1'\n", " 'ALOX12P2' 'LRIG3' 'NPC1' 'ERVK3-1' 'MKNK1' 'DHRS9' 'LINC00431' 'KLF5'\n", " 'IL1RAPL1' 'AQP3' 'SQSTM1' 'PXK' 'CD58' 'AARS' 'CCDC146' 'ACSL1' 'CTBS'\n", " 'FXYD3' 'IMPA1' 'ADAMTSL3' 'AC092920.1' 'AC025159.1' 'SLC45A4' 'KLF3'\n", " 'HK2' 'SWT1' 'ENTPD6' 'RP2' 'AC016708.1' 'SMAD3' 'P4HA1' 'GATA3' 'CDC73'\n", " 'HIST1H4H' 'KLHL20' 'MST1R' 'EFL1' 'DAB2IP' 'CPED1' 'CHD8' 'DIP2B'\n", " 'ALOX12-AS1' 'KIAA0513' 'PIWIL4' 'RAD9A' 'GAREM1' 'ENY2' 'SLC41A2'\n", " 'DNAJC6' 'RGS6' 'CREBRF' 'TM4SF4' 'MDK' 'ABAT' 'FER1L4' 'NCR3LG1' 'NMU'\n", " 'STRIP2' 'STX3' 'FKBP5' 'AIM2' 'UNK' 'RTKN2' 'AIDA' 'BCL2L13' 'SH3GLB1'\n", " 'NT5E' 'CHD6' 'GPRC5A' 'HBP1' 'CALCOCO1' 'LINC00472' 'GMDS-DT' 'POLR2J4'\n", " 'SRBD1' 'RNF38' 'SAMD12' 'LSR' 'IGFBP4' 'PKIB' 'EPB41L1' 'AC138123.1'\n", " 'ANKDD1A' 'GDAP1' 'PPP2R2A' 'EPS8L1' 'NRBP1']\n", "            </td>\n", "          </tr>\n", "        \n", "          <tr>\n", "            <th>\n", "            [no name]\n", "            </th>\n", "            <td>\n", "            ['GBP2' 'DPYD' 'VPS13C' 'CFH' 'SYNE2' 'IDO1' 'CIITA' 'MYCBP2' 'DOCK9'\n", " 'CASP8' 'UBE2L6' 'STAT3' 'DGLUCY' 'SMCHD1' 'UBA6' 'PSMB9' 'SMG1' 'GBP5'\n", " 'GOLM1' 'CD74' 'PLCH1' 'USP15' 'TOP1' 'CD2AP' 'IRF9' 'AC005515.1'\n", " 'SIPA1L1' 'WDR25' 'RBCK1' 'IL15RA' 'ATXN7' 'FOXP1' 'AFF1' 'IL18BP' 'HELB'\n", " 'CD274' 'AC005515.2' 'FAM111A' 'HDAC4' 'DENND1A' 'PSMA5' 'CALCOCO2'\n", " 'GSTK1' 'DNAJA1' 'CASP7' 'WDFY1' 'AC016831.7' 'MCUB' 'CACNA2D1' 'CADPS2'\n", " 'USP42' 'RFX3' 'PATL2' 'SLC12A7' 'XPR1' 'BTN3A3' 'BTN3A1' 'NOD1' 'PSMA4'\n", " 'HLA-F' 'TRIM31' 'SERPING1' 'CCNL1' 'TRIM26' 'NUDCD1' 'OGFR' 'CXCL9'\n", " 'NUCB1' 'FSTL4' 'LINC02328' 'PCYT1A' 'PARD3' 'LINS1' 'AL157871.4'\n", " 'PPP1R12A' 'ASCC3' 'MMP25-AS1' 'PDIA3' 'MAX' 'HAPLN3' 'OTUD4' 'ECPAS'\n", " 'ETV7' 'CASP10' 'CCDC68' 'APOL3' 'SETX' 'SLC30A7' 'RALB' 'STK3' 'SPTLC3'\n", " 'TRIM44' 'PPA1' 'NFIX' 'LPGAT1' 'PSMA3' 'BATF2' 'SFMBT2' 'CNDP2' 'USF1'\n", " 'PSMA2' 'CUL2' 'DNPEP' 'HPS3' 'UHMK1' 'GTPBP1' 'MUC16' 'USP33' 'RAB27A'\n", " 'PHC3' 'MLLT3' 'TESK2' 'CXCL11' 'C1RL-AS1' 'RNF19A' 'LPP' 'PSME1' 'KARS'\n", " 'BTN3A2' 'ERP44' 'KIF13A' 'IGF2BP2' 'VPS54' 'PSMB2' 'POMP' 'C2orf42'\n", " 'HK1' 'RPS6KA5' 'ZEB1' 'CPQ' 'SPSB1' 'LINC02577' 'CXCL10' 'HSD17B12'\n", " 'KIF2A' 'SNTB2' 'GBP1P1' 'MVP' 'TRIM69' 'CALR' 'TRIB2' 'ARHGAP6'\n", " 'TMEM140' 'IFI35' 'USP25' 'BAZ2A' 'ABHD16A' 'JADE2' 'CCDC125' 'OSBPL10'\n", " 'RNF149' 'HNF4G' 'PRDM16' 'FAR2' 'TYMP' 'C1RL' 'SEC16B' 'RARRES1' 'CASP4'\n", " 'ATP13A3' 'LIMA1' 'ATP11C' 'C1R' 'GPR135' 'ATXN7.1' 'MET' 'LINC00511'\n", " 'ANK2' 'HLA-DMA' 'HEG1' 'KPNA5' 'PDP1' 'TBC1D32' 'LINC-PINT' 'RARS'\n", " 'REC8' 'FAM241A' 'MOB3B' 'LY75' 'LGMN' 'BCL6' 'HTR7' 'GUK1' 'ITM2B'\n", " 'STON2' 'MORC3' 'GALK2' 'RNF115' 'STK24' 'MED15' 'TMEM62' 'MYO6'\n", " 'FAM208B' 'TIA1' 'TNFSF10' 'HCP5' 'TAPBPL' 'FMR1' 'PTPRK' 'RPS6KC1'\n", " 'ANKRD10' 'KRT6A' 'L3HYPDH' 'CTNNBL1' 'SCML1' 'ADAMTSL4-AS1' 'PLSCR4'\n", " 'SLC2A13' 'ADAMTS6' 'ATP6V1A' 'NDUFA9' 'CAMK1D' 'ARF3' 'RARRES3' 'SNX6'\n", " 'UBXN4' 'DGKG' 'ZNF618' 'APP' 'SEMA3F' 'CMTR1' 'ZNF280D' 'NMT1' 'NUP210'\n", " 'FYTTD1' 'INSIG1' 'MCTP2' 'NNMT' 'DTNBP1' 'MICA' 'MAN1A1' 'ZFYVE28'\n", " 'MAP3K14' 'AL136418.1' 'CLIC2' 'COQ8B' 'CIR1' 'RNF114' 'NAPA' 'CALM3'\n", " 'SLC37A3' 'CPNE8' 'ARHGAP5' 'XPO6' 'GTF2E2' 'TANC1' 'LMTK2' 'CLUAP1'\n", " 'PRRG1' 'LARP1' 'DUOX2' 'TCIRG1' 'ARAP2' 'FNBP4' 'BCL2L14' 'ESR2' 'MOCOS'\n", " 'FAM117B' 'LARS2' 'CSNK1G1' 'VPS9D1' 'AC092944.1' 'HSP90B1' 'AGPAT3'\n", " 'CGAS' 'ELMSAN1' 'TMEM51' 'SOAT1' 'LGALS8' 'HS3ST3A1' 'CLIP1' 'CTSL'\n", " 'KIF18A' 'HIRA' 'VCPIP1' 'MIR4435-2HG' 'PMAIP1' 'PGAP1' 'GNA13' 'HEXDC'\n", " 'PSMB7' 'RMDN3' 'LINC01169' 'ZFYVE26' 'TAOK3' 'LINC01619' 'CPEB3'\n", " 'FAM83B' 'ARL5B' 'C6orf62' 'CTSC' 'ERI1' 'CAMK2D' 'RHBDD1' 'ERLIN1'\n", " 'RMC1' 'AVIL' 'SH3KBP1' 'PTK2B' 'ZNF277' 'GCLM' 'GON4L' 'RAB31' 'SELENOI'\n", " 'ELF1' 'UTP4' 'VPS13B' 'GBP6' 'PCDH7' 'NCOA3' 'FAS' 'PKN2-AS1' 'STT3B'\n", " 'SLC37A1' 'STX18' 'CHN2' 'MITD1' 'CDC42EP4' 'PLCL1' 'KYAT3' 'DYNLT1'\n", " 'KDM6A' 'KREMEN1' 'PPARGC1B' 'TNFSF13B' 'KDM7A' 'SLC44A1' 'CENPA' 'MBP'\n", " 'ITGA6' 'TENM3' 'MIR3681HG' 'AP005212.4' 'EDEM2' 'CDH11' 'USP9X' 'RNF31'\n", " 'CAPS2' 'AC073610.2' 'TEP1' 'ASB7' 'ZFP36' 'AK4' 'TLK2' 'TSKU'\n", " 'AC004917.1' 'PITPNB' 'PTPRA' 'NHEJ1' 'MTAP' 'TCF4' 'RAPH1' 'ARNTL'\n", " 'RNF152' 'RFX3-AS1' 'LIPH' 'TVP23C' 'ZNF462' 'MFSD1' 'EGLN3' 'ANKRD20A5P'\n", " 'CXorf38' 'SPAG17' 'AC022335.1' 'KDSR' 'SCYL3' 'PDIA6' 'EPPK1' 'CDRT4'\n", " 'SLCO1B1' 'RICTOR' 'C18orf25' 'CEP57L1' 'AFF4' 'TUSC3' 'AL136018.1'\n", " 'KIAA0040' 'OAF' 'POLQ' 'C4orf19' 'RAB23' 'HYOU1' 'AK2' 'RAB2B' 'ANXA7'\n", " 'EIF4A3' 'SNX16' 'LINC00623' 'ADCY7' 'DDX23' 'ACSL3' 'TPGS2' 'YEATS2'\n", " 'DNMT3B' 'USO1' 'SPATA13' 'CDKN2A' 'ITPKC' 'SCARB2' 'DHX36' 'USP43'\n", " 'RPS16' 'ATR' 'MLLT6' 'ASAP1' 'ESR1' 'WDR37' 'CLIC5' 'MFSD14A' 'SMC4'\n", " 'AC006978.2' 'ME2' 'ATP10D' 'DECR1' 'RASAL2' 'FGF13' 'CMC2' 'PARP11'\n", " 'SYNJ2BP' 'HMCN1' 'FZD5' 'CLCN3' 'ZNF761']\n", "            </td>\n", "          </tr>\n", "        \n", "          <tr>\n", "            <th>\n", "            ...\n", "            </th>\n", "            <td>\n", "            ...\n", "            </td>\n", "          </tr>\n", "        \n", "          <tr>\n", "            <th>\n", "            [no name]\n", "            </th>\n", "            <td>\n", "            ['TNFAIP2' 'ICAM1' 'DOCK4' 'RELB' 'C3' 'NFKB2' 'KLHL5' 'CXCL8' 'ITGAV'\n", " 'IL6ST' 'IFNGR1' 'IFNAR2' 'LAMC2' 'FNDC3B' 'SGPP2' 'MAP4K4' 'ZMIZ2'\n", " 'PODXL' 'AC009226.1' 'EGFR' 'CLIP2' 'ITSN2' 'PTPRJ' 'HMGA2' 'ETS1'\n", " 'RAI14' 'TRAF3' 'ZBTB46' 'RFFL' 'MYO10' 'MYO1B' 'RNF207' 'ERN1' 'ASS1'\n", " 'ITPR2' 'NUAK2' 'AKR1B1' 'MTSS1' 'MMP7' 'RFTN1' 'VEZT' 'VDR' 'FMNL3'\n", " 'IFNGR2' 'BID' 'ITGB8' 'LAMC1' 'SH3RF2' 'SMURF2' 'TMEM132A' 'PLCB4'\n", " 'ST3GAL1' 'EREG' 'STEAP2' 'NAV2' 'AC025580.2' 'ARHGEF28' 'ST3GAL4'\n", " 'GNGT1' 'CYP3A5' 'CCDC9B' 'SSH1' 'ZSWIM4' 'UXS1' 'CDK6' 'TMTC2' 'PARD3B'\n", " 'TJP2' 'TM4SF1' 'VMP1' 'DIAPH2' 'SLC2A6' 'WTAP' 'SVIL' 'DMXL2' 'TMEM156'\n", " 'ATP2B4' 'UBE2E2' 'B4GALT1' 'APLF' 'FAM160A1' 'MGST1' 'TNKS1BP1' 'PTBP3'\n", " 'TXNRD1' 'SORBS1' 'NRP2' 'REL' 'AGMO' 'TRIO' 'DNAJC10' 'PTPRC' 'ZHX2'\n", " 'TBL1XR1' 'SLMAP' 'GSK3B' 'HIP1' 'SIPA1L3' 'SLC28A3' 'UBE2E1' 'SLC25A37'\n", " 'LINC02240' 'ARL6IP5' 'AFAP1' 'PTPN1' 'PTTG1IP' 'SBNO2' 'AKT3' 'SLC39A8'\n", " 'UGCG' 'AHR' 'ANO9' 'COL4A2' 'TAB2' 'PHLDA1' 'ICA1' 'BCL3' 'FNBP1'\n", " 'EFHD2' 'C5orf15' 'ACTR3C' 'AKAP2' 'TGFA' 'CD59' 'FUT8' 'ABCA5' 'PAPPA'\n", " 'ZFAND6' 'CTNND1' 'WWC2' 'NUMB' 'IQGAP1' 'HAVCR1' 'DSC2' 'PLXNA1' 'SUSD6'\n", " 'GFPT2' 'KIFAP3' 'ZC3H12A' 'WWC1' 'TPCN1' 'RAB3IP' 'IL10RB' 'VNN1'\n", " 'ARHGEF3' 'CSRP1' 'LRCH3' 'PPARD' 'SHB' 'SEPT11' 'GLIPR1' 'SERPINA1'\n", " 'TNFRSF10B' 'LRRC1' 'CHST15' 'EHD1' 'TBC1D2B' 'RAP2C-AS1' 'AMPD3'\n", " 'AC119674.2' 'SQLE' 'LHFPL2' 'PKM' 'ANKRD12' 'ERO1A' 'ADAM8' 'ST20'\n", " 'JCAD' 'NOTCH2' 'ZC3H7B' 'MYO1D' 'P4HA2' 'GABBR1' 'MYH9' 'LRIG1' 'ADGRA3'\n", " 'C15orf39' 'SHROOM3' 'ABR' 'LRRC49' 'SERPINB2' 'YAP1' 'F11R' 'DNTTIP1'\n", " 'DZIP3' 'CTNNB1' 'NTSR1' 'HS6ST2']\n", "            </td>\n", "          </tr>\n", "        \n", "          <tr>\n", "            <th>\n", "            [no name]\n", "            </th>\n", "            <td>\n", "            ['TNFAIP3' 'BIRC3' 'PARP14' 'TNFAIP2' 'TAPBP' 'RNF213' 'ICAM1' 'SOD2'\n", " 'IRAK2' 'NFKB1' 'DDX60L' 'KYNU' 'NFKBIA' 'RELB' 'EXT1' 'C3' 'OPTN'\n", " 'NFKB2' 'DDX58' 'IKBKE' 'CD47' 'IRF1' 'CTSS' 'CXCL8' 'HDAC9' 'IFNGR1'\n", " 'B2M' 'NFAT5' 'IFNAR2' 'FNDC3B' 'IFIH1' 'OAS3' 'LINC02100' 'SGPP2'\n", " 'STAT1' 'XAF1' 'IFIT3' 'ZMIZ2' 'CFB' 'ETV6' 'PARP8' 'SEMA3A' 'GBP1'\n", " 'TAP2' 'HLA-B' 'AC009226.1' 'DUSP16' 'SAMD9L' 'CLIP2' 'WARS' 'NAMPT'\n", " 'BACH1' 'DRAM1' 'ITSN2' 'AC116366.3' 'ARHGAP26' 'TAP1' 'SDC4' 'OAS2'\n", " 'NFKBIZ' 'TRIM22' 'TRAF3' 'ZBTB46' 'RFFL' 'ANKRD33B' 'TRANK1' 'SP100'\n", " 'KIAA1217' 'NLRC5' 'NUB1' 'MX1' 'ERAP1' 'TRIM25' 'CD82' 'NFE2L3' 'EPSTI1'\n", " 'MX2' 'BIRC2' 'B4GALT5' 'SDCBP' 'SAMHD1' 'STAT2' 'NCOA7' 'LAP3' 'CLIC4'\n", " 'RNF207' 'IFI44L' 'ERN1' 'SAMD9' 'FAM129A' 'ASS1' 'MAP3K8' 'MAST4'\n", " 'ARHGAP42' 'NUAK2' 'AKR1B1' 'HLA-A' 'IRF2' 'MMP7' 'NCEH1' 'IFI44' 'RFTN1'\n", " 'ZC3HAV1' 'AF117829.1' 'IFIT2' 'HERC6' 'DENND5A' 'APOL6' 'VEZT' 'CSF1'\n", " 'ERAP2' 'LYN' 'TDRD7' 'PARP12' 'MACC1' 'VDR' 'RUNX2' 'GSAP' 'HELZ2' 'PML'\n", " 'CYLD' 'IFNGR2' 'SLCO3A1' 'SLC41A2' 'TRAFD1' 'BID' 'XRN1' 'ITGB8'\n", " 'SH3RF2' 'SP140L' 'GBP3' 'TMEM132A' 'ELOVL7' 'FRMD4A' 'PARP4' 'SQOR'\n", " 'RHBDF2' 'SPPL2A' 'EREG' 'TMEM123' 'STEAP2' 'AC025580.2' 'APOL2' 'PTPN12'\n", " 'PLPP3' 'ST3GAL4' 'GNGT1' 'ZNFX1' 'DAPK2' 'CYP3A5' 'SAT1' 'CCDC9B' 'SSH1'\n", " 'GCH1' 'ZSWIM4' 'FCHSD2' 'UXS1' 'TMTC2' 'PARD3B' 'CMPK2' 'PLSCR1' 'ECE1'\n", " 'TJP2' 'HLA-E' 'TM4SF1' 'VMP1' 'ZC3H12C' 'CPEB2' 'TRIM38' 'DUSP5' 'CLDN1'\n", " 'SLC2A6' 'WTAP' 'SVIL' 'JAK1' 'DMXL2' 'TMEM156' 'DPYSL2' 'ATP2B4' 'BAZ1A'\n", " 'EFR3A' 'GRAMD2B' 'IFIT1' 'NMI' 'ISG20' 'UBE2E2' 'APLF' 'FAM160A1'\n", " 'MGST1' 'RND3' 'PARP9' 'TNKS1BP1' 'ZBTB20' 'PTBP3' 'OSMR' 'IGSF1'\n", " 'TXNRD1' 'SERPINB1' 'JAK2' 'SORBS1' 'PSME2' 'SLC9A7' 'RIPK2' 'TRIM5'\n", " 'RNF19B' 'REL' 'AGMO' 'ALCAM' 'DDX60' 'UBE2H' 'AC241585.2' 'DNAJC10'\n", " 'PTPRC' 'APOL1' 'ZHX2' 'TBL1XR1' 'OASL' 'SLMAP' 'NECTIN2' 'GSK3B' 'HIP1'\n", " 'OAS1' 'APCDD1L-DT' 'TBK1' 'DENND3' 'CD58' 'SLC28A3' 'AL390334.1'\n", " 'UBE2E1' 'DNAJC1' 'LINC02240' 'NT5C2' 'AL050403.2' 'PSMA6' 'FTH1'\n", " 'ZNF710' 'RASA2' 'TMSB4X' 'MCTP1' 'ARL6IP5' 'DTX3L' 'HLA-C' 'ADAR' 'PLK2'\n", " 'IL4I1' 'RABGAP1L' 'PTPN1' 'PTTG1IP' 'FMNL2' 'ROBO1' 'SBNO2' 'SLC39A8'\n", " 'STAT6' 'UGCG' 'AHR' 'ANO9' 'SUSD1' 'PATL1' 'GPD2' 'LRMDA' 'TEC' 'CAB39'\n", " 'BBX' 'TAB2' 'LHFPL6' 'PHLDA1' 'ICA1' 'BCL3' 'IFI6' 'CCND3' 'FNBP1'\n", " 'DYNC1H1' 'SEC24D' 'EFHD2' 'C5orf15' 'QKI' 'ADAM17' 'ACTR3C' 'RNF144B'\n", " 'ZDHHC14' 'EIF2AK2' 'CDC42SE2' 'FBXO32' 'WASHC4' 'FUT8' 'ABCA5' 'PAPPA'\n", " 'DAPP1' 'N4BP1' 'PHF11' 'ZFAND6' 'ACSL5' 'CTNND1' 'PSEN1' 'NUMB' 'HAVCR1'\n", " 'DSC2' 'PLXNA1' 'PLEKHA4' 'BMP2K' 'RAB27B' 'CAST' 'TBC1D1' 'SUSD6' 'CASK'\n", " 'KIFAP3' 'CDKL5' 'ZC3H12A' 'PLD1' 'WWC1' 'MAP3K13' 'ANKRD18B' 'TPCN1'\n", " 'SERPINB9' 'RAB3IP' 'IL10RB' 'FAAH2' 'PANX1' 'SLCO1B7' 'RGL1' 'VNN1'\n", " 'USP6NL' 'ARHGEF3' 'BCL9L' 'UBE2Z' 'MBOAT1' 'RNF24' 'FYB1' 'AC005062.1'\n", " 'LRCH3' 'BBOX1-AS1' 'SEPT11' 'GLIPR1' 'GMDS-DT' 'ANKFY1' 'SERPINA1'\n", " 'TNFRSF10B' 'LRRC1' 'EHD1' 'MOV10' 'RAP2C-AS1' 'AC119674.2' 'SQLE'\n", " 'LHFPL2' 'PKM' 'GPR158' 'ANKRD12' 'ERO1A' 'ADAM8' 'STYK1' 'EHD4' 'SPAG9'\n", " 'ST20' 'JCAD' 'RRBP1' 'NOCT' 'STARD4' 'CEACAM1' 'NBN' 'ZC3H7B' 'RAB8B'\n", " 'GRINA' 'USP18' 'FOXO3' 'SLC25A28' 'P4HA2' 'GABBR1' 'LRIG1' 'ALPK1'\n", " 'PCAT1' 'ADGRA3' 'C15orf39' 'SHROOM3' 'ABR' 'ARHGAP17' 'LRRC49' 'TRIM14'\n", " 'SERPINB2' 'C17orf67' 'YAP1' 'DNTTIP1' 'ANKS1B' 'AC104041.1' 'DZIP3'\n", " 'HS6ST2' 'MLKL' 'ZNF267']\n", "            </td>\n", "          </tr>\n", "        \n", "          <tr>\n", "            <th>\n", "            [no name]\n", "            </th>\n", "            <td>\n", "            ['PTPRK' 'FRMD6' 'TPM1' 'PACS1' 'LPCAT2' 'SLC4A7' 'THBS1' 'SH3KBP1'\n", " 'PHLDB1' 'AC010343.3' 'JAG1' 'ACTN1' 'NHS' 'NEK10' 'SKIL' 'BMPR2'\n", " 'CEP170' 'CMIP' 'ASAP2' 'KRT7' 'MARCH4' 'TUFT1' 'SPOCK1' 'MKL1' 'CAP1'\n", " 'INHBA' 'LRRC8C' 'CDK14' 'DYSF' 'VAV2' 'ELK3' 'MBOAT2' 'SCD' 'RASGRF2'\n", " 'AMIGO2' 'PSMD2' 'ACTB' 'PDXK' 'ABHD2' 'NEBL' 'LMCD1' 'TIMP2' 'KLF12'\n", " 'ARHGEF18' 'CCDC80' 'KLF7' 'ITGB4' 'FERMT1' 'KDM7A' 'PAWR' 'MPRIP' 'PXN'\n", " 'ALPK2' 'ADGRG1' 'LDLRAD4' 'GALNT10' 'TMC7' 'UACA' 'MTAP' 'NDST1'\n", " 'MIR181A2HG' 'SDCCAG8' 'UBASH3B' 'CLSTN1' 'CDH6' 'CAMK1D' 'LTBP2'\n", " 'SH3PXD2A' 'JAZF1' 'TPM4' 'NEK7' 'MOB3B' 'PCNX1' 'MICAL3' 'CAVIN1'\n", " 'LRCH1' 'WNT5B' 'ZBTB38' 'ITGA3' 'ZNF185' 'FAM129B' 'IVNS1ABP' 'ADAMTS6'\n", " 'PLEK2' 'OCIAD2' 'MAML3' 'KDM6B' 'FGF2' 'DOCK5' 'IL13RA1' 'PBX3' 'SLIT3'\n", " 'SLC22A3' 'SDC1' 'HS6ST3' 'LTBP3' 'TJP1' 'PCED1B' 'COL1A1' 'SGCD'\n", " 'PFKFB4' 'RASA1' 'DLC1' 'ITGA5' 'EPHB2' 'SYNE1' 'RBMS3' 'S100A10'\n", " 'SETBP1' 'STON2' 'GALNT14' 'NCOR2' 'PLOD2' 'PPFIBP1' 'PTPN14' 'IL11'\n", " 'KIAA1549L' 'FOXP1' 'PLA2R1' 'TSPAN14' 'P4HA3' 'PTPRF' 'VCL' 'KCNH1'\n", " 'PLS3' 'WDR66' 'PLXNA2' 'BAIAP2L1' 'PFKP' 'MEG8' 'SACS' 'DOCK9' 'EEF2'\n", " 'GALNT1' 'TPST1' 'ATP8A1' 'CTPS1' 'PRSS3' 'NET1' 'ACLY' 'SORCS2' 'YWHAZ'\n", " 'VGLL3' 'CALM2' 'UBA6' 'IQCJ-SCHIP1' 'MRC2' 'PLCXD2' 'ACTG1' 'PRR5L'\n", " 'KIF26B' 'LUZP1' 'ARHGEF40' 'PICALM' 'LINC02086' 'ANKLE2' 'CDH4' 'DNMBP'\n", " 'STX1A' 'DCLK2' 'SMTN' 'RDX' 'DCBLD1' 'FGFR1' 'MEG3' 'RALB' 'CYP24A1'\n", " 'G3BP1' 'ATXN7L1' 'SPACA6' 'LIMS1' 'BMP1' 'TGFB1' 'TNS4' 'SERPINE2'\n", " 'C16orf74' 'PTK2B' 'PXYLP1' 'CTNNA1' 'INPP5A' 'LINC00842' 'SNHG14'\n", " 'ADAMTSL4-AS1' 'AFF1' 'CYR61' 'TBC1D16' 'CORO1C' 'TGFBR1' 'GALNT2'\n", " 'KLHL25' 'MAP3K4' 'FERMT2' 'ZEB1' 'CCDC32' 'RIN2' 'LAMB1' 'PLPP4'\n", " 'FAM83A' 'MIR193BHG' 'AC027288.3' 'CALR' 'DNM3' 'GBF1' 'NPTX1' 'MED15'\n", " 'LOXL2' 'FSTL1' 'GYS1' 'PVR' 'EMP1' 'RTN4' 'SRGAP1' 'FBXL13' 'MLXIP'\n", " 'PDLIM7' 'LINC01411' 'FAT1' 'TNS1' 'SEPT10' 'CTGF' 'RAB3B' 'VCAN' 'CHIC2'\n", " 'SPTLC2' 'TMBIM1' 'EDN1' 'KALRN' 'ATP11C' 'CD151' 'SRC' 'IL4R' 'SLC7A1'\n", " 'EFR3B' 'CYTOR' 'SMOX' 'P3H2']\n", "            </td>\n", "          </tr>\n", "        \n", "        </tbody>\n", "        </table>\n", "        "], "text/plain": ["<rpy2.robjects.vectors.ListVector object at 0x2b0fa34e1400> [RTYPES.VECSXP]\n", "R classes: ('list',)\n", "[Str..., Str..., Str..., Str..., Str..., Str..., Str..., Str...]\n", "  <no name>: <class 'rpy2.rinterface_lib.sexp.StrSexpVector'>\n", "  <rpy2.rinterface_lib.sexp.StrSexpVector object at 0x2b0f86b8ec40> [RTYPES.STRSXP]\n", "  <no name>: <class 'rpy2.rinterface_lib.sexp.StrSexpVector'>\n", "  <rpy2.rinterface_lib.sexp.StrSexpVector object at 0x2b0f86b8df40> [RTYPES.STRSXP]\n", "  <no name>: <class 'rpy2.rinterface_lib.sexp.StrSexpVector'>\n", "  <rpy2.rinterface_lib.sexp.StrSexpVector object at 0x2b0f86b8e8c0> [RTYPES.STRSXP]\n", "  <no name>: <class 'rpy2.rinterface_lib.sexp.StrSexpVector'>\n", "  <rpy2.rinterface_lib.sexp.StrSexpVector object at 0x2b0f86b8df40> [RTYPES.STRSXP]\n", "  <no name>: <class 'rpy2.rinterface_lib.sexp.StrSexpVector'>\n", "  <rpy2.rinterface_lib.sexp.StrSexpVector object at 0x2b0f86b8ec40> [RTYPES.STRSXP]\n", "  <no name>: <class 'rpy2.rinterface_lib.sexp.StrSexpVector'>\n", "  <rpy2.rinterface_lib.sexp.StrSexpVector object at 0x2b0f86b8ed40> [RTYPES.STRSXP]\n", "  <no name>: <class 'rpy2.rinterface_lib.sexp.StrSexpVector'>\n", "  <rpy2.rinterface_lib.sexp.StrSexpVector object at 0x2b0f86b8e8c0> [RTYPES.STRSXP]\n", "  <no name>: <class 'rpy2.rinterface_lib.sexp.StrSexpVector'>\n", "  <rpy2.rinterface_lib.sexp.StrSexpVector object at 0x2b0f86b8df40> [RTYPES.STRSXP]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["from rpy2.robjects import r, pandas2ri\n", "pandas2ri.activate()\n", "lookobj=r[\"lookobj\"]\n", "lookobj[:]\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["%%R\n", "library(Seurat)\n", "library(SeuratObject)\n", "\n", "seurat_obj <- readRDS(\"/public/home/<USER>/whr/VCC/ST_others/Jiang_et_al_2025/Bulk_RNAseq_Seurat_object_IFNG_and_TGFB_stim.rds\")  # 替换成你自己的路径\n", "\n", "# 提取 metadata（细胞信息）和 feature metadata（基因信息）\n", "obs_df <- <EMAIL>\n", "var_df <- seurat_obj@assays$<EMAIL>\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ .rds 文件读取成功！\n", "🔹 obs (细胞元数据):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>orig.ident</th>\n", "      <th>nCount_RNA</th>\n", "      <th>nFeature_RNA</th>\n", "      <th>sample</th>\n", "      <th>stim</th>\n", "      <th>replicate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ACTTCGTCAGCT</th>\n", "      <td>SeuratProject</td>\n", "      <td>515093.0</td>\n", "      <td>13728</td>\n", "      <td>TGFB1_rep2</td>\n", "      <td>TGFB1</td>\n", "      <td>rep2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CAGACATCGCAA</th>\n", "      <td>SeuratProject</td>\n", "      <td>504670.0</td>\n", "      <td>13840</td>\n", "      <td>TGFB1_rep1</td>\n", "      <td>TGFB1</td>\n", "      <td>rep1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TAGCAGACTGGA</th>\n", "      <td>SeuratProject</td>\n", "      <td>500294.0</td>\n", "      <td>13975</td>\n", "      <td>TGFB1_rep3</td>\n", "      <td>TGFB1</td>\n", "      <td>rep3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ACAGTGCTCATG</th>\n", "      <td>SeuratProject</td>\n", "      <td>660542.0</td>\n", "      <td>16530</td>\n", "      <td>IFNG_rep1</td>\n", "      <td>IFNG</td>\n", "      <td>rep1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CAGAGTCGACTT</th>\n", "      <td>SeuratProject</td>\n", "      <td>621008.0</td>\n", "      <td>16161</td>\n", "      <td>control_rep3</td>\n", "      <td>control</td>\n", "      <td>rep3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GAACACCATAGC</th>\n", "      <td>SeuratProject</td>\n", "      <td>737066.0</td>\n", "      <td>17062</td>\n", "      <td>IFNG_rep2</td>\n", "      <td>IFNG</td>\n", "      <td>rep2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTGTAGTGTGGA</th>\n", "      <td>SeuratProject</td>\n", "      <td>637770.0</td>\n", "      <td>16578</td>\n", "      <td>IFNG_rep3</td>\n", "      <td>IFNG</td>\n", "      <td>rep3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GTTGAGCTTGAC</th>\n", "      <td>SeuratProject</td>\n", "      <td>724544.0</td>\n", "      <td>17271</td>\n", "      <td>control_rep1</td>\n", "      <td>control</td>\n", "      <td>rep1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TAAGCGTGGAAC</th>\n", "      <td>SeuratProject</td>\n", "      <td>855684.0</td>\n", "      <td>18102</td>\n", "      <td>control_rep2</td>\n", "      <td>control</td>\n", "      <td>rep2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 orig.ident  nCount_RNA  nFeature_RNA        sample     stim  \\\n", "ACTTCGTCAGCT  SeuratProject    515093.0         13728    TGFB1_rep2    TGFB1   \n", "CAGACATCGCAA  SeuratProject    504670.0         13840    TGFB1_rep1    TGFB1   \n", "TAGCAGACTGGA  SeuratProject    500294.0         13975    TGFB1_rep3    TGFB1   \n", "ACAGTGCTCATG  SeuratProject    660542.0         16530     IFNG_rep1     IFNG   \n", "CAGAGTCGACTT  SeuratProject    621008.0         16161  control_rep3  control   \n", "GAACACCATAGC  SeuratProject    737066.0         17062     IFNG_rep2     IFNG   \n", "GTGTAGTGTGGA  SeuratProject    637770.0         16578     IFNG_rep3     IFNG   \n", "GTTGAGCTTGAC  SeuratProject    724544.0         17271  control_rep1  control   \n", "TAAGCGTGGAAC  SeuratProject    855684.0         18102  control_rep2  control   \n", "\n", "             replicate  \n", "ACTTCGTCAGCT      rep2  \n", "CAGACATCGCAA      rep1  \n", "TAGCAGACTGGA      rep3  \n", "ACAGTGCTCATG      rep1  \n", "CAGAGTCGACTT      rep3  \n", "GAACACCATAGC      rep2  \n", "GTGTAGTGTGGA      rep3  \n", "GTTGAGCTTGAC      rep1  \n", "TAAGCGTGGAAC      rep2  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# ⬅️ 从 R 把 obs 和 var 转成 pandas\n", "from rpy2.robjects import r, pandas2ri\n", "pandas2ri.activate()\n", "\n", "obs_r = r['obs_df']\n", "var_r = r['var_df']\n", "\n", "obs_df = pandas2ri.rpy2py(obs_r)\n", "var_df = pandas2ri.rpy2py(var_r)\n", "\n", "print(\"✅ .rds 文件读取成功！\")\n", "print(\"🔹 obs (细胞元数据):\")\n", "obs_df\n", "\n", "# print(\"🔹 var (基因元数据):\")\n", "# display(var_df)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The rpy2.ipython extension is already loaded. To reload it, use:\n", "  %reload_ext rpy2.ipython\n"]}], "source": []}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1] \"cell_data_set\"\n", "attr(,\"package\")\n", "[1] \"monocle3\"\n"]}, {"name": "stderr", "output_type": "stream", "text": ["R[write to console]: Loading required package: monocle3\n", "\n", "R[write to console]: Loading required package: SingleCellExperiment\n", "\n", "R[write to console]: Loading required package: SummarizedExperiment\n", "\n", "R[write to console]: Loading required package: GenomicRanges\n", "\n", "R[write to console]: Loading required package: GenomeInfoDb\n", "\n", "R[write to console]: Error: package or namespace load failed for ‘GenomeInfoDb’ in loadNamespace(i, c(lib.loc, .libPaths()), versionCheck = vI[[i]]):\n", " there is no package called ‘GenomeInfoDbData’\n", "\n", "R[write to console]: Failed with error:  \n", "R[write to console]: \n", "R[write to console]: ‘package ‘GenomeInfoDb’ could not be loaded’\n", "R[write to console]: \n", "R[write to console]: \n", "\n", "R[write to console]: Error in .requirePackage(package) : \n", "  unable to find required package ‘monocle3’\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Error in .requirePackage(package) : \n", "  unable to find required package ‘monocle3’\n"]}, {"ename": "RInterpreterError", "evalue": "Failed to parse and evaluate line '# 不加载任何包，直接读入\\nraw_obj <- readRDS(\"/public/home/<USER>/whr/VCC/ST_others/McFaline_Figuero_et_al_2024/GSM7056148_sciPlexGxE_1_preprocessed_cds.rds\")\\n# 看看对象结构\\nprint(class(raw_obj))        # \"cell_data_set\"\\nstr(raw_obj, max.level = 1)\\n\\n# # 查看 colData (细胞信息)\\n# cell_metadata <- slot(raw_obj, \"colData\")\\n# head(as.data.frame(cell_metadata))\\n\\n# # 查看 rowData (基因信息)\\n# gene_metadata <- slot(raw_obj, \"rowData\")\\n# head(as.data.frame(gene_metadata))\\n\\n# # 查看 assays（表达矩阵等）\\n# assays_list <- slot(raw_obj, \"assays\")\\n# assay_names <- names(assays_list)\\n# print(assay_names)\\n\\n# # 如果 counts 存在\\n# counts_mat <- assays_list[[\"counts\"]]\\n# counts_matrix <- counts_mat@listData$counts  # 可能是稀疏矩阵（SparseMatrix）\\n'.\nR error message: 'Error in .requirePackage(package) : \\n  unable to find required package ‘monocle3’'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRRuntimeError\u001b[0m                             Traceback (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/ipython/rmagic.py:385\u001b[0m, in \u001b[0;36mRMagics.eval\u001b[0;34m(self, code)\u001b[0m\n\u001b[1;32m    383\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    384\u001b[0m     \u001b[38;5;66;03m# Need the newline in case the last line in code is a comment.\u001b[39;00m\n\u001b[0;32m--> 385\u001b[0m     value, visible \u001b[38;5;241m=\u001b[39m \u001b[43mro\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mr\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mwithVisible(\u001b[39;49m\u001b[38;5;124;43m{\u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m})\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m%\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mcode\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    386\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ri\u001b[38;5;241m.\u001b[39membedded\u001b[38;5;241m.\u001b[39mRRuntimeError, \u001b[38;5;167;01mValueError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m exception:\n\u001b[1;32m    387\u001b[0m     \u001b[38;5;66;03m# Otherwise next return seems to have copy of error.\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/robjects/__init__.py:459\u001b[0m, in \u001b[0;36mR.__call__\u001b[0;34m(self, string)\u001b[0m\n\u001b[1;32m    458\u001b[0m p \u001b[38;5;241m=\u001b[39m rinterface\u001b[38;5;241m.\u001b[39mparse(string)\n\u001b[0;32m--> 459\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43meval\u001b[49m\u001b[43m(\u001b[49m\u001b[43mp\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    460\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m conversion\u001b[38;5;241m.\u001b[39mget_conversion()\u001b[38;5;241m.\u001b[39mrpy2py(res)\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/robjects/functions.py:208\u001b[0m, in \u001b[0;36mSignatureTranslatedFunction.__call__\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    207\u001b[0m         kwargs[r_k] \u001b[38;5;241m=\u001b[39m v\n\u001b[0;32m--> 208\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (\u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mSignatureTranslatedFunction\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    209\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/robjects/functions.py:131\u001b[0m, in \u001b[0;36mFunction.__call__\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    130\u001b[0m         new_kwargs[k] \u001b[38;5;241m=\u001b[39m cv\u001b[38;5;241m.\u001b[39mpy2rpy(v)\n\u001b[0;32m--> 131\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mFunction\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mnew_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mnew_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    132\u001b[0m res \u001b[38;5;241m=\u001b[39m cv\u001b[38;5;241m.\u001b[39mrpy2py(res)\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/rinterface_lib/conversion.py:45\u001b[0m, in \u001b[0;36m_cdata_res_to_rinterface.<locals>._\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     44\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m---> 45\u001b[0m     cdata \u001b[38;5;241m=\u001b[39m \u001b[43mfunction\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     46\u001b[0m     \u001b[38;5;66;03m# TODO: test cdata is of the expected CType\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/rinterface.py:817\u001b[0m, in \u001b[0;36mSexpClosure.__call__\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    816\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m error_occured[\u001b[38;5;241m0\u001b[39m]:\n\u001b[0;32m--> 817\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m embedded\u001b[38;5;241m.\u001b[39mRRuntimeError(_rinterface\u001b[38;5;241m.\u001b[39m_geterrmessage())\n\u001b[1;32m    818\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m res\n", "\u001b[0;31mRRuntimeError\u001b[0m: Error in .requirePackage(package) : \n  unable to find required package ‘monocle3’\n", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mRInterpreterError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[14], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mget_ipython\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_cell_magic\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mR\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m# 不加载任何包，直接读入\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43mraw_obj <- readRDS(\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/public/home/<USER>/whr/VCC/ST_others/Mc<PERSON><PERSON><PERSON>_<PERSON><PERSON>_et_al_2024/GSM7056148_sciPlexGxE_1_preprocessed_cds.rds\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# 看看对象结构\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43mprint(class(raw_obj))        # \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcell_data_set\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43mstr(raw_obj, max.level = 1)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# # 查看 colData (细胞信息)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# cell_metadata <- slot(raw_obj, \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcolData\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# head(as.data.frame(cell_metadata))\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# # 查看 rowData (基因信息)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# gene_metadata <- slot(raw_obj, \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrowData\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# head(as.data.frame(gene_metadata))\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# # 查看 assays（表达矩阵等）\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# assays_list <- slot(raw_obj, \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43massays\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# assay_names <- names(assays_list)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# print(assay_names)\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# # 如果 counts 存在\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# counts_mat <- assays_list[[\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcounts\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m]]\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m# counts_matrix <- counts_mat@listData$counts  # 可能是稀疏矩阵（SparseMatrix）\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/IPython/core/interactiveshell.py:2543\u001b[0m, in \u001b[0;36mInteractiveShell.run_cell_magic\u001b[0;34m(self, magic_name, line, cell)\u001b[0m\n\u001b[1;32m   2541\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbuiltin_trap:\n\u001b[1;32m   2542\u001b[0m     args \u001b[38;5;241m=\u001b[39m (magic_arg_s, cell)\n\u001b[0;32m-> 2543\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2545\u001b[0m \u001b[38;5;66;03m# The code below prevents the output from being displayed\u001b[39;00m\n\u001b[1;32m   2546\u001b[0m \u001b[38;5;66;03m# when using magics with decorator @output_can_be_silenced\u001b[39;00m\n\u001b[1;32m   2547\u001b[0m \u001b[38;5;66;03m# when the last Python token in the expression is a ';'.\u001b[39;00m\n\u001b[1;32m   2548\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(fn, magic\u001b[38;5;241m.\u001b[39mMAGIC_OUTPUT_CAN_BE_SILENCED, \u001b[38;5;28;01mFalse\u001b[39;00m):\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/ipython/rmagic.py:943\u001b[0m, in \u001b[0;36mRMagics.R\u001b[0;34m(self, line, cell, local_ns)\u001b[0m\n\u001b[1;32m    941\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m e\u001b[38;5;241m.\u001b[39mstdout\u001b[38;5;241m.\u001b[39mendswith(e\u001b[38;5;241m.\u001b[39merr):\n\u001b[1;32m    942\u001b[0m         \u001b[38;5;28mprint\u001b[39m(e\u001b[38;5;241m.\u001b[39merr)\n\u001b[0;32m--> 943\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[1;32m    944\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    945\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdevice \u001b[38;5;129;01min\u001b[39;00m DEVICES_STATIC:\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/ipython/rmagic.py:923\u001b[0m, in \u001b[0;36mRMagics.R\u001b[0;34m(self, line, cell, local_ns)\u001b[0m\n\u001b[1;32m    921\u001b[0m         return_output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    922\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 923\u001b[0m     text_result, result, visible \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43meval\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcode\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    924\u001b[0m     text_output \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m text_result\n\u001b[1;32m    925\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m visible:\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/rpy2/ipython/rmagic.py:389\u001b[0m, in \u001b[0;36mRMagics.eval\u001b[0;34m(self, code)\u001b[0m\n\u001b[1;32m    386\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ri\u001b[38;5;241m.\u001b[39membedded\u001b[38;5;241m.\u001b[39mRRuntimeError, \u001b[38;5;167;01mValueError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m exception:\n\u001b[1;32m    387\u001b[0m     \u001b[38;5;66;03m# Otherwise next return seems to have copy of error.\u001b[39;00m\n\u001b[1;32m    388\u001b[0m     warning_or_other_msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mflush()\n\u001b[0;32m--> 389\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m RInterpreterError(code, \u001b[38;5;28mstr\u001b[39m(exception),\n\u001b[1;32m    390\u001b[0m                             warning_or_other_msg)\n\u001b[1;32m    391\u001b[0m text_output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mflush()\n\u001b[1;32m    392\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m text_output, value, visible[\u001b[38;5;241m0\u001b[39m]\n", "\u001b[0;31mRInterpreterError\u001b[0m: Failed to parse and evaluate line '# 不加载任何包，直接读入\\nraw_obj <- readRDS(\"/public/home/<USER>/whr/VCC/ST_others/McFaline_Figuero_et_al_2024/GSM7056148_sciPlexGxE_1_preprocessed_cds.rds\")\\n# 看看对象结构\\nprint(class(raw_obj))        # \"cell_data_set\"\\nstr(raw_obj, max.level = 1)\\n\\n# # 查看 colData (细胞信息)\\n# cell_metadata <- slot(raw_obj, \"colData\")\\n# head(as.data.frame(cell_metadata))\\n\\n# # 查看 rowData (基因信息)\\n# gene_metadata <- slot(raw_obj, \"rowData\")\\n# head(as.data.frame(gene_metadata))\\n\\n# # 查看 assays（表达矩阵等）\\n# assays_list <- slot(raw_obj, \"assays\")\\n# assay_names <- names(assays_list)\\n# print(assay_names)\\n\\n# # 如果 counts 存在\\n# counts_mat <- assays_list[[\"counts\"]]\\n# counts_matrix <- counts_mat@listData$counts  # 可能是稀疏矩阵（SparseMatrix）\\n'.\nR error message: 'Error in .requirePackage(package) : \\n  unable to find required package ‘monocle3’'"]}], "source": ["%%R\n", "# 不加载任何包，直接读入\n", "raw_obj <- readRDS(\"/public/home/<USER>/whr/VCC/ST_others/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_et_al_2024/GSM7056148_sciPlexGxE_1_preprocessed_cds.rds\")\n", "# 看看对象结构\n", "print(class(raw_obj))        # \"cell_data_set\"\n", "str(raw_obj, max.level = 1)\n", "\n", "# # 查看 colData (细胞信息)\n", "# cell_metadata <- slot(raw_obj, \"colData\")\n", "# head(as.data.frame(cell_metadata))\n", "\n", "# # 查看 rowData (基因信息)\n", "# gene_metadata <- slot(raw_obj, \"rowData\")\n", "# head(as.data.frame(gene_metadata))\n", "\n", "# # 查看 assays（表达矩阵等）\n", "# assays_list <- slot(raw_obj, \"assays\")\n", "# assay_names <- names(assays_list)\n", "# print(assay_names)\n", "\n", "# # 如果 counts 存在\n", "# counts_mat <- assays_list[[\"counts\"]]\n", "# counts_matrix <- counts_mat@listData$counts  # 可能是稀疏矩阵（SparseMatrix）\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[11], line 26\u001b[0m\n\u001b[1;32m     24\u001b[0m             m[d\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgene_name\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m)] \u001b[38;5;241m=\u001b[39m d\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgene_id\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     25\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m m\n\u001b[0;32m---> 26\u001b[0m gtf_map \u001b[38;5;241m=\u001b[39m \u001b[43mload_gtf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m/public/share/liuguole/references/gencode.v43.annotation.gtf\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     27\u001b[0m \u001b[38;5;66;03m# 通用函数：补ID\u001b[39;00m\n\u001b[1;32m     28\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mensure_id\u001b[39m(name):\n", "Cell \u001b[0;32mIn[11], line 19\u001b[0m, in \u001b[0;36mload_gtf\u001b[0;34m(gtf_path)\u001b[0m\n\u001b[1;32m     17\u001b[0m m \u001b[38;5;241m=\u001b[39m {}\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(gtf_path) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[0;32m---> 19\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m line \u001b[38;5;129;01min\u001b[39;00m f:\n\u001b[1;32m     20\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m line\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m#\u001b[39m\u001b[38;5;124m'\u001b[39m): \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[1;32m     21\u001b[0m         chrom, src, feat, start, end, score, strand, frame, attr \u001b[38;5;241m=\u001b[39m line\u001b[38;5;241m.\u001b[39msplit(\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/codecs.py:319\u001b[0m, in \u001b[0;36mBufferedIncrementalDecoder.decode\u001b[0;34m(self, input, final)\u001b[0m\n\u001b[1;32m    314\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_buffer_decode\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m, errors, final):\n\u001b[1;32m    315\u001b[0m     \u001b[38;5;66;03m# Overwrite this method in subclasses: It must decode input\u001b[39;00m\n\u001b[1;32m    316\u001b[0m     \u001b[38;5;66;03m# and return an (output, length consumed) tuple\u001b[39;00m\n\u001b[1;32m    317\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m\n\u001b[0;32m--> 319\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mdecode\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m, final\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[1;32m    320\u001b[0m     \u001b[38;5;66;03m# decode input (taking the buffer into account)\u001b[39;00m\n\u001b[1;32m    321\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbuffer \u001b[38;5;241m+\u001b[39m \u001b[38;5;28minput\u001b[39m\n\u001b[1;32m    322\u001b[0m     (result, consumed) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_buffer_decode(data, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39merrors, final)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# 导入库\n", "import os, re\n", "import scanpy as sc\n", "import pandas as pd\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "# RDS 处理\n", "try:\n", "    import rpy2.robjects as ro\n", "    from rpy2.robjects import pandas2ri\n", "    pandas2ri.activate()\n", "    R_AVAILABLE = True\n", "except:\n", "    R_AVAILABLE = False\n", "# GTF parser\n", "def load_gtf(gtf_path):\n", "    m = {}\n", "    with open(gtf_path) as f:\n", "        for line in f:\n", "            if line.startswith('#'): continue\n", "            chrom, src, feat, start, end, score, strand, frame, attr = line.split('\\t')\n", "            if feat!='gene': continue\n", "            d = {k:v.strip('\"') for k,v in re.findall(r'(\\S+) \"(.+?)\";', attr)}\n", "            m[d.get('gene_name', '')] = d.get('gene_id','')\n", "    return m\n", "gtf_map = load_gtf('/public/share/liuguole/references/gencode.v43.annotation.gtf')\n", "# 通用函数：补ID\n", "def ensure_id(name):\n", "    if name.startswith('ENSG'): return name\n", "    return gtf_map.get(name, '')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ✳️ Replogle 提取 (obs.index)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def extract_replogle(fp):\n", "    ad = sc.read_h5ad(fp)\n", "    genes = set()\n", "    for idx in ad.obs.index:\n", "        m = re.match(r'\\d+_([A-Za-z0-9]+)_ENSG(\\d+)', idx)\n", "        if m:\n", "            gn, gid = m.group(1), 'ENSG'+m.group(2)\n", "        else:\n", "            gn = re.match(r'\\d+_([A-Za-z0-9]+)_', idx)\n", "            gn = gn.group(1) if gn else None\n", "            gid = ensure_id(gn) if gn else ''\n", "        if gn:\n", "            genes.add((gn, gid))\n", "    return sorted(genes)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ✳️ Nadig 提取 (obs['gene','gene_id'])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_nadig(fp):\n", "    ad = sc.read_h5ad(fp)\n", "    genes = set()\n", "    df = ad.obs[['gene', 'gene_id']]\n", "    for gn,gid in df.dropna().itertuples(index=False):\n", "        if gn.lower().startswith('non-target'): continue\n", "        gid2 = gid if gid and gid.startswith('ENSG') else ensure_id(gn)\n", "        genes.add((gn, gid2))\n", "    return sorted(genes)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ✳️ Jiang 提取 (<PERSON><PERSON><PERSON> R<PERSON>, pathway gene lists)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def extract_jiang(path):\n", "    genes = set()\n", "    if not R_AVAILABLE: return []\n", "    for f in ['Pathway_genelist.rds','Pathway_Exclusive_genelist.rds']:\n", "        fn = os.path.join(path,f)\n", "        if os.path.exists(fn):\n", "            ro.r(f'gl<-readRDS(\"{fn}\")')\n", "            names = list(ro.r('names(gl)'))\n", "            for n in names:\n", "                glist = list(ro.r(f'gl$\"{n}\"'))\n", "                for gn in glist:\n", "                    gn = str(gn)\n", "                    if gn=='NA': continue\n", "                    gid = ensure_id(gn)\n", "                    genes.add((gn, gid))\n", "    return sorted(genes)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 执行 & 保存"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "OSError", "evalue": "Unable to synchronously open file (file signature not found)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mOSError\u001b[0m                                   Traceback (most recent call last)", "Cell \u001b[0;32mIn[5], line 11\u001b[0m\n\u001b[1;32m      9\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexists(fp):\n\u001b[1;32m     10\u001b[0m             fun \u001b[38;5;241m=\u001b[39m extract_replogle \u001b[38;5;28;01mif\u001b[39;00m name\u001b[38;5;241m==\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mReplogle\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m extract_nadig\n\u001b[0;32m---> 11\u001b[0m             ds \u001b[38;5;241m|\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m(\u001b[43mfun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfp\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m     12\u001b[0m     datasets[name] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28msorted\u001b[39m(ds)\n\u001b[1;32m     13\u001b[0m datasets[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m<PERSON><PERSON>g\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m extract_jiang(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/public/share/liuguole/VCC/ST_others/Jiang_et_al_2025\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[2], line 2\u001b[0m, in \u001b[0;36mextract_replogle\u001b[0;34m(fp)\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mextract_replogle\u001b[39m(fp):\n\u001b[0;32m----> 2\u001b[0m     ad \u001b[38;5;241m=\u001b[39m \u001b[43msc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_h5ad\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfp\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      3\u001b[0m     genes \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n\u001b[1;32m      4\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m idx \u001b[38;5;129;01min\u001b[39;00m ad\u001b[38;5;241m.\u001b[39mobs\u001b[38;5;241m.\u001b[39mindex:\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/anndata/_io/h5ad.py:239\u001b[0m, in \u001b[0;36mread_h5ad\u001b[0;34m(filename, backed, as_sparse, as_sparse_fmt, chunk_size)\u001b[0m\n\u001b[1;32m    233\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m(msg)\n\u001b[1;32m    235\u001b[0m rdasp \u001b[38;5;241m=\u001b[39m partial(\n\u001b[1;32m    236\u001b[0m     read_dense_as_sparse, sparse_format\u001b[38;5;241m=\u001b[39mas_sparse_fmt, axis_chunk\u001b[38;5;241m=\u001b[39mchunk_size\n\u001b[1;32m    237\u001b[0m )\n\u001b[0;32m--> 239\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[43mh5py\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFile\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mr\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[1;32m    241\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mcallback\u001b[39m(func, elem_name: \u001b[38;5;28mstr\u001b[39m, elem, iospec):\n\u001b[1;32m    242\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m iospec\u001b[38;5;241m.\u001b[39mencoding_type \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manndata\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m elem_name\u001b[38;5;241m.\u001b[39mendswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/h5py/_hl/files.py:564\u001b[0m, in \u001b[0;36mFile.__init__\u001b[0;34m(self, name, mode, driver, libver, userblock_size, swmr, rdcc_nslots, rdcc_nbytes, rdcc_w0, track_order, fs_strategy, fs_persist, fs_threshold, fs_page_size, page_buf_size, min_meta_keep, min_raw_keep, locking, alignment_threshold, alignment_interval, meta_block_size, **kwds)\u001b[0m\n\u001b[1;32m    555\u001b[0m     fapl \u001b[38;5;241m=\u001b[39m make_fapl(driver, libver, rdcc_nslots, rdcc_nbytes, rdcc_w0,\n\u001b[1;32m    556\u001b[0m                      locking, page_buf_size, min_meta_keep, min_raw_keep,\n\u001b[1;32m    557\u001b[0m                      alignment_threshold\u001b[38;5;241m=\u001b[39malignment_threshold,\n\u001b[1;32m    558\u001b[0m                      alignment_interval\u001b[38;5;241m=\u001b[39malignment_interval,\n\u001b[1;32m    559\u001b[0m                      meta_block_size\u001b[38;5;241m=\u001b[39mmeta_block_size,\n\u001b[1;32m    560\u001b[0m                      \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[1;32m    561\u001b[0m     fcpl \u001b[38;5;241m=\u001b[39m make_fcpl(track_order\u001b[38;5;241m=\u001b[39mtrack_order, fs_strategy\u001b[38;5;241m=\u001b[39mfs_strategy,\n\u001b[1;32m    562\u001b[0m                      fs_persist\u001b[38;5;241m=\u001b[39mfs_persist, fs_threshold\u001b[38;5;241m=\u001b[39mfs_threshold,\n\u001b[1;32m    563\u001b[0m                      fs_page_size\u001b[38;5;241m=\u001b[39mfs_page_size)\n\u001b[0;32m--> 564\u001b[0m     fid \u001b[38;5;241m=\u001b[39m \u001b[43mmake_fid\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muserblock_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfapl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfcpl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mswmr\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mswmr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    566\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(libver, \u001b[38;5;28mtuple\u001b[39m):\n\u001b[1;32m    567\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_libver \u001b[38;5;241m=\u001b[39m libver\n", "File \u001b[0;32m~/miniconda3/envs/pseudopy/lib/python3.10/site-packages/h5py/_hl/files.py:238\u001b[0m, in \u001b[0;36mmake_fid\u001b[0;34m(name, mode, userblock_size, fapl, fcpl, swmr)\u001b[0m\n\u001b[1;32m    236\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m swmr \u001b[38;5;129;01mand\u001b[39;00m swmr_support:\n\u001b[1;32m    237\u001b[0m         flags \u001b[38;5;241m|\u001b[39m\u001b[38;5;241m=\u001b[39m h5f\u001b[38;5;241m.\u001b[39mACC_SWMR_READ\n\u001b[0;32m--> 238\u001b[0m     fid \u001b[38;5;241m=\u001b[39m \u001b[43mh5f\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mflags\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfapl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfapl\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    239\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m mode \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mr+\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[1;32m    240\u001b[0m     fid \u001b[38;5;241m=\u001b[39m h5f\u001b[38;5;241m.\u001b[39mopen(name, h5f\u001b[38;5;241m.\u001b[39mACC_RDWR, fapl\u001b[38;5;241m=\u001b[39mfapl)\n", "File \u001b[0;32mh5py/_objects.pyx:56\u001b[0m, in \u001b[0;36mh5py._objects.with_phil.wrapper\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mh5py/_objects.pyx:57\u001b[0m, in \u001b[0;36mh5py._objects.with_phil.wrapper\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mh5py/h5f.pyx:102\u001b[0m, in \u001b[0;36mh5py.h5f.open\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mOSError\u001b[0m: Unable to synchronously open file (file signature not found)"]}], "source": ["datasets = {}\n", "for name, files in {\n", "    'Replogle':[\"rpe1_normalized_bulk_01.h5ad\",\"rpe1_raw_bulk_01.h5ad\"],\n", "    'Nadig':[\"GSE264667_hepg2_raw_singlecell_01.h5ad\",\"GSE264667_jurkat_raw_singlecell_01.h5ad\"],\n", "}.items():\n", "    ds = set()\n", "    for fn in files:\n", "        fp = os.path.join(\"/public/home/<USER>/whr/VCC\", f\"{name}_et_al_{ '2022' if name=='Replogle' else '2025' }\", fn)\n", "        if os.path.exists(fp):\n", "            fun = extract_replogle if name=='Replogle' else extract_nadig\n", "            ds |= set(fun(fp))\n", "    datasets[name] = sorted(ds)\n", "datasets['<PERSON>'] = extract_jiang(\"/public/share/liuguole/VCC/ST_others/Jiang_et_al_2025\")\n", "\n", "os.makedirs('perturb_id_results', exist_ok=True)\n", "for ds, genes in datasets.items():\n", "    out = os.path.join('perturb_id_results', f'{ds}_gene_name_id.tsv')\n", "    pd.DataFrame(genes, columns=['gene_name','gene_id']).to_csv(out, sep='\\t', index=False)\n", "    print(f\"✅ Saved {ds}: {len(genes)} genes\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "📁 结果会保存在 `perturb_id_results/` 中，每个数据集一个 `*.tsv`，包含 gene name 和 gene id。如有无 ID，会从 GTF 中尝试映射。"]}], "metadata": {"kernelspec": {"display_name": "single_cell_preprocess", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 4}