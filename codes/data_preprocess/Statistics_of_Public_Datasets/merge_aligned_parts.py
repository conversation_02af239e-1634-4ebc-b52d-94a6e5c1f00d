#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os, re, argparse, shutil, time
from pathlib import Path
import numpy as np
import h5py

# ---------- 环境：防止 NFS 锁卡住 & 限制线程 ----------
os.environ.setdefault("OMP_NUM_THREADS", "1")
os.environ.setdefault("MKL_NUM_THREADS", "1")
os.environ.setdefault("OPENBLAS_NUM_THREADS", "1")
os.environ.setdefault("NUMEXPR_NUM_THREADS", "1")
os.environ.setdefault("HDF5_USE_FILE_LOCKING", "FALSE")

# ---------- 小工具 ----------
def vlen_utf8(): return h5py.string_dtype(encoding="utf-8")

def human_bytes(x: float) -> str:
    units = ["B","KB","MB","GB","TB"]; i=0; x=float(x)
    while x>=1024 and i<len(units)-1: x/=1024; i+=1
    return f"{x:.2f}{units[i]}"

def read_obs_columns(f: h5py.File):
    g = f["obs"]
    if "column-order" in g.attrs:
        cols = []
        for c in g.attrs["column-order"]:
            cols.append(c.decode("utf-8") if isinstance(c, (bytes, bytearray)) else str(c))
        return cols
    if "column-order" in g:
        return [str(x) for x in g["column-order"][...]]
    # 兜底：除 index/column-order 外的 dataset
    return [k for k in g.keys() if isinstance(g[k], h5py.Dataset) and k not in ("index","column-order")]

# ---------- 发现需要合并的前缀 ----------
PART_RE = re.compile(r"^(?P<prefix>.+)\.aligned\.part(?P<num>\d{2})\.h5ad$")

def find_groups_and_singles(in_dir: Path):
    groups = {}
    singles = []
    for p in in_dir.glob("*.aligned.part*.h5ad"):
        m = PART_RE.match(p.name)
        if not m: continue
        prefix = m.group("prefix")
        num = int(m.group("num"))
        groups.setdefault(prefix, []).append((num, p))
    for k in list(groups.keys()):
        groups[k] = [pp for _, pp in sorted(groups[k], key=lambda t: t[0])]
    for p in in_dir.glob("*.aligned.h5ad"):
        pref = p.name[:-len(".aligned.h5ad")]
        if pref not in groups:
            singles.append(p)
    return groups, singles

# ---------- 快速合并（零重建） ----------
def merge_one(prefix, parts, out_dir: Path, rdcc_nbytes: int,
              progress_sec: int, overwrite: bool, delete_parts: bool,
              verify_var: str = "shape"):
    parts = [Path(p) for p in parts]
    t0 = time.time()

    # 预扫：汇总行数/nnz & 轻量校验（实时进度）
    total_rows, total_nnz = 0, 0
    with h5py.File(parts[0], "r", rdcc_nbytes=rdcc_nbytes) as f0:
        n_vars = int(f0["X/shape"][1])
        obs_cols = read_obs_columns(f0)
        var_index0 = f0["var/index"][...].astype(str) if verify_var == "index" else None

    meta = []
    last_scan = time.time()
    for i, p in enumerate(parts, 1):
        with h5py.File(p, "r", rdcc_nbytes=rdcc_nbytes) as f:
            rows = int(f["X/shape"][0])
            nnz  = int(f["X/indptr"][-1])
            if int(f["X/shape"][1]) != n_vars:
                raise RuntimeError(f"[{p.name}] X.shape[1] 与第一片不同")
            if verify_var == "index":
                if "var/index" not in f:
                    raise RuntimeError(f"[{p.name}] 缺少 var/index")
                if not np.array_equal(f["var/index"][...].astype(str), var_index0):
                    raise RuntimeError(f"[{p.name}] var/index 不一致")
            total_rows += rows
            total_nnz  += nnz
            meta.append((p, rows, nnz))

        if time.time() - last_scan >= progress_sec:
            print(f"[SCAN] {prefix}: {i}/{len(parts)} parts scanned", flush=True)
            last_scan = time.time()

    out = out_dir / f"{prefix}.aligned.h5ad"
    if out.exists():
        if overwrite:
            out.unlink()
        else:
            print(f"[SKIP] {out.name} 已存在（--overwrite=false）", flush=True)
            return

    # 打开第一片和目标文件（写端也用 rdcc 缓存）
    f0 = h5py.File(parts[0], "r", rdcc_nbytes=rdcc_nbytes)
    fout = h5py.File(out, "w", rdcc_nbytes=rdcc_nbytes)
    fout.attrs["encoding-type"] = "anndata"
    fout.attrs["encoding-version"] = "0.1.0"

    # X 结构（一次性分配完整长度）
    gX = fout.create_group("X")
    gX.attrs["encoding-type"] = "csr_matrix"
    gX.attrs["encoding-version"] = "0.1.0"
    # ★ 关键补充
    gX.attrs["h5sparse_format"] = np.string_("csr")
    gX.attrs["h5sparse_shape"]  = np.array([total_rows, n_vars], dtype=np.int64)

    fout.create_dataset("X/shape", data=np.array([total_rows, n_vars], dtype=np.int64))

    d_data = fout.create_dataset("X/data",    shape=(total_nnz,), dtype=np.float32,
                                 chunks=True, compression="lzf", shuffle=True)
    d_ind  = fout.create_dataset("X/indices", shape=(total_nnz,), dtype=np.int32,
                                 chunks=True, compression="lzf", shuffle=True)
    d_ptr  = fout.create_dataset("X/indptr",  shape=(total_rows+1,), dtype=np.int64)
    d_ptr[0] = 0

    # var：整组复制自第一片（最快）
    fout.copy(f0["var"], fout, "var")

    # obs：统一为字符串写出，避免跨分片 dtype 差异
    gobs = fout.create_group("obs")
    gobs.attrs["encoding-type"] = "dataframe"
    gobs.attrs["encoding-version"] = "0.2.0"
    gobs.attrs["_index"] = np.array("index", dtype=vlen_utf8())
    gobs.attrs.create("column-order", np.array(obs_cols, dtype=vlen_utf8()))
    d_obs_index = fout.create_dataset("obs/index", shape=(total_rows,), dtype=vlen_utf8())
    d_obs_cols = {c: fout.create_dataset(f"obs/{c}", shape=(total_rows,), dtype=vlen_utf8())
                  for c in obs_cols}

    # 其他空组
    for gname in ["obsm","varm","layers","obsp","varp","uns"]:
        fout.create_group(gname)

    f0.close()

    # 逐片复制（实时进度）
    row_ofs = 0
    nnz_ofs = 0
    last_prog = time.time()
    copied_bytes = 0

    for idx, (p, rows, nnz) in enumerate(meta, 1):
        with h5py.File(p, "r", rdcc_nbytes=rdcc_nbytes) as f:
            # X/data & X/indices 切片复制
            d_data[nnz_ofs:nnz_ofs+nnz] = f["X/data"][...]
            d_ind [nnz_ofs:nnz_ofs+nnz] = f["X/indices"][...]
            # indptr 叠加偏移
            part_ptr = f["X/indptr"][...].astype(np.int64)
            d_ptr[row_ofs+1 : row_ofs+rows+1] = nnz_ofs + part_ptr[1:]

            # obs/index
            d_obs_index[row_ofs:row_ofs+rows] = f["obs/index"][...].astype(str)

            # obs/各列（统一转成 str）
            for c in obs_cols:
                arr = f[f"obs/{c}"][...]
                if arr.dtype.kind in "iufb":
                    arr = arr.astype(str)
                else:
                    arr = arr.astype(str)
                d_obs_cols[c][row_ofs:row_ofs+rows] = arr

            copied_bytes += nnz*(4+4) + (rows+1)*8

        row_ofs += rows
        nnz_ofs += nnz

        now = time.time()
        if now - last_prog >= progress_sec:
            print(f"[PROG] {prefix}: {idx}/{len(meta)} parts  "
                  f"rows={row_ofs}/{total_rows}  copied≈{human_bytes(copied_bytes)}",
                  flush=True)
            last_prog = now

    fout.close()
    print(f"[MERGE-FAST] {prefix}: {len(parts)} parts -> {out.name} | rows={total_rows} | time={(time.time()-t0)/60:.1f} min", flush=True)

    if delete_parts:
        for p in parts:
            try: Path(p).unlink()
            except Exception: pass
        print(f"[CLEAN] removed {len(parts)} part files for {prefix}", flush=True)

# ---------- 单个 part / 已成品的处理 ----------
def copy_single_part(src: Path, out_dir: Path, overwrite: bool):
    dst = out_dir / (src.stem.replace(".part01", "") + src.suffix)  # 去掉 .part01
    if dst.exists() and not overwrite:
        print(f"[SKIP] {dst.name} 已存在（--overwrite=false）", flush=True); return
    if dst.exists(): dst.unlink()
    shutil.copy2(src, dst)
    print(f"[RENAME-COPY] {src.name} -> {dst.name}", flush=True)

def copy_standalone_aligned(in_dir: Path, out_dir: Path, merged_prefixes: set, overwrite: bool):
    for src in in_dir.glob("*.aligned.h5ad"):
        pref = src.name[:-len(".aligned.h5ad")]
        if pref in merged_prefixes:  # 已由分片输出，避免覆盖
            continue
        dst = out_dir / src.name
        if dst.exists() and not overwrite:
            print(f"[SKIP] {dst.name} 已存在（--overwrite=false）", flush=True); continue
        if dst.exists(): dst.unlink()
        shutil.copy2(src, dst)
        print(f"[COPY] {src.name} -> {dst.name}", flush=True)

# ---------- 主程序 ----------
def main():
    ap = argparse.ArgumentParser(description="Fast merge *.aligned.partXX.h5ad into *.aligned.h5ad (zero-rebuild).")
    ap.add_argument("--in-dir",  required=True, help="输入目录（含 *.aligned.part*.h5ad 和/或 *.aligned.h5ad）")
    ap.add_argument("--out-dir", required=True, help="输出目录（新目录更清爽）")
    ap.add_argument("--hdf5-cache-mb", type=int, default=1024, help="HDF5 原始缓存(MB)")
    ap.add_argument("--progress-sec",  type=int, default=10, help="进度打印间隔（秒）")
    ap.add_argument("--delete-parts",  type=lambda s: s.lower()=="true", default=False, help="合并后删除分片")
    ap.add_argument("--overwrite",     type=lambda s: s.lower()=="true", default=True,  help="允许覆盖 out-dir 同名文件（默认 true）")
    ap.add_argument("--verify-var",    choices=["none","shape","index"], default="shape",
                    help="合并前校验 var 的程度：none=不校验；shape=只看列数(默认)；index=还校验 var/index 完全一致")
    args = ap.parse_args()

    in_dir  = Path(args.in_dir)
    out_dir = Path(args.out_dir)
    out_dir.mkdir(parents=True, exist_ok=True)
    rdcc = args.hdf5_cache_mb * 1024 * 1024

    groups, singles = find_groups_and_singles(in_dir)
    print(f"[INFO] in={in_dir}  out={out_dir}  prefixes={len(groups)}  parts={sum(len(v) for v in groups.values())}  singles={len(singles)}", flush=True)
    if not groups and not singles:
        print("[WARN] 输入目录下没有匹配到任何 *.aligned.part*.h5ad 或 *.aligned.h5ad", flush=True)

    merged_prefixes = set()

    # 需要合并的
    for prefix, parts in groups.items():
        if len(parts) == 1:
            copy_single_part(parts[0], out_dir, overwrite=args.overwrite)
            merged_prefixes.add(prefix)
        else:
            try:
                merge_one(prefix, parts, out_dir, rdcc, args.progress_sec,
                          overwrite=args.overwrite, delete_parts=args.delete_parts,
                          verify_var=args.verify_var)
                merged_prefixes.add(prefix)
            except Exception as e:
                print(f"[ERR] {prefix}: {e}", flush=True)

    # 已成品 *.aligned.h5ad
    copy_standalone_aligned(in_dir, out_dir, merged_prefixes, overwrite=args.overwrite)
    print(f"[DONE] 输出目录：{out_dir}", flush=True)

if __name__ == "__main__":
    main()
