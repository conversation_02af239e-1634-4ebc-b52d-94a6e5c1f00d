#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os, time
os.environ.setdefault("OMP_NUM_THREADS", "1")
os.environ.setdefault("MKL_NUM_THREADS", "1")
os.environ.setdefault("OPENBLAS_NUM_THREADS", "1")
os.environ.setdefault("NUMEXPR_NUM_THREADS", "1")
os.environ.setdefault("HDF5_USE_FILE_LOCKING", "FALSE")

from pathlib import Path
import argparse
import numpy as np
import pandas as pd
import scanpy as sc
import anndata as admod
import scipy.sparse as sp
import h5py
from concurrent.futures import ProcessPoolExecutor, as_completed
from pandas.api.types import CategoricalDtype

try:
    import psutil
except Exception:
    psutil = None

TSV_MAP  = "/data/ioz_whr_wsx/datasets/ensembl/ensembl_geneid_to_symbol.tsv"
GENE_INFO = "/data/ioz_whr_wsx/datasets/ensembl/gene_info.csv"

def strip_ver(x: str) -> str:
    return x.split(".")[0] if isinstance(x, str) and x.startswith("ENSG") else x
def looks_like_ensg(x: str) -> bool:
    return isinstance(x, str) and x.startswith("ENSG")

def classify_mode(path: str) -> str:
    p = str(path)
    if p.startswith("/data/ioz_cbmi/"): return "ioz_cbmi"
    if "Replogle_et_al_2022" in p: return "replogle_bulk" if "bulk" in p.lower() else "replogle_single"
    if "Nadig_et_al_2025" in p: return "nadig_single"
    return "replogle_single"

def load_refs():
    tsv = pd.read_csv(TSV_MAP, sep="\t", dtype=str).rename(columns=str.lower)
    tsv["ensembl_gene_id"] = tsv["ensembl_gene_id"].map(strip_ver)
    ensg2sym = dict(zip(tsv["ensembl_gene_id"], tsv["gene_name"]))

    gi = pd.read_csv(GENE_INFO, dtype=str).rename(columns=str.lower)
    gi["gene_id"] = gi["gene_id"].map(strip_ver)
    gi["var_names"] = gi["var_names"].astype(str)

    sym2ensg_gi = dict(zip(gi["var_names"], gi["gene_id"]))
    ensg2sym_gi = dict(zip(gi["gene_id"], gi["var_names"]))
    ref_symbols = gi["var_names"].tolist()
    return ensg2sym, gi, sym2ensg_gi, ensg2sym_gi, ref_symbols

# ---------- DataFrame 读写辅助 ----------
def _str_dtype():  # 变长 UTF-8 字符串 dtype（dataset 用）
    return h5py.string_dtype(encoding='utf-8')

def _as_attr_str_array(values):  # attribute 用（变长 UTF-8）
    return np.array(list(map(str, values)), dtype=h5py.string_dtype('utf-8'))

def _to_py_str_list(arr):
    out=[]
    for x in arr:
        if isinstance(x, bytes): out.append(x.decode('utf-8', 'ignore'))
        else: out.append(str(x))
    return out

def _read_df_group(f: h5py.File, name: str) -> pd.DataFrame:
    if name not in f: return pd.DataFrame()
    g = f[name]

    # 索引键：优先 attribute "_index"，否则退回 "index"
    idx_key = None
    if "_index" in g.attrs:
        val = g.attrs["_index"]
        if isinstance(val, bytes): val = val.decode('utf-8', 'ignore')
        idx_key = str(val)
    elif "index" in g:  # 旧写法兼容
        idx_key = "index"

    idx = g[idx_key][...].astype(str) if (idx_key and idx_key in g) else None

    # 列顺序：优先 attribute "column-order"，否则退回 dataset
    cols = []
    if "column-order" in g.attrs:
        cols = _to_py_str_list(g.attrs["column-order"])
    elif "column-order" in g:  # 旧写法兼容
        cols = [str(x) for x in g["column-order"][...]]

    if not cols:
        # 兜底：从子 dataset 推断（排除 index、旧的 column-order）
        cols = [k for k in g.keys() if isinstance(g[k], h5py.Dataset) and k not in ("index","column-order")]

    data = {}
    for c in cols:
        ds = g.get(c)
        if ds is None: continue
        arr = ds[...]
        if arr.dtype.kind not in "iufb": arr = arr.astype(str)
        data[c] = arr
    df = pd.DataFrame(data)
    if idx is not None: df.index = idx
    return df

# ---------- CSR 适配器 ----------
class CSRAdapter:
    def __init__(self, path: str, rdcc_bytes: int = 512*1024*1024, rdcc_slots: int = 1_000_003, rdcc_w0: float = 0.75):
        self._f = h5py.File(path, "r", rdcc_nbytes=rdcc_bytes, rdcc_nslots=rdcc_slots, rdcc_w0=rdcc_w0)
        gX = self._f["X"]
        for k in ("data","indices","indptr","shape"):
            if k not in gX: raise RuntimeError("X 不是 CSR 布局（缺少 data/indices/indptr/shape）")
        self._data, self._indices, self._indptr = gX["data"], gX["indices"], gX["indptr"]
        self._shape = tuple(gX["shape"][...].tolist())
        self._obs, self._var = _read_df_group(self._f,"obs"), _read_df_group(self._f,"var")
    @property
    def n_obs(self): return self._shape[0]
    @property
    def n_vars(self): return self._shape[1]
    @property
    def obs(self): return self._obs
    @property
    def var(self): return self._var
    @property
    def var_names(self): return self._var.index.astype(str)
    def get_rows(self, i0: int, i1: int) -> sp.csr_matrix:
        indptr = self._indptr[i0:i1+1].astype(np.int64, copy=False)
        base = int(indptr[0]); indptr = indptr - base
        nnz0, nnz1 = int(base), int(self._indptr[i1])
        data = self._data[nnz0:nnz1].astype(np.float32, copy=False)
        indices = self._indices[nnz0:nnz1].astype(np.int32, copy=False)
        return sp.csr_matrix((data, indices, indptr), shape=(i1-i0, self.n_vars), dtype=np.float32)
    def close(self):
        try: self._f.close()
        except Exception: pass

def _detect_x_layout(path: str):
    with h5py.File(path, "r") as f:
        if "X" not in f: return "none"
        X = f["X"]
        if isinstance(X, h5py.Dataset): return "dense"
        has = all(k in X for k in ("data","indices","indptr","shape"))
        if not has: return "unknown"
        shape = tuple(X["shape"][...].tolist())
        indptr_len = X["indptr"].shape[0]
        if indptr_len == shape[0]+1: return "csr"
        if indptr_len == shape[1]+1: return "csc"
        return "unknown"

def open_backend_safely(path: str, rdcc_bytes: int):
    layout = _detect_x_layout(path)
    if layout == "csr":
        return ("adapter", CSRAdapter(path, rdcc_bytes), layout)
    try:
        ad = sc.read_h5ad(path, backed="r")
        return ("anndata_backed", ad, layout)
    except Exception:
        try:
            ad = admod.read_h5ad(path)
            if sp.isspmatrix_csc(ad.X): ad.X = ad.X.tocsr()
            return ("anndata_mem", ad, layout)
        except Exception:
            raise RuntimeError(f"无法以任何方式打开 {path}；检测到 X 布局：{layout}。请先转换为标准 CSR。")

# ---------- 构建映射 ----------
def build_mappings_for_stream(var_names: pd.Index, mode, ensg2sym, sym2ensg_gi, ensg2sym_gi, ref_symbols, gi_df):
    idx = pd.Index(var_names.astype(str))
    if mode == "ioz_cbmi":
        gene_name=[]
        for x in idx:
            if looks_like_ensg(x):
                e0=strip_ver(x); sym=ensg2sym.get(e0, x)
                gene_name.append(sym if isinstance(sym,str) and len(sym)>0 else x)
            else:
                gene_name.append(x)
    else:
        e0=[strip_ver(x) if looks_like_ensg(x) else x for x in idx]
        gene_name=[ensg2sym_gi.get(e, ensg2sym.get(e,e)) if looks_like_ensg(e) else e for e in e0]
    gene_name = pd.Index(gene_name, dtype="object")
    codes, u_names = pd.factorize(gene_name, sort=False)
    group_of_col = codes.astype(np.int64)

    vc = pd.Series(1, index=gene_name).groupby(level=0).size()
    dups_sorted = sorted([n for n,c in vc.items() if c>1])

    ref_pos = {s:j for j,s in enumerate(ref_symbols)}
    ref_col_of_group = np.full(len(u_names), -1, dtype=np.int64)
    for gi_,name in enumerate(u_names):
        j = ref_pos.get(str(name), None)
        if j is not None: ref_col_of_group[gi_] = j

    var_ref = gi_df[["var_names","gene_id"]].rename(columns={"var_names":"gene_name"}).copy()
    var_ref.index = var_ref["gene_name"].astype(str)
    return group_of_col, ref_col_of_group, dups_sorted, var_ref

# ---------- 读取一个块 ----------
def iter_source_csr_blocks(src, i0: int, i1: int):
    if isinstance(src, CSRAdapter):
        yield src.get_rows(i0, i1)
    else:
        Xb = src.X[i0:i1, :]
        if sp.issparse(Xb): yield Xb.tocsr() if not sp.isspmatrix_csr(Xb) else Xb
        else:               yield sp.csr_matrix(Xb)

# ---------- 合并→对齐（一个块） ----------
def transform_block_to_ref(Xb, group_of_col, ref_col_of_group, dtype=np.float32):
    n_groups = int(group_of_col.max()) + 1
    group_buf_val = np.zeros(n_groups, dtype=dtype)
    rows_data, rows_indices, indptr = [], [], [0]
    for r in range(Xb.shape[0]):
        s,e = Xb.indptr[r], Xb.indptr[r+1]
        cols = Xb.indices[s:e]; vals = Xb.data[s:e].astype(dtype, copy=False)
        if cols.size:
            g = group_of_col[cols]
            np.add.at(group_buf_val, g, vals)
            mask = (group_buf_val != 0)
            if mask.any():
                g_idx = np.nonzero(mask)[0]
                dst = ref_col_of_group[g_idx]
                ok = (dst >= 0)
                if ok.any():
                    dc = dst[ok].astype(np.int32, copy=False)
                    dv = group_buf_val[g_idx[ok]]
                    order = np.argsort(dc)
                    rows_indices.append(dc[order]); rows_data.append(dv[order])
            group_buf_val[g] = 0.0
        indptr.append(indptr[-1] + (rows_data[-1].size if rows_data else 0))
    if rows_data:
        data = np.concatenate(rows_data).astype(dtype, copy=False)
        indices = np.concatenate(rows_indices).astype(np.int32, copy=False)
    else:
        data = np.array([], dtype=dtype)
        indices = np.array([], dtype=np.int32)
    ind = np.array(indptr, dtype=np.int64)
    M = int(ref_col_of_group.max()) + 1 if (ref_col_of_group >= 0).any() else 0
    blk = sp.csr_matrix((data, indices, ind), shape=(Xb.shape[0], M), dtype=dtype)
    return blk

# ---------- 写 H5AD ----------
class H5CSRWriter:
    def __init__(self, out_path: Path, n_obs: int, n_vars: int, obs: pd.DataFrame, var: pd.DataFrame,
                 chunk_elems: int = 32_000_000):
        self.out_path = out_path
        out_path.parent.mkdir(parents=True, exist_ok=True)
        self.f = h5py.File(out_path, "w")
        f = self.f
        f.attrs["encoding-type"] = "anndata"; f.attrs["encoding-version"] = "0.1.0"

        gX = f.create_group("X")
        gX.attrs["encoding-type"] = "csr_matrix"; gX.attrs["encoding-version"] = "0.1.0"
        # ★ 关键补充：让 anndata 新版识别为 h5sparse 后端
        # gX.attrs["h5sparse_format"] = np.string_("csr")
        gX.attrs["h5sparse_format"] = np.bytes_("csr")  # or: b"csr"
        gX.attrs["h5sparse_shape"]  = np.array([n_obs, n_vars], dtype=np.int64)

        f.create_dataset("X/shape", data=np.array([n_obs, n_vars], dtype=np.int64))

        self.data_ds = f.create_dataset("X/data",    shape=(1,), maxshape=(None,), dtype=np.float32,
                                        chunks=(chunk_elems,), compression="lzf", shuffle=True)
        self.ind_ds  = f.create_dataset("X/indices", shape=(1,), maxshape=(None,), dtype=np.int32,
                                        chunks=(chunk_elems,), compression="lzf", shuffle=True)
        self.ptr_ds  = f.create_dataset("X/indptr",  shape=(n_obs+1,), dtype=np.int64)
        self.ptr_ds[0] = 0
        self.nnz = 0
        self.cap = 1

        _write_df(f, "obs", obs)
        _write_df(f, "var", var)
        for g in ["obsm","varm","layers","obsp","varp","uns"]: f.create_group(g)

    def _ensure_cap(self, need):
        if need <= self.cap: return
        new_cap = max(self.cap*2, need, 1_000_000)
        self.data_ds.resize((new_cap,))
        self.ind_ds.resize((new_cap,))
        self.cap = new_cap

    def append_block(self, i0_rel: int, block: sp.csr_matrix):
        add = block.data.size
        need = self.nnz + add
        self._ensure_cap(need)
        if add > 0:
            self.data_ds[self.nnz:self.nnz+add] = block.data
            self.ind_ds [self.nnz:self.nnz+add] = block.indices
        rb = block.shape[0]
        if rb > 0:
            self.ptr_ds[i0_rel+1:i0_rel+1+rb] = (np.int64(self.nnz) + block.indptr[1:].astype(np.int64))
        self.nnz += add

    def finalize(self):
        self.data_ds.resize((self.nnz,))
        self.ind_ds.resize((self.nnz,))
        self.f.close()

def _write_df(f: h5py.File, name: str, df: pd.DataFrame):
    g = f.create_group(name)
    # anndata dataframe v0.2.0 元信息（全部写在 attribute）
    g.attrs["encoding-type"] = "dataframe"
    g.attrs["encoding-version"] = "0.2.0"
    g.attrs["_index"] = np.array("index", dtype=h5py.string_dtype('utf-8'))
    g.attrs.create("column-order", _as_attr_str_array(df.columns))

    # index 作为 dataset
    f.create_dataset(f"{name}/index", data=df.index.astype(str).to_numpy(), dtype=_str_dtype())

    # 列作为 dataset
    for col in df.columns:
        s = df[col]
        if isinstance(s.dtype, CategoricalDtype): s = s.astype(str)
        if s.dtype.kind in "iufb":
            f.create_dataset(f"{name}/{col}", data=s.to_numpy())
        else:
            f.create_dataset(f"{name}/{col}", data=s.astype(str).to_numpy(), dtype=_str_dtype())

# ---------- 标准化 obs['gene'] ----------
def standardize_obs_gene_column(obs: pd.DataFrame,
                                mode: str,
                                ensg2sym: dict,
                                sym2ensg_gi: dict,
                                ensg2sym_gi: dict) -> pd.DataFrame:
    obs = obs.copy()
    def norm_nt(x: str) -> str:
        if not isinstance(x, str): return x
        t = x.strip().lower().replace("_","-")
        return "non-targeting" if t in {"non-targeting","non-target","non-targeted","nt","ntc"} else x
    def ensg_to_sym(ensg: str) -> str:
        e0=strip_ver(ensg); return ensg2sym_gi.get(e0, ensg2sym.get(e0, ensg))
    def to_standard_symbol(g: str) -> str:
        if not isinstance(g,str) or g=="": return g
        g2 = norm_nt(g); 
        if g2=="non-targeting": return g2
        return ensg_to_sym(g2) if looks_like_ensg(g2) else g2

    if mode == "ioz_cbmi":
        if "gene_target" not in obs.columns: raise ValueError("ioz_cbmi 数据需要 obs['gene_target']")
        obs["gene"] = obs["gene_target"].astype(str).map(to_standard_symbol)
        obs.drop(columns=[c for c in ["gene_target"] if c in obs.columns], inplace=True)
    elif mode in {"replogle_single","nadig_single"}:
        cand = [c for c in ["gene","gene_target","guide_target"] if c in obs.columns]
        if not cand: raise ValueError(f"{mode} 未找到基因列（gene/gene_target/guide_target）")
        col = cand[0]; obs["gene"] = obs[col].astype(str).map(to_standard_symbol)
        if col!="gene": obs.drop(columns=[col], inplace=True)
    elif mode == "replogle_bulk":
        idx = obs.index.astype(str)
        parts = pd.Series(idx, index=obs.index, dtype=object).str.split("_")
        sym  = parts.map(lambda xs: xs[-3] if isinstance(xs,list) and len(xs)>=3 else np.nan)
        ensg = parts.map(lambda xs: xs[-1] if isinstance(xs,list) and len(xs)>=1 else np.nan)
        gene=[]
        for s,e in zip(sym, ensg):
            if isinstance(s,str) and s in sym2ensg_gi: gene.append(s)
            elif isinstance(e,str) and looks_like_ensg(e): gene.append(ensg_to_sym(e))
            else: gene.append(s if isinstance(s,str) else (ensg_to_sym(e) if isinstance(e,str) else np.nan))
        obs["gene"] = pd.Series(gene, index=obs.index).astype(str).map(to_standard_symbol)
    else:
        raise ValueError(f"未知模式: {mode}")

    obs["gene"] = obs["gene"].astype(str).map(
        lambda x: "non-targeting" if x and x.strip().lower().replace("_","-")=="non-targeting" else x
    )
    return obs

def human(n):
    units=["","K","M","G","T"]; i=0; x=float(n)
    while x>=1000 and i<len(units)-1: x/=1000; i+=1
    return f"{x:.2f}{units[i]}"

# ---------- 单文件（支持按行分片） ----------
def process_one(path: str, row_chunk: int, out_dir: str,
                hdf5_cache_mb: int, writer_chunk_elems: int, obs_keep_all: bool,
                progress_sec: int, max_dense_rows: int, verbose: int,
                shard_rows: int) -> str:
    p = Path(path)
    out_dir = Path(out_dir); out_dir.mkdir(parents=True, exist_ok=True)
    mode = classify_mode(path)
    ensg2sym, gi, sym2ensg_gi, ensg2sym_gi, ref_symbols = load_refs()

    kind, src, layout = open_backend_safely(str(p), rdcc_bytes=hdf5_cache_mb*1024*1024)
    if kind == "adapter":
        n_obs, n_vars, var_names, obs_in = src.n_obs, src.n_vars, src.var_names, src.obs
    else:
        n_obs, n_vars, var_names, obs_in = src.shape[0], src.shape[1], src.var_names, src.obs

    eff_chunk = row_chunk if layout=="csr" else min(row_chunk, max_dense_rows)

    print(f"[START] {p.name} | shape={n_obs}x{n_vars} | layout={layout} | backend={kind} | mode={mode} | row_chunk={eff_chunk}", flush=True)

    group_of_col, ref_col_of_group, dups, var_ref = build_mappings_for_stream(
        var_names, mode, ensg2sym, sym2ensg_gi, ensg2sym_gi, ref_symbols, gi_df=gi
    )
    base = Path(path).stem
    dup_path  = out_dir / f"{base}.duplicates.txt"
    if dups:
        with open(dup_path, "w", encoding="utf-8") as f: f.write("\n".join(dups))
        if verbose: print(f"[{p.name}] duplicates: {len(dups)} → {dup_path}", flush=True)

    obs_std = standardize_obs_gene_column(obs_in, mode, ensg2sym, sym2ensg_gi, ensg2sym_gi)
    if not obs_keep_all:
        obs_std = pd.DataFrame(index=obs_std.index, data={"gene": obs_std["gene"]})

    # ---- 分片循环 ----
    part = 1
    glob_start = time.perf_counter()
    start_row = 0
    while start_row < n_obs:
        end_row = n_obs if shard_rows <= 0 else min(start_row + shard_rows, n_obs)
        part_tag = f".part{part:02d}" if shard_rows > 0 else ""
        out_path = out_dir / f"{base}.aligned{part_tag}.h5ad"

        # 本分片写器
        part_obs = obs_std.iloc[start_row:end_row]
        writer = H5CSRWriter(out_path, n_obs=end_row-start_row, n_vars=var_ref.shape[0],
                             obs=part_obs, var=var_ref[["gene_name","gene_id"]],
                             chunk_elems=writer_chunk_elems)

        processed = 0
        processed_nnzs_in = 0
        start = time.perf_counter()
        last  = start
        i0 = start_row
        while i0 < end_row:
            i1 = min(i0 + eff_chunk, end_row)
            for Xb in iter_source_csr_blocks(src, i0, i1):
                blk = transform_block_to_ref(Xb, group_of_col, ref_col_of_group, dtype=np.float32)
                writer.append_block(i0_rel=i0-start_row, block=blk)
                processed += (i1 - i0)
                processed_nnzs_in += int(Xb.nnz)
            i0 = i1

            now = time.perf_counter()
            if now - last >= progress_sec:
                elapsed = now - start
                avg_rps = processed / max(elapsed, 1e-6)
                pct = (start_row + processed) * 100.0 / n_obs
                win_rps = eff_chunk / max(now - last, 1e-6)
                remain = n_obs - (start_row + processed)
                eta = remain / max(avg_rps, 1e-6)
                rss = psutil.Process().memory_info().rss/1024**3 if psutil else None
                dens = (processed_nnzs_in / max(processed,1)) / max(n_vars,1) if processed>0 else 0.0
                msg = (f"[PROG] {p.name}{part_tag} {start_row+processed}/{n_obs} rows ({pct:.1f}%) "
                       f"avg={avg_rps:,.0f} r/s win≈{win_rps:,.0f} r/s "
                       f"ETA≈{int(eta//3600)}h{int((eta%3600)//60)}m blk_density≈{dens:.3f}")
                if rss is not None: msg += f" RSS≈{rss:.1f} GB"
                print(msg, flush=True)
                last = now

        writer.finalize()
        dur = time.perf_counter() - start
        print(f"[OK] {path}{part_tag} -> {out_path} | rows={end_row-start_row} | time={dur/60:.1f} min | avg={processed/dur:,.0f} rows/s", flush=True)

        start_row = end_row
        part += 1

    if isinstance(src, CSRAdapter): src.close()
    total = time.perf_counter() - glob_start
    print(f"[DONE] {path} | total time={total/60:.1f} min", flush=True)
    return "OK"

def main():
    ap = argparse.ArgumentParser(description="Stream align multiple .h5ad files into unified gene space (CSR-backed) with optional row sharding.")
    ap.add_argument("--files", nargs="+", required=True, help="要处理的 .h5ad 文件列表")
    ap.add_argument("--out-dir", type=str, required=True, help="输出目录（必须可写）")
    ap.add_argument("--jobs", type=int, default=2, help="并行进程数（按文件并行）")
    ap.add_argument("--row-chunk", type=int, default=int(os.environ.get("ROW_CHUNK","20000")))
    ap.add_argument("--hdf5-cache-mb", type=int, default=1024, help="每文件 HDF5 原始 chunk 缓存大小(MB)")
    ap.add_argument("--writer-chunk-elems", type=int, default=32000000, help="写端 X/data 与 X/indices 的 chunk 元素数")
    ap.add_argument("--obs-keep-all", type=lambda s: s.lower()!="false", default=True,
                    help="是否保留原 obs 的所有列；false 则仅输出标准化后的 gene 列")
    ap.add_argument("--progress-sec", type=int, default=30, help="块级进度打印间隔（秒）")
    ap.add_argument("--max-dense-rows", type=int, default=20000, help="当 X 非 CSR/dense 时限制的最大行块大小")
    ap.add_argument("--verbose", type=int, default=1, help="0=静默，1=基本信息，2=更详细")
    ap.add_argument("--shard-rows", type=int, default=0, help=">0 时按行数切分输出（每片这么多行），避免 nnz 超过 2e9 上限")
    args = ap.parse_args()

    files = list(dict.fromkeys([str(Path(p)) for p in args.files]))
    with ProcessPoolExecutor(max_workers=args.jobs) as ex:
        fut = {ex.submit(process_one, p, args.row_chunk, args.out_dir,
                         args.hdf5_cache_mb, args.writer_chunk_elems, args.obs_keep_all,
                         args.progress_sec, args.max_dense_rows, args.verbose,
                         args.shard_rows): p for p in files}
        for f in as_completed(fut):
            p = fut[f]
            try:
                f.result()
            except Exception as e:
                print(f"[ERR] {p}: {e}", flush=True)

if __name__ == "__main__":
    main()
