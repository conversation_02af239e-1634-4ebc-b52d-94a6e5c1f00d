import h5py, glob, numpy as np, os

prefix = "HEK293T_filtered_dual_guide_cells"
files = sorted(glob.glob(f"/data/ioz_whr_wsx/datasets/VCC/aligned18000datas/{prefix}.aligned.part*.h5ad"))

def get_cols(v):
    if "column-order" in v.attrs:
        arr = v.attrs["column-order"]
        return [x.decode() if isinstance(x,bytes) else str(x) for x in arr]
    elif "column-order" in v:
        return [str(x) for x in v["column-order"][...]]
    else:
        # 注意：这里的顺序并不稳定，只是用来观察
        return [k for k in v.keys() if k not in ("index","column-order")]

for f in files:
    with h5py.File(f,"r") as h:
        v = h["var"]
        cols = get_cols(v)
        idx_dtype = v["index"].dtype
        dtypes = {c: v[c].dtype.str for c in cols if c in v}
        print(os.path.basename(f), "| idx:", idx_dtype, "| cols:", cols, "| dtypes:", dtypes)
