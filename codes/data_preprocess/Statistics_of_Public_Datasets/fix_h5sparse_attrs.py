#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
from pathlib import Path
import numpy as np
import h5py
import sys

def bytes_csr():
    # 兼容 numpy 1.x/2.x
    try:
        return np.bytes_("csr")
    except AttributeError:
        # numpy<2.0
        return np.string_("csr")

def fix_one(path: Path) -> tuple[bool, str]:
    try:
        with h5py.File(path, "r+") as f:
            if "X" not in f:
                return False, "missing /X"

            gX = f["X"]
            # 取形状：优先 X/shape，其次 indptr & var/index 推断
            n_obs, n_vars = None, None
            if "shape" in gX:
                shp = gX["shape"][...]
                if shp.size == 2:
                    n_obs, n_vars = int(shp[0]), int(shp[1])
            if n_obs is None or n_vars is None:
                if "indptr" in gX:
                    n_obs = int(gX["indptr"].shape[0]) - 1
                if "var" in f and "index" in f["var"]:
                    n_vars = int(f["var"]["index"].shape[0])
            if n_obs is None or n_vars is None:
                return False, "cannot infer shape"

            need_set = False

            # h5sparse_format
            cur_fmt = gX.attrs.get("h5sparse_format", None)
            if cur_fmt not in (b"csr", b"csc", "csr", "csc"):
                gX.attrs.modify("h5sparse_format", bytes_csr())
                need_set = True

            # h5sparse_shape
            cur_shp = gX.attrs.get("h5sparse_shape", None)
            want = np.array([n_obs, n_vars], dtype=np.int64)
            if (cur_shp is None) or (tuple(cur_shp) != (n_obs, n_vars)):
                gX.attrs.modify("h5sparse_shape", want)
                need_set = True

            # （可选）补 encoding-*，有些工具会看
            et = f.attrs.get("encoding-type", None)
            ev = f.attrs.get("encoding-version", None)
            if et is None:
                f.attrs["encoding-type"] = "anndata"
            if ev is None:
                f.attrs["encoding-version"] = "0.1.0"
            if "encoding-type" not in gX.attrs:
                gX.attrs["encoding-type"] = "csr_matrix"
            if "encoding-version" not in gX.attrs:
                gX.attrs["encoding-version"] = "0.1.0"

            if need_set:
                return True, f"set h5sparse_format=csr, h5sparse_shape=({n_obs}, {n_vars})"
            else:
                return False, "already ok"
    except Exception as e:
        return False, str(e)

def main():
    ap = argparse.ArgumentParser(description="Fix missing h5sparse attrs in .h5ad files")
    ap.add_argument("--dir", required=True, help="目录，修复其中所有 *.h5ad")
    args = ap.parse_args()

    d = Path(args.dir)
    fixed = 0
    scanned = 0
    for p in sorted(d.glob("*.h5ad")):
        scanned += 1
        ok, msg = fix_one(p)
        tag = "FIX" if ok else ("OK " if msg == "already ok" else "ERR")
        print(f"[{tag}] {p.name}: {msg}")
        if ok: fixed += 1

    print(f"[DONE] scanned={scanned} fixed={fixed} dir={d}")

if __name__ == "__main__":
    # 让异常立刻可见
    try:
        main()
    except Exception as e:
        print(f"[FATAL] {e}", file=sys.stderr)
        sys.exit(1)
