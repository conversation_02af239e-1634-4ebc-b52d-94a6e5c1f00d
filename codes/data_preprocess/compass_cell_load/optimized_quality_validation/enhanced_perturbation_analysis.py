#!/usr/bin/env python3
"""
Enhanced Perturbation Efficiency Analysis Script
Generates single-cell level perturbation efficiency distribution plots and detailed statistics
"""

import pandas as pd
import numpy as np
import scanpy as sc
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
from matplotlib_venn import venn3, venn2
warnings.filterwarnings('ignore')

class EnhancedPerturbationAnalyzer:
    def __init__(self, data_paths, output_dir):
        """
        Initialize Enhanced Perturbation Analyzer

        Parameters:
        -----------
        data_paths : dict
            Dictionary of data file paths, format: {'dataset_name': 'path_to_h5ad'}
        output_dir : str or Path
            Output directory path
        """
        self.data_paths = data_paths
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.adatas = {}

        # Define column name mapping for each dataset
        self.column_mapping = {
            'Original_file': {'pert_col': 'gene', 'gene_col': 'gene_name'},
            'Hu_Jiaxin_processed': {'pert_col': 'target_gene', 'gene_col': 'index'},
            'Wang_Haoran_processed': {'pert_col': 'gene', 'gene_col': 'gene_name'}
        }

    def load_data(self):
        """Load all datasets"""
        print("Loading datasets...")

        for name, path in self.data_paths.items():
            print(f"  Loading {name}: {path}")
            try:
                adata = sc.read_h5ad(path)
                self.adatas[name] = adata
                print(f"    Success: {adata.n_obs} cells, {adata.n_vars} genes")
            except Exception as e:
                print(f"    Failed: {e}")

        print(f"Successfully loaded {len(self.adatas)} datasets\n")

    def find_common_perturbations(self, min_cells=50):
        """Find common perturbations across all datasets with sufficient cell numbers"""
        print(f"Finding common perturbations (minimum {min_cells} cells)...")

        # Get perturbations and their cell counts for each dataset
        all_perturbations = {}

        for name, adata in self.adatas.items():
            # Use correct perturbation column name
            pert_col = self.column_mapping[name]['pert_col']
            pert_counts = adata.obs[pert_col].value_counts()

            # Filter out perturbations with insufficient cells
            valid_perts = pert_counts[pert_counts >= min_cells].index.tolist()
            # Exclude control groups
            valid_perts = [p for p in valid_perts if p not in ['non-targeting', 'control', 'ctrl']]
            all_perturbations[name] = set(valid_perts)
            print(f"  {name}: {len(valid_perts)} valid perturbations")

        # Find common perturbations
        common_perts = set.intersection(*all_perturbations.values())
        print(f"  Common perturbations: {len(common_perts)}")

        # Sort by cell count, select top ones for detailed analysis
        pert_cell_counts = {}
        for pert in common_perts:
            total_cells = 0
            for name, adata in self.adatas.items():
                pert_col = self.column_mapping[name]['pert_col']
                total_cells += adata.obs[pert_col].value_counts().get(pert, 0)
            pert_cell_counts[pert] = total_cells

        # Select perturbations with most cells
        selected_perts = sorted(pert_cell_counts.items(),
                              key=lambda x: x[1], reverse=True)[:5]

        print("  Selected perturbations for detailed analysis:")
        for pert, count in selected_perts:
            print(f"    {pert}: {count} cells")

        return [pert for pert, _ in selected_perts], all_perturbations

    def calculate_single_cell_efficiency(self, perturbations):
        """Calculate single-cell level perturbation efficiency"""
        print("Calculating single-cell perturbation efficiency...")

        efficiency_results = {}

        for pert in perturbations:
            print(f"  Analyzing perturbation: {pert}")
            efficiency_results[pert] = {}

            for dataset_name, adata in self.adatas.items():
                print(f"    Dataset: {dataset_name}")

                # Get column name mapping
                pert_col = self.column_mapping[dataset_name]['pert_col']
                gene_col = self.column_mapping[dataset_name]['gene_col']

                # Check if target gene exists
                gene_idx = None

                if gene_col == 'index':
                    # Use var.index
                    if pert not in adata.var_names:
                        print(f"      Skip: target gene {pert} not found in var.index")
                        continue
                    gene_idx = adata.var_names.get_loc(pert)
                else:
                    # Use specified column in var (gene_name)
                    if gene_col not in adata.var.columns:
                        print(f"      Skip: gene column {gene_col} does not exist")
                        continue

                    # Search for target gene in gene_name column
                    gene_mask = adata.var[gene_col] == pert
                    if not gene_mask.any():
                        print(f"      Skip: target gene {pert} not found in {gene_col} column")
                        continue

                    # Get first matching gene index
                    matching_indices = adata.var.index[gene_mask]
                    if len(matching_indices) == 0:
                        print(f"      Skip: cannot find index for {pert}")
                        continue

                    # Use first matching gene
                    target_var_name = matching_indices[0]
                    gene_idx = adata.var_names.get_loc(target_var_name)

                try:
                    # Get perturbed and control cells
                    pert_mask = adata.obs[pert_col] == pert
                    ctrl_mask = adata.obs[pert_col].isin(['non-targeting', 'control', 'ctrl'])

                    # Check if there are sufficient cells
                    n_pert_cells = pert_mask.sum()
                    n_ctrl_cells = ctrl_mask.sum()

                    if n_pert_cells == 0 or n_ctrl_cells == 0:
                        print(f"      Skip: missing perturbed or control cells (perturbed:{n_pert_cells}, control:{n_ctrl_cells})")
                        continue

                    # Safely extract expression data
                    try:
                        # Convert boolean masks to index arrays
                        pert_indices = np.where(pert_mask)[0]
                        ctrl_indices = np.where(ctrl_mask)[0]

                        if hasattr(adata.X, 'toarray'):
                            # Sparse matrix - use index arrays instead of boolean masks
                            pert_expr = adata.X[pert_indices, gene_idx].toarray().flatten()
                            ctrl_expr = adata.X[ctrl_indices, gene_idx].toarray().flatten()
                        else:
                            # Dense matrix
                            pert_expr = adata.X[pert_indices, gene_idx].flatten()
                            ctrl_expr = adata.X[ctrl_indices, gene_idx].flatten()

                    except Exception as e:
                        print(f"      Skip: data extraction failed - {e}")
                        continue

                    # Calculate control group mean expression
                    ctrl_mean = np.mean(ctrl_expr)

                    # Calculate efficiency for each perturbed cell (change relative to control group mean)
                    if 'log1p' in dataset_name.lower() or np.max(adata.X) < 20:
                        # log1p transformed data: calculate direct difference
                        single_cell_efficiency = pert_expr - ctrl_mean
                    else:
                        # Raw count data: calculate log fold change
                        single_cell_efficiency = np.log2(pert_expr + 1) - np.log2(ctrl_mean + 1)

                    # Store results
                    efficiency_results[pert][dataset_name] = {
                        'single_cell_efficiency': single_cell_efficiency,
                        'pert_expr': pert_expr,
                        'ctrl_expr': ctrl_expr,
                        'ctrl_mean': ctrl_mean,
                        'n_pert_cells': len(pert_expr),
                        'n_ctrl_cells': len(ctrl_expr),
                        'mean_efficiency': np.mean(single_cell_efficiency),
                        'std_efficiency': np.std(single_cell_efficiency)
                    }

                    print(f"      Success: {len(pert_expr)} perturbed cells, "
                          f"mean efficiency = {np.mean(single_cell_efficiency):.3f}")

                except Exception as e:
                    print(f"      Failed: {e}")
                    continue

        return efficiency_results

    def create_venn_diagrams(self):
        """Create Venn diagram showing highly variable genes overlap between datasets"""
        print("Generating Venn diagram for highly variable genes...")

        highly_variable_genes = {}

        for dataset_name, adata in self.adatas.items():
            print(f"  Analyzing highly variable genes in {dataset_name}")

            # Get column mapping
            pert_col = self.column_mapping[dataset_name]['pert_col']

            # Get control cells
            ctrl_mask = adata.obs[pert_col].isin(['non-targeting', 'control', 'ctrl'])

            if ctrl_mask.sum() == 0:
                print(f"    Warning: No control cells found in {dataset_name}")
                continue

            # Calculate gene expression statistics for control vs all other cells
            try:
                # Get control and perturbed cell indices
                ctrl_indices = np.where(ctrl_mask)[0]
                pert_indices = np.where(~ctrl_mask)[0]

                if len(pert_indices) == 0:
                    print(f"    Warning: No perturbed cells found in {dataset_name}")
                    continue

                # Calculate mean expression for control and perturbed cells
                if hasattr(adata.X, 'toarray'):
                    ctrl_expr = adata.X[ctrl_indices, :].toarray().mean(axis=0)
                    pert_expr = adata.X[pert_indices, :].toarray().mean(axis=0)
                else:
                    ctrl_expr = adata.X[ctrl_indices, :].mean(axis=0)
                    pert_expr = adata.X[pert_indices, :].mean(axis=0)

                # Calculate fold change (log2)
                fold_change = np.log2(pert_expr + 1) - np.log2(ctrl_expr + 1)

                # Identify highly variable genes (absolute fold change > 0.5)
                highly_var_mask = np.abs(fold_change) > 0.5
                highly_var_genes_list = adata.var_names[highly_var_mask].tolist()

                highly_variable_genes[dataset_name] = set(highly_var_genes_list)
                print(f"    Found {len(highly_var_genes_list)} highly variable genes")

            except Exception as e:
                print(f"    Error analyzing {dataset_name}: {e}")
                continue

        # Create Venn diagram if we have data from multiple datasets
        if len(highly_variable_genes) >= 2:
            fig, ax = plt.subplots(1, 1, figsize=(12, 10))

            datasets = list(highly_variable_genes.keys())

            if len(datasets) == 2:
                venn2([highly_variable_genes[datasets[0]], highly_variable_genes[datasets[1]]],
                      set_labels=datasets, ax=ax)
                plt.title('Highly Variable Genes Overlap Between Datasets', fontsize=14, fontweight='bold')
            elif len(datasets) == 3:
                venn3([highly_variable_genes[datasets[0]], highly_variable_genes[datasets[1]],
                       highly_variable_genes[datasets[2]]], set_labels=datasets, ax=ax)
                plt.title('Highly Variable Genes Overlap Between Three Datasets', fontsize=14, fontweight='bold')

            plt.tight_layout()

            # Save Venn diagram
            venn_output_file = self.output_dir / 'highly_variable_genes_venn.png'
            plt.savefig(venn_output_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"Venn diagram saved to: {venn_output_file}")
        else:
            print("Insufficient data for Venn diagram generation")

    def create_distribution_plots(self, efficiency_results):
        """Create single combined distribution plot showing all datasets"""
        print("Generating combined perturbation efficiency distribution plot...")

        from scipy.stats import gaussian_kde

        # Define color scheme
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        datasets = list(self.adatas.keys())

        # Collect all efficiency data across all perturbations for each dataset
        dataset_efficiency_data = {dataset: [] for dataset in datasets}

        for pert, pert_data in efficiency_results.items():
            for dataset, data in pert_data.items():
                if dataset in dataset_efficiency_data:
                    dataset_efficiency_data[dataset].extend(data['single_cell_efficiency'])

        # Filter out datasets with no data
        valid_datasets = {k: v for k, v in dataset_efficiency_data.items() if len(v) > 0}

        if not valid_datasets:
            print("No efficiency data available for distribution plot")
            return

        # Create single combined figure
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        fig.suptitle('Perturbation Efficiency Distribution Comparison Across All Datasets',
                     fontsize=16, fontweight='bold')

        # Collect statistical information
        stats_info = []

        # Plot smooth density curves for each dataset
        for i, (dataset, data) in enumerate(valid_datasets.items()):
            print(f"  Plotting distribution for {dataset} ({len(data)} cells)")

            # Convert to numpy array
            data = np.array(data)

            # Use kernel density estimation to create smooth curves
            try:
                kde = gaussian_kde(data)

                # Create x-axis range (use global range for all datasets)
                all_data = np.concatenate(list(valid_datasets.values()))
                x_min, x_max = np.min(all_data), np.max(all_data)
                x_range = x_max - x_min
                x = np.linspace(x_min - 0.1*x_range, x_max + 0.1*x_range, 300)

                # Calculate density
                density = kde(x)

                # Plot smooth curves
                ax.plot(x, density, color=colors[i % len(colors)],
                       linewidth=3, label=dataset, alpha=0.8)

                # Fill area under curve with transparency
                ax.fill_between(x, density, alpha=0.15, color=colors[i % len(colors)])

                # Collect statistical information
                stats_info.append({
                    'dataset': dataset,
                    'n_cells': len(data),
                    'mean': np.mean(data),
                    'median': np.median(data),
                    'std': np.std(data),
                    'effective_ratio': (data < -0.1).sum() / len(data) * 100
                })

            except Exception as e:
                print(f"    Warning: density estimation failed for {dataset}: {e}")
                continue

        # Set figure properties
        ax.set_xlabel('Perturbation Efficiency (Log2 Fold Change)', fontsize=14)
        ax.set_ylabel('Probability Density', fontsize=14)
        ax.set_title('Combined Single-cell Perturbation Efficiency Density Distribution', fontsize=16)
        ax.legend(fontsize=12, loc='upper right')
        ax.grid(True, alpha=0.3)

        # Add vertical lines marking important thresholds
        ax.axvline(x=0, color='black', linestyle='--', alpha=0.7, linewidth=2, label='No Effect')
        ax.axvline(x=-0.1, color='red', linestyle='--', alpha=0.7, linewidth=2, label='Effective Threshold')

        # Add statistical information to the plot
        stats_text = "Statistical Summary:\n"
        for stat in stats_info:
            stats_text += f"{stat['dataset']}:\n"
            stats_text += f"  n={stat['n_cells']:,}, μ={stat['mean']:.3f}\n"
            stats_text += f"  Effective Rate={stat['effective_ratio']:.1f}%\n"

        # Place statistical information in the upper left corner
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
               fontsize=11, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))

        plt.tight_layout()

        # Save figure
        output_file = self.output_dir / 'combined_efficiency_distribution.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Combined distribution plot saved to: {output_file}")

        # Create summary statistics table
        self._create_combined_stats_table(stats_info)

    def _create_detailed_stats_table(self, pert, stats_info):
        """Create detailed statistics table for a single perturbation"""

        # Create statistics table
        stats_df = pd.DataFrame(stats_info)

        # Save as CSV
        stats_file = self.output_dir / f'stats_{pert}.csv'
        stats_df.to_csv(stats_file, index=False)

        # Create formatted text report
        report_lines = []
        report_lines.append(f"Perturbation Gene: {pert}")
        report_lines.append("=" * 50)
        report_lines.append("")

        for _, row in stats_df.iterrows():
            report_lines.append(f"Dataset: {row['dataset']}")
            report_lines.append(f"  Cell Count: {row['n_cells']:,}")
            report_lines.append(f"  Mean Efficiency: {row['mean']:.4f}")
            report_lines.append(f"  Median Efficiency: {row['median']:.4f}")
            report_lines.append(f"  Standard Deviation: {row['std']:.4f}")
            report_lines.append(f"  Effective Perturbation Ratio: {row['effective_ratio']:.2f}%")
            report_lines.append("")

        # Save text report
        report_file = self.output_dir / f'report_{pert}.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

    def _create_combined_stats_table(self, stats_info):
        """Create combined statistics table for all datasets"""

        # Create statistics table
        stats_df = pd.DataFrame(stats_info)

        # Save as CSV
        stats_file = self.output_dir / 'combined_efficiency_stats.csv'
        stats_df.to_csv(stats_file, index=False)

        # Create formatted text report
        report_lines = []
        report_lines.append("Combined Perturbation Efficiency Analysis")
        report_lines.append("=" * 50)
        report_lines.append("")

        for _, row in stats_df.iterrows():
            report_lines.append(f"Dataset: {row['dataset']}")
            report_lines.append(f"  Total Cells: {row['n_cells']:,}")
            report_lines.append(f"  Mean Efficiency: {row['mean']:.4f}")
            report_lines.append(f"  Median Efficiency: {row['median']:.4f}")
            report_lines.append(f"  Standard Deviation: {row['std']:.4f}")
            report_lines.append(f"  Effective Perturbation Ratio: {row['effective_ratio']:.2f}%")
            report_lines.append("")

        # Save text report
        report_file = self.output_dir / 'combined_efficiency_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

    def create_summary_plot(self, efficiency_results):
        """Create summary comparison plot for all perturbations"""
        print("Generating summary comparison plot...")

        # Prepare summary data
        summary_data = []

        for pert, pert_data in efficiency_results.items():
            for dataset, data in pert_data.items():
                for eff in data['single_cell_efficiency']:
                    summary_data.append({
                        'Perturbation': pert,
                        'Dataset': dataset,
                        'Efficiency': eff
                    })

        if not summary_data:
            return

        df = pd.DataFrame(summary_data)

        # Create summary plot
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('Summary of Efficiency Distribution for All Perturbations', fontsize=18, fontweight='bold')

        # 1. Box plot grouped by perturbation
        ax1 = axes[0, 0]
        sns.boxplot(data=df, x='Perturbation', y='Efficiency', ax=ax1)
        ax1.set_title('Efficiency Distribution Grouped by Perturbation')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)

        # 2. Box plot grouped by dataset
        ax2 = axes[0, 1]
        sns.boxplot(data=df, x='Dataset', y='Efficiency', ax=ax2)
        ax2.set_title('Efficiency Distribution Grouped by Dataset')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)

        # 3. Heatmap: mean efficiency
        ax3 = axes[1, 0]
        pivot_mean = df.groupby(['Perturbation', 'Dataset'])['Efficiency'].mean().unstack()
        sns.heatmap(pivot_mean, annot=True, fmt='.3f', cmap='RdBu_r', center=0, ax=ax3)
        ax3.set_title('Mean Perturbation Efficiency Heatmap')

        # 4. Heatmap: efficiency standard deviation
        ax4 = axes[1, 1]
        pivot_std = df.groupby(['Perturbation', 'Dataset'])['Efficiency'].std().unstack()
        sns.heatmap(pivot_std, annot=True, fmt='.3f', cmap='Reds', ax=ax4)
        ax4.set_title('Perturbation Efficiency Standard Deviation Heatmap')

        plt.tight_layout()

        # Save summary plot
        output_file = self.output_dir / 'perturbation_efficiency_summary.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Summary plot saved to: {output_file}")

    def generate_detailed_report(self, efficiency_results):
        """Generate detailed statistical report"""
        print("Generating detailed statistical report...")

        report = []
        report.append("=" * 80)
        report.append("Single-cell Perturbation Efficiency Detailed Analysis Report")
        report.append("=" * 80)
        report.append("")

        # Dataset overview
        report.append("1. Dataset Overview:")
        for name, adata in self.adatas.items():
            report.append(f"   {name}: {adata.n_obs:,} cells, {adata.n_vars:,} genes")
        report.append("")

        # Detailed perturbation efficiency analysis
        report.append("2. Single-cell Perturbation Efficiency Detailed Analysis:")

        for pert, pert_data in efficiency_results.items():
            report.append(f"\n   Perturbation: {pert}")
            report.append("   " + "-" * 50)

            for dataset, data in pert_data.items():
                eff_data = data['single_cell_efficiency']
                report.append(f"   Dataset: {dataset}")
                report.append(f"     Cell Count: {data['n_pert_cells']:,}")
                report.append(f"     Control Cell Count: {data['n_ctrl_cells']:,}")
                report.append(f"     Mean Efficiency: {np.mean(eff_data):.4f}")
                report.append(f"     Median Efficiency: {np.median(eff_data):.4f}")
                report.append(f"     Standard Deviation: {np.std(eff_data):.4f}")
                report.append(f"     Minimum Value: {np.min(eff_data):.4f}")
                report.append(f"     Maximum Value: {np.max(eff_data):.4f}")
                report.append(f"     25th Percentile: {np.percentile(eff_data, 25):.4f}")
                report.append(f"     75th Percentile: {np.percentile(eff_data, 75):.4f}")

                # Calculate proportion of cells with effective perturbation (efficiency < -0.1)
                effective_cells = np.sum(eff_data < -0.1)
                effective_ratio = effective_cells / len(eff_data) * 100
                report.append(f"     Effective Perturbation Cell Ratio: {effective_ratio:.1f}% ({effective_cells}/{len(eff_data)})")
                report.append("")

        # Save report
        report_file = self.output_dir / 'detailed_perturbation_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        print(f"Detailed report saved to: {report_file}")

        # Print report summary
        print("\nReport Summary:")
        for line in report[:20]:  # Only print first 20 lines
            print(line)
        print("...")

    def run_analysis(self):
        """Run complete enhanced perturbation analysis"""
        print("Starting enhanced perturbation efficiency analysis...\n")

        try:
            # 1. Load data
            self.load_data()

            # 2. Find common perturbations
            common_perturbations, all_perturbations = self.find_common_perturbations()

            if not common_perturbations:
                print("No sufficient common perturbations found, analysis terminated")
                return

            # 3. Generate Venn diagrams for highly variable genes
            self.create_venn_diagrams()

            # 4. Calculate single-cell perturbation efficiency
            efficiency_results = self.calculate_single_cell_efficiency(common_perturbations)

            # 5. Generate distribution plots
            self.create_distribution_plots(efficiency_results)

            # 6. Generate summary plot
            self.create_summary_plot(efficiency_results)

            # 7. Generate detailed report
            self.generate_detailed_report(efficiency_results)

            print(f"\n✓ Enhanced perturbation analysis completed! All results saved in: {self.output_dir}")

        except Exception as e:
            print(f"Error during analysis: {e}")
            import traceback
            traceback.print_exc()

def main():
    """Main function"""

    # Data file paths - using the three specified files
    data_paths = {
        'Original_file': '/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Replogle/rpe1_raw_singlecell_01.aligned.h5ad',
        'Hu_Jiaxin_processed': '/data/vcc/compass_loaded/colab_like/rpe1_Training.h5ad',
        'Wang_Haoran_processed': '/data/vcc/compass_loaded/compass_cell_load_filtered_log1p/Replogle/rpe1_raw_singlecell_01.aligned.cellload.filtered.h5ad'
    }

    # Output directory
    output_dir = './enhanced_analysis'

    # Create analyzer and run
    analyzer = EnhancedPerturbationAnalyzer(data_paths, output_dir)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
