#!/usr/bin/env python3
"""
增强的扰动效率分析脚本
生成单细胞水平的扰动效率分布图和详细统计
"""

import pandas as pd
import numpy as np
import scanpy as sc
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class EnhancedPerturbationAnalyzer:
    def __init__(self, data_paths, output_dir):
        """
        初始化增强的扰动分析器

        Parameters:
        -----------
        data_paths : dict
            数据文件路径字典，格式: {'dataset_name': 'path_to_h5ad'}
        output_dir : str or Path
            输出目录路径
        """
        self.data_paths = data_paths
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.adatas = {}

        # 定义每个数据集的列名映射
        self.column_mapping = {
            'original': {'pert_col': 'gene', 'gene_col': 'gene_name'},
            'log1p_kd': {'pert_col': 'gene', 'gene_col': 'gene_name'},
            'batch_kd': {'pert_col': 'target_gene', 'gene_col': 'index'}
        }
        
    def load_data(self):
        """加载所有数据集"""
        print("加载数据集...")
        
        for name, path in self.data_paths.items():
            print(f"  加载 {name}: {path}")
            try:
                adata = sc.read_h5ad(path)
                self.adatas[name] = adata
                print(f"    成功: {adata.n_obs} 细胞, {adata.n_vars} 基因")
            except Exception as e:
                print(f"    失败: {e}")
        
        print(f"成功加载 {len(self.adatas)} 个数据集\n")
    
    def find_common_perturbations(self, min_cells=50):
        """找到所有数据集中共同的扰动，且细胞数足够"""
        print(f"寻找共同扰动（最少 {min_cells} 个细胞）...")

        # 获取每个数据集中的扰动及其细胞数
        all_perturbations = {}

        for name, adata in self.adatas.items():
            # 使用正确的扰动列名
            pert_col = self.column_mapping[name]['pert_col']
            pert_counts = adata.obs[pert_col].value_counts()

            # 过滤掉细胞数不足的扰动
            valid_perts = pert_counts[pert_counts >= min_cells].index.tolist()
            # 排除对照组
            valid_perts = [p for p in valid_perts if p not in ['non-targeting', 'control', 'ctrl']]
            all_perturbations[name] = set(valid_perts)
            print(f"  {name}: {len(valid_perts)} 个有效扰动")

        # 找到共同扰动
        common_perts = set.intersection(*all_perturbations.values())
        print(f"  共同扰动: {len(common_perts)} 个")

        # 按细胞数排序，选择前几个进行详细分析
        pert_cell_counts = {}
        for pert in common_perts:
            total_cells = 0
            for name, adata in self.adatas.items():
                pert_col = self.column_mapping[name]['pert_col']
                total_cells += adata.obs[pert_col].value_counts().get(pert, 0)
            pert_cell_counts[pert] = total_cells

        # 选择细胞数最多的扰动
        selected_perts = sorted(pert_cell_counts.items(),
                              key=lambda x: x[1], reverse=True)[:5]

        print("  选择用于详细分析的扰动:")
        for pert, count in selected_perts:
            print(f"    {pert}: {count} 个细胞")

        return [pert for pert, _ in selected_perts]
    
    def calculate_single_cell_efficiency(self, perturbations):
        """计算单细胞水平的扰动效率"""
        print("计算单细胞扰动效率...")

        efficiency_results = {}

        for pert in perturbations:
            print(f"  分析扰动: {pert}")
            efficiency_results[pert] = {}

            for dataset_name, adata in self.adatas.items():
                print(f"    数据集: {dataset_name}")

                # 获取列名映射
                pert_col = self.column_mapping[dataset_name]['pert_col']
                gene_col = self.column_mapping[dataset_name]['gene_col']

                # 检查目标基因是否存在
                if gene_col == 'index':
                    # 使用 var.index
                    if pert not in adata.var_names:
                        print(f"      跳过: 目标基因 {pert} 不存在于 var.index")
                        continue
                    gene_idx = adata.var_names.get_loc(pert)
                else:
                    # 使用 var 中的指定列
                    if gene_col not in adata.var.columns:
                        print(f"      跳过: 基因列 {gene_col} 不存在")
                        continue
                    if pert not in adata.var[gene_col].values:
                        print(f"      跳过: 目标基因 {pert} 不存在于 {gene_col}")
                        continue
                    gene_idx = adata.var[adata.var[gene_col] == pert].index[0]
                    gene_idx = adata.var_names.get_loc(gene_idx)

                try:
                    # 获取扰动细胞和对照细胞
                    pert_mask = adata.obs[pert_col] == pert
                    ctrl_mask = adata.obs[pert_col].isin(['non-targeting', 'control', 'ctrl'])

                    if pert_mask.sum() == 0 or ctrl_mask.sum() == 0:
                        print(f"      跳过: 缺少扰动或对照细胞")
                        continue
                    
                    # 提取表达数据
                    if hasattr(adata.X, 'toarray'):
                        pert_expr = adata.X[pert_mask, gene_idx].toarray().flatten()
                        ctrl_expr = adata.X[ctrl_mask, gene_idx].toarray().flatten()
                    else:
                        pert_expr = adata.X[pert_mask, gene_idx].flatten()
                        ctrl_expr = adata.X[ctrl_mask, gene_idx].flatten()
                    
                    # 计算对照组的平均表达
                    ctrl_mean = np.mean(ctrl_expr)
                    
                    # 计算每个扰动细胞的效率（相对于对照组平均值的变化）
                    if 'log1p' in dataset_name.lower() or np.max(adata.X) < 20:
                        # log1p转换数据：直接计算差值
                        single_cell_efficiency = pert_expr - ctrl_mean
                    else:
                        # 原始计数数据：计算log fold change
                        single_cell_efficiency = np.log2(pert_expr + 1) - np.log2(ctrl_mean + 1)
                    
                    # 存储结果
                    efficiency_results[pert][dataset_name] = {
                        'single_cell_efficiency': single_cell_efficiency,
                        'pert_expr': pert_expr,
                        'ctrl_expr': ctrl_expr,
                        'ctrl_mean': ctrl_mean,
                        'n_pert_cells': len(pert_expr),
                        'n_ctrl_cells': len(ctrl_expr),
                        'mean_efficiency': np.mean(single_cell_efficiency),
                        'std_efficiency': np.std(single_cell_efficiency)
                    }
                    
                    print(f"      成功: {len(pert_expr)} 个扰动细胞, "
                          f"平均效率 = {np.mean(single_cell_efficiency):.3f}")
                    
                except Exception as e:
                    print(f"      失败: {e}")
                    continue
        
        return efficiency_results
    
    def create_distribution_plots(self, efficiency_results):
        """创建单细胞扰动效率分布图"""
        print("生成扰动效率分布图...")
        
        perturbations = list(efficiency_results.keys())
        datasets = list(self.adatas.keys())
        
        # 为每个扰动创建分布图
        for pert in perturbations:
            print(f"  绘制 {pert} 的分布图")
            
            # 准备数据
            plot_data = []
            for dataset in datasets:
                if dataset in efficiency_results[pert]:
                    data = efficiency_results[pert][dataset]
                    for eff in data['single_cell_efficiency']:
                        plot_data.append({
                            'Dataset': dataset,
                            'Efficiency': eff,
                            'Perturbation': pert
                        })
            
            if not plot_data:
                continue
            
            df = pd.DataFrame(plot_data)
            
            # 创建图形
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'扰动效率分析: {pert}', fontsize=16, fontweight='bold')
            
            # 1. 分布直方图
            ax1 = axes[0, 0]
            for dataset in datasets:
                if dataset in efficiency_results[pert]:
                    data = efficiency_results[pert][dataset]['single_cell_efficiency']
                    ax1.hist(data, alpha=0.6, label=dataset, bins=30, density=True)
            ax1.set_xlabel('扰动效率')
            ax1.set_ylabel('密度')
            ax1.set_title('扰动效率分布直方图')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 箱线图
            ax2 = axes[0, 1]
            sns.boxplot(data=df, x='Dataset', y='Efficiency', ax=ax2)
            ax2.set_title('扰动效率箱线图')
            ax2.tick_params(axis='x', rotation=45)
            ax2.grid(True, alpha=0.3)
            
            # 3. 小提琴图
            ax3 = axes[1, 0]
            sns.violinplot(data=df, x='Dataset', y='Efficiency', ax=ax3)
            ax3.set_title('扰动效率小提琴图')
            ax3.tick_params(axis='x', rotation=45)
            ax3.grid(True, alpha=0.3)
            
            # 4. 统计摘要
            ax4 = axes[1, 1]
            ax4.axis('off')
            
            # 创建统计表格
            stats_text = f"统计摘要 - {pert}\n" + "="*30 + "\n\n"
            
            for dataset in datasets:
                if dataset in efficiency_results[pert]:
                    data = efficiency_results[pert][dataset]
                    stats_text += f"{dataset}:\n"
                    stats_text += f"  细胞数: {data['n_pert_cells']}\n"
                    stats_text += f"  平均效率: {data['mean_efficiency']:.3f}\n"
                    stats_text += f"  标准差: {data['std_efficiency']:.3f}\n"
                    stats_text += f"  中位数: {np.median(data['single_cell_efficiency']):.3f}\n"
                    stats_text += f"  25%分位: {np.percentile(data['single_cell_efficiency'], 25):.3f}\n"
                    stats_text += f"  75%分位: {np.percentile(data['single_cell_efficiency'], 75):.3f}\n\n"
            
            ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, 
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
            
            plt.tight_layout()
            
            # 保存图形
            output_file = self.output_dir / f'single_cell_efficiency_{pert}.png'
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"    保存到: {output_file}")
    
    def create_summary_plot(self, efficiency_results):
        """创建所有扰动的汇总比较图"""
        print("生成汇总比较图...")
        
        # 准备汇总数据
        summary_data = []
        
        for pert, pert_data in efficiency_results.items():
            for dataset, data in pert_data.items():
                for eff in data['single_cell_efficiency']:
                    summary_data.append({
                        'Perturbation': pert,
                        'Dataset': dataset,
                        'Efficiency': eff
                    })
        
        if not summary_data:
            return
        
        df = pd.DataFrame(summary_data)
        
        # 创建汇总图
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('所有扰动的效率分布汇总', fontsize=18, fontweight='bold')
        
        # 1. 按扰动分组的箱线图
        ax1 = axes[0, 0]
        sns.boxplot(data=df, x='Perturbation', y='Efficiency', ax=ax1)
        ax1.set_title('按扰动分组的效率分布')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # 2. 按数据集分组的箱线图
        ax2 = axes[0, 1]
        sns.boxplot(data=df, x='Dataset', y='Efficiency', ax=ax2)
        ax2.set_title('按数据集分组的效率分布')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # 3. 热图：平均效率
        ax3 = axes[1, 0]
        pivot_mean = df.groupby(['Perturbation', 'Dataset'])['Efficiency'].mean().unstack()
        sns.heatmap(pivot_mean, annot=True, fmt='.3f', cmap='RdBu_r', center=0, ax=ax3)
        ax3.set_title('平均扰动效率热图')
        
        # 4. 热图：效率标准差
        ax4 = axes[1, 1]
        pivot_std = df.groupby(['Perturbation', 'Dataset'])['Efficiency'].std().unstack()
        sns.heatmap(pivot_std, annot=True, fmt='.3f', cmap='Reds', ax=ax4)
        ax4.set_title('扰动效率标准差热图')
        
        plt.tight_layout()
        
        # 保存汇总图
        output_file = self.output_dir / 'perturbation_efficiency_summary.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"汇总图保存到: {output_file}")
    
    def generate_detailed_report(self, efficiency_results):
        """生成详细的统计报告"""
        print("生成详细统计报告...")
        
        report = []
        report.append("=" * 80)
        report.append("单细胞扰动效率详细分析报告")
        report.append("=" * 80)
        report.append("")
        
        # 数据集概览
        report.append("1. 数据集概览:")
        for name, adata in self.adatas.items():
            report.append(f"   {name}: {adata.n_obs:,} 细胞, {adata.n_vars:,} 基因")
        report.append("")
        
        # 详细的扰动效率分析
        report.append("2. 单细胞扰动效率详细分析:")
        
        for pert, pert_data in efficiency_results.items():
            report.append(f"\n   扰动: {pert}")
            report.append("   " + "-" * 50)
            
            for dataset, data in pert_data.items():
                eff_data = data['single_cell_efficiency']
                report.append(f"   数据集: {dataset}")
                report.append(f"     细胞数: {data['n_pert_cells']:,}")
                report.append(f"     对照细胞数: {data['n_ctrl_cells']:,}")
                report.append(f"     平均效率: {np.mean(eff_data):.4f}")
                report.append(f"     中位数效率: {np.median(eff_data):.4f}")
                report.append(f"     标准差: {np.std(eff_data):.4f}")
                report.append(f"     最小值: {np.min(eff_data):.4f}")
                report.append(f"     最大值: {np.max(eff_data):.4f}")
                report.append(f"     25%分位数: {np.percentile(eff_data, 25):.4f}")
                report.append(f"     75%分位数: {np.percentile(eff_data, 75):.4f}")
                
                # 计算有效扰动的细胞比例（效率 < -0.1）
                effective_cells = np.sum(eff_data < -0.1)
                effective_ratio = effective_cells / len(eff_data) * 100
                report.append(f"     有效扰动细胞比例: {effective_ratio:.1f}% ({effective_cells}/{len(eff_data)})")
                report.append("")
        
        # 保存报告
        report_file = self.output_dir / 'detailed_perturbation_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"详细报告保存到: {report_file}")
        
        # 打印报告摘要
        print("\n报告摘要:")
        for line in report[:20]:  # 只打印前20行
            print(line)
        print("...")
    
    def run_analysis(self):
        """运行完整的增强扰动分析"""
        print("开始增强的扰动效率分析...\n")
        
        try:
            # 1. 加载数据
            self.load_data()
            
            # 2. 找到共同扰动
            common_perturbations = self.find_common_perturbations()
            
            if not common_perturbations:
                print("未找到足够的共同扰动，分析终止")
                return
            
            # 3. 计算单细胞扰动效率
            efficiency_results = self.calculate_single_cell_efficiency(common_perturbations)
            
            # 4. 生成分布图
            self.create_distribution_plots(efficiency_results)
            
            # 5. 生成汇总图
            self.create_summary_plot(efficiency_results)
            
            # 6. 生成详细报告
            self.generate_detailed_report(efficiency_results)
            
            print(f"\n✓ 增强扰动分析完成！所有结果保存在: {self.output_dir}")
            
        except Exception as e:
            print(f"分析过程中出错: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    
    # 数据文件路径
    data_paths = {
        'original': '/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Replogle/K562_essential_raw_singlecell_01.aligned.h5ad',
        'log1p_kd': '/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p/Replogle/K562_essential_raw_singlecell_01.aligned.cellload.filtered.h5ad',
        'batch_kd': '/data/vcc/compass_loaded/colab_like/K562_essential_Training.h5ad'
    }
    
    # 输出目录
    output_dir = './enhanced_analysis'
    
    # 创建分析器并运行
    analyzer = EnhancedPerturbationAnalyzer(data_paths, output_dir)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
