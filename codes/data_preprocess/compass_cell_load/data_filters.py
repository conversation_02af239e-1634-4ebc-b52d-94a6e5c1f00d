import logging
import warnings
import re
import pandas as pd
import anndata 
import h5py
import numpy as np
import scipy.sparse as sp
import torch
import logging
from compass_singleton import Singleton

log = logging.getLogger(__name__)

warnings.filterwarnings("ignore")


class H5MetadataCache:
    """Cache for H5 file metadata to avoid repeated disk reads."""

    def __init__(
        self,
        h5_path: str,
        pert_col: str = "drug",
        cell_type_key: str = "cell_name",
        control_pert: str = "DMSO_TF",
        batch_col: str = "sample",
    ):
        """
        Args:
            h5_path: Path to the .h5ad or .h5 file
            pert_col: obs column name for perturbation
            cell_type_key: obs column name for cell type
            control_pert: the perturbation to treat as control
            batch_col: obs column name for batch/plate
        """
        self.h5_path = h5_path
        with h5py.File(h5_path, "r") as f:
            obs = f["obs"]

            # -- Categories --
            self.pert_categories = safe_decode_array(obs[pert_col]["categories"][:])
            self.cell_type_categories = safe_decode_array(
                obs[cell_type_key]["categories"][:]
            )

            # -- Batch: handle categorical vs numeric storage --
            batch_ds = obs[batch_col]
            if "categories" in batch_ds:
                self.batch_is_categorical = True
                self.batch_categories = safe_decode_array(batch_ds["categories"][:])
                self.batch_codes = batch_ds["codes"][:].astype(np.int32)
            else:
                self.batch_is_categorical = False
                raw = batch_ds[:]
                self.batch_categories = raw.astype(str)
                self.batch_codes = raw.astype(np.int32)

            # -- Codes for pert & cell type --
            self.pert_codes = obs[pert_col]["codes"][:].astype(np.int32)
            self.cell_type_codes = obs[cell_type_key]["codes"][:].astype(np.int32)

            # -- Control mask & counts --
            idx = np.where(self.pert_categories == control_pert)[0]
            if idx.size == 0:
                raise ValueError(
                    f"control_pert='{control_pert}' not found in {pert_col} categories"
                )
            self.control_pert_code = int(idx[0])
            self.control_mask = self.pert_codes == self.control_pert_code

            self.n_cells = len(self.pert_codes)

    def get_batch_names(self, indices: np.ndarray) -> np.ndarray:
        """Return batch labels for the provided cell indices."""
        return self.batch_categories[indices]

    def get_cell_type_names(self, indices: np.ndarray) -> np.ndarray:
        """Return cell‐type labels for the provided cell indices."""
        return self.cell_type_categories[indices]

    def get_pert_names(self, indices: np.ndarray) -> np.ndarray:
        """Return perturbation labels for the provided cell indices."""
        return self.pert_categories[indices]


class GlobalH5MetadataCache(metaclass=Singleton):
    """
    Singleton managing a shared dict of H5MetadataCache instances.
    Keys by h5_path only (same as before).
    """

    def __init__(self):
        self._cache: dict[str, H5MetadataCache] = {}

    def get_cache(
        self,
        h5_path: str,
        pert_col: str = "drug",
        cell_type_key: str = "cell_name",
        control_pert: str = "DMSO_TF",
        batch_col: str = "drug",
    ) -> H5MetadataCache:
        """
        If a cache for this file doesn’t yet exist, create it with the
        given parameters; otherwise return the existing one.
        """
        if h5_path not in self._cache:
            self._cache[h5_path] = H5MetadataCache(
                h5_path, pert_col, cell_type_key, control_pert, batch_col
            )
        return self._cache[h5_path]


def safe_decode_array(arr) -> np.ndarray:
    """
    Decode any byte-strings in `arr` to UTF-8 and cast all entries to Python str.

    Args:
        arr: array-like of bytes or other objects
    Returns:
        np.ndarray[str]: decoded strings
    """
    decoded = []
    for x in arr:
        if isinstance(x, (bytes, bytearray)):
            # decode bytes, ignoring errors
            decoded.append(x.decode("utf-8", errors="ignore"))
        else:
            decoded.append(str(x))
    return np.array(decoded, dtype=str)


def generate_onehot_map(keys) -> dict:
    """
    Build a map from each unique key to a fixed-length one-hot torch vector.

    Args:
        keys: iterable of hashable items
    Returns:
        dict[key, torch.FloatTensor]: one-hot encoding of length = number of unique keys
    """
    unique_keys = sorted(set(keys))
    num_classes = len(unique_keys)
    # identity matrix rows are one-hot vectors
    onehots = torch.eye(num_classes)
    return {k: onehots[i] for i, k in enumerate(unique_keys)}


def data_to_torch_X(X):
    """
    Convert input data to a dense torch FloatTensor.

    If passed an AnnData, extracts its .X matrix.
    If the result isn’t a NumPy array (e.g. a sparse matrix), calls .toarray().
    Finally wraps with torch.from_numpy(...).float().

    Args:
        X: anndata.AnnData or array-like (dense or sparse).
    Returns:
        torch.FloatTensor of shape (n_cells, n_features).
    """
    if isinstance(X, anndata.AnnData):
        X = X.X
    if not isinstance(X, np.ndarray):
        X = X.toarray()
    return torch.from_numpy(X).float()


def split_perturbations_by_cell_fraction(
    pert_groups: dict,
    val_fraction: float,
    rng: np.random.Generator = None,
):
    """
    Partition the set of perturbations into two subsets: 'val' vs 'train',
    such that the fraction of total cells in 'val' is as close as possible
    to val_fraction, using a greedy approach.

    Here, pert_groups is a dictionary where the keys are perturbation names
    and the values are numpy arrays of cell indices.

    Returns:
        train_perts: list of perturbation names
        val_perts:   list of perturbation names
    """
    if rng is None:
        rng = np.random.default_rng(42)

    # 1) Compute total # of cells across all perturbations
    total_cells = sum(len(indices) for indices in pert_groups.values())
    target_val_cells = val_fraction * total_cells

    # 2) Create a list of (pert_name, size), then shuffle
    pert_size_list = [(p, len(pert_groups[p])) for p in pert_groups.keys()]
    rng.shuffle(pert_size_list)

    # 3) Greedily add perts to the 'val' subset if it brings us closer to the target
    val_perts = []
    current_val_cells = 0
    for pert, size in pert_size_list:
        new_val_cells = current_val_cells + size

        # Compare how close we'd be to target if we add this perturbation vs. skip it
        diff_if_add = abs(new_val_cells - target_val_cells)
        diff_if_skip = abs(current_val_cells - target_val_cells)

        if diff_if_add < diff_if_skip:
            # Adding this perturbation gets us closer to the target fraction
            val_perts.append(pert)
            current_val_cells = new_val_cells
        # else: skip it; it goes to train

    train_perts = list(set(pert_groups.keys()) - set(val_perts))

    return train_perts, val_perts


def suspected_discrete_torch(x: torch.Tensor, n_cells: int = 100) -> bool:
    """Check if data appears to be discrete/raw counts by examining row sums.
    Adapted from validate_normlog function for PyTorch tensors.
    """
    top_n = min(x.shape[0], n_cells)
    rowsum = x[:top_n].sum(dim=1)

    # Check if row sums are integers (fractional part == 0)
    frac_part = rowsum - rowsum.floor()
    return torch.all(torch.abs(frac_part) < 1e-7)


def suspected_log_torch(x: torch.Tensor) -> bool:
    """Check if the data is log transformed already."""
    global_max = x.max()
    return global_max.item() < 15.0


def _mean(expr) -> float:
    """Return the mean of a dense or sparse 1-D/2-D slice."""
    if sp.issparse(expr):
        return float(expr.mean())
    return float(np.asarray(expr).mean())

def _overlap_frac(a_start: int, a_end: int, b_start: int, b_end: int) -> float:
    """区间重叠长度 / min(len1,len2)。无重叠返回 0。"""
    if a_end < a_start or b_end < b_start:
        return 0.0
    lo = max(a_start, b_start)
    hi = min(a_end, b_end)
    inter = max(0, hi - lo + 1)
    len_a = max(1, a_end - a_start + 1)
    len_b = max(1, b_end - b_start + 1)
    return inter / float(min(len_a, len_b))


def _build_components_by_gene(
    var_df: "pd.DataFrame",
    gene_name_col: str,
    chr_col: str,
    start_col: str,
    end_col: str,
    min_overlap_frac: float = 0.5,
) -> tuple[dict[str, list[list[int]]], pd.DataFrame]:
    """
    按 gene_name 精确相等分组；在组内按 (chr,start,end) 重叠 ≥ 阈值连边，取连通分量。
    返回：
      gene -> [component(indices_in_var), ...]
      log_df：记录每个 gene 的组件与成员（用于 CSV）
    """
    # 只读不改：var_index 保存 Ensembl 索引 & 行号
    var_index = var_df.index.astype(str)
    row_ids = np.arange(var_df.shape[0], dtype=int)

    need_cols = [gene_name_col, chr_col, start_col, end_col]
    for c in need_cols:
        if c not in var_df.columns:
            return {}, pd.DataFrame()

    df = var_df[[gene_name_col, chr_col, start_col, end_col]].copy()
    df["__row__"] = row_ids
    df["__ens__"] = var_index

    # 统一类型
    df[chr_col] = df[chr_col].astype(str)
    df[start_col] = pd.to_numeric(df[start_col], errors="coerce").astype("Int64")
    df[end_col]   = pd.to_numeric(df[end_col],   errors="coerce").astype("Int64")
    df[gene_name_col] = df[gene_name_col].astype(str)

    gene_to_components: dict[str, list[list[int]]] = {}
    logs = []

    for g, grp in df.groupby(gene_name_col, sort=False):
        if len(grp) == 1:
            gene_to_components[g] = [[int(grp["__row__"].iloc[0])]]
            continue

        # 建图（同 chr 且 overlap_frac ≥ 阈值连边）
        idxs = grp["__row__"].to_numpy(dtype=int)
        chrs = grp[chr_col].to_numpy(dtype=str)
        starts = grp[start_col].to_numpy()
        ends = grp[end_col].to_numpy()

        n = len(idxs)
        adj = [[] for _ in range(n)]
        for i in range(n):
            for j in range(i + 1, n):
                if chrs[i] != chrs[j]:
                    continue
                s1, e1 = starts[i], ends[i]
                s2, e2 = starts[j], ends[j]
                if pd.isna(s1) or pd.isna(e1) or pd.isna(s2) or pd.isna(e2):
                    continue
                frac = _overlap_frac(int(s1), int(e1), int(s2), int(e2))
                if frac >= min_overlap_frac:
                    adj[i].append(j); adj[j].append(i)

        # 连通分量
        seen = [False] * n
        comps: list[list[int]] = []
        for i in range(n):
            if seen[i]: continue
            stack = [i]; seen[i] = True
            comp = []
            while stack:
                u = stack.pop()
                comp.append(u)
                for v in adj[u]:
                    if not seen[v]:
                        seen[v] = True; stack.append(v)
            comps.append(sorted([idxs[k] for k in comp]))

        gene_to_components[g] = comps

        # 仅当该 gene 出现多条记录时记日志
        if len(grp) >= 2:
            for cid, comp in enumerate(comps):
                sub = grp.set_index("__row__").loc[comp]
                logs.append({
                    "gene_name": g,
                    "component_id": cid,
                    "n_transcripts": len(comp),
                    "var_indices": ";".join(map(str, comp)),
                    "ensembl_ids": ";".join(sub["__ens__"].astype(str).tolist()),
                    "chrs": ";".join(sub[chr_col].astype(str).tolist()),
                    "starts": ";".join(sub[start_col].astype(str).tolist()),
                    "ends": ";".join(sub[end_col].astype(str).tolist()),
                })

    return gene_to_components, pd.DataFrame(logs)



def is_on_target_knockdown(
    adata: anndata.AnnData,
    target_gene: str,
    perturbation_column: str = "gene",
    control_label: str = "non-targeting",
    residual_expression: float = 0.30,
    layer: str | None = None,
) -> bool:
    """
    True ⇢ average expression of *target_gene* in perturbed cells is below
    `residual_expression` × (average expression in control cells).

    Parameters
    ----------
    adata : AnnData
    target_gene : str
        Gene symbol to check.
    perturbation_column : str, default "gene"
        Column in ``adata.obs`` holding perturbation identities.
    control_label : str, default "non-targeting"
        Category in *perturbation_column* marking control cells.
    residual_expression : float, default 0.30
        Residual fraction (0‒1). 0.30 → 70 % knock-down.
    layer : str | None, optional
        Use this matrix in ``adata.layers`` instead of ``adata.X``.

    Raises
    ------
    KeyError
        *target_gene* not present in ``adata.var_names``.
    ValueError
        No perturbed cells for *target_gene*, or control mean is zero.

    Returns
    -------
    bool
    """
    if target_gene == control_label:
        # Never evaluate the control itself
        return False

    if target_gene not in adata.var_names:
        print(f"Gene {target_gene!r} not found in `adata.var_names`.")
        return 1

    gene_idx = adata.var_names.get_loc(target_gene)
    X = adata.layers[layer] if layer is not None else adata.X

    control_cells = adata.obs[perturbation_column] == control_label
    perturbed_cells = adata.obs[perturbation_column] == target_gene

    if not perturbed_cells.any():
        raise ValueError(f"No cells labelled with perturbation {target_gene!r}.")

    try:
        control_mean = _mean(X[control_cells, gene_idx])
    except:
        control_cells = (adata.obs[perturbation_column] == control_label).values
        control_mean = _mean(X[control_cells, gene_idx])

    if control_mean == 0:
        raise ValueError(
            f"Mean {target_gene!r} expression in control cells is zero; "
            "cannot compute knock-down ratio."
        )

    try:
        perturbed_mean = _mean(X[perturbed_cells, gene_idx])
    except:
        perturbed_cells = (adata.obs[perturbation_column] == target_gene).values
        perturbed_mean = _mean(X[perturbed_cells, gene_idx])

    knockdown_ratio = perturbed_mean / control_mean
    return knockdown_ratio < residual_expression


def filter_on_target_knockdown(
    adata: anndata.AnnData,
    perturbation_column: str = "gene",
    control_label: str = "non-targeting",
    residual_expression: float = 0.30,
    cell_residual_expression: float = 0.50,
    min_cells: int = 30,
    layer: str | None = None,
    var_gene_name: str = "gene_name",
    # --- 聚合/组件选择相关 ---
    aggregate_transcripts: bool = False,
    gene_name_col: str = "gene_name",
    chr_col: str = "chr",
    start_col: str = "start",
    end_col: str = "end",
    min_overlap_frac: float = 0.5,
    reverse_log1p: bool = False,
    obs_gene_id_col: str = "gene_id",
    prefer_obs_gene_id: bool = True,
    log_duplicates_csv: str | None = None,
    dataset_name: str = "unknown",
    # --- 可选写回 obs 的诊断信息 ---
    cell_residual_obs_key: str | None = "cell_residual_ratio",
    cell_target_expr_obs_key: str | None = None,
    cell_ctrl_mean_obs_key: str | None = None,
    # --- 归一化控制 ---
    skip_normalization: bool = False,

) -> anndata.AnnData:
    """
    省内存 + 带进度日志 的 KD 过滤：
    - 对照/扰动均值都按「行分片」计算（不构造巨型数组）
    - 关键循环插入进度日志（基因层、行分片层）
    - 与原始数学定义等价
    """
    import os
    import time
    from math import ceil
    logger = logging.getLogger(__name__)
    logger.info("start KD")
    # ----------- 环境参数（可调） -----------
    ROW_CHUNK = int(os.environ.get("KD_ROW_CHUNK", "50000"))           # 行分片大小
    PRINT_EVERY = int(os.environ.get("KD_PRINT_EVERY", "10"))           # 每多少个分片/基因打印一次
    USE_PROGRESS = os.environ.get("PROGRESS", "1").lower() in ("1", "true", "y")
    DEBUG_KD = os.environ.get("DEBUG_KD", "0").lower() in ("1", "true", "y") or skip_normalization
    # --------------------------------------

    a = adata  # 不 copy，减少内存占用；返回切片 view
    X = a.layers[layer] if layer is not None else a.X
    n_obs = a.n_obs

    # 预备 obs 字段（方便后续直接写入）
    if cell_residual_obs_key is not None and cell_residual_obs_key not in a.obs:
        a.obs[cell_residual_obs_key] = np.nan
    if cell_target_expr_obs_key is not None and cell_target_expr_obs_key not in a.obs:
        a.obs[cell_target_expr_obs_key] = np.nan
    if cell_ctrl_mean_obs_key is not None and cell_ctrl_mean_obs_key not in a.obs:
        a.obs[cell_ctrl_mean_obs_key] = np.nan

    # 读取扰动列，并拿到对照/每基因的行索引（避免在循环里反复 np.where）
    perts = a.obs[perturbation_column].astype(str)
    ctrl_rows = np.where(perts.values == control_label)[0]
    if ctrl_rows.size == 0:
        logger.warning("[KD] 没有找到对照细胞，直接返回空结果。")
        return a[[]]  # 空

    # gene_name -> 列索引列表（尽量轻量的构造）
    # 注意：这里不把所有 gene_name 都一次性放大到巨型 dict（保留为 numpy arrays）

    def robust_gene_name_check(adata, gene_name_col):
        """健壮的基因名称检测函数"""
        # 详细日志记录
        logger.info(f"[GENE_CHECK] Checking for gene column: '{gene_name_col}'")
        logger.info(f"[GENE_CHECK] Available columns: {list(adata.var.columns)}")

        # 多种检测方法
        methods = [
            ("direct", lambda: gene_name_col in adata.var.columns),
            ("string", lambda: str(gene_name_col) in [str(col) for col in adata.var.columns]),
            ("lower", lambda: gene_name_col.lower() in [str(col).lower() for col in adata.var.columns]),
            ("strip", lambda: gene_name_col.strip() in [str(col).strip() for col in adata.var.columns])
        ]

        for method_name, check_func in methods:
            try:
                if check_func():
                    logger.info(f"[GENE_CHECK] Found using method '{method_name}'")
                    return True
            except Exception as e:
                logger.warning(f"[GENE_CHECK] Method '{method_name}' failed: {e}")

        logger.warning(f"[GENE_CHECK] Column '{gene_name_col}' not found with any method")
        return False

    if robust_gene_name_check(a, gene_name_col):
        # 借助 pandas 的 groupby 但只拿必要的数据，避免中间副本
        name_to_rows = (
            a.var.reset_index()[[gene_name_col]]
            .assign(idx=lambda df: df.index.astype(int))
            .groupby(gene_name_col, sort=False)["idx"]
            .apply(lambda s: s.to_numpy(dtype=int, copy=False))
            .to_dict()
        )
    else:
        # 当gene_name_col不存在时，使用var.index作为基因名称
        logger.warning(f"Gene name column '{gene_name_col}' not found, using var.index for gene matching")
        name_to_rows = {
            gene_name: np.array([i], dtype=int)
            for i, gene_name in enumerate(a.var.index)
        }

    # 若开启组件构建
    gene_to_components = {}
    if aggregate_transcripts:
        try:
            gene_to_components, log_df = _build_components_by_gene(
                a.var, gene_name_col, chr_col, start_col, end_col, min_overlap_frac
            )
            if log_duplicates_csv and log_df is not None and not log_df.empty:
                df_to_save = log_df.copy()
                df_to_save.insert(0, "dataset_name", dataset_name)
                mode = "a" if os.path.exists(log_duplicates_csv) else "w"
                header = not os.path.exists(log_duplicates_csv)
                df_to_save.to_csv(log_duplicates_csv, mode=mode, header=header, index=False)
        except Exception as e:
            logger.warning(f"[KD] 组件构建失败，将回退为按 {gene_name_col} 等值聚合。err={e}")
            gene_to_components = {}

    # 每个基因在 obs 中最常见的 Ensembl（如需）
    gene_to_obs_gid = {}
    if prefer_obs_gene_id and obs_gene_id_col in a.obs.columns:
        tmp = a.obs[[perturbation_column, obs_gene_id_col]].astype(str)
        vc = tmp.groupby(perturbation_column)[obs_gene_id_col].agg(lambda s: s.value_counts().index[0])
        gene_to_obs_gid = vc.to_dict()

    # 需要检查的基因集合（排除 control）
    all_genes = perts.unique().tolist()
    genes_to_check = [g for g in all_genes if g != control_label]

    logger.info(f"[KD] 总细胞: {n_obs:,} | 对照细胞: {ctrl_rows.size:,} | 待检查扰动基因: {len(genes_to_check):,}")

    # 添加数据特征诊断
    if DEBUG_KD or skip_normalization:
        X_sample = a.X[:min(1000, n_obs), :min(1000, a.n_vars)]
        if sp.issparse(X_sample):
            X_sample_dense = X_sample.toarray()
        else:
            X_sample_dense = X_sample

        logger.info(f"[DEBUG] Data characteristics (sample {X_sample_dense.shape}):")
        logger.info(f"[DEBUG] - Value range: [{X_sample_dense.min():.6f}, {X_sample_dense.max():.6f}]")
        logger.info(f"[DEBUG] - Mean: {X_sample_dense.mean():.6f}, Std: {X_sample_dense.std():.6f}")
        logger.info(f"[DEBUG] - Sparsity: {np.count_nonzero(X_sample_dense)}/{X_sample_dense.size} ({100*np.count_nonzero(X_sample_dense)/X_sample_dense.size:.1f}% non-zero)")
        logger.info(f"[DEBUG] - Skip normalization: {skip_normalization}")

        # 检查控制组细胞的表达特征
        if ctrl_rows.size > 0:
            ctrl_sample = a.X[ctrl_rows[:min(100, len(ctrl_rows))], :min(100, a.n_vars)]
            if sp.issparse(ctrl_sample):
                ctrl_sample_dense = ctrl_sample.toarray()
            else:
                ctrl_sample_dense = ctrl_sample
            logger.info(f"[DEBUG] Control group sample ({ctrl_sample_dense.shape}):")
            logger.info(f"[DEBUG] - Control range: [{ctrl_sample_dense.min():.6f}, {ctrl_sample_dense.max():.6f}]")
            logger.info(f"[DEBUG] - Control mean: {ctrl_sample_dense.mean():.6f}")

    logger.info(f"[KD] Stage 1 - 开始基因级KD判定，共 {len(genes_to_check)} 个基因")
    t0 = time.perf_counter()

            # --------- 辅助：对 X[row_idx, col_idx] 做反 log1p 后的行和（分片版本） ---------
    def _rowsum_over_cols_chunked(row_idx: np.ndarray, col_idx: np.ndarray, reverse: bool) -> np.ndarray:
        """Fixed version using direct sparse matrix operations"""
        if col_idx.size == 0 or row_idx.size == 0:
            return np.zeros(row_idx.size, dtype=np.float32)

        out = np.zeros(row_idx.size, dtype=np.float32)
        n_chunks = ceil(row_idx.size / ROW_CHUNK)
        
        for ci in range(n_chunks):
            s = ci * ROW_CHUNK
            e = min((ci + 1) * ROW_CHUNK, row_idx.size)
            rows = row_idx[s:e]

            sub = X[rows][:, col_idx]  
            
            if sp.issparse(sub):
                # Use the simple, correct method
                row_sums = np.asarray(sub.sum(axis=1)).ravel()
                if reverse:
                    row_sums = np.expm1(row_sums.astype(np.float64)).astype(np.float32)
            else:
                arr = np.asarray(sub, dtype=np.float64)
                if reverse:
                    arr = np.expm1(arr)
                row_sums = arr.sum(axis=1).astype(np.float32)

            out[s:e] = row_sums

        return out


    # --------- 组件选择：优先包含 obs Ensembl 的组件，否则取 ctrl_mean 最大的 ---------
    def _choose_cols_for_gene(g: str) -> tuple[np.ndarray, float]:
        # 候选列组
        groups = []
        if aggregate_transcripts and g in gene_to_components:
            groups = gene_to_components[g]
        else:
            rows = name_to_rows.get(g, [])
            if len(rows) > 0:
                groups = [rows]

        if not groups:
            return np.array([], dtype=int), 0.0

        best_cols = None
        best_ctrl_mean = -1.0

        # 若 obs 给了 Ensembl 提示，先尝试命中
        gid_hint = gene_to_obs_gid.get(g)
        if prefer_obs_gene_id and gid_hint is not None:
            for cols in groups:
                ens_here = set(a.var.index.to_numpy()[cols].astype(str).tolist())
                if gid_hint in ens_here:
                    ctrl_vals = _rowsum_over_cols_chunked(ctrl_rows, np.asarray(cols, dtype=int), reverse_log1p)
                    m = float(ctrl_vals.mean()) if ctrl_vals.size else 0.0
                    if m > 0:
                        return np.asarray(cols, dtype=int), m
                    # 命中了但均值为 0，则继续下面“最大均值”逻辑
                    break

        # 否则取 ctrl_mean 最大
        for cols in groups:
            col_idx = np.asarray(cols, dtype=int)
            ctrl_vals = _rowsum_over_cols_chunked(ctrl_rows, col_idx, reverse_log1p)
            m = float(ctrl_vals.mean()) if ctrl_vals.size else 0.0
            if m > best_ctrl_mean:
                best_ctrl_mean, best_cols = m, col_idx

        # 当跳过归一化时，使用更宽松的阈值
        min_ctrl_threshold = -1e-6 if skip_normalization else 0.0

        if best_cols is None or best_ctrl_mean <= min_ctrl_threshold:
            return np.array([], dtype=int), 0.0
        return best_cols, best_ctrl_mean

    # ------------------------ Stage 1：基因级 KD 判定 ------------------------
    perts_to_keep = [control_label]
    for gi, g in enumerate(genes_to_check, 1):
        t_g0 = time.perf_counter()
        cols, ctrl_mean = _choose_cols_for_gene(g)
        # 当跳过归一化时，使用更宽松的阈值
        min_ctrl_threshold = -1e-6 if skip_normalization else 0.0

        # 添加详细诊断信息
        if DEBUG_KD and gi <= 5:  # 只为前5个基因输出详细信息
            logger.info(f"[DEBUG] Gene {g}: found cols.size={cols.size}, ctrl_mean={ctrl_mean:.6f}, threshold={min_ctrl_threshold}")
            if cols.size > 0:
                # 检查原始数据特征
                sample_vals = _rowsum_over_cols_chunked(ctrl_rows[:min(100, len(ctrl_rows))], cols, reverse_log1p)
                logger.info(f"[DEBUG] Gene {g}: sample ctrl values - min={sample_vals.min():.6f}, max={sample_vals.max():.6f}, mean={sample_vals.mean():.6f}, nonzero={np.count_nonzero(sample_vals)}/{len(sample_vals)}")

        if cols.size == 0 or ctrl_mean <= min_ctrl_threshold:
            if DEBUG_KD and gi <= 10:  # 为前10个失败的基因输出详细信息
                logger.info(f"[DEBUG/stage1] {gi}/{len(genes_to_check)} gene={g} → REJECTED: cols.size={cols.size}, ctrl_mean={ctrl_mean:.6f} <= {min_ctrl_threshold}")
            elif USE_PROGRESS and (gi % PRINT_EVERY == 0 or gi == len(genes_to_check)):
                logger.info(f"[KD/stage1] {gi}/{len(genes_to_check)} gene={g} → 跳过（无有效组件或ctrl_mean<={min_ctrl_threshold}）")
            continue

        pert_rows = np.where(perts.values == g)[0]
        if pert_rows.size == 0:
            if USE_PROGRESS and (gi % PRINT_EVERY == 0 or gi == len(genes_to_check)):
                logger.info(f"[KD/stage1] {gi}/{len(genes_to_check)} gene={g} → 跳过（无扰动细胞）")
            continue

        pert_vals = _rowsum_over_cols_chunked(pert_rows, cols, reverse_log1p)
        pert_mean = float(pert_vals.mean()) if pert_vals.size else 0.0
        kd_ratio = (pert_mean / ctrl_mean) if ctrl_mean > 0 else np.inf

        if kd_ratio < residual_expression:
            perts_to_keep.append(g)

        if USE_PROGRESS:
            dt = time.perf_counter() - t_g0
            if (gi % PRINT_EVERY == 0) or gi == len(genes_to_check):
                logger.info(
                    f"[KD/stage1] {gi}/{len(genes_to_check)} gene={g} "
                    f"cols={cols.size} ctrl_mean={ctrl_mean:.4g} pert_mean={pert_mean:.4g} "
                    f"ratio={kd_ratio:.3f} keep={kd_ratio<residual_expression} "
                    f"({dt:.2f}s)"
                )

    # ------------------------ Stage 2：细胞级过滤 ------------------------
    keep_mask = np.zeros(n_obs, dtype=bool)
    keep_mask[ctrl_rows] = True  # 对照直接保留
    # 记下每个保留基因的组件与 ctrl_mean，避免重复计算
    selected_group: dict[str, tuple[np.ndarray, float]] = {}

    # 可选把对照的 residual_ratio 记为 1.0
    if cell_residual_obs_key is not None:
        a.obs.loc[a.obs.index[ctrl_rows], cell_residual_obs_key] = 1.0

    genes_after_stage1 = [g for g in perts_to_keep if g != control_label]
    for gi, g in enumerate(genes_after_stage1, 1):
        t_g0 = time.perf_counter()
        # 组件选择（若第一次见到就算一次）
        if g in selected_group:
            cols, ctrl_mean = selected_group[g]
        else:
            cols, ctrl_mean = _choose_cols_for_gene(g)
            selected_group[g] = (cols, ctrl_mean)

        # 当跳过归一化时，使用更宽松的阈值
        min_ctrl_threshold = -1e-6 if skip_normalization else 0.0
        if cols.size == 0 or ctrl_mean <= min_ctrl_threshold:
            if USE_PROGRESS and (gi % PRINT_EVERY == 0 or gi == len(genes_after_stage1)):
                logger.info(f"[KD/stage2] {gi}/{len(genes_after_stage1)} gene={g} → 跳过（组件无效/ctrl_mean<={min_ctrl_threshold}）")
            continue

        pert_rows = np.where(perts.values == g)[0]
        if pert_rows.size == 0:
            if USE_PROGRESS and (gi % PRINT_EVERY == 0 or gi == len(genes_after_stage1)):
                logger.info(f"[KD/stage2] {gi}/{len(genes_after_stage1)} gene={g} → 跳过（无扰动细胞）")
            continue

        # 分片处理该基因的扰动细胞
        n_chunks = ceil(pert_rows.size / ROW_CHUNK)
        kept_here = 0
        for ci in range(n_chunks):
            s = ci * ROW_CHUNK
            e = min((ci + 1) * ROW_CHUNK, pert_rows.size)
            rows = pert_rows[s:e]

            vals = _rowsum_over_cols_chunked(rows, cols, reverse_log1p)  # 当前分片的聚合表达
            ratios = vals / ctrl_mean

            # 记录 keep
            part_keep = ratios < cell_residual_expression
            keep_mask[rows] = part_keep

            # 可选写回诊断 obs
            if cell_residual_obs_key is not None:
                a.obs.loc[a.obs.index[rows], cell_residual_obs_key] = ratios.astype(np.float32, copy=False)
            if cell_target_expr_obs_key is not None:
                a.obs.loc[a.obs.index[rows], cell_target_expr_obs_key] = vals.astype(np.float32, copy=False)
            if cell_ctrl_mean_obs_key is not None:
                a.obs.loc[a.obs.index[rows], cell_ctrl_mean_obs_key] = float(ctrl_mean)

            kept_here += int(part_keep.sum())

            if USE_PROGRESS and ((ci + 1) % PRINT_EVERY == 0 or (ci + 1) == n_chunks):
                logger.info(
                    f"[KD/stage2] gene={g} 分片 {ci+1}/{n_chunks} "
                    f"kept_in_chunk={int(part_keep.sum())} kept_total={kept_here} "
                    f"rows {s}-{e-1}"
                )

        if USE_PROGRESS:
            dt = time.perf_counter() - t_g0
            logger.info(f"[KD/stage2] gene={g} 完成，保留细胞 {kept_here}/{pert_rows.size}（{dt:.2f}s）")

    # ------------------------ Stage 3：min_cells 约束 ------------------------
    # 对每个基因，若保留细胞数 < min_cells，则把这部分也去掉
    for gi, g in enumerate(genes_after_stage1, 1):
        rows = np.where(perts.values == g)[0]
        kept = keep_mask[rows]
        if kept.sum() < min_cells:
            keep_mask[rows] = False
            if USE_PROGRESS and (gi % PRINT_EVERY == 0 or gi == len(genes_after_stage1)):
                logger.info(f"[KD/stage3] gene={g} 通过细胞数 {int(kept.sum())} < {min_cells} → 全部剔除")

    # ------------------------ 汇总 ------------------------
    total_kept = int(keep_mask.sum())
    dt_all = time.perf_counter() - t0
    logger.info(
        f"[KD] 完成：保留 {total_kept:,}/{n_obs:,} 细胞 | "
        f"对照 {ctrl_rows.size:,} | 通过基因 {len(genes_after_stage1):,} | 用时 {dt_all:.2f}s"
    )

    return a[keep_mask]



def set_var_index_to_col(adata: anndata.AnnData, col: str = "col", copy=True) -> None:
    """
    Set `adata.var` index to the values in the specified column, allowing non-unique indices.

    Parameters
    ----------
    adata : AnnData
        The AnnData object to modify.
    col : str
        Column in `adata.var` to use as the new index.

    Raises
    ------
    KeyError
        If the specified column does not exist in `adata.var`.
    """
    if col not in adata.var.columns:
        raise KeyError(f"Column {col!r} not found in adata.var.")

    adata.var.index = adata.var[col].astype("str")
    adata.var_names_make_unique()
    return adata
