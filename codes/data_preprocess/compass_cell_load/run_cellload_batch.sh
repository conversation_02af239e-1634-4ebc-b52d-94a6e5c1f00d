#!/usr/bin/env bash
set -euo pipefail
export PROGRESS="${PROGRESS:-1}"

# 新增参数说明:
# SKIP_NORMALIZATION=true  - 跳过1e4归一化，直接对原始count数据进行KD过滤
# SKIP_NORMALIZATION=false - 保持现有行为，先进行1e4归一化再进行KD过滤 (默认)
# 注意: 此参数仅影响 is_log1p=False 的数据处理分支

# ===== 内存高效配置 =====
INPUT_DIR="${1:-/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Replogle/secondbatch_only_rpe1}"
PYTHON_BIN="${PYTHON_BIN:-python}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPT_PATH="${SCRIPT_PATH:-${SCRIPT_DIR}/cellload_auto.py}"

# 更保守的内存配置 - 防止内存爆炸
N_JOBS="${N_JOBS:-3}"                    # 降低并行数
MAX_MEMORY_GB="${MAX_MEMORY_GB:-400}"    # 每个进程200GB限制
BATCH_SIZE="${BATCH_SIZE:-1}"

# 内存高效环境变量
export LOAD_CHUNK_ROWS=50000            # 大幅减小chunk size
export FULL_READ_KEEP_RATIO=0.85        # 降低全读取阈值
export RUN_SLICE_ROWS=500000            # 减小slice size
export MAX_GAP_ROWS=5000                # 减小gap容忍度
export TARGET_CALLS=16                  # 增加调用次数，减少每次数据量
export MAX_SPARSE_INDEX_SIZE=50000000000 # 50GB稀疏矩阵限制

# 更严格的内存管理
export MALLOC_ARENA_MAX=2               # 减少内存arena
export MALLOC_MMAP_THRESHOLD_=16384     # 更小的mmap阈值
export MALLOC_TRIM_THRESHOLD_=16384     # 更积极的内存trim
export MALLOC_TOP_PAD_=16384            # 减少top pad

# 数值库线程限制
export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1  
export OPENBLAS_NUM_THREADS=1
export NUMEXPR_NUM_THREADS=1

# Python优化
export PYTHONHASHSEED=0
export PYTHONUNBUFFERED=1

# KD参数
PERTURB_COL="${PERTURB_COL:-gene}"
CONTROL_LABEL="${CONTROL_LABEL:-non-targeting}"
RESIDUAL="${RESIDUAL:-0.30}"
CELL_RESIDUAL="${CELL_RESIDUAL:-0.50}"
MIN_CELLS="${MIN_CELLS:-30}"
LAYER="${LAYER:-}"
CHR_COL="${CHR_COL:-chr}"
START_COL="${START_COL:-start}"
END_COL="${END_COL:-end}"
MIN_OVLP="${MIN_OVLP:-0.5}"
DATASET_NAME="${DATASET_NAME:-unknown}"
NORM_TOL_ABS="${NORM_TOL_ABS:-1.0}"
SKIP_NORMALIZATION="${SKIP_NORMALIZATION:-true}"

DEBUG="${DEBUG:-0}"
[[ "$DEBUG" == "1" ]] && set -x

# 创建日志目录
LOG_DIR="${INPUT_DIR}/processing_logs"
mkdir -p "$LOG_DIR"

# 记录开始时间
START_TIME=$(date +%s)

# 验证输入
[[ -d "${INPUT_DIR}" ]] || { echo "[ERR] INPUT_DIR must be a directory: ${INPUT_DIR}" >&2; exit 1; }
[[ -f "${SCRIPT_PATH}" ]] || { echo "[ERR] Script not found: ${SCRIPT_PATH}" >&2; exit 1; }

# 内存检查和动态调整
TOTAL_MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
AVAILABLE_MEMORY_GB=$(free -g | awk '/^Mem:/{print $7}')

echo "[MEMORY] Total: ${TOTAL_MEMORY_GB}GB, Available: ${AVAILABLE_MEMORY_GB}GB"

# 根据可用内存动态调整参数
if [[ $AVAILABLE_MEMORY_GB -lt 400 ]]; then
    echo "[WARN] Low available memory (${AVAILABLE_MEMORY_GB}GB), using ultra-conservative settings"
    export LOAD_CHUNK_ROWS=25000
    export RUN_SLICE_ROWS=250000
    MAX_MEMORY_GB=100
    N_JOBS=1
elif [[ $AVAILABLE_MEMORY_GB -lt 600 ]]; then
    echo "[WARN] Moderate available memory (${AVAILABLE_MEMORY_GB}GB), using conservative settings"  
    export LOAD_CHUNK_ROWS=35000
    export RUN_SLICE_ROWS=350000
    MAX_MEMORY_GB=150
    N_JOBS=1
else
    echo "[INFO] Good available memory (${AVAILABLE_MEMORY_GB}GB), using standard settings"
    export LOAD_CHUNK_ROWS=50000
    export RUN_SLICE_ROWS=500000
fi

# 输出配置
cat << EOF
[CONFIG] Memory-Efficient Settings:
  INPUT_DIR: ${INPUT_DIR}
  N_JOBS: ${N_JOBS}
  MAX_MEMORY_GB: ${MAX_MEMORY_GB}
  LOAD_CHUNK_ROWS: ${LOAD_CHUNK_ROWS}
  RUN_SLICE_ROWS: ${RUN_SLICE_ROWS}
  MAX_SPARSE_INDEX_SIZE: ${MAX_SPARSE_INDEX_SIZE}
  AVAILABLE_MEMORY: ${AVAILABLE_MEMORY_GB}GB
EOF

# 构建通用参数
COMMON_ARGS=(
    "--perturbation-column" "${PERTURB_COL}"
    "--control-label" "${CONTROL_LABEL}"
    "--residual-expression" "${RESIDUAL}"
    "--cell-residual-expression" "${CELL_RESIDUAL}"
    "--min-cells" "${MIN_CELLS}"
    "--chr-col" "${CHR_COL}"
    "--start-col" "${START_COL}"
    "--end-col" "${END_COL}"
    "--min-overlap-frac" "${MIN_OVLP}"
    "--dataset-name" "${DATASET_NAME}"
    "--norm-tol-abs" "${NORM_TOL_ABS}"
    "--max-memory-gb" "${MAX_MEMORY_GB}"
)

[[ -n "${LAYER}" ]] && COMMON_ARGS+=("--layer" "${LAYER}")
[[ "${SKIP_NORMALIZATION}" == "true" ]] && COMMON_ARGS+=("--skip-normalization")

# 收集文件并按大小排序
echo "[INFO] Collecting files..."
mapfile -d '' FILES < <(find "${INPUT_DIR}" -type f -name '*.h5ad' -print0)

if [[ ${#FILES[@]} -eq 0 ]]; then
    echo "[ERR] No .h5ad files found under ${INPUT_DIR}" >&2
    exit 1
fi

# 按文件大小排序（大文件优先，便于早期发现内存问题）
declare -a SORTED_FILES
while IFS= read -r -d '' file; do
    SORTED_FILES+=("$file")
done < <(printf '%s\0' "${FILES[@]}" | xargs -0 ls -Sr | tac | tr '\n' '\0')

echo "[INFO] Found ${#SORTED_FILES[@]} .h5ad files"

# 显示文件大小信息
declare -A FILE_SIZES
for file in "${SORTED_FILES[@]}"; do
    size_bytes=$(stat -c%s "$file" 2>/dev/null || echo 0)
    size_gb=$(echo "scale=1; $size_bytes / 1024 / 1024 / 1024" | bc -l 2>/dev/null || echo "0.0")
    FILE_SIZES["$file"]=$size_gb
    echo "  $(basename "$file"): ${size_gb}GB"
done

monitor_memory() {
    local interval=${1:-30}
    local log_dir="${LOG_DIR:-.}"
    mkdir -p "$log_dir"
    local log_file="${log_dir}/memory_monitor_$(date +%Y%m%d_%H%M%S).log"

    (
        # 独立的子进程：先重定向，再进入循环
        exec >>"$log_file" 2>&1
        while true; do
            mem_usage=$(free | awk '/^Mem:/ {printf "%.1f", $3/$2 * 100}')
            available_gb=$(free -g | awk '/^Mem:/ {print $7}')
            load_avg=$(awk '{print $(NF-2)}' /proc/loadavg)
            printf "[MONITOR %s] Memory: %s%% (%sGB free) Load: %s\n" \
                   "$(date '+%H:%M:%S')" "$mem_usage" "$available_gb" "$load_avg"

            # （可选）高内存告警；若你的系统没有 bc，可以去掉这段判断
            if command -v bc >/dev/null 2>&1; then
                if (( $(echo "$mem_usage > 90" | bc -l) )); then
                    echo "[WARNING] High memory usage: ${mem_usage}%"
                fi
            fi

            sleep "$interval"
        done
    ) &
    echo $!   # 只把子进程 PID 打印给命令替换
}


# 清理函数
cleanup() {
    local exit_code=$?
    echo "[INFO] Performing cleanup..."
    
    # 停止监控
    [[ -n "${MONITOR_PID:-}" ]] && kill $MONITOR_PID 2>/dev/null || true
    
    # 强制垃圾回收
    sync 2>/dev/null || true
    
    # 清理系统缓存（如果有权限）
    if [[ -w /proc/sys/vm/drop_caches ]]; then
        echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true
        echo "[INFO] Cleared system caches"
    fi
    
    # 显示最终内存状态
    local final_mem=$(free -g | awk '/^Mem:/ {print $7}')
    echo "[INFO] Final available memory: ${final_mem}GB"
    
    exit $exit_code
}

trap cleanup EXIT

# 启动内存监控
echo "[INFO] Starting memory monitoring..."
MONITOR_PID=$(monitor_memory 60)

# 检查Python环境
echo "[INFO] Checking Python environment..."
if ! "$PYTHON_BIN" -c "import anndata, scipy, pandas, numpy; print('Dependencies OK')" 2>/dev/null; then
    echo "[ERR] Required Python dependencies not found" >&2
    exit 1
fi

# 处理统计
success_count=0
fail_count=0
declare -a failed_files

# 日志文件
FAILED_LOG="${LOG_DIR}/failed_files_$(date +%Y%m%d_%H%M%S).log"
SUCCESS_LOG="${LOG_DIR}/success_files_$(date +%Y%m%d_%H%M%S).log"

echo "[INFO] Processing ${#SORTED_FILES[@]} files sequentially for memory safety"

# 逐个处理文件
for i in "${!SORTED_FILES[@]}"; do
    file="${SORTED_FILES[$i]}"
    size_gb=${FILE_SIZES["$file"]}
    
    echo ""
    echo "=============================================="
    echo "[PROCESSING $((i+1))/${#SORTED_FILES[@]}] $(basename "$file") (${size_gb}GB)"
    echo "=============================================="
    
    # 检查输出是否已存在
    output_dir="$(dirname "$file")/compass_cellload_processed"
    expected_output="$output_dir/$(basename "${file%.*}").cellload.filtered.h5ad"
    
    if [[ -f "$expected_output" ]] && [[ "$expected_output" -nt "$file" ]]; then
        echo "[SKIP] Output already exists and is newer: $(basename "$expected_output")"
        echo "$(basename "$file")" >> "$SUCCESS_LOG"
        ((success_count++))
        continue
    fi
    
    # 检查当前内存使用情况
    current_mem_usage=$(free | awk '/^Mem:/ {printf "%.0f", $3/$2 * 100}')
    available_gb=$(free -g | awk '/^Mem:/ {print $7}')
    
    echo "[INFO] Before processing - Memory usage: ${current_mem_usage}%, Available: ${available_gb}GB"
    
    # 如果内存使用过高，强制清理
    if [[ $current_mem_usage -gt 75 ]]; then
        echo "[WARN] High memory usage ($current_mem_usage%), performing aggressive cleanup..."
        sync 2>/dev/null || true
        if [[ -w /proc/sys/vm/drop_caches ]]; then
            echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true
        fi
        sleep 5
        
        # 重新检查
        current_mem_usage=$(free | awk '/^Mem:/ {printf "%.0f", $3/$2 * 100}')
        available_gb=$(free -g | awk '/^Mem:/ {print $7}')
        echo "[INFO] After cleanup - Memory usage: ${current_mem_usage}%, Available: ${available_gb}GB"
    fi
    
    # 如果可用内存不足，调整参数
    if [[ $available_gb -lt 200 ]]; then
        echo "[WARN] Low available memory (${available_gb}GB), using emergency settings"
        export LOAD_CHUNK_ROWS=20000
        export RUN_SLICE_ROWS=200000
        local_max_memory=50
    else
        local_max_memory=$MAX_MEMORY_GB
    fi
    
    # 根据文件大小设置超时
    if (( $(echo "$size_gb > 40" | bc -l) )); then
        timeout_minutes=2400  # 40小时对于超大文件
    elif (( $(echo "$size_gb > 20" | bc -l) )); then
        timeout_minutes=1200  # 20小时对于大文件
    elif (( $(echo "$size_gb > 5" | bc -l) )); then
        timeout_minutes=3000   # 5小时对于中等文件
    else
        timeout_minutes=120   # 2小时对于小文件
    fi
    
    timeout_seconds=$((timeout_minutes * 60))
    echo "[INFO] Processing with timeout: ${timeout_minutes} minutes (${timeout_seconds}s)"
    
    start_time=$(date +%s)
    
    # 使用修复后的Python脚本处理单个文件
    set +e  # 暂时禁用错误退出
    
    echo "[INFO] Starting processing..."
    if timeout $timeout_seconds "$PYTHON_BIN" "$SCRIPT_PATH" \
        --infile "$file" \
        --n-jobs 1 \
        --max-memory-gb "$local_max_memory" \
        "${COMMON_ARGS[@]}" > "${LOG_DIR}/$(basename "${file%.*}").log" 2>&1; then
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        duration_min=$((duration / 60))
        duration_sec=$((duration % 60))
        
        # 验证输出文件
        if [[ -f "$expected_output" ]] && [[ -s "$expected_output" ]]; then
            output_size=$(stat -c%s "$expected_output" 2>/dev/null || echo 0)
            output_mb=$((output_size / 1024 / 1024))
            
            echo "[SUCCESS] $(basename "$file") completed in ${duration_min}m${duration_sec}s"
            echo "[SUCCESS] Output file: ${output_mb}MB"
            echo "$(basename "$file") - ${duration_min}m${duration_sec}s - ${output_mb}MB - $(date)" >> "$SUCCESS_LOG"
            ((success_count++))
        else
            echo "[ERROR] $(basename "$file") reported success but output file missing/empty"
            echo "$(basename "$file") - NO_OUTPUT - $(date)" >> "$FAILED_LOG"
            failed_files+=("$(basename "$file")")
            ((fail_count++))
        fi
    else
        exit_code=$?
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        duration_min=$((duration / 60))
        
        if [[ $exit_code -eq 124 ]]; then
            echo "[ERROR] $(basename "$file") TIMEOUT after ${timeout_minutes} minutes"
            echo "$(basename "$file") - TIMEOUT_${timeout_minutes}m - $(date)" >> "$FAILED_LOG"
        else
            echo "[ERROR] $(basename "$file") failed (exit code: $exit_code) after ${duration_min} minutes"
            echo "$(basename "$file") - FAILED_EXIT_${exit_code} - $(date)" >> "$FAILED_LOG"
        fi
        
        failed_files+=("$(basename "$file")")
        ((fail_count++))
    fi
    set -e  # 重新启用错误退出
    
    # 处理后内存清理
    echo "[INFO] Post-processing cleanup..."
    sync 2>/dev/null || true
    if [[ -w /proc/sys/vm/drop_caches ]]; then
        echo 1 > /proc/sys/vm/drop_caches 2>/dev/null || true
    fi
    
    # 显示处理后内存状态
    final_mem_usage=$(free | awk '/^Mem:/ {printf "%.1f", $3/$2 * 100}')
    final_available=$(free -g | awk '/^Mem:/ {print $7}')
    echo "[INFO] After processing - Memory usage: ${final_mem_usage}%, Available: ${final_available}GB"
    
    # 如果不是最后一个文件，稍作等待让系统稳定
    if [[ $((i+1)) -lt ${#SORTED_FILES[@]} ]]; then
        echo "[INFO] Waiting 10 seconds before next file..."
        sleep 10
    fi
done

# 最终报告
echo ""
echo "=============================================="
echo "[SUMMARY] Processing completed"
echo "=============================================="
echo "  Input directory: ${INPUT_DIR}"
echo "  Total files: ${#SORTED_FILES[@]}"
echo "  Successful: $success_count"
echo "  Failed: $fail_count"
echo "  Success rate: $(( success_count * 100 / ${#SORTED_FILES[@]} ))%"
echo "=============================================="

# 检查输出文件
output_dir="${INPUT_DIR}/compass_cellload_processed"
if [[ -d "$output_dir" ]]; then
    output_count=$(find "$output_dir" -name "*.cellload.filtered.h5ad" | wc -l)
    echo "  Output files created: $output_count"
    
    if [[ $output_count -gt 0 ]]; then
        echo ""
        echo "Output files:"
        find "$output_dir" -name "*.cellload.filtered.h5ad" -exec ls -lh {} \; | head -10
        if [[ $output_count -gt 10 ]]; then
            echo "... and $((output_count - 10)) more files"
        fi
        
        # 计算总输出大小
        total_output_bytes=$(find "$output_dir" -name "*.cellload.filtered.h5ad" -exec stat -c%s {} \; | awk '{sum+=$1} END {print sum}')
        total_output_gb=$(echo "scale=1; $total_output_bytes / 1024 / 1024 / 1024" | bc -l 2>/dev/null || echo "0.0")
        echo "Total output size: ${total_output_gb}GB"
    fi
fi

# 日志文件信息
log_count=$(find "${LOG_DIR}" -name "*.log" | wc -l)
if [[ $log_count -gt 0 ]]; then
    echo ""
    echo "Log files created: $log_count in $LOG_DIR"
fi

# 显示失败文件信息
if [[ ${#failed_files[@]} -gt 0 ]]; then
    echo ""
    echo "=============================================="
    echo "[FAILED FILES] ${#failed_files[@]} files failed processing"
    echo "=============================================="
    for failed_file in "${failed_files[@]}"; do
        echo "  - $failed_file"
    done
    echo ""
    echo "Failed files logged to: $FAILED_LOG"
    echo ""
    echo "To retry failed files with even more conservative settings:"
    echo "  export LOAD_CHUNK_ROWS=10000"
    echo "  export RUN_SLICE_ROWS=100000"
    echo "  $PYTHON_BIN $SCRIPT_PATH --infile <failed_file.h5ad> --max-memory-gb 50 ${COMMON_ARGS[*]}"
    echo "=============================================="
else
    echo ""
    echo "=============================================="
    echo "All files processed successfully!"
    echo "=============================================="
fi

# 成功文件信息
if [[ -f "$SUCCESS_LOG" ]]; then
    echo "Successful files logged to: $SUCCESS_LOG"
fi

# 最终状态检查
final_mem_usage=$(free | awk '/^Mem:/ {printf "%.1f", $3/$2 * 100}')
final_available=$(free -g | awk '/^Mem:/ {print $7}')
echo ""
echo "[INFO] Final system state:"
echo "  Memory usage: ${final_mem_usage}%"
echo "  Available memory: ${final_available}GB"

echo ""
echo "=============================================="
echo "[COMPLETE] Batch processing finished"
echo "=============================================="
echo "Results available in: $output_dir"
echo "Total processing time: $(( $(date +%s) - ${START_TIME} )) seconds"
echo "Success rate: $success_count/${#SORTED_FILES[@]} ($(( success_count * 100 / ${#SORTED_FILES[@]} ))%)"
echo "=============================================="

# 退出码：如果有任何成功的文件，就返回0
if [[ $success_count -gt 0 ]]; then
    echo "[INFO] At least some files were processed successfully"
    exit 0
else
    echo "[ERROR] No files were processed successfully"
    exit 1
fi