#!/usr/bin/env python3
"""
最终测试修复后的代码是否能处理失败的文件
"""

import sys
from pathlib import Path
import cellload_auto

def final_test():
    """最终测试修复后的代码"""
    
    print("🎯 最终测试：SciPy 索引兼容性修复")
    print("=" * 60)
    
    # 失败的文件
    input_file = Path("/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Replogle/K562_gwps_raw_singlecell_01.aligned.h5ad")
    output_dir = Path("/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Replogle/final_test_output")
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    
    print(f"📁 输入文件: {input_file.name}")
    print(f"📁 输出目录: {output_dir}")
    print(f"📊 文件大小: {input_file.stat().st_size / (1024**3):.1f} GB")
    
    print(f"\n🔧 应用的修复:")
    print(f"1. 智能分块大小调整（基于数据密度）")
    print(f"2. 增强的错误处理和重试机制")
    print(f"3. SciPy 非连续索引兼容性修复 ← 关键修复")
    
    try:
        print(f"\n🚀 开始处理...")
        print(f"注意观察新的索引策略和错误处理")
        print("-" * 60)
        
        result = cellload_auto.process_one_file_optimized(
            infile=input_file,
            outdir=output_dir,
            perturbation_column="gene",
            control_label="non-targeting",
            residual_expression=0.30,
            cell_residual_expression=0.50,
            min_cells=30,
            layer=None,
            aggregate_transcripts=False,
            gene_name_col="gene_name",
            chr_col="chr",
            start_col="start",
            end_col="end",
            min_overlap_frac=0.5,
            dataset_name="K562_gwps_final_test",
            norm_tol_abs=1.0,
            max_memory_gb=100.0,
            skip_normalization=False,
        )
        
        print("-" * 60)
        if result == 0:
            print("🎉 最终修复成功！")
            print("✅ 文件处理完成")
            print("✅ SciPy 索引兼容性问题已解决")
            return True
        else:
            print(f"⚠️ 处理返回非零码: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    print("🔧 SciPy 索引兼容性问题最终修复测试")
    print("=" * 60)
    
    success = final_test()
    
    if success:
        print(f"\n🎉 修复验证成功！")
        print(f"问题已完全解决：")
        print(f"✅ 高密度数据文件现在可以正常处理")
        print(f"✅ SciPy 非连续索引兼容性问题已修复")
        print(f"✅ 批处理脚本现在应该能成功处理所有文件")
    else:
        print(f"\n❌ 修复验证失败！")
        print(f"需要进一步调查问题。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
