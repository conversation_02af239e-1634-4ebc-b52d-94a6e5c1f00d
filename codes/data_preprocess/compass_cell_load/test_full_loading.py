#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整数据集加载策略的脚本（简化版，不依赖anndata）
"""

import os
import sys
import psutil
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_memory_check():
    """测试内存检查功能"""
    logger.info("=== 测试内存检查功能 ===")
    
    # 测试不同文件大小的内存需求
    test_sizes = [1.0, 5.0, 10.0, 20.0, 50.0]  # GB
    
    for size_gb in test_sizes:
        memory_ok, memory_msg = check_memory_requirements(size_gb)
        logger.info(f"文件大小 {size_gb}GB: {'通过' if memory_ok else '失败'} - {memory_msg}")

def test_environment_setup():
    """测试环境设置"""
    logger.info("=== 测试环境设置 ===")
    
    logger.info("设置前的环境变量:")
    for var in ["OMP_NUM_THREADS", "MKL_NUM_THREADS", "MAX_SPARSE_INDEX_SIZE"]:
        logger.info(f"  {var}: {os.environ.get(var, '未设置')}")
    
    setup_memory_efficient_environment()
    
    logger.info("设置后的环境变量:")
    for var in ["OMP_NUM_THREADS", "MKL_NUM_THREADS", "MAX_SPARSE_INDEX_SIZE"]:
        logger.info(f"  {var}: {os.environ.get(var, '未设置')}")

def test_system_info():
    """显示系统信息"""
    logger.info("=== 系统内存信息 ===")
    
    memory = psutil.virtual_memory()
    logger.info(f"总内存: {memory.total / (1024**3):.1f}GB")
    logger.info(f"可用内存: {memory.available / (1024**3):.1f}GB")
    logger.info(f"已用内存: {memory.used / (1024**3):.1f}GB")
    logger.info(f"内存使用率: {memory.percent:.1f}%")
    
    current_process_memory = monitor_memory()
    logger.info(f"当前进程内存使用: {current_process_memory:.2f}GB")

def main():
    """主测试函数"""
    logger.info("开始测试完整数据集加载策略...")
    
    test_system_info()
    test_environment_setup()
    test_memory_check()
    
    logger.info("测试完成!")

if __name__ == "__main__":
    main()
