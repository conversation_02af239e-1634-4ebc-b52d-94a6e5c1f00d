#!/usr/bin/env python3
"""
检查数据文件结构和列名
"""

import scanpy as sc
import pandas as pd
from pathlib import Path

def check_data_files():
    """检查可用的数据文件和结构"""
    
    print("检查数据文件结构...")
    
    # 可能的数据文件路径
    possible_paths = [
        '/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/processed_data/',
        '/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/',
        '/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p/Jiang/',
        '/data/vcc/compass_loaded/colab_like/',
    ]
    
    found_files = []
    
    for base_path in possible_paths:
        path = Path(base_path)
        if path.exists():
            print(f"\n检查目录: {path}")
            h5ad_files = list(path.glob("*.h5ad"))
            for file in h5ad_files:
                print(f"  找到文件: {file.name}")
                found_files.append(str(file))
    
    # 检查前几个文件的结构
    print(f"\n检查文件结构...")
    
    for i, file_path in enumerate(found_files[:3]):  # 只检查前3个文件
        print(f"\n文件 {i+1}: {Path(file_path).name}")
        try:
            adata = sc.read_h5ad(file_path)
            print(f"  维度: {adata.n_obs} × {adata.n_vars}")
            print(f"  obs 列: {list(adata.obs.columns)}")
            
            # 查找可能的扰动列
            possible_pert_cols = []
            for col in adata.obs.columns:
                if any(keyword in col.lower() for keyword in ['gene', 'pert', 'target', 'guide']):
                    possible_pert_cols.append(col)
            
            print(f"  可能的扰动列: {possible_pert_cols}")
            
            # 如果找到扰动列，显示一些值
            if possible_pert_cols:
                pert_col = possible_pert_cols[0]
                unique_values = adata.obs[pert_col].value_counts().head(10)
                print(f"  {pert_col} 的前10个值:")
                for val, count in unique_values.items():
                    print(f"    {val}: {count}")
            
        except Exception as e:
            print(f"  读取失败: {e}")
    
    return found_files

def main():
    found_files = check_data_files()
    
    print(f"\n总结:")
    print(f"找到 {len(found_files)} 个 h5ad 文件")
    
    if len(found_files) >= 3:
        print(f"\n建议使用的文件:")
        for i, file in enumerate(found_files[:3]):
            print(f"  数据集{i+1}: {file}")

if __name__ == "__main__":
    main()
