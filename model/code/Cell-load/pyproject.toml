[project]
name = "cell-load"
version = "0.7.7"
description = "Dataloaders for training models on huge single-cell datasets"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "No<PERSON>yssier", email = "<EMAIL>" },
    { name = "Rishi Verma", email = "<EMAIL>" },
]
requires-python = ">=3.10,<3.13"
dependencies = ["torch>=1.13.0", "anndata>=0.9.1", "lightning>=2.0.0", "toml>=0.10.2"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = ["pytest>=8.3.5", "ruff>=0.11.8"]

[project.scripts]
run_eval = "cell_load.__main__:main"

[tool.pyright]
venvPath = "."
venv = ".venv"
