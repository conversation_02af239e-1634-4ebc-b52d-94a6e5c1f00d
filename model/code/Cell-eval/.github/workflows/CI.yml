name: "CI"

on: [push, pull_request]

jobs:
  all_jobs:
    runs-on: ubuntu-latest
    needs: [formatting, pytest, cli-test]
    steps:
      - name: Complete
        run: echo "Complete"

  install-job:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "pyproject.toml"
          python-version: "3.12"

      - name: install dependencies
        run: |
          uv sync --all-extras --dev

  formatting:
    runs-on: ubuntu-latest

    needs: [install-job]

    steps:
      - uses: actions/checkout@v4

      - name: install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "pyproject.toml"
          python-version: "3.12"

      - name: install dependencies
        run: |
          uv sync --all-extras --dev

      - name: run formatting
        run: |
          uv run ruff format --check

  pytest:
    runs-on: ubuntu-latest

    needs: [install-job]

    steps:
      - uses: actions/checkout@v4

      - name: install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "pyproject.toml"
          python-version: "3.12"

      - name: install dependencies
        run: |
          uv sync --all-extras --dev

      - name: run pytest
        run: |
          uv run pytest -v

  cli-test:
    runs-on: ubuntu-latest

    needs: [install-job]

    steps:
      - uses: actions/checkout@v4

      - name: install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "pyproject.toml"
          python-version: "3.12"

      - name: install dependencies
        run: |
          uv sync --all-extras --dev

      - name: run cli tests
        run: |
          uv run cell-eval --help
