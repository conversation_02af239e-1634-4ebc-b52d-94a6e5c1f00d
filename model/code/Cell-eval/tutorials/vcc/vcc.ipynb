{"cells": [{"cell_type": "markdown", "id": "837a8ecd", "metadata": {}, "source": ["# VCC Submission Notebook\n", "\n", "Hello! \n", "\n", "This is a notebook that will help you prepare your predicted AnnData to be ready to be scored by `cell-eval` against a validation dataset.\n", "\n", "Before we begin you will need a few things:\n", "\n", "1. `cell-eval` installed and in your `$PATH` (see our [installation guide](https://github.com/ArcInstitute/cell-eval?tab=readme-ov-file#installation))\n", "2. The number of expected cells / perturbation in the validation dataset (CSV) ([download](https://virtualcellchallenge.org/app))\n", "3. The gene names to predict (CSV) ([download](https://virtualcellchallenge.org/app))\n", "4. Your model predictions in an AnnData (h5ad)\n", "5. (Optional) The training AnnData (if you are not predicting Non-Targeting Controls) ([download](https://virtualcellchallenge.org/app))\n", "\n", "\n", "> Note: Your model predictions **may not exceed 100K cells total**"]}, {"cell_type": "markdown", "id": "b5cc204d", "metadata": {}, "source": ["## Building an Example Submission\n", "\n", "For the purposes of this tutorial we will be generating **random predictions** and preparing them to be evaluated.\n", "\n", "We will create an AnnData with *random gene abundances* for each cell, where the number of cells for each perturbation matches the number of cells in the validation dataset."]}, {"cell_type": "markdown", "id": "3e9c543f", "metadata": {}, "source": ["### Load in our Expected Counts"]}, {"cell_type": "code", "execution_count": 1, "id": "2d172eea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dimensions: (50, 3)\n"]}, {"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (5, 3)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>target_gene</th><th>n_cells</th><th>median_umi_per_cell</th></tr><tr><td>str</td><td>i64</td><td>f64</td></tr></thead><tbody><tr><td>&quot;SH3BP4&quot;</td><td>2925</td><td>54551.0</td></tr><tr><td>&quot;ZNF581&quot;</td><td>2502</td><td>53803.5</td></tr><tr><td>&quot;ANXA6&quot;</td><td>2496</td><td>55175.0</td></tr><tr><td>&quot;PACSIN3&quot;</td><td>2101</td><td>54088.0</td></tr><tr><td>&quot;MGST1&quot;</td><td>2096</td><td>54217.5</td></tr></tbody></table></div>"], "text/plain": ["shape: (5, 3)\n", "┌─────────────┬─────────┬─────────────────────┐\n", "│ target_gene ┆ n_cells ┆ median_umi_per_cell │\n", "│ ---         ┆ ---     ┆ ---                 │\n", "│ str         ┆ i64     ┆ f64                 │\n", "╞═════════════╪═════════╪═════════════════════╡\n", "│ SH3BP4      ┆ 2925    ┆ 54551.0             │\n", "│ ZNF581      ┆ 2502    ┆ 53803.5             │\n", "│ ANXA6       ┆ 2496    ┆ 55175.0             │\n", "│ PACSIN3     ┆ 2101    ┆ 54088.0             │\n", "│ MGST1       ┆ 2096    ┆ 54217.5             │\n", "└─────────────┴─────────┴─────────────────────┘"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import polars as pl\n", "\n", "# Define our path\n", "pert_counts_path = \"./pert_counts_Validation.csv\"\n", "\n", "# Read in the csv\n", "pert_counts = pl.read_csv(pert_counts_path)\n", "\n", "# Show the dimensions\n", "print(f\"Dimensions: {pert_counts.shape}\")\n", "pert_counts.head()"]}, {"cell_type": "markdown", "id": "34164d85", "metadata": {}, "source": ["### Load in our Expected Gene Names"]}, {"cell_type": "code", "execution_count": 2, "id": "04e3cfad", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['SAMD11', 'NOC2L', 'KLHL17', ..., 'MT-ND5', 'MT-ND6', 'MT-CYB'],\n", "      shape=(18080,), dtype=object)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["gene_names_path = \"./gene_names.csv\"\n", "\n", "# Read this in and immediately convert to array\n", "gene_names = pl.read_csv(gene_names_path, has_header=False).to_numpy().flatten()\n", "\n", "gene_names"]}, {"cell_type": "markdown", "id": "b6895f5b", "metadata": {}, "source": ["### Define our random predictor"]}, {"cell_type": "code", "execution_count": 3, "id": "0262449f", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from numpy.typing import NDArray\n", "import anndata as ad\n", "\n", "\n", "def random_predictor(\n", "    pert_names: NDArray[np.str_],\n", "    cell_counts: NDArray[np.int64],\n", "    gene_names: NDArray[np.str_],\n", "    max_count: int | float = 1e4,\n", "    log1p: bool = True,\n", ") -> ad.<PERSON>:\n", "    \"\"\"Generate a random AnnData with the expected number of cells / perturbation.\n", "\n", "    This is a dummy function that is meant to stand-in for a perturbation model.\n", "    \"\"\"\n", "    matrix = np.random.randint(\n", "        0,\n", "        int(max_count),\n", "        size=(\n", "            cell_counts.sum(),\n", "            gene_names.size,\n", "        ),\n", "    )\n", "    if log1p:\n", "        matrix = np.log1p(matrix)\n", "    return ad.<PERSON>(\n", "        X=matrix,\n", "        obs=pd.DataFrame(\n", "            {\n", "                \"target_gene\": np.repeat(pert_names, cell_counts),\n", "            },\n", "            index=np.arange(cell_counts.sum()).astype(str),\n", "        ),\n", "        var=pd.DataFrame(index=gene_names),\n", "    )"]}, {"cell_type": "markdown", "id": "d5dbd56d", "metadata": {}, "source": ["### Run our random predictor"]}, {"cell_type": "code", "execution_count": 4, "id": "1273bea1", "metadata": {}, "outputs": [{"data": {"text/plain": ["AnnData object with n_obs × n_vars = 60751 × 18080\n", "    obs: 'target_gene'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["adata = random_predictor(\n", "    pert_names=pert_counts[\"target_gene\"].to_numpy(),\n", "    cell_counts=pert_counts[\"n_cells\"].to_numpy(),\n", "    gene_names=gene_names,\n", ")\n", "adata"]}, {"cell_type": "markdown", "id": "9f9de9dd", "metadata": {}, "source": ["### Adding in Non-Targeting Controls if you are not predicting them\n", "\n", "Our evaluation framework expects non-targeting controls to be included in the predicted AnnData, but not all models may try to predict non-targeting controls.\n", "If you are not predicting non-targeting controls, you can take the non-targeting from the training AnnData and just copy them over into your predicted AnnData for validation."]}, {"cell_type": "code", "execution_count": 5, "id": "3879267c", "metadata": {}, "outputs": [], "source": ["# Define our path to the training anndata\n", "tr_adata_path = \"./adata_Training.h5ad\"\n", "\n", "# Read in the anndata\n", "tr_adata = ad.read_h5ad(tr_adata_path)\n", "\n", "# Filter for non-targeting\n", "ntc_adata = tr_adata[tr_adata.obs[\"target_gene\"] == \"non-targeting\"]\n", "\n", "# Append the non-targeting controls to the example annda<PERSON> if they're missing\n", "if \"non-targeting\" not in adata.obs[\"target_gene\"].unique():\n", "    assert np.all(adata.var_names.values == ntc_adata.var_names.values), (\n", "        \"Gene-Names are out of order or unequal\"\n", "    )\n", "    adata = ad.concat(\n", "        [\n", "            adata,\n", "            ntc_adata,\n", "        ]\n", "    )"]}, {"cell_type": "markdown", "id": "6f19ac72", "metadata": {}, "source": ["### Write our predictions to some output path"]}, {"cell_type": "code", "execution_count": 6, "id": "386ee994", "metadata": {}, "outputs": [], "source": ["adata.write_h5ad(\"./example.h5ad\")"]}, {"cell_type": "markdown", "id": "29cd57ef", "metadata": {}, "source": ["## Running `cell-eval prep`\n", "\n", "Now that we have our predictions, we will run `cell-eval` to prepare our AnnData for competition scoring."]}, {"cell_type": "markdown", "id": "7716a83e", "metadata": {}, "source": ["```bash\n", "cell-eval prep \\\n", "    -i ./example.h5ad \\\n", "    --genes ./gene_names.csv\n", "```"]}, {"cell_type": "markdown", "id": "6f938a60", "metadata": {}, "source": ["And that's it! Your model outputs will be output to path: `./example.prep.vcc` are ready for scoring."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}