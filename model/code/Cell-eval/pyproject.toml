[project]
name = "cell-eval"
version = "0.5.42"
description = "Evaluation metrics for single-cell perturbation predictions"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON> Teyssier", email = "<EMAIL>" },
    { name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
requires-python = ">=3.10,<3.13"
dependencies = [
    "igraph>=0.11.8",
    "pdex>=0.1.20",
    "polars>=1.30.0",
    "pyyaml>=6.0.2",
    "scanpy>=1.10.3",
    "pyarrow>=18.0.0",
    "tqdm>=4.67.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = ["ipykernel>=6.29.5", "pytest>=8.3.5", "ruff>=0.11.8"]

[project.scripts]
cell-eval = "cell_eval.__main__:main"

[tool.pyright]
venvPath = "."
venv = ".venv"
