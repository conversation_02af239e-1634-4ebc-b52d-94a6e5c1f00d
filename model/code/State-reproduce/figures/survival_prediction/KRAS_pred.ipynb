{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"cpu\""]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n", "import pandas as pd\n", "import seaborn as sns\n", "import numpy as np\n", "import scipy\n", "import jax.numpy as jnp\n", "import xgboost as xgb\n", "import jax\n", "from sklearn.decomposition import PCA\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import anndata"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def get_pert_vectors(adata):\n", "    # Extract metadata and group labels\n", "    meta_df = adata.obs[['cell_name', 'drugname_drugconc']].copy()\n", "    meta_df['group'] = meta_df['cell_name'].astype(str) + '|' + meta_df['drugname_drugconc'].astype(str)\n", "    # Map groups to integer indices\n", "    group_idx, unique_groups = pd.factorize(meta_df['group'])\n", "    # Then run your code\n", "    # Extract metadata and group labels\n", "    meta_df = adata.obs[['cell_name', 'drugname_drugconc']].copy()\n", "    meta_df['group'] = meta_df['cell_name'].astype(str) + '|' + meta_df['drugname_drugconc'].astype(str)\n", "    adata.obs['group'] = adata.obs['cell_name'].astype(str) + '|' + adata.obs['drugname_drugconc'].astype(str)\n", "    print('here')\n", "    # Map groups to integer indices\n", "    group_idx, unique_groups = pd.factorize(meta_df['group'])\n", "    # X_jax: (n_cells, n_dims)\n", "    # group_idx: int array of length n_cells (e.g., from pd.factorize)\n", "    X_jax = jnp.array(adata.X.astype('float32'))\n", "    print('here')\n", "    group_idx = jnp.array(group_idx)\n", "    n_groups = len(unique_groups)\n", "    sums = jax.ops.segment_sum(X_jax, group_idx, n_groups)\n", "    counts = jax.ops.segment_sum(jnp.ones((X_jax.shape[0], 1)), group_idx, n_groups)\n", "    centroids = sums / counts\n", "    # Back to Pandas to identify DMSO indices\n", "    group_labels = pd.Series(unique_groups)\n", "    cell_types = group_labels.str.split('|').str[0]\n", "    perts = group_labels.str.split('|').str[1]\n", "    # Find the index of each DMSO centroid by cell_type\n", "    dmso_idx = (perts == \"[('DMSO_TF', 0.0, 'uM')]\")\n", "    # Convert boolean mask to indices for JAX\n", "    dmso_indices = np.where(dmso_idx.values)[0]  # Convert pandas boolean to JAX indices\n", "    dmso_centroids = centroids[dmso_indices]\n", "    # Match non-DMSO centroids to their corresponding DMSO by cell_type\n", "    pert_indices = np.where(~dmso_idx.values)[0]  # Also convert this to JAX indices\n", "    pert_cell_types = cell_types[~dmso_idx].values\n", "    # Build a lookup: cell_type → index in dmso_centroids\n", "    dmso_cell_types = cell_types[dmso_idx].values\n", "    dmso_cell_type_to_idx = dict(zip(dmso_cell_types, dmso_indices))\n", "    # Get DMSO centroid for each perturbation group\n", "    dmso_for_pert = np.stack([\n", "        centroids[dmso_cell_type_to_idx[ct]] for ct in pert_cell_types\n", "    ])\n", "    # Compute perturbation vectors\n", "    pert_vectors = centroids[pert_indices] - dmso_for_pert\n", "    reference_df = pd.DataFrame({\n", "    'variable': cell_types,\n", "    'drug': perts,\n", "    'order': range(len(cell_types))\n", "    })\n", "    return pert_vectors, reference_df"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["adata_real = sc.read_h5ad(\"/scratch/goodarzilab/cachris/adata_KRAS_pred_real.h5ad\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["here\n", "here\n"]}], "source": ["real_vectors, ref = get_pert_vectors(adata_real)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["del adata_real"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["survival = pd.read_csv('/large_storage/ctc/public/tahoe/survival_data/log2fc_survivals_averaged_across_plate.csv')\n", "survival = survival.melt(id_vars= ['drug'])\n", "survival['drug'] = survival['drug'].apply(\n", "    lambda x: f\"[('{x.split('_')[0]}', {x.split('_')[1]}, 'uM')]\"\n", ")\n", "# Create combined reference\n", "\n", "ref = ref[ref['drug'] != \"[('DMSO_TF', 0.0, 'uM')]\"]\n", "# Merge to get the order, then sort\n", "survival = survival.merge(ref, on=['variable', 'drug'], how='right')\n", "survival = survival.sort_values('order').drop('order', axis=1)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["pert_vectors = np.asarray(real_vectors)\n", "# Get the mask from the original survival dataframe\n", "valid_mask = ~survival['value'].isna()\n", "x = pert_vectors[valid_mask.values, :]\n", "y = survival['value'][valid_mask].values\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SVR Regression Metrics:\n", "R² Score: -0.3214\n", "Mean Squared Error: 0.0141\n", "Root Mean Squared Error: 0.1188\n", "Mean Absolute Error: 0.0994\n", "<PERSON> r  = 0.5548\n", "Pearson r² = 0.3078\n", "p-value    = 2.5316e-01\n"]}], "source": ["from sklearn.svm import SVR\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "from sklearn.preprocessing import StandardScaler\n", "import numpy as np\n", "valid_mask = ~survival['value'].isna()\n", "# Apply the same mask to both\n", "x = pert_vectors[valid_mask.values, :]\n", "y = survival['value'][valid_mask].values\n", "train_mask =  ~(survival['drug'].str.contains('grasib')) & ~(survival['value'].isna())\n", "X_train = pert_vectors[train_mask, :]\n", "y_train = survival['value'][train_mask].values\n", "test_mask = (survival['drug'].str.contains(\"grasib\")) & ~(survival['value'].isna())\n", "X_test = pert_vectors[test_mask, :]\n", "y_test = survival['value'][test_mask].values\n", "# Scale the features (important for SVM)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "\n", "# Scale the features (important for SVM)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Fit SVM\n", "svm = SVR(kernel='rbf')  # or 'rbf', 'poly', etc.\n", "svm.fit(X_train_scaled, y_train)\n", "\n", "# Make predictions\n", "y_pred = svm.predict(X_test_scaled)\n", "\n", "# Regression metrics\n", "print(\"SVR Regression Metrics:\")\n", "print(f\"R² Score: {r2_score(y_test, y_pred):.4f}\")\n", "print(f\"Mean Squared Error: {mean_squared_error(y_test, y_pred):.4f}\")\n", "print(f\"Root Mean Squared Error: {np.sqrt(mean_squared_error(y_test, y_pred)):.4f}\")\n", "print(f\"Mean Absolute Error: {mean_absolute_error(y_test, y_pred):.4f}\")\n", "from scipy.stats import pearsonr\n", "from scipy.stats import spearmanr\n", "from scipy.stats import pearsonr\n", "\n", "r, p = pearsonr(y_test, y_pred)\n", "\n", "print(f\"Pearson r  = {r:.4f}\")\n", "print(f\"Pearson r² = {r**2:.4f}\")\n", "print(f\"p-value    = {p:.4e}\")\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.scatterplot(x=y_test, y=y_pred)\n", "plt.xlabel('Actual Survival Values')\n", "plt.ylabel('Predicted Survival Values')\n", "plt.title('Actual vs Predicted Survival Values SVM')\n", "\n", "# Add a diagonal line for perfect predictions\n", "min_val = min(y_test.min(), y_pred.min())\n", "max_val = max(y_test.max(), y_pred.max())\n", "plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Perfect Prediction')\n", "plt.legend()\n", "\n", "# Optional: Add R² score as text on the plot\n", "r2 = r2_score(y_test, y_pred)\n", "plt.text(0.05, 0.95, f'pearson 3 = {r:.3f}, pearson p = {p:.4e}', transform=plt.gca().transAxes, \n", "        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["XgB Regression Metrics:\n", "R² Score: -1.3209\n", "Mean Squared Error: 0.0248\n", "Root Mean Squared Error: 0.1575\n", "Mean Absolute Error: 0.1294\n"]}], "source": ["xg = xgb.XGBRegressor()\n", "xg.fit(X_train_scaled, y_train)\n", "xg_pred = xg.predict(X_test_scaled)\n", "print(\"XgB Regression Metrics:\")\n", "print(f\"R² Score: {r2_score(y_test, xg_pred):.4f}\")\n", "print(f\"Mean Squared Error: {mean_squared_error(y_test, xg_pred):.4f}\")\n", "print(f\"Root Mean Squared Error: {np.sqrt(mean_squared_error(y_test, xg_pred)):.4f}\")\n", "print(f\"Mean Absolute Error: {mean_absolute_error(y_test, xg_pred):.4f}\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnYAAAHWCAYAAAD6oMSKAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAprZJREFUeJzs3Xd4FFUXwOHfphfIQkgPIaGEJr2Xj6YI0nsRCNWgIiK9qDQbighIE0SaFLGACohBepHQQUC60iGQUJJQUvd+f4xZWFLIQpJNOe/z5GHn7p2Zc7ckh7lldEophRBCCCGEyPGsLB2AEEIIIYTIGJLYCSGEEELkEpLYCSGEEELkEpLYCSGEEELkEpLYCSGEEELkEpLYCSGEEELkEpLYCSGEEELkEpLYCSGEEELkEpLYCSGEEELkEpLYiRxhxowZ6HQ6ypUr98zHuHbtGhMmTODIkSMZF1gaGjZsSMOGDbPkXGkJCAhAp9MZf/Lly0fNmjX59ttvs+T8ixcvRqfTceHCBWPZs742n3zyCb/88kuGxZbkwoUL6HQ6Fi9e/NS6J0+eJCgoiGLFiuHg4ICbmxtVqlRh4MCBREVFZXhs6ZHSa5zRJkyYgE6nS7NO5cqV8fX1JTExMdU6devWxc3Njbi4uHSd15z3JqvFxsbywgsvEBgYyIMHD5I936xZMwoUKMCVK1dMys+fP8+gQYMoU6YMzs7OODg4EBAQQI8ePdi6dSuP3xAq6b19/Mfd3Z2GDRuybt26TG/j0zx48IAJEyawbds2S4ci/iOJncgRFi5cCMDff//N3r17n+kY165dY+LEiVmW2GUndevWJTQ0lNDQUOMfil69evHVV19ZJJ45c+YwZ84cs/fLrMQuvQ4fPkzVqlU5ceIE48aNIyQkhLlz59KiRQs2bNjA7du3LRJXixYtCA0Nxdvb2yLnT9KvXz+uXbvGhg0bUnz+zJkz7N69m6CgIOzs7LI4uoxnb2/PkiVLuHDhAqNGjTJ5bt68eYSEhPDll19SuHBhY/maNWsoX748a9asoVevXvz8889s2LCBsWPHcuvWLV588UW2bNmS7FyLFi0iNDSU3bt38/XXX2NtbU2rVq1Yu3ZtprczLQ8ePGDixImS2GUnSohsbv/+/QpQLVq0UIAKDg5+ruMsWrQoYwNMRYMGDVSDBg2y5Fxp8ff3Vy1atDApu3PnjnJxcVElSpRIdb+EhAQVExPz3OdftGiRAtT58+ef+1jOzs6qV69ez32cJ50/fz5dn42ePXsqZ2dnFRUVleLzBoMhQ+KJi4tT8fHxGXKsjDJ+/Hj1tD8Zt2/fVg4ODqpDhw4pPj9q1CgFqKNHj6b7vOl9byzp/fffVzqdTm3evFkppdQ///yj8uXLp1q1amVS79y5c8rJyUlVr15dRUZGpnisrVu3qiNHjhi3k74/+/fvN6n34MEDZW9vr1599dUMbo15wsPDFaDGjx9v0TjEI3LFTmR7CxYsAODTTz+lTp06rFy5MsVuj6tXr9K/f3/8/Pyws7PDx8eHjh07cuPGDbZt20b16tUB6NOnj7FLY8KECUDqXYO9e/cmICDApGzixInUrFkTV1dXXFxcqFKlCgsWLDDpPkmvtm3b4u/vj8FgSPZczZo1qVKlinH7xx9/pGbNmuj1epycnChWrBh9+/Y1+5wABQoUoFSpUly8eBF41N01efJkPvroI4oWLYq9vT1bt24F4MCBA7Ru3RpXV1ccHByoXLkyP/zwQ7Lj7tmzh7p16+Lg4ICPjw9jxowhPj4+Wb2UXu/Y2Fg++OADypQpg4ODA4UKFaJRo0bs3r0bAJ1Ox/3791myZInx/Xv8GGFhYbz++usULlwYOzs7ihYtysSJE0lISDA5z7Vr1+jcuTP58+dHr9fTpUsXwsLC0vW63bp1CxcXF/Lly5fi8493VQYEBNC7d++ntn3btm3odDqWLl3KsGHD8PX1xd7enr///hudTmf8/D/u999/R6fTsWbNGiB5V+zgwYNxdnZOsWu4S5cueHp6Gt+X77//niZNmuDt7Y2joyNlypRh9OjR3L9/P12vyeMKFixIu3btWLt2Lbdu3TJ5LjExkaVLl1K9enXKly/PuXPn6NOnD4GBgTg5OeHr60urVq04duzYU8+T0vcSUu4uVkoxZ84cKlWqhKOjIwULFqRjx478+++/JvUOHz5My5Yt8fDwwN7eHh8fH1q0aJGsGzUl48aNo0KFCvTt25e7d+/Su3dv7O3t+frrr03qTZ06lQcPHjBnzhxcXFxSPFbDhg2pWLHiU8/p4OCAnZ0dtra2JuW3b99mwIAB+Pr6YmdnR7FixXjvvfeIjY01qRcTE8OYMWMoWrQodnZ2+Pr68tZbb3H37l2Telu2bKFhw4YUKlQIR0dHihQpQocOHXjw4AEXLlzA3d0d0H4vJn0vU/rci6wjiZ3I1h4+fMh3331H9erVKVeuHH379iU6Opoff/zRpN7Vq1epXr06P//8M0OHDuX3339n+vTp6PV67ty5Q5UqVVi0aBEA77//vrFb8rXXXjM7pgsXLvD666/zww8/sHr1atq3b8/bb7/Nhx9+aPax+vbty6VLl5J1vZw6dYp9+/bRp08fAEJDQ+nSpQvFihVj5cqV/Pbbb4wbNy5Z0pJe8fHxXLx40fhLOcmMGTPYsmULU6ZM4ffff6d06dJs3bqVunXrcvfuXebOncuvv/5KpUqV6NKli8m4pxMnTvDSSy9x9+5dFi9ezNy5czl8+DAfffTRU+NJSEigWbNmfPjhh7Rs2ZKff/6ZxYsXU6dOHS5dumR8DRwdHWnevLnx/Uvqzg0LC6NGjRps2LCBcePG8fvvv9OvXz8mTZpEcHCw8TwPHz6kcePG/PHHH0yaNIkff/wRLy8vunTpkq7XrXbt2ly/fp3u3buzfft2Hj58mK790mPMmDFcunSJuXPnsnbtWvz8/KhcubLxc/u4xYsX4+HhQfPmzVM8Vt++fXnw4EGy5Pvu3bv8+uuv9OjRw5gQnD17lubNm7NgwQJCQkIYPHgwP/zwA61atXqmdvTr14+4uDiWLVtmUr5hwwauXbtGv379AC3BLlSoEJ9++ikhISHMnj0bGxsbatasyenTp5/p3Cl5/fXXGTx4MI0bN+aXX35hzpw5/P3339SpU4cbN24AcP/+fV5++WVu3LjB7Nmz2bhxI9OnT6dIkSJER0c/9Ry2trYsWbKEa9euUa1aNXbu3Mns2bPx8vIyqbdx40a8vb2pVq2a2e1ITEwkISGB+Ph4rly5wuDBg7l//z7dunUz1omJiaFRo0Z8++23DB06lN9++40ePXowefJk2rdvb6ynlKJt27ZMmTKFoKAgfvvtN4YOHcqSJUt48cUXjUnghQsXaNGiBXZ2dixcuJCQkBA+/fRTnJ2diYuLw9vbm5CQEEB735O+l2PHjjW7fSIDWfiKoRBp+vbbbxWg5s6dq5RSKjo6WuXLl0/Vq1fPpF7fvn2Vra2tOnHiRKrHSqsrNrVu0169eil/f/9Uj5mYmKji4+PVBx98oAoVKmTSFZeertj4+Hjl6empunXrZlI+cuRIZWdnpyIiIpRSSk2ZMkUB6u7du2keLyX+/v6qefPmKj4+XsXHx6vz58+rXr16KUCNGDFCKfWou6t48eIqLi7OZP/SpUurypUrJ+sabNmypfL29laJiYlKKaW6dOmiHB0dVVhYmLFOQkKCKl26dLKu2Cdfm6T3ef78+Wm2JbWu2Ndff13ly5dPXbx40aQ86XX7+++/lVJKffXVVwpQv/76q0m94ODgdHX3xcTEqLZt2ypAAcra2lpVrlxZvffee+rmzZsmdf39/VOM9cm2b926VQGqfv36yerOmDFDAer06dPGstu3byt7e3s1bNgwY1lK3d1VqlRRderUMTnenDlzFKCOHTuWYvsMBoOKj49X27dvV4D666+/jM+lpys26RhFixZVFSpUMCnv0KGDcnJySrULMiEhQcXFxanAwEA1ZMgQY3lKXbGpfS+fjDE0NFQB6osvvjCpd/nyZeXo6KhGjhyplFLqwIEDClC//PLLU9uXlv79+ytAtWzZMsXnHRwcVK1atZKVJ/0eSfpJ+k4p9ei9ffLH3t5ezZkzx+Q4c+fOVYD64YcfTMo/++wzBag//vhDKaVUSEiIAtTkyZNN6n3//fcKUF9//bVSSqmffvpJASZdw0+SrtjsR67YiWxtwYIFODo60rVrVwDy5ctHp06d2LlzJ2fPnjXW+/3332nUqBFlypTJ9Ji2bNlC48aN0ev1WFtbY2try7hx47h16xY3b94061g2Njb06NGD1atXExkZCTzqsmrTpg2FChUCMHYjd+7cmR9++IGrV6+adZ7169dja2uLra0tRYsW5YcffuDtt99OdjWtdevWJl07586d49SpU3Tv3h3Qrqwl/TRv3pzr168br65s3bqVl156CU9PT+P+1tbW6boa9vvvv+Pg4PDMXcvr1q2jUaNG+Pj4mMTYrFkzALZv326MMX/+/LRu3dpk/8eveqTF3t6en3/+mRMnTjBt2jS6du1KeHg4H3/8MWXKlHmuK00dOnRIVta9e3fs7e1Nrox+9913xMbGGq/mpqZPnz7s3r3bJKZFixYZr34n+ffff+nWrRteXl7Gz3ODBg0AbQawuXQ6HX369OHo0aMcPHgQ0Lqw165dS4cOHYxdkAkJCXzyySeULVsWOzs7bGxssLOz4+zZs8903pSsW7cOnU5Hjx49TD4XXl5eVKxY0Tjgv0SJEhQsWJBRo0Yxd+5cTpw4Yfa5rl27xo8//oiVlRUHDx7kzp076d63ffv2xu+nra0tgwYNSlbn22+/Zf/+/ezfv5/ff/+dXr168dZbbzFr1ixjnS1btuDs7EzHjh1N9k3qGt28ebOx3uPlSTp16oSzs7OxXqVKlbCzs6N///4sWbIkWfe1yJ4ksRPZ1rlz59ixYwctWrRAKcXdu3e5e/eu8ZdW0kxZgPDwcJOZZ5ll3759NGnSBID58+fz559/sn//ft577z2AZ+qa69u3LzExMaxcuRLQuqyuX79u8oe7fv36/PLLLyQkJNCzZ08KFy5MuXLl+O6779J1jv/973/s37+fAwcOcOLECe7evcuMGTOSzUx8clZlUlfV8OHDTf7w2NraMmDAAAAiIiIA7Y/3k11PQIplTwoPD8fHxwcrq2f7lXTjxg3Wrl2bLMYXXnghWYyPJ57mxPi4MmXKMHjwYJYtW8alS5eYOnUqt27deq4uqJRmtLq6utK6dWu+/fZb4xIiixcvpkaNGsa2pebJpPDEiRPs37/f5HN179496tWrx969e/noo4/Ytm0b+/fvZ/Xq1cCzfZ5BSyqtrKyM3cjLly8nLi7O2A0LMHToUMaOHUvbtm1Zu3Yte/fuZf/+/VSsWDHDurhv3LiBUgpPT89kn409e/YYPxd6vZ7t27dTqVIl3n33XV544QV8fHwYP358imNEUxIcHExiYiK///47d+7cSTE5K1KkiHFc6+O++OILY9KWmjJlylCtWjWqVavGK6+8wrx582jSpAkjR440jotL+g4+Oc7Qw8MDGxsb47jHW7duYWNjk2wohk6nw8vLy1ivePHibNq0CQ8PD9566y2KFy9O8eLF+fLLL9P1mgjLsLF0AEKkZuHChSil+Omnn/jpp5+SPb9kyRI++ugjrK2tcXd3T9cg59Q4ODgYr5g9LukXf5KVK1dia2vLunXrcHBwMJY/zxIcZcuWpUaNGixatIjXX3+dRYsW4ePjY0wgk7Rp04Y2bdoQGxvLnj17mDRpEt26dSMgIIDatWuneQ69Xp+ucT1P/kFwc3MDtPFfj4/ReVypUqUAKFSoUIqTENIzMcHd3Z1du3ZhMBieKblzc3OjQoUKfPzxxyk+7+PjY4xx3759zxRjanQ6HUOGDOGDDz7g+PHjxnIHB4dkA9ZB+0wlva5PHiclffr04ccff2Tjxo0UKVKE/fv3p2uZmoIFC9KmTRu+/fZbPvroIxYtWoSDgwOvvvqqsc6WLVu4du0a27ZtM16lA5INoDdX4cKFadKkCStWrOCLL75g0aJFlChRgvr16xvrLFu2jJ49e/LJJ5+Y7BsREUGBAgXSPH5ar+3j3Nzc0Ol07Ny5E3t7+2T1Hy8rX748K1euRCnF0aNHWbx4MR988AGOjo6MHj06zXgWLFjA+vXrWbhwIU2aNGHixImMGjWKzp07m4xVfPnll5k9ezYHDhww+T4WL148zeOnpkKFCmzYsIEzZ85Qo0YNChUqxN69e1FKmXyebt68SUJCgvFzV6hQIRISEggPDzdJ7pRShIWFGXsIAOrVq0e9evVITEzkwIEDzJw5k8GDB+Pp6WnsSRHZi1yxE9lSYmIiS5YsoXjx4mzdujXZz7Bhw7h+/Tq///47oC0EunXr1jS7wpJ+iad0NSAgIIAzZ86Y/LG4deuWcUZmEp1Oh42NDdbW1sayhw8fsnTp0udqb58+fdi7dy+7du1i7dq19OrVy+QcT7ajQYMGfPbZZ4A2my+zlCpVisDAQP766y/j1YInf/Lnzw9Ao0aN2Lx5s/EqH2jv4/fff//U8zRr1oyYmJinLkJrb2+f4vvXsmVLjh8/TvHixVOMMSmxa9SoEdHR0cbZpElWrFjx1BgBrl+/nmL5tWvXiIqKMp4HtM/U0aNHTeqdOXPG7O7aJk2a4Ovry6JFi1JMztLSp08frl27xvr161m2bBnt2rUzSZqS/vg/mfTMmzfPrBhT0q9fP+7cucO4ceM4cuSIcTb64+d+8ry//fZbuoYZBAQEcPPmTZPPWlxcXLL181q2bIlSiqtXr6b4uShfvnyyY+t0OipWrMi0adMoUKAAhw4dSjOWS5cuMXToUFq0aGG8Gjps2DBq1qzJ66+/btIlO2TIEJycnHjrrbfSNSnjaZLW5ExKzl566SXu3buX7D+aSYuRv/TSSyb/PjnBZdWqVdy/f9/4/OOsra2pWbMms2fPBjC+Lmn9XhUWYsHxfUKkau3atQpQn332WYrPh4eHK3t7e9W2bVullFJXrlxR3t7eysPDQ02fPl1t3rxZrVq1SgUHB6uTJ08qpZS6f/++cnR0VHXr1lVbt25V+/fvV1evXlVKKbVr1y4FqI4dO6oNGzaoFStWqEqVKil/f3+TQdqbN2821vvjjz/Ud999p6pWraoCAwOfOkEgLXfv3lWOjo6qcOHCyQbLK6XU2LFjVZ8+fdSyZcvUtm3b1C+//KIaNWqkbG1t1fHjx9M8dkrr2D0paYD6559/nuy5LVu2KHt7e9WkSRO1YsUKtX37dvXzzz+rTz75RHXs2NFY79ixY8rR0VGVLVtWrVy5Uq1Zs0Y1bdpU+fn5PfW1iY+PN7Zn5MiR6vfff1e//fabGjdunPruu+9M9vPw8FBr1qxR+/fvV6dOnVJKKXXt2jXl7++vSpcurebMmaM2b96sfvvtNzV79mzVokULdfnyZaWU9hkoWbKk0uv1atasWWrDhg3qnXfeUUWKFEnX5ImWLVuq//3vf2ratGlq48aNasuWLWrOnDkqMDBQ2djYGAenK6XUsmXLFKDefPNNtWnTJrVgwQJVqlQp5e3tneLkiR9//DHV844ZM0bZ29srd3f3ZBNtlEp9rcDExERVuHBh4+fq8fiUUioiIkIVLFhQVaxYUa1evVqtXbtWde3a1fh5fvz1SO/kiSSxsbHKzc1N6XQ6ZW1tra5cuWLyfM+ePZW9vb2aNm2a2rx5s5o8ebJyd3dXhQsXNnl9Upo88e+//ypbW1vVsGFD9dtvv6lVq1apBg0aqKJFiyaLsX///srJyUmNGDFCrV27Vm3ZskUtX75cvfnmm8bJB2vXrlXNmjVT8+bNUxs3blR//PGHeuONN0wmEqTEYDCoxo0bq4IFC6pr166ZPHfy5Enl4OCgevToYVL+66+/KicnJ+Xv768+/fRT9ccff6jt27erH3/80TiJZ8yYMcb6Se/tokWLVGhoqAoNDVXr1q1Tffv2VYBq166dse7Dhw9VhQoVVP78+dXUqVPVxo0b1fjx45Wtra1q3ry5SdxNmzZVtra2asKECWrjxo3qiy++UPny5VOVK1c2rl/51VdfqU6dOqnFixerLVu2qPXr16uOHTsqQG3YsMF4PH9/f1WqVCm1YcMGtX///gxZs1I8O0nsRLbUtm1bZWdnl2ym4eO6du2qbGxsjLMwL1++rPr27au8vLyUra2t8vHxUZ07d1Y3btww7vPdd9+p0qVLK1tb22QzuZYsWaLKlCmjHBwcVNmyZdX333+f4uy7hQsXqlKlSil7e3tVrFgxNWnSJLVgwYLnSuyUUqpbt24KUHXr1k323Lp161SzZs2Ur6+vsrOzUx4eHqp58+Zq586dTz3u8yZ2Sin1119/qc6dOysPDw9la2urvLy81IsvvmicrZzkzz//VLVq1VL29vbKy8tLjRgxQn399dfpem0ePnyoxo0bpwIDA5WdnZ0qVKiQevHFF9Xu3buNdY4cOaLq1q2rnJycFGByjPDwcDVo0CBVtGhRZWtrq1xdXVXVqlXVe++9p+7du2esd+XKFdWhQweVL18+lT9/ftWhQwe1e/fudCV2GzZsUH379lVly5ZVer1e2djYKG9vb9W+fXsVGhpqUtdgMKjJkyerYsWKKQcHB1WtWjW1ZcuWVGfFppXYnTlzxjgbcuPGjcmeT2sR6HfffVcBys/Pz2S2ZZLdu3er2rVrKycnJ+Xu7q5ee+01dejQoedO7JRSasiQIQowSSqS3LlzR/Xr1095eHgoJycn9b///U/t3Lkz2euT2gLF69evV5UqVVKOjo6qWLFiatasWanGuHDhQlWzZk3l7OysHB0dVfHixVXPnj3VgQMHlFJKnTp1Sr366quqePHiytHRUen1elWjRg21ePHiNNs3e/ZsBajly5en+PzkyZNTnIX9zz//qLfffluVKlVKOTo6Knt7e+Xv7686deqkfv75Z5PZ9SnNitXr9apSpUpq6tSpyRYRv3XrlnrjjTeUt7e3srGxUf7+/mrMmDHJ6j18+FCNGjVK+fv7K1tbW+Xt7a3efPNNdefOHWOd0NBQ1a5dO+Xv76/s7e1VoUKFVIMGDdSaNWtMjrVp0yZVuXJlZW9vr4BMWURcpJ9OqWdYVVUIIYQQQmQ7MsZOCCGEECKXkMROCCGEECKXkMROCCGEECKXkMROCCGEECKXkMROCCGEECKXkMROCCGEECKXkFuKZQCDwcC1a9fInz9/qrcFEkIIIYR4FkopoqOj03VPbUnsMsC1a9fw8/OzdBhCCCGEyMUuX75M4cKF06wjiV0GSLpX5uXLl3Fxccn08xkMBuPNm5/lhuk5kbRZ2pxbSZulzbmVtDnj2hwVFYWfn58x30iLJHYZIKn71cXFJcsSu5iYGFxcXPLUl0XanPtJm6XNuZW0WdqcEdIz3EsSuxwkMjKSsLAw4uPjuXXrFhEREXnqyyJtzv2kzXmvzXZ2dvj6+uLs7GzpsITIFSSxywFOnDjBjBkzOHToEAaDAYDExESsra0tHFnWkjbnDdLmvOHxNtva2lK7dm2GDRuGr6+vhSMTImeTxC6bO3PmDAMGDMDb25sxY8ZQqlQpbGxsSEhIwMbGJs/MwlVKSZvzAGlz3mtzXFwcR48eZeXKlbz++ussXLgQDw8PS4coRI4liV02t2zZMgoUKMA333xj7KrI638IpM25l7Q5b7a5XLlyNG7cmE6dOvHLL7/Qv39/S4coRI6VNwZ05FBKKbZv306zZs1k/IkQIlfz8PCgfv36bN++3dKhCJGjSWKXjcXExHD//n38/f0tHYoQQmQ6f39/wsPDLR2GEDmaJHbZWNJEibw2qFoIkTfZ2NgYf+8JIZ6NJHYiR7h16xYtW7bE19cXe3t7/Pz8GDhwIFFRUZl6XqUUEyZMwMfHB0dHRxo2bMjff/+d5j6LFy9Gp9Ml+4mJiTHWmTBhQrLnvby8TI7Tp08f7OzssLKyMtapVatWprRTPJ9t27bRpk0bvL29cXZ2plKlSixfvvyp+wUEBGBlZWXyPo8ePdr4/K1bt3jllVfw8fFJ9XMfExND7969KV++PDY2NrRt2zbFc8XGxvLee+/h7++Pvb09xYsXZ+HChc/d9kuXLtGqVSucnZ1xc3Nj0KBBxMXFmR2fECJj5LjEbs6cORQtWhQHBweqVq3Kzp0706y/fft2qlatioODA8WKFWPu3Lkmz6fnj7B45PFf2FnJysqKVq1a8euvv3LmzBkWL17Mpk2beOONNzL1vJMnT2bq1KnMmjWL/fv34+Xlxcsvv0x0dHSa+7m4uHD9+nWTHwcHB5M6L7zwgsnzx44dS3acpk2bcu3aNWOd9evXZ2j7MkvS4Pi8Yvfu3VSoUIFVq1Zx9OhR+vbtS8+ePVm7du1T9504cSKXLl0yvs/vv/++8TkrKyvatGnDmjVrUv3cJyYm4ujoyKBBg2jcuHGq5+ncuTObN29mwYIFnD59mu+++47SpUs/V7sTExNp0aIF9+/fZ9euXaxcuZJVq1YxbNgws+MTQmQQlYOsXLlS2draqvnz56sTJ06od955Rzk7O6uLFy+mWP/ff/9VTk5O6p133lEnTpxQ8+fPV7a2tuqnn34y1lm0aJFycXFR169fN/kxR2RkpAJUZGTkc7XvSffu3VNVq1ZVf/zxh0m5wWBQ9evXVwMGDFBvvfWW0uv1ytXVVb333nvKYDAY68XGxqoRI0YoHx8f5eTkpGrUqKG2bt1qfD4iIkJ17dpV+fr6KkdHR1WuXDm1YsUKk3M1aNBAvfXWW2rIkCGqUKFCqn79+koppcaPH6/8/PyUnZ2d8vb2Vm+//bZxn9u3b6ugoCBVoEAB5ejoqF555RV15swZ4/OLFi1Ser1ehYSEqNKlSytnZ2fVtGlTde3atVRfC4PBoOLi4kza9+WXX6rChQub96KawWAwKC8vL/Xpp58ay2JiYpRer1dz585Ndb+k9qVl/PjxqmLFimnW6dWrl2rVqpVJm821detWBah169apChUqKHt7e1WjRg119OhRk3p//vmnqlevnnJwcFCFCxdWb7/9trp3757x+aVLl6qqVauqfPnyKU9PT/Xqq6+qGzduJDtPSEiIqlq1qrK1tVVbtmxRR44cUQ0bNlT58uVT+fPnV1WqVFH79+837vfTTz+psmXLKjs7O+Xv768+//xzk/fZ399fffzxx6pPnz4qX758ys/PT82bNy/NNid9ZtP6bmSF5s2bqz59+qRZx9/fX02dOjXZZzstaX3ue/Xqpdq0aZOs/Pfff1d6vV7dunUrzWMvXLhQlS5dWtnb26tSpUqp2bNnp1l//fr1ysrKSl29etVY9t133yl7e/sUfx8mxZfS91kp7bvz0ksvpXnOnCoxMVFdv35dJSYmWjqULCNtzjjm5Bk56ord1KlT6devH6+99hplypRh+vTp+Pn58dVXX6VYf+7cuRQpUoTp06dTpkwZXnvtNfr27cuUKVNM6iV1gz3+kxN8++232NjYsHfvXmbMmMG0adP45ptvjM/36dOHP//8k5UrV3L06FE6derEK6+8wtmzZwGti6Rq1aqsW7eO48eP079/f4KCgti7d6/JeZYsWYKNjQ1//vkn8+bN46effmLatGnMmzePs2fP8ssvv1C+fHlj/d69e3PgwAHWrFlDaGgoSimaN29OfHy8sc6DBw+YMmUKS5cuZceOHVy6dInhw4enu+3Xrl1j9erVNGjQIM16zZo1I1++fGn+pOb8+fOEhYXRpEkTY5m9vT0NGjRg9+7daZ733r17+Pv7U7hwYVq2bMnhw4eT1Tl79iw+Pj4ULVqUrl278u+//yars2PHDjw9PSlZsiTBwcHcvHkzzfOmZsSIEUyZMoX9+/fj4eFB69atje/HsWPHaNq0Ke3bt+fo0aN8//337Nq1i4EDBxr3j4uL48MPP+Svv/7il19+4fz58/Tu3TvZeUaOHMmkSZM4efIkFSpUoHv37hQuXJj9+/dz8OBBRo8eja2tLQAHDx6kc+fOdO3alWPHjjFhwgTGjRvHt99+a3LML774gmrVqnH48GEGDBjAm2++yalTp9Jsb9JnNrXvxpN27tz51M/JJ598kt6XG9DuFOPq6vrUepMnT8bLy4vKlSvz8ccfp3lVPL2f+yetWbOGatWqMXnyZHx9fSlZsiTDhw/n4cOHxjrz58/nvffe4+OPP+bkyZN88sknjB07liVLlqR63NDQUMqVK4ePj4+xrGnTpsTGxnLw4EGzYhRCZJAMTSkzUWxsrLK2tlarV682KR80aJDxKtKT6tWrpwYNGmRStnr1amVjY6Pi4uKUUtr/EK2trVWRIkWUr6+vatGihTp06JBZsVnqil2ZMmVM/sc7atQoVaZMGaWUUufOnVM6nc7kf9JKKfXSSy+pMWPGpHrO5s2bq2HDhhm3GzRooCpVqmRS54svvlAlS5Y0voaPO3PmjALUn3/+aSyLiIhQjo6O6ocfflBKaa85oM6dO2esM3v2bOXp6ZlqXEn/w+/atatydHRUgGrVqpV6+PBhqvsopdSVK1fU2bNn0/xJzZ9//qmAZK9hcHCwatKkSar7hYaGqqVLl6ojR46oHTt2qA4dOihHR0eTq5br169XP/30kzp69KjauHGjatCggfL09FQRERHGOt9995365Zdf1NGjR9WaNWtUxYoV1QsvvKBiYmLSbPPjkq6krVy50lh269Yt5ejoqL7//nullFJBQUGqf//+Jvvt3LlTWVlZpfr67tu3TwEqOjra5Dy//PKLSb38+fOrxYsXp3iMbt26qZdfftmkbPjw4Safa39/f9WjRw/j8waDQXl4eKivvvoq1TY3aNAgze9GSh48ePDUz8nTrnY97scff1R2dnbq+PHjadabOnWq2rp1qzpw4ID6+uuvlZubm+rXr1+yeun93Kd2xa5p06bK3t5etWjRQu3du1f99ttvyt/f3+SKop+fX7Ir9h9++KGqXbt2qvEHBwcnew+VUsrOzi7ZsR6PT67YZY2792PVuRvR6tDF2+rczWh1935slp1bKblil5HMyTNyzALFERERJCYm4unpaVLu6elJWFhYivuEhYWlWD8hIYGIiAi8vb0pXbo0ixcvpnz58kRFRfHll19St25d/vrrLwIDA1M8bmxsLLGxscbtpIHMBoMhQ2d0JR1LKYVSKtnzNWvWND4PUKtWLb744gsSEhI4ePAgSilKliyZLPZChQqhlCIxMZFPP/2UH374gatXrxrb5ezsbHK+qlWrmmx37NiR6dOnU6xYMZo2bUrz5s1p1aoVNjY2nDhxAhsbG2rUqGHcx9XVlVKlSnHixAljW5ycnChWrJixjpeXFzdv3kyxnUmUUkydOpVx48Zx+vRp3nvvPYYMGcKcOXNS3efxKwlpHfdp5Y8/NhgM6HS6VPerWbOm8b0BqFOnDlWrVmXGjBnMmDEDgFdeecX4fLly5ahVqxYlSpRg8eLFDB06FIAuXboQHx+Pra0t5cqVo2rVqgQEBLBu3Trat2//1HY9HnetWrWMjwsWLGjyfhw8eJBz586ZDPZXSmEwGPj3338pU6YMhw8fZuLEiRw5coTbt28bP5sXL16kbNmyxmM/+VkZMmQIr732GkuXLuWll16iU6dOFC9eHICTJ0/SunVrk/p169blyy+/NLndVPny5U3qeHl5cePGjTQ/K2l9N1KaZe7g4GCMKz2vZ1q2bdtG7969+frrr01em5QMHjwYgPj4eKpUqULBggXp1KkTn376KYUKFTLWM/dz/+Q5kz6zy5YtQ6/XA9qV0E6dOjFr1izu3bvH5cuX6devH8HBwcb9EhIS0Ov1xqvuSWOa/f39OX78OEqpFL8LSdtP+z4/WSfpcW6cGWswGIzfq6xwPfIhY1YfY9fZCGNZvUA3PmlfHm+9Y5bEkNVtzg4yq83mHC/HJHZJnlyZPekXizn1Hy+vVauWyUzDunXrUqVKFWbOnGn8I/ykSZMmMXHixGTl4eHhGTrp4sGDByQkJJCYmGgyED0pOVJPDFBPTEwEtF/G8fHxWFtbs2fPnmR/yPLly0dCQgJTpkxh+vTpTJkyhXLlyuHs7Mzw4cOJiYkxHjcpCXv8PN7e3hw/fpxNmzaxZcsW3nrrLT7//HM2b95srJeQkJAsGUqK12AwYGtra3LMx59PSVIiWqhQIdzc3ChRogQFChSgUaNGjBkzBm9v7xT3a9WqFbt27Urzdb5z506K5W5ubgBcuXIFd3d3Y/mNGzdwd3c3a3JA1apVOXv2bKr72NvbU65cOc6cOWPy2ie9pzqdDnd3d/z9/Tl9+nS6z/34ZyKlz1DS5ys4OJi33nor2f5FihQhMjKSpk2b0rhxYxYvXoybmxuXL1+mRYsWJp/RpHY8fp7333+fzp078/vvv7NhwwYmTJjAsmXLaNu2bYrveVL3cHx8vPHzY21tnay9T7bncU/7bqSUbOzatYtWrVql8UrCqFGjTGaspmTHjh20bduWzz//nG7duqXrfXr8fa5WrRoAp0+fpkaNGsY6bm5u6frcJ/3n8snzenp64uvri7Ozs/G5wMBAlFJcuHABFxcXAL766iuT88Kj1/+rr74ydt0mfX89PDzYu3evyfnu3LlDfHw8bm5uyeJIii8+Pt7ks/348/Hx8c885CA7MxgMREZGopTCyipzR0Hdj41n3o5/uRURRZmCj8ojIsKZse4A/esXw9neNlNjgKxtc3aRWW1+2oS9x+WYxM7NzQ1ra+tkV+du3ryZ7KpcEi8vrxTr29jYmPxv+HFWVlZUr17dOA4tJWPGjDFeVQHtip2fnx/u7u7GX5AZ4f79+9jY2GBtbY2NjelbpdPp2Ldvn0n5/v37CQwMxN7enmrVqpGYmMjt27epV69eisffvXs3rVu3plevXoD2gTx37hxlypQxHjdplvCT58+fPz/t2rWjXbt2DBw4kDJlynDy5EnKly9vvGJYp04dQFuy4ezZs7zwwgvY2NgYP+yPHzMp+XzyPE9KGp8FGI+TmJiY6n7ffPONyTiilKS2b2BgIF5eXmzdupXq1asD2liznTt38umnnz411iRKKY4ePUq5cuVS3Sc2NpZTp05Rr169ZHWS2nzr1i0uX76Mr69vus+d9LoeOHCAYsWKAdof3rNnz1K2bFlsbGyoWrUqJ0+eTHWG5MGDB4mIiOCzzz7Dz88PgCNHjgDaa5f0GX18+3Fly5albNmyDBs2jG7durF06VI6duzICy+8QGhoqEn9ffv2ERgYaDKD2MrKyqSOTqdLVva4p303UlKzZs0Ux0E+ztXVNc3XPWnJk08//fSZZmvb2toaZ0YXLlw41XOl9bm3srJK8bX53//+x6pVq4iJiTGOK/3333+xsrIiICAAR0dHfH19uXjxIj179kzxvCktlF63bl0+/fRTwsPDjUnmli1bsLe3p0aNGqnGl/SZfvz7nPS8ra1trrxXbNJVU3d390xPcv4Nv8dPf0cDyS96nLwTTXATPR7uqY8vzihZ2WaLuX8fvv4aGjWCSpUyrc1PrqqQlhyT2NnZ2VG1alU2btxIu3btjOUbN26kTZs2Ke5Tu3btZMsN/PHHH1SrVi3ZL5QkSimOHDliMhngSfb29in+gUj6pZVRko6VlFw9HiPA5cuXGTZsGK+//jqHDh1i1qxZfPHFF+h0OkqVKkX37t3p1asXX3zxBZUrVyYiIoItW7ZQvnx5mjdvTokSJVi1ahWhoaEULFiQqVOnEhYWRpkyZUzO9+T5Fy9eTGJiIjVr1sTJyYlly5bh6OhIQEAAhQoVok2bNvTv35958+aRP39+Ro8eja+vL23btjU51pPneLLscb/99hvXr1+nZs2a5M+fnxMnTjBy5Ejq1q1L0aJFU30NCxcubO7LbhLT4MGDmTRpEiVLliQwMJBPPvkEJycnunfvboy1Z8+e+Pr6MmnSJEBbvqJWrVoEBgYSFRXFjBkzOHLkCLNnzzbuM3z4cFq1akWRIkW4efMmH330EVFRUfTu3RudTse9e/cYP348bdu2pXDhwly8eJF3330XNzc32rdvn+57iibV+/DDD3Fzc8PT05P33nsPNzc32rVrh06nY9SoUdSqVYuBAwcSHByMs7MzJ0+eZOPGjcycORN/f3/s7OyYNWsWb7zxBsePH+ejjz4yHv/J9zTp8cOHDxkxYgQdO3akaNGiXLlyhf3799OhQwd0Oh3Dhg2jevXqfPTRR3Tp0oXQ0FBmz57NzJkzTWJ/8vOXWtnj0vpupMTJySnVoRfpsW3bNlq2bMk777xDx44duXHjBqD93kqaQLFv3z569uzJ5s2b8fX1JTQ0lD179tCwYUOcnZ05fPgwQ4cOpXXr1sYkav369dy4cYPq1auTL1++VD/3J06cIC4ujtu3bxMdHc1ff/0FQKVKlQDo3r07H330EX379mXixIlEREQwcuRI+vbti5OTE6CtrTho0CD0ej3NmjUjNjaWAwcOcOfOHZP/yD6uadOmlC1blp49e/L5559z+/ZtRowYQXBwsLHLN6X4jhw5QkJCAtWqVUvx90BuTQKS/lOS2e2Ljk3EkEJSl+RebGKWvcZZ1WaLOHUKPv8cbt6E48dh/nz4bz3KzMoH0uWpo/CykaTlThYsWKBOnDihBg8erJydndWFCxeUUkqNHj1aBQUFGesnLXcyZMgQdeLECbVgwYJky51MmDBBhYSEqH/++UcdPnxY9enTR9nY2Ki9e/emOy5LTZ5488031RtvvKFcXFxUwYIF1ejRo00GI8fFxalx48apgIAAZWtrq7y8vFS7du2MS13cunVLtWnTRuXLl095eHio999/X/Xs2dNk8HWDBg3UO++8Y3L+n3/+WdWsWVO5uLgoZ2dnVatWLbVp0ybj80nLnej1euXo6KiaNm2a4nInTx4zrY/j5s2bVa1atZRer1cODg4qMDBQjRo1St25cyedr+azMRgMavz48crLy0vZ29ur+vXrq2PHjpnUadCggerVq5dxe/DgwapIkSLKzs5Oubu7qyZNmqjdu3eb7NOlSxfl7e2tbG1tlY+Pj2rfvr36+++/jc8/ePBANWnSRLm7uytbW1tVpEgR1atXL3Xp0iWT4/Tq1Us1aNAg1fiTJjWsXbtWvfDCC8rOzk5Vr15dHTlyxKTevn371Msvv6zy5cunnJ2dVYUKFdTHH39sfH7FihUqICBA2dvbq9q1a6s1a9YoQB0+fNjkPI+/H7Gxsapr167GZXF8fHzUwIEDTQb+Jy13ktTGyZMnJ1vuZNq0aSaxVqxYUY0fPz7VNjdo0EANGDAgze9GRuvVq5cCkv08/t4kvUbnz59XSil18OBBVbNmTeNnulSpUmr8+PHq/v37xn22bNmiateu/dTPvb+/f4rnf9zJkydV48aNlaOjoypcuLAaOnSoevDggUmd5cuXq0qVKik7OztVsGBBVb9+/WQT1p508eJF1aJFC+Xo6KhcXV3VwIEDk03wSS0+mTyROc7diFb+o9al+nPuRnSmx6BULp48kZio1HffKdW6tVItWyrVt69SJ0/+95TlJ0/kqMROKW32pL+/v7Kzs1NVqlRR27dvNz6X0h+5bdu2qcqVKys7OzsVEBCQbDZdev4IP42lErsnZ/zmZqnNosvN0tPmBg0apJnkpJRwZWcZ8T6n9J+R7Ew+249IYpcx7t6PVUHf7EkxqQv6Zk+WzY7NtYnd5MlaQteypVJTpij12Jqf2SGxyzFdsUkGDBjAgAEDUnxu8eLFycoaNGjAoUOHUj3etGnTmDZtWkaFJ0SWiY6O5p9//mHdunWWDkUIkY3onez4tEMFRq86yo7HZsXWD3Tjsw4V0DvZWTC6XOCVV+DgQXj9dW1sXTaT4xK7vEilY4kFkffkz5+fy5cvWzoMITJMXloWI7P5FHBk5quVibgXR3RMPPkdbHHLZydJ3bOIiYF//oEXXtC2y5eHBQvA2dmycaVCErtszNHRESsrK27fvp3suU2bNqV7ZqTIuxo2bJjn/mOwbds2S4cgntHt27fTvBuMMI/eSRK553b2LEyZArdvw5dfQtLaqNk0qQPIhdNUcg8rKyuqVq3K9u3b89wfZyFE3mIwGNixY4dxaSEhLEopWLUKRoyAa9cgXz4wYy05S5LELptr164d+/fvZ+rUqYSHh1s6HCGEyHBXrlzhvffeIyws7KkLRQuR6W7dgrFjYfFiSEyEunVhxgwoVcrSkaWL9OVlc02aNCE8PJwZM2awcuVKXF1dsbOzM7nlUl4hbc4bpM15Q1KbY2NjuX37Ng4ODnzyySdUqFDB0qGJvGzvXq3LNToa7O21CRKNG0M61w7NDiSxywG6d+9uvDXWlStXiI+PJzo6mvz586d7odqcTiklbc4DpM15r8329vYULVqUOnXq4OiYNfcwFSJVp09rSV3x4lo3rK+vpSMymyR2OYSLiwvNmzcHtLEoN2/exMPDI3eu5p0CabO0ObeSNueNNotsTKlHV+S6dYMCBaB5c8ihExTlGyWEEEKIvEcp+PVXGDUKEhK0MhsbaN06xyZ1IFfshBBCCJHX3L0L06drCw0DbN8OL71kyYgyjCR2QgghhMg7DhzQkrrISLCzg3794MUXLR1VhpHETgghhBC5X1wcLFkCa9Zo2wEB2gSJIkUsGlZGk8ROCCGEELnfnDmwebP2uFUr6N1bu2KXy0hiJ4QQQojcr0sX+Ptv6N8fcvEdTmRWrBBCCCFyn6goePze0d7eMHdurk7qQK7YCSGEECK3+esvmDoV7twBV1dIuqNJHrjDiyR2QgghhMgdEhJg6VL4+WdtnbrChSF/fktHlaUksRNCCCFEznf1KkyZAufOadvNmmlLmdjbWzauLCaJnRBCCCFytq1bYfZsiI3VrtANGgS1alk6KouQxE4IIYQQOZvBoCV1FSrA0KFQqJClI7IYSeyEEEIIkfPExICDg/b4xRfB2Rlq1ACrvL3gR95uvRBCCCFyloQEWLYMBgyA6GitTKfTul7zeFIHktgJIYQQIqcIC4PRo+H77yE8HHbutHRE2Y50xQohhBAi+9u2Tbst2MOHWrfrW29BvXqWjirbkcROCCGEENnXgwfaHSO2btW2y5SB4cPBw8OycWVTktgJIYQQIvtatkxL6qys4NVXoVOnPHEHiWcliZ0QQgghsq/u3eH8eejZU7taJ9IkkyeEEEIIkX2Eh8PKldotwUAbTzdpkiR16SRX7IQQQgiRPezaBbNmwf374OoKTZpYOqIcRxI7IYQQQlhWTAx8/TVs3KhtlywJ5ctbNqYcShI7IYQQQljOuXPw+edw7Zq20HCnTtokCRtJUZ6FvGpCCCGEsIw//oCvvtLuJlGoEAwbJlfqnpMkdkIIIYSwDF9fSEyEOnVg4EDIn9/SEeV4ktgJIYQQIutERICbm/b4hRdg6lQoXlzrhhXPLcctdzJnzhyKFi2Kg4MDVatWZedT7hO3fft2qlatioODA8WKFWPu3LnJ6qxatYqyZctib29P2bJl+fnnnzMrfCGEECJvio3Vul3feAOuXHlUXqKEJHUZKEcldt9//z2DBw/mvffe4/Dhw9SrV49mzZpx6dKlFOufP3+e5s2bU69ePQ4fPsy7777LoEGDWLVqlbFOaGgoXbp0ISgoiL/++ougoCA6d+7M3r17s6pZQgghRO524QIMGQLr12sJ3pEjlo4o19IplbQCYPZXs2ZNqlSpwldffWUsK1OmDG3btmXSpEnJ6o8aNYo1a9Zw8uRJY9kbb7zBX3/9RWhoKABdunQhKiqK33//3VjnlVdeoWDBgnz33XfpiisqKgq9Xk9kZCQuLi7P2rx0MxgM3Lx5Ew8PD6ysclRu/sykzdLm3EraLG3OrQwGAzdv3MBj3z6sliyB+HgoWBCGDoVKlSwdXqbIrPfZnDwjx3y64uLiOHjwIE2eWKywSZMm7N69O8V9QkNDk9Vv2rQpBw4cID4+Ps06qR1TCCGEEOkQGUm+L75AN3++ltRVrw4zZ+bapC67yDGTJyIiIkhMTMTT09Ok3NPTk7CwsBT3CQsLS7F+QkICEREReHt7p1ontWMCxMbGEhsba9yOiooCtEzdYDCY1a5nYTAYUEplybmyC2lz3iBtzhukzXmDCgnB5q+/UPnzo/r0gebNtbF0ufg1yKz32Zzj5ZjELonuiQGWSqlkZU+r/2S5ucecNGkSEydOTFYeHh5OTExM6sFnEIPBQGRkJEqpPHVJX9qc+0mbpc25VZ5sc926WB07hq5tW1SRIto9YHO5zHqfo6Oj0103xyR2bm5uWFtbJ7uSdvPmzWRX3JJ4eXmlWN/GxoZChQqlWSe1YwKMGTOGoUOHGrejoqLw8/PD3d09y8bY6XQ63N3d884vCGmzpcPJEtJmaXNulSfafPkyrFqlrUdnY4PBYCD87bdxy81tfkJmvc8ODg7prptjEjs7OzuqVq3Kxo0badeunbF848aNtGnTJsV9ateuzdq1a03K/vjjD6pVq4atra2xzsaNGxkyZIhJnTp16qQai729Pfb29snKrayssuzDq9PpsvR82YG0OW+QNucN0uZcRCnYsAHmz4e4OPD0hO7dgVzc5jRkRpvNOVaOSewAhg4dSlBQENWqVaN27dp8/fXXXLp0iTfeeAPQrqRdvXqVb7/9FtBmwM6aNYuhQ4cSHBxMaGgoCxYsMJnt+s4771C/fn0+++wz2rRpw6+//sqmTZvYtWuXRdoohBBC5BhRUTBjBiQtEVa5MjRrZtmY8rgcldh16dKFW7du8cEHH3D9+nXKlSvH+vXr8ff3B+D69esma9oVLVqU9evXM2TIEGbPno2Pjw8zZsygQ4cOxjp16tRh5cqVvP/++4wdO5bixYvz/fffU7NmzSxvnxBCCJFjHD0KX3wBt2+DjQ306gVt2shiwxaWo9axy65kHbvMJ22WNudW0mZpc44UEgJz5mjdsIULw4gRUKyYSZVc1+Z0yA7r2OWoK3ZCCCGEyAYqVQIHB2jQAPr10x6LbEESOyGEEEKkTSn45x/tvq4AXl7afV//W2FCZB9549qoEEIIIZ7N/fswebJ2r9fH7/EqSV22JImdEEIIIVJ24gS8/Tbs2gXW1nDtmqUjEk8hXbFCCCGEMJWYCCtXwvffa92w3t4wfDiULGnpyMRTSGInhBBCiEdu3IApU+DUKW37pZfg9dfB0dGycYl0kcROCCGEEI+cOKEldU5O8NZbUL++pSMSZpDETgghhBCPNGwI4eHavx4elo5GmEkmTwghhBB52enTMHq0dnsw0O4c0bmzJHU5lCR2QgghRF5kMMAPP8DIkfD337B0qaUjEhlAumKFEEKIvCYiQrvP6/Hj2nb9+tC7t0VDEhlDEjshhBAiL/nzT5g5U1t42MEB3nwTGjXSumBFjieJnRBCCJFXbNwIM2ZojwMDYcQIbY06kWtIYieEEELkFXXqaOPq6tWDbt3ARtKA3EbeUSGEECK3UgpCQ6F2ba2r1dkZZs0Ce3tLRyYyicyKFUIIIXKj27dh3DiYNAk2bHhULkldriZX7IQQQojcZt8++PJLbW06Ozvpcs1D5J0WQgghcou4OFi4EH77TdsuVkybIFG4sGXjEllGEjshhBAiN7h4ESZPhkuXtO127SAoCGxtLRuXyFKS2AkhhBC5wYMHcPkyFCgAQ4dC5cqWjkhYgCR2QgghRE6VmAjW1trjMmW0btcKFUCvt2xcwmJkVqwQQgiREx06BG+8oV2lS1KvniR1eZwkdkIIIUROEh8PCxbA+PEQFgYrV1o6IpGNSFesEEIIkVNcuQKffw7//qttt2gBfftaNiaRrUhil81FPogj4l4cUTHxuDja4uZsR34HeduEECJPUUpbZHj+fG1JExcXeOcdqFHD0pGJbEYyhGzs2t2HjFp1lJ1nI4xl9QPdmNS+HNYWjEsIIUQW27kTZs/WHleqBEOGgKurRUMS2ZOMscumIh/EJUvqAHacjeDd1ce4HxtvociEEEJkubp1tdmuffvCBx9IUidSJVfssqmIe3HJkrokO89GEFXXM4sjEkJkJykN09A72Vk6LJFREhK0u0c0b64tMGxtDR99BDqdpSMT2ZwkdtlUVEzaV+QexidmUSRCiOwmtWEan3aogE8BRwtGJjLE9evaBImzZyEiAvr108olqRPpIF2x2ZSLQ9q3gHG0lVF2QuRFaQ3TGL3qKJEP4iwUmXhuSsHmzTBokJbU5cunLToshBnkil025ZbPjvqBbuxIoTu2XqAbLo7y1gmRlyR1vd5+EEefukWp6FeAhbvO8yDu0dX7HWcjiLgXJ12yOdH9+9rkiJ07te1y5WDYMHBzs2xcIseR7CCb0jvZ8WmHCoxeddQkuUuaFWsVG23B6IQQWSmlrte6JQox49XKDPrusElyF/2UYRwiGzp3Dj75BMLDwcoKuneHjh21x0KYSRK7bMyngCMzX61MxL04omPiye9gi1s+bR27mzclsRMiL0it6/XPc7cA6Pu/oszacs5Ynv8pwzhENpQ/v3bFzssLhg+HUqUsHZHIwXLMfwfu3LlDUFAQer0evV5PUFAQd+/eTXMfpRQTJkzAx8cHR0dHGjZsyN9//21Sp2HDhuh0OpOfrl27ZmJLzKN3sqO4Rz4qFSlIcY980sUiRB6T1gz5P8/dorJfAeN2/UA33PLJ74gc4f79R489PWHCBPjyS0nqxHPLMYldt27dOHLkCCEhIYSEhHDkyBGCgoLS3Gfy5MlMnTqVWbNmsX//fry8vHj55ZeJjja92hUcHMz169eNP/PmzcvMpgghRLo9bYZ8bIIB0JK6zzpUkP/85QQ7dmgzXQ8fflRWpgw4OVkuJpFr5Iiu2JMnTxISEsKePXuoWbMmAPPnz6d27dqcPn2aUin8D0cpxfTp03nvvfdo3749AEuWLMHT05MVK1bw+uuvG+s6OTnh5eWVNY0RQggzPG2GfEAhJzYPbYBbPlnHLtt7+BCmT4etW7XtkBCoXNmiIYncJ0dcsQsNDUWv1xuTOoBatWqh1+vZvXt3ivucP3+esLAwmjRpYiyzt7enQYMGyfZZvnw5bm5uvPDCCwwfPjzZFT0hhLCUpBnyKakf6IZvAUcZppETnDmDy9ix6LZs0daje/VVGDnS0lGJXChHXLELCwvDw8MjWbmHhwdhYWGp7gPg6Wl6hwZPT08uXrxo3O7evTtFixbFy8uL48ePM2bMGP766y82btyYajyxsbHExsYat6OiogAwGAwYDIb0N+wZGQwGlFJZcq7sQtqcN0ibk8vvYMOk9uV4d/Uxk7F29f6bIZ/fwSbHvV556n02GGDVKlixAqsHD1C+vqjhw6Fs2UfP51J56n3+T2a12ZzjWTSxmzBhAhMnTkyzzv79+wHQpbDitlIqxfLHPfn8k/sEBwcbH5crV47AwECqVavGoUOHqFKlSorHnDRpUopxh4eHExMTk2Y8GcFgMBAZGYlSCqs8Mh1e2ixtzq3S02ZrYFxjX6LqevIwPhFHW2tcHG2wio3OkTPk89L7bHP0KPkWLACliKxYEcMbb6DLlw9u3rR0aJkuL73PSTKrzeb0JFo0sRs4cOBTZ6AGBARw9OhRbty4key58PDwZFfkkiSNmQsLC8Pb29tYfvPmzVT3AahSpQq2tracPXs21cRuzJgxDB061LgdFRWFn58f7u7uuLi4pNmejGAwGNDpdLi7u+epL4u0OfeTNkubc53GjeHsWVRgIIZy5XD38Mj9bf5Pnnqf/5NZbXZwcEh3XYsmdm5ubrilY1Xt2rVrExkZyb59+6hRowYAe/fuJTIykjp16qS4T1L36saNG6n83+DUuLg4tm/fzmeffZbquf7++2/i4+NNksEn2dvbY29vn6zcysoqyz68Op0uS8+XHUib8wZpc96Qa9scEwMrVkCHDqDXa2VvvaX9wb95M3e2OQ259n1OQ2a02ZxjmX3WJUuW8Ntvvxm3R44cSYECBahTp47J2LWMVKZMGV555RWCg4PZs2cPe/bsITg4mJYtW5rMiC1dujQ///wzoL2wgwcP5pNPPuHnn3/m+PHj9O7dGycnJ7p16wbAP//8wwcffMCBAwe4cOEC69evp1OnTlSuXJm6detmSluEEELkUv/+C4MHw88/w8yZlo5G5FFmJ3affPIJjo6OgDZbddasWUyePBk3NzeGDBmS4QEmWb58OeXLl6dJkyY0adKEChUqsHTpUpM6p0+fJjIy0rg9cuRIBg8ezIABA6hWrRpXr17ljz/+IH/+/ADY2dmxefNmmjZtSqlSpRg0aBBNmjRh06ZNWFtbZ1pbhBBC5CJKwS+/aPd2vXoVXF2hdWtLRyXyKLO7Yi9fvkyJEiUA+OWXX+jYsSP9+/enbt26NGzYMKPjM3J1dWXZsmVp1lFKmWzrdDomTJjAhAkTUqzv5+fH9u3bMypEIYQQec3t29radEmLDdeqBYMGabcJE8ICzL5ily9fPm7d0u5R+Mcff9C4cWNAG9j38OHDjI1OCCGEyK7OnoW339aSOjs7eOstePddSeqERZl9xe7ll1/mtddeo3Llypw5c4YWLVoA2qSDgICAjI5PCCGEyJ58fcHREQoVghEjwM/P0hEJYf4Vu9mzZ1O7dm3Cw8NZtWoVhQoVAuDgwYO8+uqrGR6gEEIIkW3cuKGNqQPt3q4ffghffCFJncg2zL5iV6BAAWbNmpWs/GkLDQshhBA5llKwfj0sWACvvQbNm2vlaSyNJYQlPNMiKzt37qRHjx7UqVOHq1evArB06VJ27dqVocEJIYQQFhcZqV2ZmzsX4uPhr78eXbUTIpsxO7FbtWoVTZs2xdHRkUOHDhnvmRodHc0nn3yS4QEKIYQQFnPkiDZBYv9+sLGB/v1h9Gh4yu0shbAUsxO7jz76iLlz5zJ//nxsbW2N5XXq1OHQoUMZGpwQQghhEfHxsHAhjB0Ld+5oY+imTYNWrSSpE9ma2WPsTp8+Tf369ZOVu7i4cPfu3YyISQghhLCsixfh11+1x82bQ79+2pImQmRzZid23t7enDt3LtnSJrt27aJYsWIZFZcQQghhOSVKaMmcpyfUrGnpaIRIN7O7Yl9//XXeeecd9u7di06n49q1ayxfvpzhw4czYMCAzIhRCCGEyFzR0fD559qVuiStW0tSJ3Ics6/YjRw5ksjISBo1akRMTAz169fH3t6e4cOHM3DgwMyIUQghhMg8x45pa9HduqXd63XaNBlHJ3IssxM7gI8//pj33nuPEydOYDAYKFu2LPny5cvo2IQQQojMk5AAK1bATz9py5f4+mozYCWpEznYMyV2AE5OTlSrVi0jYxFCCCGyxvXrMGUKnDmjbTdpAsHB4OBg2biEeE5mJ3aNGjVCl8b/ZrZs2fJcAQkhhBCZ6vx5GDkSYmLA2Vm7Sle3rqWjEiJDmJ3YVapUyWQ7Pj6eI0eOcPz4cXr16pVRcQkhhBCZw98fihfXulyHDQM3N0tHJESGMTuxmzZtWorlEyZM4N69e88dkBBCCJHhzp7VEjo7O7Cygvfe067WWT3TnTWFyLYy7BPdo0cPFi5cmFGHE0IIIZ5fYiJ89512ZW7Jkkfl+fNLUidypWeePPGk0NBQHGTQqRBCiOzi5k1tgsTJk9p2dLQ2+1VmvYpczOzErn379ibbSimuX7/OgQMHGDt2bIYFJoQQQjyzHTtgzhy4fx8cHWHAAGjY0NJRCZHpzE7s9Hq9ybaVlRWlSpXigw8+oEmTJhkWmBBCCGG2hw9h3jzYvFnbLlUKRozQbg0mRB5gdmK3aNGizIhDCCGEeH7R0bBnj9bd2qULdO0K1taWjkqILJNhY+yEEEIIi3h83JyHBwwZAvnywQsvWDYuISwgXYldwYIF01yU+HG3b99+roCEEEKIdIuI0O7t2r49VK2qldWsadmYhLCgdCV206dPz+QwhBBCCDOFhsLMmVr3682bMHeudLuKPC9diZ3cUUIIIUS2ERMDCxZASIi2XaIEDB8uSZ0QPOcYu4cPHxIfH29S5uLi8lwBCSGEEKn691/4/HO4ckUbV9e+PfToATYyZFwIeIbE7v79+4waNYoffviBW7duJXs+MTExQwITQgghTFy/rt1BIiEBXF1h6FCoWNHSUQmRrZh9P5WRI0eyZcsW5syZg729Pd988w0TJ07Ex8eHb7/9NjNiFEIIIcDbGxo00CZHzJwpSZ0QKTD7it3atWv59ttvadiwIX379qVevXqUKFECf39/li9fTvfu3TMjTiGEEHnRgQPaGLoCBbTtt97Sul3ltmBCpMjsK3a3b9+maNGigDaeLml5k//973/s2LEjY6MTQgiRN8XFaXeQmDgRpk/X1qoDsLWVpE6INJid2BUrVowLFy4AULZsWX744QdAu5JXIOl/VEIIIcSzunhRGz+3bp227esLMn5biHQxuyu2T58+/PXXXzRo0IAxY8bQokULZs6cSUJCAlOnTs2MGIUQQuQFSsH69dpSJvHxoNdrd5FIWnhYCPFU6U7sBg8ezGuvvcaQIUOMZY0aNeLUqVMcOHCA4sWLU1EGsgohhHgW0dHaHST279e2q1aFwYMfja0TQqRLurtiQ0JCqFixIjVq1ODrr78mKioKgCJFitC+fftMT+ru3LlDUFAQer0evV5PUFAQd+/eTXOf1atX07RpU9zc3NDpdBw5ciRZndjYWN5++23c3NxwdnamdevWXLlyJXMaIYQQImU2NtradDY2EBwM48dLUifEM0h3Ynfq1Cl27NhB+fLlGT58OD4+PvTs2TPLJkx069aNI0eOEBISQkhICEeOHCEoKCjNfe7fv0/dunX59NNPU60zePBgfv75Z1auXMmuXbu4d+8eLVu2lPX4hBAisyUkPJoU4egIo0bB1KnQurVMkBDiGZk1xq5u3brUrVuXmTNn8v3337No0SIaNmxI8eLF6devHz179sTHxyfDgzx58iQhISHs2bOHmv/d3Hn+/PnUrl2b06dPU6pUqRT3S0r8kiZ7PCkyMpIFCxawdOlSGjduDMCyZcvw8/Nj06ZNNG3aNMPbIoQQAqyuX0f3ySfw8svQqpVWWLy4ZYMSIhd4pnuwODk50adPH/r06cM///zDwoULmTx5MuPGjSMuLi6jYyQ0NBS9Xm9M6gBq1aqFXq9n9+7dqSZ2T3Pw4EHi4+Np0qSJsczHx4dy5cqxe/fuVBO72NhYYmNjjdtJ3dIGgwGDwfBMsZjDYDCglMqSc2UX0ua8QdqcByiF+uMP8s+ejQL46SfUyy+DnZ2lI8tUee59Rtqc0cdNr+e6ud79+/fZvn0727dv5+7du8+cYD1NWFgYHh4eyco9PDwICwt7ruPa2dlRsGBBk3JPT880jztp0iQmTpyYrDw8PJyYmJhnjie9DAYDkZGRKKWwsjJ7xZocSdosbc6t8lKbdffu4bRwIbb795OQkEBChQo8eOMN1FPGS+cGeel9TiJtzrg2R0dHp7vuMyV2O3bsYNGiRfz0008AdOrUic8++4y6deuadZwJEyakmCA9bv9/M6R0KYy3UEqlWP68nnbcMWPGMHToUON2VFQUfn5+uLu74+LikuHxPMlgMKDT6XB3d89TXxZpc+4nbc7FbT52DN20aRARgXJy4mHr1uTv2RNna2tLR5Yl8sz7/Bhpc8a12cHBId11053YXblyhSVLlrB48WL++ecfatasybRp0+jatSv58uV7pkAHDhxI165d06wTEBDA0aNHuXHjRrLnwsPD8fT0fKZzA3h5eREXF8edO3dMrtrdvHmTOnXqpLqfvb099vb2ycqtrKyy7MOr0+my9HzZgbQ5b5A250K3b8OECdpkCV9f1LBhxLq4oLe2zr1tTkGuf59TIG3OGOYcK92JXUBAAIUKFSIoKIh+/fpRpkyZZwrucW5ubri5uT21Xu3atYmMjGTfvn3UqFEDgL179xIZGZlmAvY0VatWxdbWlo0bN9K5c2cArl+/zvHjx5k8efIzH1cIIcRjXF2hWze4fh3699fG0928aemohMiV0p3Y/fDDD7Ru3Robm+calvdMypQpwyuvvEJwcDDz5s0DoH///rRs2dJkXF/p0qWZNGkS7dq1A7T72l66dIlr164BcPr0aUC7Uufl5YVer6dfv34MGzaMQoUK4erqyvDhwylfvrxxlqwQQggzKQXbtkHRohAQoJV17PhoCZM8NJheiKyW7mt77du3t0hSl2T58uWUL1+eJk2a0KRJEypUqMDSpUtN6pw+fZrIyEjj9po1a6hcuTItWrQAoGvXrlSuXJm5c+ca60ybNo22bdvSuXNn6tati5OTE2vXrsU6j4z7EEKIDHX/PkyZoq1H9/nnkLRSQjZdly7yQRz/3LzH4Ut3+Cf8HpEPMn5lByGykuUyNTO5urqybNmyNOuopIUu/9O7d2969+6d5j4ODg7MnDmTmTNnPm+IQgiRt508qSV1N2+ClRXUr6/dSSKbunb3IaNWHWXn2QhjWf1ANz7tUAGfAo4WjEyIZ5d3RjMKIYTIHImJsHIljB6tJXWenjB5MnTpoiV42VDkg7hkSR3AjrMRjF51VK7ciRwr+/5XSgghRPYXHQ0ffQQnTmjbDRvCm2+Ck5NFw3qaiHtxyZK6JDvORhBxLw69U+5eNFnkTpLYCSGEeHbOztpVOUdHLaFr1MjSEaVLVEx8ms9HP+V5IbKrdCV27du3T/cBV69e/czBCCGEyAEePgRra23ZEisrGDZMW6POy8vSkaWbi4Ntms/nf8rzQmRX6Urs9Hp9ZschhBAiJzhzRpsgUbUqvP66VpaO9UizG7d8dtQPdGNHCt2x9QPdcMsn3bAiZ0pXYrdo0aLMjkMIIUR2ZjDA6tWwbJk2WWLvXujRQ+uKzYH0TnZ82qECo1cdNUnu6ge68VmHCjK+TuRYMsZOCCFE2iIiYNo0OHpU265bFwYOzLFJXRKfAo7MfLUyEffiiI6JJ7+DLW757CSpEznaMyV2P/30Ez/88AOXLl0iLs50SvihQ4cyJDAhhBDZQGgozJypzX61t9e6Xxs3zrYLDptL7ySJnMhdzF5gaMaMGfTp0wcPDw8OHz5MjRo1KFSoEP/++y/NmjXLjBiFEEJYwv37MGOGltSVKAFffgkvv5xrkjohciOzr9jNmTOHr7/+mldffZUlS5YwcuRIihUrxrhx47h9+3ZmxCiEEMISnJ21LtezZ7XxdNn4LhJCCI3ZV+wuXbpEnTp1AHB0dCQ6OhqAoKAgvvvuu4yNTgghRNZRCn79FQ4ceFRWty707i1JnRA5hNmJnZeXF7du3QLA39+fPXv2AHD+/Plk92oVQgiRQ9y5AxMmwDffwPTpWverECLHMTuxe/HFF1m7di0A/fr1Y8iQIbz88st06dKFdu3aZXiAQgghMtmBA/D223DokLbocPfukC+fpaMSQjwDs6+tf/311xgMBgDeeOMNXF1d2bVrF61ateKNN97I8ACFEEJkkrg4WLwY/vvPOgEBMHIk+PlZMiohxHMwO7GzsrLCyurRhb7OnTvTuXPnDA1KCCFEJnv4UEviLlzQttu0gZ49tSt2Qogcy+yu2KJFizJ27FhOnTqVGfEIIYTICo6OEBgIer02tu611ySpEyIXMDuxe/vttwkJCaFs2bJUrVqV6dOnc/369cyITQghREaKioLIyEfb/fvDrFnafV+FELmC2Ynd0KFD2b9/P6dOnaJly5Z89dVXFClShCZNmvDtt99mRoxCCCGe15Ej2gSJadO0ZU0AHBygQAFLRiWEyGBmJ3ZJSpYsycSJEzl9+jQ7d+4kPDycPn36ZGRsQgghnldCAixaBGPHwu3bcPOm6VU7IUSu8lwrTu7bt48VK1bw/fffExkZSceOHTMqLiGEEM/r6lX4/HP45x9tu1kz6NdPu+erECJXMjuxO3PmDMuXL2fFihVcuHCBRo0a8emnn9K+fXvy58+fGTEKIYQwh1KwaRPMmwexsZA/PwwaBLVqWToyIUQmMzuxK126NNWqVeOtt96ia9eueHl5ZUZcQgghnlV8PPz0k5bUVagAQ4dCoUKWjkoIkQXMTuxOnTpFyZIlMyMWIYQQGcHODoYPh6NHoX170OksHZEQIouYndhJUieEENlMQgKsXKmtSdeqlVYWGKj9CCHylHQldq6urpw5cwY3NzcKFiyILo3//d2+fTvDghNCCPEUYWEwZQqcPg02Nto4Ond3S0clhLCQdCV206ZNM06MmDZtWpqJnRBCiCyydSt89ZV2ezBnZxg4UJI6IfK4dCV2vXr1Mj7u3bt3ZsUihBAiPe7fh7lzYds2bbtsWRg2DDw8LBqWEMLyzF6guFGjRixYsIBIWeBSCCGyXny8lsRt2wZWVtC9O3zyiSR1QgjgGRK78uXL8/777+Pl5UWHDh345ZdfiIuLy4zYhBBCPMnWFho31hK5zz6Drl3B2trSUQkhsgmzE7sZM2Zw9epVfv31V/Lnz0+vXr3w8vKif//+bN++PTNiFEKIvC08HC5ffrTdoQPMnAmlS1suJiFEtvRM94q1srKiSZMmLF68mBs3bjBv3jz27dvHiy++mNHxCSFE3rZrF7z9NkyapC04DNq6dE5Olo1LCJEtPde9YsPCwli5ciXLli3j6NGjVK9ePaPiEkKIvC0mRrsl2KZN2nbhwtqkCbnPqxAiDWYndlFRUaxatYoVK1awbds2ihUrRrdu3Vi5ciUlSpTIjBiFECJvOXtWW5vu2jXt6lynTvDqq9o6dUIIkQazu2I9PT157733eOGFF9i9ezenT59m/PjxmZ7U3blzh6CgIPR6PXq9nqCgIO7evZvmPqtXr6Zp06a4ubmh0+k4cuRIsjoNGzZEp9OZ/HTt2jVzGiGEEGlRClatghEjtKTOzU2b8RoUJEmdECJdzPpNoZTiyy+/pEePHjhl8fiObt26ceXKFUJCQgDo378/QUFBrF27NtV97t+/T926denUqRPBwcGp1gsODuaDDz4wbjs6OmZc4EIIkV5KwaFDkJgIdepoCw7/tzi8EEKkh9mJ3cCBA2nUqBGBWXgPwpMnTxISEsKePXuoWbMmAPPnz6d27dqcPn2aUqVKpbhfUFAQABcuXEjz+E5OTnh5eWVozEIIkW5Kaf9aWcGQIXDkCLz0ktYNK4QQZjArsbOysiIwMJBbt25laWIXGhqKXq83JnUAtWrVQq/Xs3v37lQTu/Ravnw5y5Ytw9PTk2bNmjF+/HjjLdRSEhsbS2zS7DS0cYcABoMBg8HwXLGkh8FgQCmVJefKLqTNeUOea3NsLOqbb3C8fx/D8OFamasrvPiiluwlJXy5TJ57n5E25xWZ1WZzjmf2oI3JkyczYsQIvvrqK8qVK2fu7s8kLCwMjxRWVffw8CAsLOy5jt29e3eKFi2Kl5cXx48fZ8yYMfz1119s3Lgx1X0mTZrExIkTk5WHh4cTExPzXPGkh8FgIDIyEqUUVlbPtGJNjiNtljbnNtaXLuE8Zw5WV69ilZhIxEsvgZ+fpcPKEnnpfU4ibZY2P4/o6Oh01zU7sevRowcPHjygYsWK2NnZJRuPdvv27XQfa8KECSkmSI/bv38/ALoUuiSUUimWm+PxsXflypUjMDCQatWqcejQIapUqZLiPmPGjGHo0KHG7aioKPz8/HB3d8fFxeW54kkPg8GATqfD3d09T31ZpM25X55os1Kwdi26xYshIQHl5UV0z564Va6ce9v8hDzxPj9B2ixtfh4ODg7prmt2Yjd9+nRzd0nVwIEDnzoDNSAggKNHj3Ljxo1kz4WHh+Pp6Zlh8QBUqVIFW1tbzp49m2piZ29vj30Ka0lZWVll2YdXp9Nl6fmyA2lz3pCr23z3LkyfDgcPats1a6IGDiQxNjb3tjkVufp9ToW0OW/IjDabcyyzE7tevXqZu0uq3NzccHNze2q92rVrExkZyb59+6hRowYAe/fuJTIykjp16mRYPAB///038fHxeHt7Z+hxhRB5nFLw7rvarcHs7KBfP2jWTCu/edPS0QkhcgmzE7tLly6l+XyRIkWeOZjUlClThldeeYXg4GDmzZsHaMudtGzZ0mTiROnSpZk0aRLt2rUDtG7hS5cuce3aNQBOnz4NgJeXF15eXvzzzz8sX76c5s2b4+bmxokTJxg2bBiVK1embt26Gd4OIUQeptNBjx7w3XfaOnVJvytz6QQJIYRlmJ3YBQQEpDmuLTEx8bkCSs3y5csZNGgQTZo0AaB169bMmjXLpM7p06eJjIw0bq9Zs4Y+ffoYt5O6fcePH8+ECROws7Nj8+bNfPnll9y7dw8/Pz9atGjB+PHjsba2zpR2CCHykMuX4c4dqFBB265TB2rWBPn9IoTIJGYndocPHzbZjo+P5/Dhw0ydOpWPP/44wwJ7kqurK8uWLUuzjnrif769e/emd+/eqdb38/Nj+/btGRGeEEI8ohSEhMA332j3dp01S1vGBCSpE0JkKrMTu4oVKyYrq1atGj4+Pnz++ee0b98+QwITQogcKSoKZsyAvXu17XLltIWHhRAiC2TYzQdLlixpXJpECCHypL/+gqlT4fZt7d6uvXtD69ZyBwkhRJYxO7FLustCEqUU169fZ8KECVl6NwohhMg2lIIlS2D1au1x4cLaBIlixSwdmRAijzE7sStQoECyyRNKKfz8/Fi5cmWGBSaEEDmGTgf37mlJ3SuvwGuvaWPrhBAii5md2G3dutVk28rKCnd3d0qUKIGNTYb17AohRPamFMTFPUrgXnsNatWCatUsG5cQIk8zOxNr0KBBZsQhhBA5x717MHu29u8HH2hX7BwcJKkTQlhcuqdqnTt3joNJt8H5z+bNm2nUqBE1atTgk08+yfDghBAi2zl+HN5+G3btgmPH4OxZS0ckhBBG6U7sRowYwS+//GLcPn/+PK1atcLOzo7atWszadKkDL2PrBBCZCsJCbBsmXZbsIgI8PaGzz+HkiUtHZkQQhiluyv2wIEDjBw50ri9fPlySpYsyYYNGwCoUKECM2fOZPDgwRkepBBCWFRYGEyZAv/dlpCXXoLXXwdHR8vGJYQQT0j3FbuIiAgKFy5s3N66dSutWrUybjds2JALFy5kaHBCCGFxSsHkyVpS5+ysLWMyeLAkdUKIbCndiZ2rqyvXr18HwGAwcODAAWrWrGl8Pi4uLtktvYQQIsfT6eCtt7T7vc6YAfXrWzoiIYRIVboTuwYNGvDhhx9y+fJlpk+fjsFgoFGjRsbnT5w4QUBAQGbEKIQQWev0adi06dF28eLw8cfg4WG5mIQQIh3SPcbu448/5uWXXyYgIAArKytmzJiBs7Oz8fmlS5fy4osvZkqQQgiRJQwG+PFHWLFCu79rsWJy9wghRI6S7sSuaNGinDx5khMnTuDu7o6Pj4/J8xMnTjQZgyeEEDlKeLh2n9fjx7Xt//0PPD0tG5MQQpjJrAWKbW1tqVixYorPpVYuhBDZ3p9/wsyZcP++ttDwgAHQsKE2vk4IIXIQuQeYECJv++orWL9ee1yyJAwfrq1RJ4QQOZAkdkKIvM3LS7sy16kTvPoqyD2vhRA5mPwGE0LkLUrBnTvg6qptt20L5ctDiRIWDUsIITKCJHZCiLzj9m1tgsStWzBtmjaeTqeTpE4IkWukK7E7evRoug9YoUKFZw5GCCEyzd698OWXEB0N9vZw7hyUK2fpqIQQIkOlK7GrVKkSOp0OpRS6p8wSS0xMzJDAhBAiQ8TFwYIFjyZIFCum3RZMlmcSQuRC6Urszp8/b3x8+PBhhg8fzogRI6hduzYAoaGhfPHFF0yePDlzohRCiGdx4YJ2n9fLl7Xtdu0gKAhsbS0alhBCZJZ0JXb+/v7Gx506dWLGjBk0b97cWFahQgX8/PwYO3Ysbdu2zfAghRDimXz7rZbUFSwIQ4dCpUqWjkgIITKV2ZMnjh07RtGiRZOVFy1alBMnTmRIUEIIkSEGDoTFi6FfP9DrLR2NEEJkOitzdyhTpgwfffQRMTExxrLY2Fg++ugjypQpk6HBCSGEWQ4ehKVLH227umpX6iSpE0LkEWZfsZs7dy6tWrXCz8/PeBuxv/76C51Ox7p16zI8QCGEeKr4eFiyBH79VdsuWxaqVrVsTEIIYQFmJ3Y1atTg/PnzLFu2jFOnTqGUokuXLnTr1g1nZ+fMiFEIIVJ3+TJ8/jkkTfJq2VJbcFgIIfKgZ1qg2MnJif79+2d0LEIIkX5KwYYNMH++tqSJiwsMHgzVq1s6MiGEsBizx9gBLF26lP/973/4+Phw8eJFAKZNm8avSd0gQgiR2WbNgtmztaSucmWYOVOSOiFEnmd2YvfVV18xdOhQmjVrxp07d4wLEhcsWJDp06dndHxCCJGyGjXAxkab8Tpx4qN7vwohRB5mdmI3c+ZM5s+fz3vvvYeNzaOe3GrVqnHs2LEMDU4IIYwSErQFh5PUrKl1w7Ztq93vVQghhPmJ3fnz56lcuXKycnt7e+7fv58hQQkhhIlr12DkSHj3Xbh161G5m5vlYhJCiGzI7MSuaNGiHDlyJFn577//TtmyZTMiJiGE0CgFmzbBO+/A2bNa2fXrlo1JCCGyMbMTuxEjRvDWW2/x/fffo5Ri3759fPzxx7z77ruMGDEiM2IE4M6dOwQFBaHX69Hr9QQFBXH37t1U68fHxzNq1CjKly+Ps7MzPj4+9OzZk2vXrpnUi42N5e2338bNzQ1nZ2dat27NlStXMq0dQoh0un9fu8/rl19CTIy2hMmMGVCunKUjE0KIbMvs5U769OlDQkICI0eO5MGDB3Tr1g1fX1++/PJLunbtmhkxAtCtWzeuXLlCSEgIAP379ycoKIi1a9emWP/BgwccOnSIsWPHUrFiRe7cucPgwYNp3bo1Bw4cMNYbPHgwa9euZeXKlRQqVIhhw4bRsmVLDh48iLW1daa1RwiRhr//hmnTIDwcrK2hRw9o3x6snmkivxBC5BnPtI5dcHAwwcHBREREYDAY8PDwyOi4TJw8eZKQkBD27NlDzZo1AZg/fz61a9fm9OnTlCpVKtk+er2ejRs3mpTNnDmTGjVqcOnSJYoUKUJkZCQLFixg6dKlNG7cGIBly5bh5+fHpk2baNq0aaa2SwiRih07tKTO2xuGD4eSJS0dkRBC5AhmJ3Yvvvgiq1evpkCBArg9NnA5KiqKtm3bsmXLlgwNECA0NBS9Xm9M6gBq1aqFXq9n9+7dKSZ2KYmMjESn01GgQAEADh48SHx8PE2aNDHW8fHxoVy5cuzevTvVxC42NpbY2FjjdlRUFAAGgwGDwWBu88xmMBhQSmXJubILaXPeYGxz797g7AydOoGjI+Ti1yBPv8/S5lxN2pyxx00vsxO7bdu2ERcXl6w8JiaGnTt3mnu4dAkLC0vxqqCHhwdhYWHpOkZMTAyjR4+mW7duuLi4GI9rZ2dHwYIFTep6enqmedxJkyYxceLEZOXh4eHExMSkK57nYTAYiIyMRCmFVR7pmpI25+422+7ejd3+/UQPHEhkVJTW5ldegeho7ScXy0vvcxJps7Q5t8qsNkeb8Xsw3Ynd0aNHjY9PnDhhkvgkJiYSEhKCr69vuk8MMGHChBQTpMft378fAF0K61QppVIsf1J8fDxdu3bFYDAwZ86cp9Z/2nHHjBnD0KFDjdtRUVH4+fnh7u5uTBozk8FgQKfT4e7unqe+LNLmXOjBA5g3D93WrQA4/f03uvLlc3ebn5An3ucnSJulzblVZrXZwcEh3XXTndhVqlQJnU6HTqfjxRdfTPa8o6MjM2fOTPeJAQYOHPjUCRcBAQEcPXqUGzduJHsuPDwcT0/PNPePj4+nc+fOnD9/ni1btpgkXl5eXsTFxXHnzh2Tq3Y3b96kTp06qR7T3t4ee3v7ZOVWVlZZ9uHV6XRZer7sQNqcy5w+DVOmQFiYNini1VfRNWqE7tat3NvmVOTq9zkV0ua8QdqcMcw5VroTu/Pnz6OUolixYuzbtw93d3fjc3Z2dnh4eJg9i9TNzc1knF5qateuTWRkJPv27aNGjRoA7N27l8jIyDQTsKSk7uzZs2zdupVChQqZPF+1alVsbW3ZuHEjnTt3BuD69escP36cyZMnm9UWIUQ6GQzw00+wfLn22MNDmyBRpkyuHksnhBBZId2Jnb+/P2DeAL6MUqZMGV555RWCg4OZN28eoC130rJlS5OJE6VLl2bSpEm0a9eOhIQEOnbsyKFDh1i3bh2JiYnG7mNXV1fs7OzQ6/X069ePYcOGUahQIVxdXRk+fDjly5c3zpIVQmSwOXNgwwbtcf36MGCANlFCCCHEczN78sSkSZPw9PSkb9++JuULFy4kPDycUaNGZVhwj1u+fDmDBg0yzmBt3bo1s2bNMqlz+vRpIiMjAbhy5Qpr1qwBtG7kx23dupWGDRsCMG3aNGxsbOjcuTMPHz7kpZdeYvHixbKGnRCZpXlz2L0bXnsNGjWS+7wKIUQGMjuxmzdvHitWrEhW/sILL9C1a9dMS+xcXV1ZtmxZmnWUUsbHAQEBJtupcXBwYObMmWaPDxRCpFNMDJw4AVWqaNvFisHChWDGYGAhhBDpY/bIvrCwMLy9vZOVu7u7c13u4SiEeNw//8DgwfDBB4/u9QqS1AkhRCYxO7Hz8/Pjzz//TFb+559/4uPjkyFBCSFyOKXg55+1SRFXr0KBAhAfb+mohBAi1zO7K/a1115j8ODBxMfHG5c92bx5MyNHjmTYsGEZHqAQIoe5fVu7z+uRI9p27drw9tuQP79FwxJCiLzA7MRu5MiR3L59mwEDBhjvQOHg4MCoUaMYM2ZMhgcohMhB9u+H6dMhKgrs7CA4GJo2lQkSQgiRRcxO7HQ6HZ999hljx47l5MmTODo6EhgYmOKCvUKIPObaNS2pK1YMRoyAwoUtHZEQQuQpZid2SfLly0f16tUzMhYhRE6UmAhJywO1bg329vDSS2Bra9m4hBAiD0pXYte+fXsWL16Mi4sL7du3T7Pu6tWrMyQwIUQ2pxSsWwd//AGff67NdNXp4JVXLB2ZEELkWelK7PR6Pbr/xsjo9fpMDUgIkQNERmpj6Q4c0LY3boRWrSwakhBCiHQmdosWLUrxsRAiDzp0SJv1eveu1t3apw+0bGnpqIQQQvAcY+yEEHlMfDwsWQK//qptFymiTZAICLBoWEIIIR5JV2JXuXJlY1fs0xw6dOi5AhJCZFOLFsHatdrjFi2gb19tSRMhhBDZRroSu7Zt2xofx8TEMGfOHMqWLUvt2rUB2LNnD3///TcDBgzIlCCFENlAx47aosO9e0ONGpaORgghRArSldiNHz/e+Pi1115j0KBBfPjhh8nqXL58OWOjE0JYTnQ0/Pnno1murq4we7YsNiyEENmY2WPsfvzxRw4kzYR7TI8ePahWrRoLFy7MkMCEEBZ09ChMnQq3bmm3AqtbVyuXpE4IIbI1sxM7R0dHdu3aRWBgoEn5rl27cHBwyLDAhBAWkJAAy5fDqlXaOnW+vuDlZemohBBCpJPZid3gwYN58803OXjwILVq1QK0MXYLFy5k3LhxGR6gECKLXL+uLTR89qy23bQpvPaatvCwEEKIHMHsxG706NEUK1aML7/8khUrVgBQpkwZFi9eTOfOnTM8QCFEFti5E2bMgJgYyJcP3n4b6tSxdFRCCCHM9Ezr2HXu3FmSOCFyEwcHLakrVw6GDQM3N0tHJIQQ4hk8U2J39+5dfvrpJ/7991+GDx+Oq6srhw4dwtPTE19f34yOUQiRGR48ACcn7XH16jBxIlSqBFZWFg1LCCHEszM7sTt69CiNGzdGr9dz4cIFXnvtNVxdXfn555+5ePEi3377bWbEKYTIKImJ8P33sH69dr/XpKtzVapYNCwhhBDPz+z/mg8dOpTevXtz9uxZk1mwzZo1Y8eOHRkanBAig928CWPGwHffQWQkyHdWCCFyFbOv2O3fv5958+YlK/f19SUsLCxDghJCZIIdO7QFhpO6YAcMgAYNLB2VEEKIDGR2Yufg4EBUVFSy8tOnT+Pu7p4hQQkhMtDDhzBvHmzerG2XLg3Dh4Onp2XjEkIIkeHM7opt06YNH3zwAfHx8QDodDouXbrE6NGj6dChQ4YHKIR4TqtXa0mdTgddu8Knn0pSJ4QQuZTZid2UKVMIDw/Hw8ODhw8f0qBBA0qUKEH+/Pn5+OOPMyNGIcTz6NQJqlXTErru3cHa2tIRCSGEyCRmd8W6uLiwa9cutmzZwqFDhzAYDFSpUoXGjRtnRnxCCHNFRMDatdCrl7Z0iZ0djB9v6aiEEEJkAbMSu4SEBBwcHDhy5AgvvvgiL774YmbFJYR4Frt3w8yZcO8e5M8PHTtaOiIhhBBZyKzEzsbGBn9/fxITEzMrHiHEs4iJgQULICRE2w4MlFuCCSFEHmT2GLv333+fMWPGcPv27cyIRwhhrn//hSFDtKROp9Ou0k2eDD4+lo5MCCFEFjN7jN2MGTM4d+4cPj4++Pv74+zsbPL8oUOHMiw4IcRTbN0KM2ZAQgK4umr3ea1QwdJRCSGEsBCzE7s2bdqg0+kyIxYhhLmKFtWu0tWqBYMGaePqhBBC5FlmJ3YTJkzIhDCEEOl248ajdegCAmDaNChSREvwhBBC5GnpHmP34MED3nrrLXx9ffHw8KBbt25ERERkZmxCiMfFxWl3kHjjDThz5lG5v78kdUIIIQAzErvx48ezePFiWrRoQdeuXdm4cSNvvvlmZsZm4s6dOwQFBaHX69Hr9QQFBXH37t1U68fHxzNq1CjKly+Ps7MzPj4+9OzZk2vXrpnUa9iwITqdzuSna9eumdwaIcx08SIMHQrr1mnj6Y4ds3REQgghsqF0d8WuXr2aBQsWGJOeHj16ULduXRITE7HOgpXsu3XrxpUrVwj5bzmH/v37ExQUxNq1a1Os/+DBAw4dOsTYsWOpWLEid+7cYfDgwbRu3ZoDBw6Y1A0ODuaDDz4wbjs6OmZeQ4Qwh1JaMrd4McTHQ4ECMHgwVK1q4cCEEEJkR+lO7C5fvky9evWM2zVq1MDGxoZr167h5+eXKcElOXnyJCEhIezZs4eaNWsCMH/+fGrXrs3p06cpVapUsn30ej0bN240KZs5cyY1atTg0qVLFClSxFju5OSEl5dXprZBCLNFRuI8bRq6Eye0rtZq1bSkTq+3dGRCCCGyqXQndomJidjZ2ZnubGNDQkJChgf1pNDQUPR6vTGpA6hVqxZ6vZ7du3enmNilJDIyEp1OR4ECBUzKly9fzrJly/D09KRZs2aMHz+e/GnMLoyNjSU2Nta4HRUVBYDBYMBgMJjRsmdjMBhQSmXJubKLvNhm9eef2B4+jMqXD9W7N7RsqSV4ufg1yIvvs7Q5b5A25w2Z1WZzjpfuxE4pRe/evbG3tzeWxcTE8MYbb5isZbd69ep0nzy9wsLC8PDwSFbu4eFBWFhYuo4RExPD6NGj6datGy4uLsby7t27U7RoUby8vDh+/Dhjxozhr7/+Sna173GTJk1i4sSJycrDw8OJiYlJVzzPw2AwEBkZiVIKKyuz15jOkfJkmytXRlevHlZNm6L8/SE83NIhZbo8+T5Lmy0dTpaQNkubn0d0dHS666Y7sevVq1eysh49eqT7RCmZMGFCignS4/bv3w+Q4tp5Sql0rakXHx9P165dMRgMzJkzx+S54OBg4+Ny5coRGBhItWrVOHToEFWqVEnxeGPGjGHo0KHG7aioKPz8/HB3dzdJGjOLwWBAp9Ph7u6ep74sub7NV67A8uXaenSOjhgMBsKDgymUm9v8hDzxPj9B2ixtzq2kzRnXZgcHh3TXTXdit2jRomcKJi0DBw586gzUgIAAjh49yo0bN5I9Fx4ejmfSel6piI+Pp3Pnzpw/f54tW7Y8NfGqUqUKtra2nD17NtXEzt7e3uTKZRIrK6ss+/DqdLosPV92kGvbrBRs3Ahffw2xsdodJF5/HcjFbU6DtDlvkDbnDdLmjGHOscxeoDgjubm54ebm9tR6tWvXJjIykn379lGjRg0A9u7dS2RkJHXSuNF5UlJ39uxZtm7dSqFChZ56rr///pv4+Hi8vb3T3xAhnlV0NMyaBbt3a9uVKkGnThYNSQghRM5l0cQuvcqUKcMrr7xCcHAw8+bNA7TlTlq2bGkycaJ06dJMmjSJdu3akZCQQMeOHTl06BDr1q0jMTHROB7P1dUVOzs7/vnnH5YvX07z5s1xc3PjxIkTDBs2jMqVK1O3bl2LtFXkIceOwRdfwK1bYGMDPXtC27ay2LAQQohnliMSO9Bmrg4aNIgmTZoA0Lp1a2bNmmVS5/Tp00RGRgJw5coV1qxZA0ClSpVM6m3dupWGDRtiZ2fH5s2b+fLLL7l37x5+fn60aNGC8ePHZ8nafCIP27pVuxWYUuDrC8OHQ4kSlo5KCCFEDpdjEjtXV1eWLVuWZh2llPFxQECAyXZK/Pz82L59e4bEJ4RZKlcGFxeoUQP69wczBsYKIYQQqckxiZ0QOZpScOoUlCmjbRcoALNny2LDQgghMlTemaYihKXcvw9TpsDIkbBr16NySeqEEEJkMLliJ0RmOnlSS+pu3gQrK22ihBBCCJFJJLETIjMkJsKPP8KKFVo3rKcnjBgB6bz9nRBCCPEsJLETIqPdvKktY3LihLbdqBG88QY4OVk2LiGEELmeJHZCZLTLl7WkztERBgyAhg0tHZEQQog8QhI7ITKCUo8WFq5aVbslWLVq4OVl2biEEELkKTIrVojndeYMDBumdcEmadlSkjohhBBZThI7IZ6VwaBNkBg5Es6ehcWLLR2REEKIPE66YoV4FhER2i3Bjh7VtuvW1cbTCSGEEBYkiZ0Q5goNhZkzIToa7O218XSNGz8aYyeEEEJYiCR2Qphj1y747DPtcYkSMHw4+PpaNiYhhBDiP5LYCWGOGjWgaFGoUgV69AAb+QoJIYTIPuSvkhBpUQp27oT//U+7JZidnXaLMDs7S0cmhBBCJCOJnRCpuXNHmyBx+DCEhUHnzlq5JHVCCCGyKUnshEjJ/v3w5ZcQGaklcnq9pSMSQgghnkoSOyEeFxenrUe3dq22XbQojBgBfn4WDUsIIYRID0nshEhy+bI24/XiRW27TRvo2VO6XoUQQuQYktgJkcRggOvXtW7XIUO0e74KIYQQOYgkdiJvS0h4tGSJvz+MHg2BgVCggEXDEkIIIZ6F3CtW5F1HjkBwMJw+/aisenVJ6oQQQuRYktiJvCchARYuhLFjtXu+fv+9pSMSQgghMoR0xYq85epVmDwZ/v1X227eHPr2tWxMQgghRAaRxE7kDUrBxo3w9dcQGwv588M770DNmpaOTAghhMgwktiJvOHAAZg5U3tcsSIMHQqurpaNSQghhMhgktiJvKFaNahTB0qVgnbtQKezdERCCCFEhpPETuROCQnw66/QrBk4OWmJ3OjRktAJIYTI1SSxE7nP9eswZQqcOaPdRWLoUK1ckjohhBC5nCR2IvdQCrZuha++gpgYcHaGGjUsHZUQQgiRZSSxE7nD/fswZw7s2KFtv/ACDBsG7u6WjUsIIYTIQpLYiZzvwgX48EO4eROsrKBbN+jUSXsshBBC5CGS2Imcr2BBiIsDT08YPhxKl7Z0REIIIYRF5JhLGnfu3CEoKAi9Xo9erycoKIi7d++muc+ECRMoXbo0zs7OFCxYkMaNG7N3716TOrGxsbz99tu4ubnh7OxM69atuXLlSia2RGSI6OhHj/V6mDABvvxSkjohhBB5Wo5J7Lp168aRI0cICQkhJCSEI0eOEBQUlOY+JUuWZNasWRw7doxdu3YREBBAkyZNCA8PN9YZPHgwP//8MytXrmTXrl3cu3ePli1bkpiYmNlNEs9q504IDn40ng6geHFtsoQQQgiRh+WIrtiTJ08SEhLCnj17qPnfLaDmz59P7dq1OX36NKVKlUpxv27duplsT506lQULFnD06FFeeuklIiMjWbBgAUuXLqVx48YALFu2DD8/PzZt2kTTpk0zt2HCPA8falfltmzRtjdvhnr1ZBkTIYQQ4j854opdaGgoer3emNQB1KpVC71ez+7du9N1jLi4OL7++mv0ej0VK1YE4ODBg8THx9OkSRNjPR8fH8qVK5fu44oscvYsLmPHotu8WUvkunaFsWMlqRNCCCEekyOu2IWFheHh4ZGs3MPDg7CwsDT3XbduHV27duXBgwd4e3uzceNG3NzcjMe1s7OjYMGCJvt4enqmedzY2FhiY2ON21FRUQAYDAYMBkO62/WsDAYDSqksOZfFKQWrVsHy5Vg9eIDy8UENGwblymnP5+LXIE+9z/+RNucN0ua8QdqcscdNL4smdhMmTGDixIlp1tm/fz8AuhSuzCilUix/XKNGjThy5AgRERHMnz+fzp07s3fv3hQTxfQed9KkSSnGHR4eTkxMTJrxZASDwUBkZCRKKaxy+ZIe1mfPkv+bb0ApoipUIPGNN9Dlz68tbZLL5aX3OYm0WdqcW0mbpc3PI/rxCYNPYdHEbuDAgXTt2jXNOgEBARw9epQbN24key48PBxPT88093d2dqZEiRKUKFGCWrVqERgYyIIFCxgzZgxeXl7ExcVx584dk6t2N2/epE6dOqkec8yYMQxNuk0V2hU7Pz8/3N3dcXFxSTOejGAwGNDpdLi7u+f+L4uHB3TvjvLyIrF8edw9PHJ/m/+Tp97n/0ibpc25lbRZ2vw8HBwc0l3Xoomdm5ubsVs0LbVr1yYyMpJ9+/ZR479bRO3du5fIyMg0E7CUKKWM3ahVq1bF1taWjRs30rlzZwCuX7/O8ePHmTx5cqrHsLe3x97ePlm5lZVVln14dTpdlp4vy8TGwtKl0Lq1ltQB9OqlfVlu3sydbU5Drn2f0yBtzhukzXmDtDljmHOsHPFKlylThldeeYXg4GD27NnDnj17CA4OpmXLliYzYkuXLs3PP/8MwP3793n33XfZs2cPFy9e5NChQ7z22mtcuXKFTp06AaDX6+nXrx/Dhg1j8+bNHD58mB49elC+fHnjLFmRhf79F4YMgV9/hS++0MbXCSGEECLdcsTkCYDly5czaNAg4wzW1q1bM2vWLJM6p0+fJjIyEgBra2tOnTrFkiVLiIiIoFChQlSvXp2dO3fywgsvGPeZNm0aNjY2dO7cmYcPH/LSSy+xePFirK2ts65xeZ1SsGYNLF4MCQng6gqvviozXoUQQggz5ZjEztXVlWXLlqVZRz12hcfBwYHVq1c/9bgODg7MnDmTmTNnPneM4hncvQvTp8PBg9p2zZowaBBkwVhFIYQQIrfJMYmdyIUuXoT33oPISLCzg379oFkzuVInhBBCPCNJ7ITl+Pho3a4FC8KIEVCkiKUjEkIIIXI0SexE1rp2DTw9wdoabG1h3Dit29XOztKRCSGEEDlejpgVK3IBpWD9enj7bfjxx0flbm6S1AkhhBAZRK7YicwXFQUzZsDevdr2mTNaoidj6YQQQogMJYmdyFxHjsC0aXD7NtjYQO/e2uLDktQJIYQQGU4SO5E5EhK0O0gkLTnj56dNkCha1LJxCSGEELmYJHYic9y4AevWaY+bNdOWMknhNmxCCCGEyDiS2InM4esLAwaAszPUqmXpaIQQQog8QWbFioxx7x5MngwnTz4qe+klSeqEEEKILCRX7MTzO34cvvgCIiLgn39gzhxtnTohhBBCZClJ7MSzS0iA777T1qVTCry9YfhwSeqEEEIIC5HETjybsDCYMgVOn9a2GzeG118HBwfLxiWEEELkYZLYCfNdvQpDhsDDh9rkiLfegnr1LB2VEEIIkedJYifM5+MDFSpAdDQMGwYeHpaOSAghhBBIYifS6/RpKFxYu0Kn08HQodq6dDKeTgghhMg2ZLkTkTaDAVauhJEjYe7cR+VOTpLUCSGEENmMXLETqQsP15Yx+ftvbVspbSasjXxshBBCiOxI/kKLlO3aBbNmwf374OgIb74JjRpZOiohhBBCpEESO2EqJga+/ho2btS2S5XSJkh4e1s2LiGEEEI8lSR2wlRcHBw8qE2Q6NQJXn1Vul6FEEKIHEL+Ygtt7JxOpz12cdHuHqHTQblylo1LCCGEEGaRWbF53e3bMHYsbN36qKx8eUnqhBBCiBxIrtjlZXv3wpdfagsNX7oEdeuCnZ2loxJCCCHEM5LELi+KjYWFC2H9em27WDFtnTpJ6oQQQogcTRK7vObCBZg8GS5f1rbbtYOgILC1tWhYQgiRWRITE4mPj7doDAaDgfj4eGJiYrCyyhujoKTN6W+ztbU1NjY26JLGuz8HSezyktu3taVL4uKgYEHttmCVKlk6KiGEyDT37t3jypUrKKUsGodSCoPBQHR0dIb88c4JpM3mtdnJyQlvb2/snrP3TBK7vMTVFVq0gKtXYdAg0OstHZEQmSryQRwR9+KIionHxdEWN2c79E4y5CCvSExM5MqVKzg5OeHu7m7R5EIpRUJCQoZdlckJpM3pa7NSiri4OMLDwzl//jyBgYHPdYVTErvc7uBB8PUFLy9tu1cvsLJ6tLyJELnUtbsPGbXqKDvPRhjL6ge68WmHCvgUcLRgZCKrxMfHo5TC3d0dR0fLvueS5Eib0+Lo6IitrS0XL14kLi4OBweHZ44hb3R650VxcTB/PkyYAFOmQGKiVm5tLUmdyPUiH8QlS+oAdpyNYPSqo0Q+iLNQZMIS8kpSIXK2jBqHKFfscqPLl7UJEhcuaNslS4LBoCV1QuQBEffikiV1SXacjSDiXpx0yQohciW5YpebKKUtYTJ4sJbUubjAuHHQv7/MehV5SlRM2jMgo5/yvBC51ddff42fnx9WVlZMnz7d0uFkim3btqHT6bh79y4AixcvpkCBAs91zIw4RlaRxC63uH8fPv4YvvpK64atXBlmzYLq1S0dmRBZzsUh7f/I5H/K80JYUu/evdHpdOh0OmxtbSlWrBjDhw/n/v37z3XcqKgoBg4cyKhRo7h69Sr9+/d/7ljTm/AsXrzY2CadToe3tzedO3fm/Pnzzx3D03Tp0oUzZ86ku35AQECypNfcY1hSjkns7ty5Q1BQEHq9Hr1eT1BQkDEbT82ECRMoXbo0zs7OFCxYkMaNG7N3716TOg0bNjT5sOl0Orp27ZqJLckkdnYQHg42NvDaazBxorakiRB5kFs+O+oHuqX4XP1AN9zySTesyN5eeeUVrl+/zr///stHH33EnDlzGD58+DMdK2lA/6VLl4iPj6dFixZ4e3vj5OSUwVGnzcXFhevXr3Pt2jVWrFjBkSNHaN26NYlJY8BTiDkjODo64uHhYfFjZJUck9h169aNI0eOEBISQkhICEeOHCEoKCjNfUqWLMmsWbM4duwYu3btIiAggCZNmhAeHm5SLzg4mOvXrxt/5s2bl5lNyTgJCY8mRdjaaneP+OILaNNGJkiIPE3vZMenHSokS+7qB7rxWYcKMr5OZHv29vZ4eXnh5+dHt27d6N69O7/88gugJT2TJ0+mWLFiODo6UrFiRX766SfjvkldkRs2bKBatWrY29uzdOlSypcvD0CxYsXQ6XRc+G8c9tq1a6latSoODg4UK1aMiRMnmiRVd+/epX///nh6euLg4EC5cuVYt24d27Zto0+fPkRGRhovjEyYMCHVNul0Ory8vPD29qZRo0aMHz+e48ePc+7cuRRj3rlz51PbCrB+/XpKliyJo6MjjRo1MrYrSUpXFdesWUO1atVwcHDAzc2N9u3bA9rFnosXLzJkyBBjm1I7xldffUXx4sWxs7OjVKlSLF26NFl7v/nmG9q1a4eTkxOBgYGsWbMm1dcnw6gc4MSJEwpQe/bsMZaFhoYqQJ06dSrdx4mMjFSA2rRpk7GsQYMG6p133nmu+JKOGxkZ+VzHSa/ExER149AhZXjnHaWWLcuSc1paYmKiun79ukpMTLR0KFlG2vz87t6PVeduRKvDF2+rczei1d37sRly3Iwk73PmefjwoTpx4oR6+PDhk0+k/hMbm/F1lVIGg0HFxcUpg8Hw1Lh79eql2rRpY1L29ttvq0KFCimllHr33XdV6dKlVUhIiPrnn3/UokWLlL29vdq2bZtSSqmtW7cqQFWoUEH98ccf6ty5c+rKlStq06ZNClD79u1T169fVwkJCSokJES5uLioxYsXq3/++Uf98ccfKiAgQE2YMEEppb1XtWrVUi+88IL6448/1D///KPWrl2r1q9fr2JjY9X06dOVi4uLun79urp+/bqKjo42xvx4mxctWqT0er1Jm1atWqUAdezYsRRjjoiIeGpbL126pOzt7dU777yjTp06pZYtW6Y8PT0VoO7cuaOUUsnOvW7dOmVtba3GjRunTpw4oY4cOaI+/vhjpZRSt27dUoULF1YffPCBsU0pHWP16tXK1tZWzZ49W50+fVp98cUXytraWm3evNnYZkAVLlxYrVixQp09e1YNGjRI5cuXT926dSvF9z3Vz6syL8/IEYndggULkn0glFJKr9erhQsXpusYsbGx6vPPP1d6vV6Fh4cbyxs0aKDc3NxUoUKFVNmyZdWwYcNUVFSUWfFlaWJnMKjEkBD1sGVLZWjRQqnu3ZW6dy/zz2th8scvb5A25w0WT+xatkz957+ExqhDh9Trjh5tWrdbt5TrqedL7Pbu3asKFSqkOnfurO7du6ccHBzU7t27Tfbp16+fevXVV5VSjxK7X375xaTO4cOHFaDOnz9vLKtXr5765JNPTOotXbpUeXt7K6WU2rBhg7KyslKnT59OMdaUErYkaSV2ly9fVrVq1VKFCxdWsbGxKcacnraOGTNGlSlTxuR1HTVqVJqJXe3atVX37t1TjFkppfz9/dW0adPSbGedOnVUcHCwSZ1OnTqp5s2bmyR277//vkl7dDqd+v3331M8b0YldjliuZOwsLAU+7Y9PDwICwtLc99169bRtWtXHjx4gLe3Nxv/3969R0VVrn8A/84wF5DLqAxxUQQVBG+IgIZUimlCwdKOEZJY6uG41LXMypMmv0owu1BrHbXL0VYsBDvK8Qaa6coTlprGeOOWiHjU8FJqCCEXjcs4z+8Pm33cMAyDDhc3z2ctVs7ez977/c47Ey979rsnJwda7f8+nomPj8fAgQPh5uaG4uJiJCYmoqioCDk5Oa3us6GhAQ0NDcLjmpoaAHe/I85gMLQ3nuXq6oB164AjR4CGBlBICGjJEsDO7u7tTCTMYDAIX9XSU3DmnoEzd/xxjD8WIbr7Y83aP2uo2X/N2bNnDxwcHKDX69HU1IRp06bhk08+wenTp1FfX4+nnnpKVN/Y2IjRo0eLsgYHB4uOde/xjf/Oy8vDiRMn8N577wl1d+7cQX19PW7duoWCggL0798fvr6+JtvdVqZ711dXV8PBwQFEhNu3byMoKAhZWVlQKpUm22xJ1jNnzuDRRx8VHSs0NFSUs3kbCwsL8be//c1sPzR/zTTfx5kzZzBv3jxRTVhYGD755BPRspEjRwqPe/XqBUdHR/z222+tPpfG90Xz90Z73itdOrBLTk7GypUrzdacOHECgOkbTBJRmzeenDhxIgoLC1FRUYHU1FTExsbi2LFjwkBx3rx5Qu2IESPg6+uLkJAQ5OfnIygoyOQ+P/jgA5PtvnHjBurr6822534pzp5Fr/XrIf/9d5BcjsqoKMhjYiC/cwcoL++QY3YnBoMB1dXVIKIe9WXSnFn6OHPHZW5qaoLBYIBerxdfiJ+Z2fpGcvnd65eN0tMtr/38c9N1ej2ISJgk0NbvLYPBgPDwcHz66adQKpXw8PCA8s9bVp0/fx4A8NVXX8HDw0O0nVqthl6vF45jfPy/ZuiF/xr/bTAYsGLFCjz77LMt2qFQKKBWq0Xbmmpra+vvzWwwGODo6Ihjx45BLpfD1dUV9vb2wram2tzU1GRRVmo20cK4L2PO5m20s7MTXhetab7eVM7mNcbj3jsZRC6Xi2pkMlnL1+OfjG2trKwU+tuotra21bY216UDu0WLFrU5A9Xb2xs//fQTfvvttxbrbty4AVdXV7Pb29vbw8fHBz4+PggNDYWvry/S0tKQmJhosj4oKAhKpRLnzp1rdWCXmJiIJUuWCI9ramrg6ekJFxcXODk5mW3Pfamrg+yzz4A//gC8vHBnyRLY9O4NFxeXHvWLQCaTcWaJ48yc2Zrq6+tRW1sLhUIBheKeX3cODpbvxMq1zX9hmyKXy+Hg4AB/f/8W60aOHAm1Wo1ff/0VTz75pMntbf68GX3z3MZ/37s8KCgI586dM3ksAAgMDMQvv/yCn3/+GUOGDGmx3tbWFnfu3BE/v80olUrI5XLI5fJWj2OqzZZkHT58OL766ivR8Y0nhIz7Mr7GjDUBAQE4ePAgEhISTO5TpVKBiET7bL6PoUOHQqfTYe7cuULNsWPHMGzYMNjY2Aj9bGNj0+K5kcvlJp8vY1udnZ1bfKVYe75irEsHdlqtVvSxaGvGjRuH6upqHD9+HGPHjgVw9wmsrq5GWFhYu45JRKKPUZs7ffo0mpqa4O7u3mqNWq0W/oq5l/GFa3VOTndvYVJaCsyfD7lKBVl5eccdr5uSyWScuQfgzD1DZ2SWy+WiW1l1pXs/YbK0LabqnJyc8Prrr2PJkiUgIjz++OOoqalBbm4uHBwcMHv2bNFx7t2HqeUrVqxAdHQ0BgwYgOeffx5yuRw//fQTTp06hXfffRfh4eEYP348YmJisHr1avj4+KC0tBQymQyRkZEYOHAg6urq8P3332PUqFHo1auXcBsVU5lby26qbZZkXbhwIVavXo2///3vmD9/PvLy8rBx40bRvpofOykpCZMmTcLgwYMRFxcHvV6Pb775BsuWLQNw94TS4cOH8cILL0CtVkOr1bbYx9KlSxEbG4vg4GBMmjQJX3/9NbKzs5GTk9PiOW+eubXXo3G5qfdFu94nbV6F101ERkZSQEAA6XQ60ul0NHLkSIr+86JUIz8/P8rOziaiuxcpJiYmkk6no4sXL1JeXh4lJCSQWq2m4uJiIiI6f/48rVy5kk6cOEFlZWW0d+9e8vf3p9GjR5Ner7e4bZ0yeeKeC0P5YuuegTP3DJy545i7GL2zPeis2Ob7+vjjj8nPz4+USiW5uLhQREQEHTp0iIj+N3nCOHnAyNTkCSKiffv2UVhYGNnZ2ZGTkxONHTuWvvjiC2F9ZWUlzZ07l5ydncnW1pZGjBhBe/bsEdYvWLCAnJ2dCQAlJSWZzGxukoW5NreVlYjo66+/Jh8fH1Kr1fTEE0/Qhg0bzE6eILo7IzcwMJBUKhVptVqaPn26sE6n01FAQACp1WoyDpNM7WPdunU0aNAgUiqVNGTIEPryyy9FmQHQzp07RdtoNBpKT083+RxYa/KEjMjSq0S71u+//47FixcL94CZOnUqPvvsM9F9ZWQyGdLT0zFnzhzU19dj5syZOHbsGCoqKuDs7IwxY8bgrbfewpg/v43hypUrmDVrFoqLi1FXVwdPT09ERUUhKSkJffv2tbhtNTU10Gg0qK6u7piPYpsxGAwoLy/HI4880mP+wufMnFmqOHPHfhRbVlaGgQMHtuujrI5Af14HplAouvzsYWfhzO3LbO712p5xxkMxKxYA+vbti02bNpmtuXeMamtri+zsbLP1np6eOHTokFXaxxhjjDHW1XrGn4eMMcYYYz0AD+wYY4wxxiSCB3aMMcYYYxLBAzvGGGOMMYnggR1jjDFJe0hu/sB6OGu9TnlgxxhjTJKM32bQ2NjYxS1hrG23b98GYNm3k5jz0NzuhDHGGGsPhUKBXr164caNG8LXWnUVvqcbZza3ze3bt1FeXo7evXsLf5DcLx7YMcYYkySZTAZ3d3eUlZXh0qVLXdoWIoLBYBC+5qwn4Mzty9y7d2+4ubk9cBt4YMcYY0yyVCoVfH19u/zjWIPBgMrKSjg7O/eobxjhzJZRKpUPfKbOiAd2jDHGJE0ul3f5V4oZDAYolUrY2tr2qEEOZ+58PeOZZowxxhjrAXhgxxhjjDEmETywY4wxxhiTCL7GzgqMNxWsqanplOMZDAbU1tb2uOsWOLP0cWbOLFWcmTM/COP4wpKbGPPAzgpqa2sBAJ6enl3cEsYYY4xJVW1tLTQajdkaGfF3rTwwg8GAq1evwtHRsVPu1VNTUwNPT09cuXIFTk5OHX687oAzc2ap4sycWao4s/UyExFqa2vh4eHR5plAPmNnBXK5HP379+/04zo5OfWYN4sRZ+4ZOHPPwJl7Bs5sHW2dqTPqGR96M8YYY4z1ADywY4wxxhiTCB7YPYTUajWSkpKgVqu7uimdhjP3DJy5Z+DMPQNn7ho8eYIxxhhjTCL4jB1jjDHGmETwwI4xxhhjTCJ4YMcYY4wxJhE8sOuGqqqq8OKLL0Kj0UCj0eDFF1/EzZs3zW6TnJwMf39/2Nvbo0+fPpg8eTKOHTsmqgkPD4dMJhP9xMXFdWASy3VU5oaGBrz88svQarWwt7fH1KlT8csvv3RgEsu1N3NTUxPeeOMNjBw5Evb29vDw8MBLL72Eq1eviuqk1M+WZpZSPwNAdnY2IiIioNVqIZPJUFhY2KJGSv0MWJZZav1MREhOToaHhwfs7OwQHh6O06dPi2q6Wz+vW7cOAwcOhK2tLYKDg3H48GGz9YcOHUJwcDBsbW0xaNAgfP755y1qsrKyMGzYMKjVagwbNgw7d+7sqObfF2tnzsjIaNGnMpkM9fX11mkwsW4nMjKSRowYQbm5uZSbm0sjRoyg6Ohos9ts3ryZcnJy6MKFC1RcXEwJCQnk5ORE5eXlQs2ECRNo3rx5dO3aNeHn5s2bHR3HIh2VecGCBdSvXz/Kycmh/Px8mjhxIo0aNYr0en1HR2pTezPfvHmTJk+eTFu3bqXS0lLS6XT06KOPUnBwsKhOSv1saWYp9TMR0ZdffkkrV66k1NRUAkAFBQUtaqTUz0SWZZZaP6ekpJCjoyNlZWXRqVOnaMaMGeTu7k41NTVCTXfq5y1btpBSqaTU1FQqKSmhV155hezt7enSpUsm63/++Wfq1asXvfLKK1RSUkKpqamkVCppx44dQk1ubi7Z2NjQ+++/T2fOnKH333+fFAoFHT16tLNimdURmdPT08nJyUnUp9euXbNam3lg182UlJQQANGLWqfTEQAqLS21eD/V1dUEgPbv3y8smzBhAr3yyivWbK5VdFTmmzdvklKppC1btgg1v/76K8nlctq3b5/1AtwHa2U+fvw4ARD9T0bq/dw8s5T7uayszOzATor93FpmqfWzwWAgNzc3SklJEZbV19eTRqOhzz//XFjWnfp57NixtGDBAtEyf39/Wr58ucn6ZcuWkb+/v2jZ/PnzKTQ0VHgcGxtLkZGRopqIiAiKi4uzUqsfTEdkTk9PJ41GY/W2GvFHsd2MTqeDRqPBo48+KiwLDQ2FRqNBbm6uRftobGzEF198AY1Gg1GjRonWbd68GVqtFsOHD8frr7+O2tpaq7b/fnRU5ry8PDQ1NWHKlClCnYeHB0aMGGHxfjuKNTIDQHV1NWQyGXr37i1aLtV+Blpm7gn93Bop93NzUuvnsrIyXL9+XZRHrVZjwoQJLbbpDv3c2NiIvLw8UXsBYMqUKa1m1Ol0LeojIiJw8uRJNDU1ma3p6j4FOi4zANTV1cHLywv9+/dHdHQ0CgoKrNZu/q7Ybub69et45JFHWix/5JFHcP36dbPb7tmzB3Fxcbh9+zbc3d2Rk5MDrVYrrI+Pj8fAgQPh5uaG4uJiJCYmoqioCDk5OVbP0R4dlfn69etQqVTo06ePaBtXV9c299vRHiSzUX19PZYvX46ZM2eKvpNQiv1sZCqz1Pu5NVLu59b2K6V+Ni53dXUVLXd1dcWlS5eEx92lnysqKnDnzh2T7TWX0VS9Xq9HRUUF3N3dW63p6j4FOi6zv78/MjIyMHLkSNTU1ODjjz/GY489hqKiIvj6+j5wu/mMXSdJTk42ebHkvT8nT54EAMhkshbbE5HJ5feaOHEiCgsLkZubi8jISMTGxqK8vFxYP2/ePEyePBkjRoxAXFwcduzYgf379yM/P9+6Yf/UHTKbYsl+71dnZAbuTiqIi4uDwWDAunXrROuk2M+A+cymSKGfzZFqP7fXw97Pzdc336az+7ktbbXXkvrmy9u7z85m7cyhoaGYNWsWRo0ahSeeeALbtm3DkCFD8Omnn1qlvXzGrpMsWrSozZlM3t7e+Omnn/Dbb7+1WHfjxo0WfwU0Z29vDx8fH/j4+CA0NBS+vr5IS0tDYmKiyfqgoCAolUqcO3cOQUFBloexUFdndnNzQ2NjI6qqqkR/5ZeXlyMsLOz+QrWhMzI3NTUhNjYWZWVl+P7770Vn60yRQj+byyzVfm4vKfSzOVLrZzc3NwB3z/C4u7sLy8vLy80+Tx3dz63RarWwsbFpcabKXHvd3NxM1isUCjg7O5utsfb74350VObm5HI5xowZg3Pnzlmn4R129R67L8aLcI8dOyYsO3r0aLsvMCciGjx4MCUlJbW6/tSpUwSADh06dL/NtYqOymy82Hrr1q3C+qtXr3ari63bm7mxsZGeffZZGj58uGj2rzkPez+3lVmK/WxkbvJEcw97Pxu1NXlCKv1snDzx4YcfCssaGhpaTJ5oriv7eezYsbRw4ULRsqFDh5qdSDB06FDRsgULFrSYPPH000+LaiIjI7vV5AlrZ27OYDBQSEgIzZ0798EbTDwrtluKjIykgIAA0ul0pNPpaOTIkS2mzfv5+VF2djYREdXV1VFiYiLpdDq6ePEi5eXlUUJCAqnVaiouLiYiovPnz9PKlSvpxIkTVFZWRnv37iV/f38aPXp0t7lVgLUzE919Q/Xv35/2799P+fn59OSTT3ar2yO0J3NTUxNNnTqV+vfvT4WFhaJp8g0NDUQkvX62JDORtPqZiKiyspIKCgpo7969BIC2bNlCBQUFwi0RpNbPRG1nJpJeP6ekpJBGo6Hs7Gw6deoUvfDCC6LbnXS3fjbe+iMtLY1KSkro1VdfJXt7e7p48SIRES1fvpxefPFFod5464/XXnuNSkpKKC0trcWtP3788UeysbGhlJQUOnPmDKWkpHTL251YM3NycjLt27ePLly4QAUFBTR37lxSKBSiPwweBA/suqHKykqKj48nR0dHcnR0pPj4eKqqqhLVAKD09HQiIvrjjz/oL3/5C3l4eJBKpSJ3d3eaOnUqHT9+XKi/fPkyjR8/nvr27UsqlYoGDx5MixcvpsrKyk5M1rqOyGysW7RoEfXt25fs7OwoOjqaLl++3EmpzGtvZuOZDFM/Bw4cICLp9bMlmYmk1c9Ed2+HYCqz8Wy01PqZqO3MRNLrZ4PBQElJSeTm5kZqtZrGjx9Pp06dEtZ3x37+5z//SV5eXqRSqSgoKEh05nD27Nk0YcIEUf3Bgwdp9OjRpFKpyNvbm9avX99in9u3byc/Pz9SKpXk7+9PWVlZHR2jXayd+dVXX6UBAwaQSqUiFxcXmjJlCuXm5lqtvTKiP6/qY4wxxhhjDzWeFcsYY4wxJhE8sGOMMcYYkwge2DHGGGOMSQQP7BhjjDHGJIIHdowxxhhjEsEDO8YYY4wxieCBHWOMMcaYRPDAjjHGGGNMInhgxxjrEWQyGXbt2tXVzTDp4MGDkMlkuHnzptX2mZycjMDAQKvtz5SOaDdj7MHwwI4xZlW5ubmwsbFBZGRku7f19vbG2rVrrd8oC5SXl2P+/PkYMGAA1Go13NzcEBERAZ1O1+HHDgsLw7Vr16DRaDr8WACQl5cHmUyGI0eOmFwfERGBqVOndkpbGGPWxQM7xphVbdiwAS+//DKOHDmCy5cvd3VzLPbcc8+hqKgIGzduxH//+1/s3r0b4eHh+P333+97n0QEvV7fZp1KpYKbmxtkMtl9H6s9goODMWrUKKSnp7dYd+XKFezfvx8JCQmd0hbGmHXxwI4xZjW3bt3Ctm3bsHDhQkRHRyMjI6NFze7duxESEgJbW1totVpMnz4dABAeHo5Lly7htddeg0wmEwY5pj5SXLt2Lby9vYXHJ06cwFNPPQWtVguNRoMJEyYgPz/f4nbfvHkTR44cwYcffoiJEyfCy8sLY8eORWJiIqKiogAAFy9ehEwmQ2FhoWg7mUyGgwcPAvjfR5P/+c9/EBISArVajbS0NMhkMpSWloqOuXr1anh7e4OIRB9pVldXw87ODvv27RPVZ2dnw97eHnV1dQCAN954A0OGDEGvXr0waNAgvP3222hqarI4c0JCArZt24Zbt26JlmdkZMDFxQVRUVHYtGkTQkJC4OjoCDc3N8ycORPl5eWt7tOSvgKA9PR0DB06FLa2tvD398e6deuEdY2NjVi0aBHc3d1ha2sLb29vfPDBBxbnYqyn44EdY8xqtm7dCj8/P/j5+WHWrFlIT08HEQnr9+7di+nTpyMqKgoFBQX47rvvEBISAuDuwKV///545513cO3aNVy7ds3i49bW1mL27Nk4fPgwjh49Cl9fXzzzzDOora21aHsHBwc4ODhg165daGhoaF9oE5YtW4YPPvgAZ86cQUxMDIKDg7F582ZRTWZmJmbOnNniLJ1Go0FUVJTJ+mnTpsHBwQEA4OjoiIyMDJSUlODjjz9Gamoq1qxZY3Eb4+Pj0dTUhO3btwvLiAgZGRmYPXs2FAoFGhsbsWrVKhQVFWHXrl0oKyvDnDlz2vlsiKWmpuLNN9/Ee++9hzNnzuD999/H22+/jY0bNwIAPvnkE+zevRvbtm3D2bNnsWnTphYDQ8aYGcQYY1YSFhZGa9euJSKipqYm0mq1lJOTI6wfN24cxcfHt7q9l5cXrVmzRrQsKSmJRo0aJVq2Zs0a8vLyanU/er2eHB0d6euvvxaWAaCdO3e2us2OHTuoT58+ZGtrS2FhYZSYmEhFRUXC+rKyMgJABQUFwrKqqioCQAcOHCAiogMHDhAA2rVrl2jfq1evpkGDBgmPz549SwDo9OnTou2qqqqIiCg7O5scHBzo1q1bRERUXV1Ntra2tHfv3lbb/9FHH1FwcLDw2NTz1tyMGTNo/PjxwuPvv/+eAFBpaanJ+uPHjxMAqq2tNdluS/rK09OTMjMzRTWrVq2icePGERHRyy+/TE8++SQZDAazbWeMmcZn7BhjVnH27FkcP34ccXFxAACFQoEZM2Zgw4YNQk1hYSEmTZpk9WOXl5djwYIFGDJkCDQaDTQaDerq6tp1jd9zzz2Hq1evYvfu3YiIiMDBgwcRFBRk8uPkthjPQhrFxcXh0qVLOHr0KABg8+bNCAwMxLBhw0xuHxUVBYVCgd27dwMAsrKy4OjoiClTpgg1O3bswOOPPw43Nzc4ODjg7bffbvc1jQkJCfjhhx9w/vx5AHevj3zsscfg5+cHACgoKMC0adPg5eUFR0dHhIeHA8B9Xzt548YNXLlyBQkJCcJZUgcHB7z77ru4cOECAGDOnDkoLCyEn58fFi9ejG+//fa+jsVYT8UDO8aYVaSlpUGv16Nfv35QKBRQKBRYv349srOzUVVVBQCws7Nr937lcrno41wALa4lmzNnDvLy8rB27Vrk5uaisLAQzs7OaGxsbNexbG1t8dRTT2HFihXIzc3FnDlzkJSUJLQDgKgtrV3TZm9vL3rs7u6OiRMnIjMzEwDw73//G7NmzWq1HSqVCjExMUJ9ZmYmZsyYAYVCAQA4evQo4uLi8PTTT2PPnj0oKCjAm2++2e68kydPhpeXFzIyMlBTU4Ps7Gxh0sStW7cwZcoUODg4YNOmTThx4gR27twJAK0ep62+MhgMAO5+HFtYWCj8FBcXC4PeoKAglJWVYdWqVfjjjz8QGxuLmJiYduVirCfjgR1j7IHp9Xp8+eWX+Mc//iH6hV1UVAQvLy/herGAgAB89913re5HpVLhzp07omUuLi64fv26aMBw7wQGADh8+DAWL16MZ555BsOHD4darUZFRcUD5xo2bJgwucDFxQUARNf+NW+HOfHx8di6dSt0Oh0uXLggnNk0V79v3z6cPn0aBw4cQHx8vLDuxx9/hJeXF958802EhITA19cXly5dakeyu2QyGebOnYuNGzciMzMTcrkcsbGxAIDS0lJUVFQgJSUFTzzxBPz9/c1OnADa7itXV1f069cPP//8M3x8fEQ/AwcOFOqcnJwwY8YMpKamYuvWrcjKynqg2cmM9SSKrm4AY+zht2fPHlRVVSEhIaHFvdhiYmKQlpaGRYsWISkpCZMmTcLgwYMRFxcHvV6Pb775BsuWLQNw9z52P/zwA+Li4qBWq6HVahEeHo4bN27go48+QkxMDPbt24dvvvkGTk5OwjF8fHzwr3/9CyEhIaipqcHSpUvbdXawsrISzz//PP76178iICAAjo6OOHnyJD766CNMmzYNwN2zjaGhoUhJSYG3tzcqKirw1ltvWXyM6dOnY+HChVi4cCEmTpyIfv36ma2fMGECXF1dER8fD29vb4SGhoryXr58GVu2bMGYMWOwd+9e4Wxae82dOxfvvPMO/u///g9xcXHC2cYBAwZApVLh008/xYIFC1BcXIxVq1aZ3ZclfZWcnIzFixfDyckJTz/9NBoaGnDy5ElUVVVhyZIlWLNmDdzd3REYGAi5XI7t27fDzc0NvXv3vq98jPU4XXqFH2NMEqKjo+mZZ54xuS4vL48AUF5eHhERZWVlUWBgIKlUKtJqtTR9+nShVqfTUUBAAKnVarr3f0/r168nT09Psre3p5deeonee+890QX5+fn5FBISQmq1mnx9fWn79u0tJmLAzOSJ+vp6Wr58OQUFBZFGo6FevXqRn58fvfXWW3T79m2hrqSkhEJDQ8nOzo4CAwPp22+/NTl5wjiZoLnnn3+eANCGDRtEy1vbbunSpQSAVqxY0WJfS5cuJWdnZ3JwcKAZM2bQmjVrSKPRCOstmTxhNGXKFAJAubm5ouWZmZnk7e1NarWaxo0bR7t37xZNIDHV7rb6ioho8+bNwmugT58+NH78eMrOziYioi+++IICAwPJ3t6enJycaNKkSZSfn29RDsYYkYyo2QURjDHGGGPsocTX2DHGGGOMSQQP7BhjjDHGJIIHdowxxhhjEsEDO8YYY4wxieCBHWOMMcaYRPDAjjHGGGNMInhgxxhjjDEmETywY4wxxhiTCB7YMcYYY4xJBA/sGGOMMcYkggd2jDHGGGMSwQM7xhhjjDGJ+H9XhanWdhbk7gAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.scatterplot(x=y_test, y=xg_pred)\n", "plt.xlabel('Actual Survival Values')\n", "plt.ylabel('Predicted Survival Values')\n", "plt.title('Actual vs Predicted Survival Values XGBoost')\n", "\n", "# Add a diagonal line for perfect predictions\n", "min_val = min(y_test.min(), xg_pred.min())\n", "max_val = max(y_test.max(), xg_pred.max())\n", "plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Perfect Prediction')\n", "plt.legend()\n", "\n", "# Optional: Add R² score as text on the plot\n", "r2 = r2_score(y_test, xg_pred)\n", "plt.text(0.05, 0.95, f'pearson 3 = {r:.3f}, pearson p = {p:.4e}', transform=plt.gca().transAxes, \n", "        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["adata_pred = sc.read_h5ad('./adata_pred.h5ad')\n"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["here\n", "here\n"]}], "source": ["pert_indices_pred, ref_pred = get_pert_vectors(adata_pred)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["del adata_pred"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["\n", "survival = pd.read_csv('/large_storage/ctc/public/tahoe/survival_data/log2fc_survivals_averaged_across_plate.csv')\n", "survival = survival.melt(id_vars= ['drug'])\n", "survival['drug'] = survival['drug'].apply(\n", "    lambda x: f\"[('{x.split('_')[0]}', {x.split('_')[1]}, 'uM')]\"\n", ")\n", "\n", "ref_pred = ref_pred[ref_pred['drug'] != \"[('DMSO_TF', 0.0, 'uM')]\"]\n", "# Merge to get the order, then sort\n", "survival = survival.merge(ref_pred, on=['variable', 'drug'], how='right')\n", "survival = survival.sort_values('order').drop('order', axis=1)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>drug</th>\n", "      <th>variable</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>[('<PERSON><PERSON><PERSON><PERSON>', 0.05, 'uM')]</td>\n", "      <td>NCI-H2030</td>\n", "      <td>-0.025779</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>[('<PERSON><PERSON><PERSON><PERSON>', 0.5, 'uM')]</td>\n", "      <td>NCI-H2030</td>\n", "      <td>-0.137606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>[('<PERSON><PERSON><PERSON><PERSON>', 0.05, 'uM')]</td>\n", "      <td>MIA PaCa-2</td>\n", "      <td>-0.070542</td>\n", "    </tr>\n", "    <tr>\n", "      <th>542</th>\n", "      <td>[('<PERSON>grasi<PERSON>', 5.0, 'uM')]</td>\n", "      <td>NCI-H2030</td>\n", "      <td>-0.141353</td>\n", "    </tr>\n", "    <tr>\n", "      <th>827</th>\n", "      <td>[('<PERSON>grasi<PERSON>', 5.0, 'uM')]</td>\n", "      <td>MIA PaCa-2</td>\n", "      <td>-0.352728</td>\n", "    </tr>\n", "    <tr>\n", "      <th>929</th>\n", "      <td>[('<PERSON><PERSON><PERSON><PERSON>', 0.5, 'uM')]</td>\n", "      <td>MIA PaCa-2</td>\n", "      <td>-0.182577</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            drug    variable     value\n", "79   [('<PERSON><PERSON><PERSON><PERSON>', 0.05, 'uM')]   NCI-H2030 -0.025779\n", "87    [('<PERSON><PERSON><PERSON><PERSON>', 0.5, 'uM')]   NCI-H2030 -0.137606\n", "102  [('<PERSON><PERSON><PERSON><PERSON>', 0.05, 'uM')]  MIA PaCa-2 -0.070542\n", "542   [('<PERSON><PERSON><PERSON><PERSON>', 5.0, 'uM')]   NCI-H2030 -0.141353\n", "827   [('<PERSON><PERSON><PERSON><PERSON>', 5.0, 'uM')]  MIA PaCa-2 -0.352728\n", "929   [('<PERSON><PERSON><PERSON><PERSON>', 0.5, 'uM')]  MIA PaCa-2 -0.182577"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["survival[survival['drug'].str.contains('grasib')]"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["valid_mask = ~survival['value'].isna()\n", "\n", "# Apply the same mask to both\n", "x = pert_indices_pred[valid_mask.values, :]\n", "y = survival['value'][valid_mask].values\n", "train_mask =  ~(survival['drug'].str.contains('grasib')) & ~(survival['value'].isna())\n", "X_train = pert_indices_pred[train_mask.values, :]\n", "y_train = survival['value'][train_mask].values\n", "test_mask = (survival['drug'].str.contains('grasib')) & ~(survival['value'].isna())\n", "X_test = pert_indices_pred[test_mask.values, :]\n", "y_test = survival['value'][test_mask].values\n", "# Scale the features (important for SVM)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Fit SVM\n", "#svm = SVR(kernel='linear')  # or 'rbf', 'poly', etc.\n", "#svm.fit(X_train_scaled, y_train)\n", "\n", "# Make predictions\n", "y_pred = xg.predict(scaler.fit_transform(X_test))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["R² Score: -0.3538\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(f\"R² Score: {r2_score(y_test, y_pred):.4f}\")\n", "sns.scatterplot(x=y_test, y=y_pred)\n", "plt.xlabel('Actual Survival Values')\n", "plt.ylabel('Predicted Survival Values')\n", "plt.title('Actual vs Predicted Survival Values XgBoost')\n", "\n", "# Add a diagonal line for perfect predictions\n", "min_val = min(y_test.min(), y_pred.min())\n", "max_val = max(y_test.max(), y_pred.max())\n", "plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Perfect Prediction')\n", "plt.legend()\n", "\n", "\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.05, 0.95, 'pearson R = 0.555, pearson p = 2.5316e-01')"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.scatterplot(x=y_test, y=y_pred, hue = survival.loc[test_mask, :]['drug'])\n", "plt.xlabel('Actual Survival Values')\n", "plt.ylabel('Predicted Survival Values')\n", "plt.title('Actual vs Predicted Survival Values SVM')\n", "\n", "# Add a diagonal line for perfect predictions\n", "min_val = min(y_test.min(), y_pred.min())\n", "max_val = max(y_test.max(), y_pred.max())\n", "plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Perfect Prediction')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "r2 = r2_score(y_test, y_pred)\n", "plt.text(0.05, 0.95, f'pearson R = {r:.3f}, pearson p = {p:.4e}', transform=plt.gca().transAxes, \n", "        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.05, 0.95, 'pearson R = 0.555, pearson p = 2.5316e-01')"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y_pred = svm.predict(scaler.fit_transform(X_test))\n", "\n", "sns.scatterplot(x=y_test, y=y_pred, hue = survival.loc[test_mask, :]['variable'])\n", "plt.xlabel('Actual Survival Values')\n", "plt.ylabel('Predicted Survival Values')\n", "plt.title('Actual vs Predicted Survival Values SVM')\n", "\n", "# Add a diagonal line for perfect predictions\n", "min_val = min(y_test.min(), y_pred.min())\n", "max_val = max(y_test.max(), y_pred.max())\n", "plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Perfect Prediction')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "r2 = r2_score(y_test, y_pred)\n", "plt.text(0.05, 0.95, f'pearson R = {r:.3f}, pearson p = {p:.4e}', transform=plt.gca().transAxes, \n", "        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sc-env3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}