{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"cpu\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n", "import pandas as pd\n", "import seaborn as sns\n", "import numpy as np\n", "import scipy\n", "import jax.numpy as jnp\n", "import xgboost as xgb\n", "import jax\n", "from sklearn.decomposition import PCA\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import anndata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## define function to get vector from DMSO to pert for each pert to each cell line.  GPU-ing this did make a difference"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_pert_vectors(adata):\n", "    # Extract metadata and group labels\n", "    meta_df = adata.obs[['cell_name', 'drugname_drugconc']].copy()\n", "    meta_df['group'] = meta_df['cell_name'].astype(str) + '|' + meta_df['drugname_drugconc'].astype(str)\n", "    # Map groups to integer indices\n", "    group_idx, unique_groups = pd.factorize(meta_df['group'])\n", "    # Then run your code\n", "    # Extract metadata and group labels\n", "    meta_df = adata.obs[['cell_name', 'drugname_drugconc']].copy()\n", "    meta_df['group'] = meta_df['cell_name'].astype(str) + '|' + meta_df['drugname_drugconc'].astype(str)\n", "    adata.obs['group'] = adata.obs['cell_name'].astype(str) + '|' + adata.obs['drugname_drugconc'].astype(str)\n", "    print('here')\n", "    # Map groups to integer indices\n", "    group_idx, unique_groups = pd.factorize(meta_df['group'])\n", "    # X_jax: (n_cells, n_dims)\n", "    # group_idx: int array of length n_cells (e.g., from pd.factorize)\n", "    X_jax = jnp.array(adata.X.astype('float32'))\n", "    print('here')\n", "    group_idx = jnp.array(group_idx)\n", "    n_groups = len(unique_groups)\n", "    sums = jax.ops.segment_sum(X_jax, group_idx, n_groups)\n", "    counts = jax.ops.segment_sum(jnp.ones((X_jax.shape[0], 1)), group_idx, n_groups)\n", "    centroids = sums / counts\n", "    # Back to Pandas to identify DMSO indices\n", "    group_labels = pd.Series(unique_groups)\n", "    cell_types = group_labels.str.split('|').str[0]\n", "    perts = group_labels.str.split('|').str[1]\n", "    # Find the index of each DMSO centroid by cell_type\n", "    dmso_idx = (perts == \"[('DMSO_TF', 0.0, 'uM')]\")\n", "    # Convert boolean mask to indices for JAX\n", "    dmso_indices = np.where(dmso_idx.values)[0]  # Convert pandas boolean to JAX indices\n", "    dmso_centroids = centroids[dmso_indices]\n", "    # Match non-DMSO centroids to their corresponding DMSO by cell_type\n", "    pert_indices = np.where(~dmso_idx.values)[0]  # Also convert this to JAX indices\n", "    pert_cell_types = cell_types[~dmso_idx].values\n", "    # Build a lookup: cell_type → index in dmso_centroids\n", "    dmso_cell_types = cell_types[dmso_idx].values\n", "    dmso_cell_type_to_idx = dict(zip(dmso_cell_types, dmso_indices))\n", "    # Get DMSO centroid for each perturbation group\n", "    dmso_for_pert = np.stack([\n", "        centroids[dmso_cell_type_to_idx[ct]] for ct in pert_cell_types\n", "    ])\n", "    # Compute perturbation vectors\n", "    pert_vectors = centroids[pert_indices] - dmso_for_pert\n", "    reference_df = pd.DataFrame({\n", "    'variable': cell_types,\n", "    'drug': perts,\n", "    'order': range(len(cell_types))\n", "    })\n", "    return pert_vectors, reference_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## My code is setup for a previous version of the objects that had the embeddings in .X"]}, {"cell_type": "markdown", "metadata": {}, "source": ["additionally, the objects are big enough that if the have both embeddings and values, it breaks.  I subset, but that doesn;t make much of a difference, because I ultimatley calculate centroids"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["adata_real = sc.read_h5ad(\"../../../../adata_real.h5ad\", backed = True)\n", "adata_real = anndata.AnnData(X = adata_real.obsm['X_vci_1.5.2_4'], obs = adata_real.obs)\n", "sc.pp.sample(adata_real, 0.25)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'adata_real' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[15], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43madata_real\u001b[49m\u001b[38;5;241m.\u001b[39mobs\n", "\u001b[0;31mNameError\u001b[0m: name 'adata_real' is not defined"]}], "source": ["adata_real.obs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'adata_real' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[16], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m real_vectors, ref \u001b[38;5;241m=\u001b[39m get_pert_vectors(\u001b[43madata_real\u001b[49m)\n", "\u001b[0;31mNameError\u001b[0m: name 'adata_real' is not defined"]}], "source": ["real_vectors, ref = get_pert_vectors(adata_real)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'adata_real' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[17], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[43madata_real\u001b[49m\n", "\u001b[0;31mNameError\u001b[0m: name 'adata_real' is not defined"]}], "source": ["del adata_real"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## import the survival data and align with the pert-vectors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["survival = pd.read_csv('/large_storage/ctc/public/tahoe/survival_data/log2fc_survivals_averaged_across_plate.csv')\n", "survival = survival.melt(id_vars= ['drug'])\n", "survival['drug'] = survival['drug'].apply(\n", "    lambda x: f\"[('{x.split('_')[0]}', {x.split('_')[1]}, 'uM')]\"\n", ")\n", "# Create combined reference\n", "\n", "ref = ref[ref['drug'] != \"[('DMSO_TF', 0.0, 'uM')]\"]\n", "# Merge to get the order, then sort\n", "survival = survival.merge(ref, on=['variable', 'drug'], how='right')\n", "survival = survival.sort_values('order').drop('order', axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["annoyingly, a couple of perts are missing from the survival data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pert_vectors = np.asarray(real_vectors)\n", "# Get the mask from the original survival dataframe\n", "valid_mask = ~survival['value'].isna()\n", "x = pert_vectors[valid_mask.values, :]\n", "y = survival['value'][valid_mask].values\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["variable\n", "C32          735\n", "HOP62        735\n", "Hs 766T      734\n", "PANC-1       734\n", "HepG2/C3A    734\n", "Name: count, dtype: int64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ref['variable'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["variable\n", "C32          735\n", "HOP62        735\n", "Hs 766T      734\n", "PANC-1       734\n", "HepG2/C3A    734\n", "Name: count, dtype: int64"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["ref['variable'].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["train a SVM to predict the survival from the pert-vectors.  I pick 4 random drugs to mask, and I mask all of their doses.  If you are trying to reporduce this, you will need to copy the names as outputted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\"xadustat'\", \"limetinib dimesylate'\", \"citabine'\", \"rosemide'\"]\n"]}], "source": ["import random\n", "drugs = survival['drug'].str.split(',').str[0].unique()\n", "drugs = [x[5:] for x in drugs]\n", "drugs = random.sample(list(drugs), 4)\n", "print(drugs)\n", "drugs_regex = '|'.join(drugs)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SVR Regression Metrics:\n", "R² Score: 0.5900\n", "Mean Squared Error: 0.0130\n", "Root Mean Squared Error: 0.1138\n", "Mean Absolute Error: 0.0893\n", "<PERSON> r  = 0.8250\n", "<PERSON> r² = 0.6806\n", "p-value    = 5.3030e-16\n"]}], "source": ["from sklearn.svm import SVR\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "from sklearn.preprocessing import StandardScaler\n", "import numpy as np\n", "valid_mask = ~survival['value'].isna()\n", "# Apply the same mask to both\n", "x = pert_vectors[valid_mask.values, :]\n", "y = survival['value'][valid_mask].values\n", "train_mask =  ~(survival['drug'].str.contains(drugs_regex)) & ~(survival['value'].isna())\n", "X_train = pert_vectors[train_mask, :]\n", "y_train = survival['value'][train_mask].values\n", "test_mask = (survival['drug'].str.contains(drugs_regex)) & ~(survival['value'].isna())\n", "X_test = pert_vectors[test_mask, :]\n", "y_test = survival['value'][test_mask].values\n", "# Scale the features (important for SVM)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "\n", "# Scale the features (important for SVM)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Fit SVM\n", "svm = SVR(kernel='rbf')  # or 'rbf', 'poly', etc.\n", "svm.fit(X_train_scaled, y_train)\n", "\n", "# Make predictions\n", "y_pred = svm.predict(X_test_scaled)\n", "\n", "# Regression metrics\n", "print(\"SVR Regression Metrics:\")\n", "print(f\"R² Score: {r2_score(y_test, y_pred):.4f}\")\n", "print(f\"Mean Squared Error: {mean_squared_error(y_test, y_pred):.4f}\")\n", "print(f\"Root Mean Squared Error: {np.sqrt(mean_squared_error(y_test, y_pred)):.4f}\")\n", "print(f\"Mean Absolute Error: {mean_absolute_error(y_test, y_pred):.4f}\")\n", "from scipy.stats import pearsonr\n", "from scipy.stats import spearmanr\n", "from scipy.stats import pearsonr\n", "\n", "r, p = pearsonr(y_test, y_pred)\n", "\n", "print(f\"Pearson r  = {r:.4f}\")\n", "print(f\"Pearson r² = {r**2:.4f}\")\n", "print(f\"p-value    = {p:.4e}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.scatterplot(x=y_test, y=y_pred)\n", "plt.xlabel('Actual Survival Values')\n", "plt.ylabel('Predicted Survival Values')\n", "plt.title('Actual vs Predicted Survival Values SVM')\n", "\n", "# Add a diagonal line for perfect predictions\n", "min_val = min(y_test.min(), y_pred.min())\n", "max_val = max(y_test.max(), y_pred.max())\n", "plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Perfect Prediction')\n", "plt.legend()\n", "\n", "# Optional: Add R² score as text on the plot\n", "r2 = r2_score(y_test, y_pred)\n", "plt.text(0.05, 0.95, f'pearson 3 = {r:.3f}, pearson p = {p:.4e}', transform=plt.gca().transAxes, \n", "        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.savefig('model_real_embeddings_survival_fit.pdf')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["XgB Regression Metrics:\n", "R² Score: 0.4492\n", "Mean Squared Error: 0.0174\n", "Root Mean Squared Error: 0.1319\n", "Mean Absolute Error: 0.1013\n"]}], "source": ["xg = xgb.XGBRegressor()\n", "xg.fit(X_train_scaled, y_train)\n", "xg_pred = xg.predict(X_test_scaled)\n", "print(\"XgB Regression Metrics:\")\n", "print(f\"R² Score: {r2_score(y_test, xg_pred):.4f}\")\n", "print(f\"Mean Squared Error: {mean_squared_error(y_test, xg_pred):.4f}\")\n", "print(f\"Root Mean Squared Error: {np.sqrt(mean_squared_error(y_test, xg_pred)):.4f}\")\n", "print(f\"Mean Absolute Error: {mean_absolute_error(y_test, xg_pred):.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["adata_pred = sc.read_h5ad('/large_storage/ctc/userspace/alishba.imran/state-sets/kras_split1_vci_copy/eval_step=200000.ckpt/adata_pred.h5ad')\n", "adata_pred = sc.read_h5ad(\"../adata_real.h5ad\", backed = True)\n", "adata_pred = anndata.AnnData(X = adata_pred.obsm['X_vci_1.5.2_4'], obs = adata_pred.obs)\n", "\n", "sc.pp.sample(adata_pred, 0.25)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["here\n", "here\n"]}], "source": ["pert_indices_pred, ref_pred = get_pert_vectors(adata_pred)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del adata_pred"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "survival = pd.read_csv('/large_storage/ctc/public/tahoe/survival_data/log2fc_survivals_averaged_across_plate.csv')\n", "survival = survival.melt(id_vars= ['drug'])\n", "survival['drug'] = survival['drug'].apply(\n", "    lambda x: f\"[('{x.split('_')[0]}', {x.split('_')[1]}, 'uM')]\"\n", ")\n", "\n", "ref_pred = ref_pred[ref_pred['drug'] != \"[('DMSO_TF', 0.0, 'uM')]\"]\n", "# Merge to get the order, then sort\n", "survival = survival.merge(ref_pred, on=['variable', 'drug'], how='right')\n", "survival = survival.sort_values('order').drop('order', axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["True     3596\n", "False      76\n", "Name: count, dtype: int64"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["train_mask.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["valid_mask = ~survival['value'].isna()\n", "\n", "# Apply the same mask to both\n", "x = pert_indices_pred[valid_mask.values, :]\n", "y = survival['value'][valid_mask].values\n", "train_mask =  ~(survival['drug'].str.contains(drugs_regex)) & ~(survival['value'].isna())\n", "X_train = pert_indices_pred[train_mask.values, :]\n", "y_train = survival['value'][train_mask].values\n", "test_mask = (survival['drug'].str.contains(drugs_regex)) & ~(survival['value'].isna())\n", "X_test = pert_indices_pred[test_mask.values, :]\n", "y_test = survival['value'][test_mask].values\n", "# Scale the features (important for SVM)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Fit SVM\n", "#svm = SVR(kernel='linear')  # or 'rbf', 'poly', etc.\n", "#svm.fit(X_train_scaled, y_train)\n", "\n", "# Make predictions\n", "y_pred = xg.predict(scaler.fit_transform(X_test))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["R² Score: 0.4839\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(f\"R² Score: {r2_score(y_test, y_pred):.4f}\")\n", "sns.scatterplot(x=y_test, y=y_pred)\n", "plt.xlabel('Actual Survival Values')\n", "plt.ylabel('Predicted Survival Values')\n", "plt.title('Actual vs Predicted Survival Values XgBoost')\n", "\n", "# Add a diagonal line for perfect predictions\n", "min_val = min(y_test.min(), y_pred.min())\n", "max_val = max(y_test.max(), y_pred.max())\n", "plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Perfect Prediction')\n", "plt.legend()\n", "\n", "\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_pred = svm.predict(scaler.fit_transform(X_test))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> r  = 0.7976\n", "<PERSON> r² = 0.6362\n", "p-value    = 2.3946e-14\n"]}], "source": ["from scipy.stats import pearsonr\n", "r, p = pearsonr(y_test, y_pred)\n", "\n", "print(f\"Pearson r  = {r:.4f}\")\n", "print(f\"Pearson r² = {r**2:.4f}\")\n", "print(f\"p-value    = {p:.4e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "sns.scatterplot(x=y_test, y=y_pred)\n", "plt.xlabel('Actual Survival Values')\n", "plt.ylabel('Predicted Survival Values')\n", "plt.title('Actual vs Predicted Survival Values SVM')\n", "\n", "# Add a diagonal line for perfect predictions\n", "min_val = min(y_test.min(), y_pred.min())\n", "max_val = max(y_test.max(), y_pred.max())\n", "plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Perfect Prediction')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "r2 = r2_score(y_test, y_pred)\n", "plt.text(0.05, 0.95, f'pearson R = {r:.3f}, pearson p = {p:.4e}', transform=plt.gca().transAxes, \n", "        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "plt.savefig('survival_on_new_predictions.pdf')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sc-env3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}