{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Attaching package: ‘dplyr’\n", "\n", "\n", "The following objects are masked from ‘package:stats’:\n", "\n", "    filter, lag\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    intersect, setdiff, setequal, union\n", "\n", "\n", "── \u001b[1mAttaching core tidyverse packages\u001b[22m ──────────────────────── tidyverse 2.0.0 ──\n", "\u001b[32m✔\u001b[39m \u001b[34mforcats  \u001b[39m 1.0.0     \u001b[32m✔\u001b[39m \u001b[34mstringr  \u001b[39m 1.5.1\n", "\u001b[32m✔\u001b[39m \u001b[34mggplot2  \u001b[39m 3.5.1     \u001b[32m✔\u001b[39m \u001b[34mtibble   \u001b[39m 3.2.1\n", "\u001b[32m✔\u001b[39m \u001b[34mlubridate\u001b[39m 1.9.3     \u001b[32m✔\u001b[39m \u001b[34mtidyr    \u001b[39m 1.3.1\n", "\u001b[32m✔\u001b[39m \u001b[34mpurrr    \u001b[39m 1.0.4     \n", "── \u001b[1mConflicts\u001b[22m ────────────────────────────────────────── tidyverse_conflicts() ──\n", "\u001b[31m✖\u001b[39m \u001b[34mdplyr\u001b[39m::\u001b[32mfilter()\u001b[39m masks \u001b[34mstats\u001b[39m::filter()\n", "\u001b[31m✖\u001b[39m \u001b[34mdplyr\u001b[39m::\u001b[32mlag()\u001b[39m    masks \u001b[34mstats\u001b[39m::lag()\n", "\u001b[36mℹ\u001b[39m Use the conflicted package (\u001b[3m\u001b[34m<http://conflicted.r-lib.org/>\u001b[39m\u001b[23m) to force all conflicts to become errors\n"]}], "source": ["library(readr)\n", "library(readxl)\n", "library(dplyr)\n", "library(tidyverse)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["options(repr.plot.width = 20, repr.plot.height = 10)"]}, {"cell_type": "code", "execution_count": 196, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["library(fgsea)\n", "get_gsea <- function(df) {\n", "  df <- df %>% drop_na()\n", "  # Ensure gene names are rownames and vector is a matrix\n", "  gene_vec <- df$fold_change\n", "  names(gene_vec) <- df$gene\n", "\n", "  # Wrap into a matrix for GSVA (genes x samples)\n", "  # Read GMT\n", "  gmt <- gmtPathways(\"mapk.gmt\")\n", "\n", "  # Run ssGSEA\n", "  ss <- fgsea(stats = gene_vec, pathways = gmt)\n", "\n", "  # Return as data frame\n", "  ss_df <- as.data.frame((ss))\n", "  return(ss_df)\n", "}\n"]}, {"cell_type": "code", "execution_count": 197, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1mRows: \u001b[22m\u001b[34m1470000\u001b[39m \u001b[1mColumns: \u001b[22m\u001b[34m10\u001b[39m\n", "\u001b[36m──\u001b[39m \u001b[1mColumn specification\u001b[22m \u001b[36m────────────────────────────────────────────────────────\u001b[39m\n", "\u001b[1mDelimiter:\u001b[22m \",\"\n", "\u001b[31mchr\u001b[39m (2): target, reference\n", "\u001b[32mdbl\u001b[39m (8): feature, target_mean, reference_mean, percent_change, fold_change, ...\n", "\n", "\u001b[36mℹ\u001b[39m Use `spec()` to retrieve the full column specification for this data.\n", "\u001b[36mℹ\u001b[39m Specify the column types or set `show_col_types = FALSE` to quiet this message.\n", "\u001b[1mRows: \u001b[22m\u001b[34m1470000\u001b[39m \u001b[1mColumns: \u001b[22m\u001b[34m10\u001b[39m\n", "\u001b[36m──\u001b[39m \u001b[1mColumn specification\u001b[22m \u001b[36m────────────────────────────────────────────────────────\u001b[39m\n", "\u001b[1mDelimiter:\u001b[22m \",\"\n", "\u001b[31mchr\u001b[39m (2): target, reference\n", "\u001b[32mdbl\u001b[39m (8): feature, target_mean, reference_mean, percent_change, fold_change, ...\n", "\n", "\u001b[36mℹ\u001b[39m Use `spec()` to retrieve the full column specification for this data.\n", "\u001b[36mℹ\u001b[39m Specify the column types or set `show_col_types = FALSE` to quiet this message.\n"]}], "source": ["fold1_pred <- read_csv('/large_storage/ctc/userspace/aadduri/preprint/tahoe_llama_58562784/holdout/eval_last.ckpt/C32_real_de.csv') %>% filter(grepl('Vemurafenib', target)) %>% mutate(cell_line = 'C32')\n", "fold2_pred <- read_csv('/large_storage/ctc/userspace/aadduri/preprint/tahoe_llama_212693232/holdout/eval_last.ckpt/C32_real_de.csv') %>% filter(grepl('Vemurafenib', target))%>% mutate(cell_line = 'C32')"]}, {"cell_type": "code", "execution_count": 198, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1mRows: \u001b[22m\u001b[34m6000\u001b[39m \u001b[1mColumns: \u001b[22m\u001b[34m10\u001b[39m\n", "\u001b[36m──\u001b[39m \u001b[1mColumn specification\u001b[22m \u001b[36m────────────────────────────────────────────────────────\u001b[39m\n", "\u001b[1mDelimiter:\u001b[22m \",\"\n", "\u001b[31mchr\u001b[39m (2): target, reference\n", "\u001b[32mdbl\u001b[39m (8): feature, target_mean, reference_mean, percent_change, fold_change, ...\n", "\n", "\u001b[36mℹ\u001b[39m Use `spec()` to retrieve the full column specification for this data.\n", "\u001b[36mℹ\u001b[39m Specify the column types or set `show_col_types = FALSE` to quiet this message.\n", "\u001b[1mRows: \u001b[22m\u001b[34m6000\u001b[39m \u001b[1mColumns: \u001b[22m\u001b[34m10\u001b[39m\n", "\u001b[36m──\u001b[39m \u001b[1mColumn specification\u001b[22m \u001b[36m────────────────────────────────────────────────────────\u001b[39m\n", "\u001b[1mDelimiter:\u001b[22m \",\"\n", "\u001b[31mchr\u001b[39m (2): target, reference\n", "\u001b[32mdbl\u001b[39m (8): feature, target_mean, reference_mean, percent_change, fold_change, ...\n", "\n", "\u001b[36mℹ\u001b[39m Use `spec()` to retrieve the full column specification for this data.\n", "\u001b[36mℹ\u001b[39m Specify the column types or set `show_col_types = FALSE` to quiet this message.\n", "\u001b[1mRows: \u001b[22m\u001b[34m6000\u001b[39m \u001b[1mColumns: \u001b[22m\u001b[34m10\u001b[39m\n", "\u001b[36m──\u001b[39m \u001b[1mColumn specification\u001b[22m \u001b[36m────────────────────────────────────────────────────────\u001b[39m\n", "\u001b[1mDelimiter:\u001b[22m \",\"\n", "\u001b[31mchr\u001b[39m (2): target, reference\n", "\u001b[32mdbl\u001b[39m (8): feature, target_mean, reference_mean, percent_change, fold_change, ...\n", "\n", "\u001b[36mℹ\u001b[39m Use `spec()` to retrieve the full column specification for this data.\n", "\u001b[36mℹ\u001b[39m Specify the column types or set `show_col_types = FALSE` to quiet this message.\n", "\u001b[1mRows: \u001b[22m\u001b[34m6000\u001b[39m \u001b[1mColumns: \u001b[22m\u001b[34m10\u001b[39m\n", "\u001b[36m──\u001b[39m \u001b[1mColumn specification\u001b[22m \u001b[36m────────────────────────────────────────────────────────\u001b[39m\n", "\u001b[1mDelimiter:\u001b[22m \",\"\n", "\u001b[31mchr\u001b[39m (2): target, reference\n", "\u001b[32mdbl\u001b[39m (8): feature, target_mean, reference_mean, percent_change, fold_change, ...\n", "\n", "\u001b[36mℹ\u001b[39m Use `spec()` to retrieve the full column specification for this data.\n", "\u001b[36mℹ\u001b[39m Specify the column types or set `show_col_types = FALSE` to quiet this message.\n"]}], "source": ["predicted1 <- read_csv(\"C32_pred_de.csv\")\n", "predicted2 <- read_csv(\"SK-MEL-2_pred_de.csv\")\n", "predicted1$cell_line <- 'C32'\n", "predicted2$cell_line <- 'SK-MEL-2'\n", "predicted <- bind_rows(predicted1, predicted2)\n", "ground_truth1 <- read_csv(\"C32_real_de.csv\")\n", "ground_truth2 <- read_csv(\"SK-MEL-2_real_de.csv\")\n", "ground_truth1$cell_line <- 'C32'\n", "ground_truth2$cell_line <- 'SK-MEL-2'\n", "ground_truth <- bind_rows(ground_truth1, ground_truth2)"]}, {"cell_type": "code", "execution_count": 199, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["library(reticulate)\n", "library(dplyr)\n", "\n", "# Load numpy and the .npy file\n", "np <- import(\"numpy\")\n", "gene_names <- np$load(\"/large_storage/ctc/userspace/aadduri/datasets/tahoe_19k_to_2k_names.npy\", allow_pickle = TRUE)\n", "# Convert to R character vector\n", "gene_names <- as.character(gene_names)\n", "# Create a data frame mapping index to gene name\n", "gene_map <- tibble(\n", "  feature = seq_along(gene_names) - 1,  # Python 0-based index\n", "  gene = gene_names\n", ")\n", "# Join with your predicted data frame\n", "predicted <- predicted %>%\n", "  left_join(gene_map, by = \"feature\")\n", "ground_truth<- ground_truth %>%\n", "  left_join(gene_map, by = \"feature\")\n", "fold1_pred<- fold1_pred %>%\n", "  left_join(gene_map, by = \"feature\")\n", "fold2_pred<- fold2_pred %>%\n", "  left_join(gene_map, by = \"feature\")"]}, {"cell_type": "code", "execution_count": 200, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (24.95% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 9 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some of the pathways the P-values were likely overestimated. For such pathways log2err is set to NA.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (32.94% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 9 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (16.75% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 2 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some of the pathways the P-values were likely overestimated. For such pathways log2err is set to NA.”\n"]}], "source": ["\n", "fold2_pred_gsea <- fold2_pred %>%\n", "  group_by(cell_line, target) %>% \n", "  group_split() %>%\n", "  map_dfr(function(df_group) {\n", "    gsea_res <- get_gsea(df_group)\n", "    gsea_res$cell_line <- unique(df_group$cell_line)\n", "    gsea_res$target <- unique(df_group$target)\n", "    gsea_res\n", "  })"]}, {"cell_type": "code", "execution_count": 204, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (23.39% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 3 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some of the pathways the P-values were likely overestimated. For such pathways log2err is set to NA.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (32.31% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 3 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some of the pathways the P-values were likely overestimated. For such pathways log2err is set to NA.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (16.6% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 3 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n"]}], "source": ["\n", "fold1_pred_gsea <- fold1_pred %>%\n", "  group_by(cell_line, target) %>% \n", "  group_split() %>%\n", "  map_dfr(function(df_group) {\n", "    gsea_res <- get_gsea(df_group)\n", "    gsea_res$cell_line <- unique(df_group$cell_line)\n", "    gsea_res$target <- unique(df_group$target)\n", "    gsea_res\n", "  })"]}, {"cell_type": "code", "execution_count": 205, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (13.09% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 2 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some pathways, in reality P-values are less than 1e-50. You can set the `eps` argument to zero for better estimation.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (17.1% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (11.78% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some of the pathways the P-values were likely overestimated. For such pathways log2err is set to NA.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (12.19% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some pathways, in reality P-values are less than 1e-50. You can set the `eps` argument to zero for better estimation.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (13.18% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (10.88% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 1 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some pathways, in reality P-values are less than 1e-50. You can set the `eps` argument to zero for better estimation.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (7.02% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 1 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some pathways, in reality P-values are less than 1e-50. You can set the `eps` argument to zero for better estimation.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (5.43% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some pathways, in reality P-values are less than 1e-50. You can set the `eps` argument to zero for better estimation.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (2.77% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 3 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some pathways, in reality P-values are less than 1e-50. You can set the `eps` argument to zero for better estimation.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (4.01% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some pathways, in reality P-values are less than 1e-50. You can set the `eps` argument to zero for better estimation.”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (7.82% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 2 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in preparePathwaysAndStats(pathways, stats, minSize, maxSize, gseaParam, :\n", "“There are ties in the preranked stats (3.24% of the list).\n", "The order of those tied genes will be arbitrary, which may produce unexpected results.”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“There were 1 pathways for which P-values were not calculated properly due to unbalanced (positive and negative) gene-level statistic values. For such pathways pval, padj, NES, log2err are set to NA. You can try to increase the value of the argument nPermSimple (for example set it nPermSimple = 10000)”\n", "Warning message in fgseaMultilevel(pathways = pathways, stats = stats, minSize = minSize, :\n", "“For some pathways, in reality P-values are less than 1e-50. You can set the `eps` argument to zero for better estimation.”\n"]}], "source": ["grounds_gsea <- ground_truth %>%\n", "  group_by(cell_line, target) %>%\n", "  group_split() %>%\n", "  map_dfr(function(df_group) {\n", "    gsea_res <- get_gsea(df_group)\n", "    gsea_res$cell_line <- unique(df_group$cell_line)\n", "    gsea_res$target <- unique(df_group$target)\n", "    gsea_res\n", "  })\n", "\n", "predicted_gsea <- predicted %>%\n", "  group_by(cell_line, target) %>%\n", "  group_split() %>%\n", "  map_dfr(function(df_group) {\n", "    gsea_res <- get_gsea(df_group)\n", "    gsea_res$cell_line <- unique(df_group$cell_line)\n", "    gsea_res$target <- unique(df_group$target)\n", "    gsea_res\n", "  })"]}, {"cell_type": "code", "execution_count": 206, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'C32'</li><li>'SK-MEL-2'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'C32'\n", "\\item 'SK-MEL-2'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'C32'\n", "2. 'SK-MEL-2'\n", "\n", "\n"], "text/plain": ["[1] \"C32\"      \"SK-MEL-2\""]}, "metadata": {}, "output_type": "display_data"}], "source": ["unique(merged2$cell_line)"]}, {"cell_type": "code", "execution_count": 210, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# Step 1: merge predicted and truth\n", "merged <- merge(predicted_gsea, grounds_gsea, by = c('cell_line', 'target', 'pathway'), suffixes = c(\"_predicted\", \"_truth\"))\n", "\n", "# Step 2: merge with fold1\n", "merged2 <- merge(merged1, fold1_pred_gsea, by = c('cell_line', 'target', 'pathway'), all = TRUE)\n", "names(merged2)[names(merged2) == \"NES\"] <- \"NES_fold1\"\n", "\n", "# Step 3: merge with fold2\n", "merged <- merge(merged2, fold2_pred_gsea, by = c('cell_line', 'target', 'pathway'), all = TRUE)\n", "names(merged)[names(merged) == \"NES\"] <- \"NES_fold2\"\n"]}, {"cell_type": "code", "execution_count": 211, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["merged$cell_line <- ifelse(\n", "  merged$cell_line == 'C32',\n", "  'C32 - BRAF v600E mutant Melanoma',\n", "  'SK-MEL-2 - <PERSON><PERSON>T. <PERSON>AF melanoma'\n", ")"]}, {"cell_type": "code", "execution_count": 212, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["merged <- merged %>% pivot_longer(c('NES_predicted', 'NES_truth', 'NES_fold1', 'NES_fold2')) %>% as.data.frame()\n"]}, {"cell_type": "code", "execution_count": 213, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["merged$name <- ifelse(merged$name == 'NES_truth', 'True Normalized Enrichment', 'predicted Normalized Enrichment')"]}, {"cell_type": "code", "execution_count": 214, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["set <- c('GOBP_REGULATION_OF_CYCLASE_ACTIVITY' )"]}, {"cell_type": "code", "execution_count": 215, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["library(ggpubr)"]}, {"cell_type": "code", "execution_count": 216, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“\u001b[1m\u001b[22mThere was 1 warning in `mutate()`.\n", "\u001b[1m\u001b[22m\u001b[36mℹ\u001b[39m In argument: `dose = as.numeric(dose)`.\n", "Caused by warning:\n", "\u001b[33m!\u001b[39m NAs introduced by coercion”\n"]}], "source": ["library(dplyr)\n", "library(stringr)\n", "\n", "merged <- merged %>%\n", "  mutate(dose = str_split_fixed(target, \",\", 2)[, 2]) %>%\n", "  mutate(dose = as.numeric(dose))\n"]}, {"cell_type": "code", "execution_count": 217, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“\u001b[1m\u001b[22mRemoved 6 rows containing non-finite outside the scale range\n", "(`stat_compare_means()`).”\n", "Warning message:\n", "“\u001b[1m\u001b[22mRemoved 6 rows containing missing values or values outside the scale range\n", "(`geom_point()`).”\n"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 1200, "width": 1200}}, "output_type": "display_data"}], "source": ["# Assuming 'name' contains your true/predicted labels\n", "merged <- merged %>%\n", "  mutate(group = interaction(cell_line, name, sep = \"_\"))\n", "\n", "summary_df <- summary_df %>%\n", "  mutate(group = interaction(cell_line, name, sep = \"_\"))\n", "\n", "p <- ggplot(\n", "  merged %>% filter(pathway %in% set),\n", "  aes(x = group, y = value, color = dose)\n", ") +\n", "  geom_point(position = position_jitter(width = 0.2), alpha = 0.5) +\n", "  geom_errorbar(\n", "    data = summary_df,\n", "    aes(\n", "      x = group,\n", "      y = mean_value,\n", "      ymin = mean_value - se,\n", "      ymax = mean_value + se,\n", "      color = dose\n", "    ),\n", "    inherit.aes = FALSE,\n", "    width = 0.2\n", "  ) +\n", "  geom_point(\n", "    data = summary_df,\n", "    aes(x = group, y = mean_value, color = dose),\n", "    inherit.aes = FALSE,\n", "    size = 3\n", "  ) +\n", "  stat_compare_means(method = \"t.test\") +\n", "  theme_bw() +\n", "  facet_grid(rows = vars(pathway)) +\n", "  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +\n", "  ylab(\"GOBP_REGULATION_OF_CYCLASE_ACTIVITY\")\n", "\n", "print(p)"]}, {"cell_type": "code", "execution_count": 224, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["options(repr.plot.width = 10, repr.plot.height = 10)"]}, {"cell_type": "code", "execution_count": 241, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“\u001b[1m\u001b[22mRemoved 7 rows containing missing values or values outside the scale range\n", "(`geom_point()`).”\n"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 300, "width": 1200}}, "output_type": "display_data"}], "source": ["library(ggplot2)\n", "library(ggpubr)\n", "\n", "# Define comparisons for both scenarios\n", "cell_line_comparisons <- list(c('C32 - BRAF v600E mutant Melanoma',\n", "                               'SK-MEL-2 - <PERSON><PERSON><PERSON>. BRAF melanoma'))\n", "\n", "dodge_width <- 0.5\n", "\n", "p <- ggplot(\n", "  merged %>% filter(pathway %in% set),\n", "  aes(x = cell_line, y = value, color = name, fill = name)\n", ") +\n", "  geom_point(\n", "    position = position_jitterdodge(jitter.width = 0.2, dodge.width = dodge_width),\n", "    alpha = 0.5\n", "  ) +\n", "  geom_errorbar(\n", "    data = summary_df,\n", "    aes(\n", "      x = cell_line,\n", "      y = mean_value,\n", "      ymin = mean_value - se,\n", "      ymax = mean_value + se,\n", "      color = name\n", "    ),\n", "    inherit.aes = FALSE,\n", "    position = position_dodge(width = dodge_width),\n", "    width = 0.2\n", "  ) + \n", "  ylim(0, 1.5) + \n", "  # T-test comparing cell lines within each 'name' group\n", "  # T-test comparing 'name' groups within each cell line\n", "  theme_bw() +\n", "  ylab(\"GOBP_REGULATION_OF_CYCLASE_ACTIVITY\")\n", "\n", "print(p)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["ggsave('BRAF_predictions.pdf', p)"]}, {"cell_type": "code", "execution_count": 237, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["options(repr.plot.width = 20, repr.plot.height = 5)"]}, {"cell_type": "code", "execution_count": 238, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“\u001b[1m\u001b[22mRemoved 6 rows containing non-finite outside the scale range\n", "(`stat_compare_means()`).”\n"]}, {"data": {"image/png": "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**************************/X93kb1Ue9M03kw7O+51yjJTW3n1ph9075Xigd+1F7oN+/vnerDO5U1vS95vzZFWkYPCYsKDDwdXTyHaIenfM94xCZhiE/md/ilj0fdt0/qbpemk/VFhC2Wna+VfqLAgAAAAAAAAAA+HzUF86q79/V0kGjUcurkLiD+3XrNf5sVaUsm8/qyz5ae6ieXfI9of54alx65XGx/XtIzzX1trfLkWFjplvKt58R8tQePK6f3ScMUHbM4ctjMqycjPH6znHf643SMg/yrZSWGAUAAAAAAAAAAPgaqE4dE2VKk8IUoj59QtRpDlWC/xzgPXhn0M2d0wa1a9K0w8BpO+7ExB85PbWuz7JLlxe3L+9UavwVEZHoO7tmDurSvH79lt3Grz4X9G6QV+fWjuvl06jJ94Nn7nn4bqbb6al1q09KWLxS7X94yYjurRo0aN1t/O+XQjQi/uu615l+Sp5v6OndYeX99Iz/Ebe+i4bk3D2g16aAJA+HXvxjQt/2TRo06zRw6rZbb+bE+W/s5T1y76M9o+oUzNF2Y3Tglr7ew/4KuPjH2B6tGrfs8dO2W3HqpwdmD+jQuH7Tzj/uuPt2rl3ota1TerdtWqd2g1adRyw99vyjt/7t7Z/4cCnQWpNOJPRJ7pajrm+d3K99k8Zt+03a4peu2Xunp9Ztufja67Nrf+rj07BRm/6/HHqqSup+319B9NXZ1eN7t21Yv3nHEcuOv0g08zLm7q4p/ds3qt+089g/roXFt52b0aDl4itP/jdncIemTToMWng8WMIvrxvbo1WDhq37zz/+9m40QSdXju39XaN6jdsNnLPvXrT2Ck9MrdVzQ4Acn1jTe3yaFksVkdQGhDGPDswf0aNtw8rF89nZOHiUqdmiy+A5e+/HpPVqAAAAAAAAAAAAGUR96Xzs76sS/lu7Un33tqg1KZyjEU1kROyqxW9PVF86n5prxTy54LtjaovvlkeV8OnerlT4xvalas27JSLy8vrh/y3u7DM/tOwPPWs7SMzJ8eWLtl31wNyzUgXn8D2Dq9QYfuy1iEj4wYGlyndafcO8cPFcr3f18Oq5502g9fL64UN+gSIiT/5oVbTaj8fV7mUKG9/8rWuZGj9fijUv/V3fmk5iXrLN4I4VsqVn/I8ZlB67vF+27X36bX354aHw//UrUbrL2gfWxcoXMbm6pLVnubGnY0Xil9DcOqZF30M5mw5sU0w3+vF5381ju831L9+xTzv3W780r1bRu8N6o/q9BrTJdXV6szbz7omIyJPf2lRoMe+CrnN5r0IWDzf28a474/oHV3x7+7mrfN8lQee6Nvf/Pf5CYy4iyd6y6vzkSiVbzD2jdC+eT3NkRLU2vz9Pzafyo6v7bhrfY/4zz7b9+zS0ODq8ZpNf7iRxv1GPzvmeeRApIvJ6T4/i5Qfsfu1YurhtwLbBXl4DfcPjB3u2oW2zWY8dSlUqKMdnta06YF+0iEjwjSP7F/YZdMC2cc8e9RX7+9aqUrHRqGtF2w/oUU21o0/dQbsiRUSermteyHvc4ai85SoVs7g5v1mFNmvuqbRU6FJvcLvSZuJSf/Dwxh5pWSxVRLQvMSoiIrEP9k7r33fSn7ciRJSG2XK7OJi9vPbP5lMHNq9YtLjFuGXzhnjZpfmqAAAAAAAAAAAAn0jt/zyV8d6HJ958F1IpstundrnFW09LnvQdW1pXRGp5WT5x6jdxV+df64tI4AO3pfd/b2IqIg9/GTDxZae/r/xSyUREZEjHH0t7tPmx0f2Z9guG/RJQ+9ebf35nKyKjuy+o7tb73vvjx/pOGLzN8aer/wx3V4qMapu3SOF5y46Nnle5TtHsstOlYr0q7p80fmKGZScs672tSu/BbauurGf5rv3m/CHzX7bcemdNIwsRGdG9ZF2XrqNW9djfNYeIyK24RtdOjXBXiMgTEXnh/N38AbWNRUqO2Luo+u6Cvx7pXlVXpOCAbUu/O3tJJK/E+t2OKDN81eaJNU1FZKh3XI5a/54IG5rfNKmacpb3aVteRCT6/MTKXcNqLT44ooCIPFyc9C2PvDVqwsXiM68d7O+iIyL9K7fI1zzZOz4yMKfe4PcCrRITb58Y6igiEuBn9f1fQ2oYiJQtErZ/TdN/T0QNcDH84H4fvD3x8qwBS6TPsaMzyuqLSNci1VwGLtg7rUpJEXkRW3/vgZ88dUR6lo1xqbj3yG2pVVBE5JV4j53atoBIRafua3/ro2q6b0Ize5FyP3dYvnnz2btSP/++cX33FJx5+0APB4WIDOvr7ZPPp/vq+vs6WiVTYdtCNcrlNZToojVreRok/4lORgoBYeTJsXUaTPEzzN98wpTx3Wt52BgqRUQTE3zz70WjBkzcNKzOC8NLB/s6ExECAAAAAAAAAIDPS2lnrylSLOGFWq2+cjGVJypc3BQmJm8HSe31bGvXL/0mWLGpX79su5lH/aS+iEj5WjXjI6/Y08fPxIW/Hlvf+0108vK5zoOrV8NiH588p1t/VBvbhApydWhfrfc/749/58iRZ0U7tHKPDywVBUedDRuo1n8vg/mk8d9nXGnikq5/1uoxtO21pdXeNEaePnHRqN6kRhYJr+1at67asc/JCxIfEDrWqOWeqJ48Hh7GIiKitLe3FVcPj/h3x8Te3izueZyIiF71CX9XV4c9vX7m7N3bl4+s/jtICqgSrciZFP/tXRv9FNJ1377vnZTabvnS+ZORlaZ1dNGJb7Vq9n1Ds833kxm0cM9N85pbJ24xc377iS/tVTEhYjNycLCS23FvKvzgfkVEJPDokRu5G68pqx//Mlf3vSHt43T15amI5K7bwDO+HJ1ChTxkV1zcm7M8PDziP7Cztxcrc4+Ea9vb20tcXJzI7RMnApVhq9pW3ZhwQvTjuNioazdFymitML20BoTh/w79bpqfcdWFZ/f1yJeop0Lfyr3OyM3V6v1cscyY4d/PrX2ovxsRIQAAAAAAAAAA+JyURYop3waEIjHTJ2gCA0VSWmVUqdRv31kMjdJ8PVPTRBPfjE1NFYGBgSIiomNunhA3ilqt1ivQdPDgMu+Ck8FiXVA/4u/XKvOcVu8mK5pYWenL+4uABgQEiJWV1btC9Y1N5AOfMv5H91Nj6pL2O+t2HdHmcu2EplfBwRqLPIlmFBpYWhqFBgUl7Dpnbm6eeAClUpnsq3ix97YN/a778lvWhUsUzp+/cJHC9nu0R1sxl2c0/W67++xTM6q8CSmTuWW9F4fCdKyszN6da2VlKfeTGdciX/nKle2SPqZrZpbMP4YP7ldEPv4sKfSMTPQSPra2tv6ofzxl4t0xk3in1Gq12FfpNrx1rkSNerldU6owvbTNmg1aP3nRbbNGC35/Lx18R7/oiDVjSsQcnrHgRFKHAQAAAAAAAAAAPhulZ8mU00Fay7spAAAgAElEQVSFQpm/YHrSQRF5dOFC0JuP486cOq9xcnJKGPRN+qPn5pY3NtjQrd4b5a0Drz9XGetbuLhkCzh37tHbwa5fvBjzwfguLi5yw89PnfBSff33YX3mHwlP3OWTxv+YRe1ZC9qELfxh3NGEKNHO2dn4+eXLgW973Ll8OSK3i0vCdLl3N5o6sQfGfT8vtu/Jp37H9mxcOXtMfSftn5+gPd0bjfT3Wb+xp5vOm7bkbtnAxSWv6sK5S2/eLgm8ePFJmqpLWVL3m9fFRXnPzy/qzevHO3/sM2mf/ydeKa+bm25AtFWlWgmqOcddvxqqY5HymemjLSA8euiQSlmxeePkp9bquDes5y5PjhzRsogtAAAAAAAAAABA5tOtWEVhYipaMiyFQhQK3Vr10nmBuAM/9Vh1NVSliXywa0if5f4VOrd1/7BP0c49y1+f1XPKyUCVxARdXtG9acc5V8RapGLHTm5HJ3eZdTwgVmIDjk/vPe/KhyGNQ4uONYOXDR53+EWcxARcmDeo58yDr6wSJhFGRER86vhJsW4855emIbNnbI2Of4eqduyY23dSz5VXQ9WiCj41q9ecy4U6dyidzncsJigoTGFoZCAisS9v7pv40zp/eRUSknTnuBtzmvtszjlpx4LaVonbk7vlQu06lb4/r8eo/z2O0qheX1rRfYpvOstME8N6HVsbbBo9eNfDaE1c8I3Vw3qO3/bMxOZTR23Ss5PxxlF9t96O1KjDHv4zqk2zkfteWOqkeGJ4eHiKfZKg7d+Gv3+kuBQpkuQ+kW94FCqklBcvXqTn2gAAAAAAAAAAABnGyEi3XWdRKkSSyggVChHRbdJSYe+QzvFd27Yznl7SysTI3KnBCtX3a1Z3d/q4k1Pvtet9Xk8rl93ExCJ7kf7nK/yyfVJFXRHdMmM3Lqh0d2T57KaGpva1N5acMqTYh+fm6LxiY1fFksr2psYW9sVG3a698I8hBUXEpFTZwoFLm+SsPuPGJ42fJJtW8+bUMYlNmNpn6DVxy7xSF3oXsjQ1NbUpM/5po1UbhhZJOadKmkmzkePKXBviapHdLpt9ufH+7Wb2LXBuiFvbrUmtM/rPzLG+rxT3ljcr4vFG3QW3tLylLv3Wrm4RMb9GbjMjE+uyMyx+Hl8z2UqODMyp9wHTzn+l66YsGszdMj7H9iaOZibm2fN3PVJg6saJFdP7Dr1lXGPqn9Py/8/H1dzEzNqx1m96PTb+2iWZNVHf8ChT1sK3f37HnjvSvCWhQqNJdjLn+uY6Pvu+++vVb3WSjxEfz6mYe4DMe3akd6q38MxUTZo0yZcv3/Tp07O6EODrEBfwQjXjZxFRdOml7/rRn7oAANIrKChIo9GYmJgYGWXwAvHAf9iQIUMOHjy4ZcsWR0fHj4/27dvXwMCAR30g9eK3wzE1NTU0NMzqWgDgvyMkJCQuLs7IyMjE5KNdwQAkI/5Rf/bs2V5eXp/nippHD2L/+E3zMlCUClFrREQUIhoRfSO9lj7Kwp7pG9Z/gbf94irXL4/N43/D75meUwEX64RlN4NvHLms8aiUP1vi7nGvHvrdDNDL6e7qYJo4O9JEvrh1/amOU0EXKz1VTLRa10BP+eEIca8fX7/pr5vT3TXHu3Njg25cvK3K7VnAziDN478n+ObRS1HOFYvkSHxa0PUjV/wt8nsVtovvrYkKvHv9frilS34nq4TblOjH50/4ZytTIk/882XMkwvHn1mVLuloJCIScf/06Vc5yxd10BMRCb76154AjzZV8oqISMTz636PY+3cC+Qy0xFRhwa+1LW2UTw5dzIwe7liufTf3r7y5pFLz95PuoydSpVyNNb+lsa8vOt3P9ahoJutgSY2WqU00PswrAu6fviKv1o+pLQv7OVu/cGb/9pv/+6nTs2ruel9cL8fvBR1+LOb15+obF3d8ljoJdFB9fzykYemJUvnNZGQm0cvqtwre8TPMgz0O3RT17O8q4WISNyj49vO6nk3Lmnz5vMXcMvvfpSls7tzNoM3n7HkKhQRdfijy1f8LfKXdErjYqTaAsJrPxUpOC5yxNkbk4onlxBGbm6dvcXB9v/6L/hMX9YpISAE0oSAEAAyCQEhkA4EhEDGIiAEgMxAQAikw+cPCEVEXl6P3b5X8+iZJipK1BpRKkWlVtYbq1cp/QtBvgkIx/GLVPwHaFtitECbTmUMb8/u+nNyu1iGHhw6dGOYba1aqZujCgAAAAAAAAAAkMme/hM7b5H6hb6yfD3d73rpD/zZoF9dhYjoGaR4KvCN0Lo/Zb6+q6dUVp79sX69kZv9Qt8/Fnpl3ZC6reffs2m2cGZDrdsUAgAAAAAAAAAAfCbqMwfVUfl0e/fUrV5Jp4CbIru5RLz+9GGtm8zxXfV9nk8fCPgC6Go9qnTru+Y3v0Y/LJ3cosiygl4Vinvkd7FTBty57nf2yL9XA9TZSg/5fWFz289UKwAAAAAAAAAAgFYaiY4WjUre7mUX+UC1/7RGRGKSWS8xdfQcilZ2+OTqgC+D9oBQRJG7+eIzldovHj50xp9Hd1w9uD2+Wd/SsUyn+bOm9SidTescRAAAAAAAAAAAgM9HoSxVTnHuYNzMH9WOuUXxSnPvuRQtpXzxr/qfBbGK7/UqMQkQSDEgFBFRZi/fc+WRnivjwp7d8bv5TJXdtaBrTrOPztTcPbjB2bt1JhQJAAAAAAAAAACQOk6N9XrlVJ28pnkdp7AtoKz0vY6LlZRzVp1/INlNsro44IuQmoDwbV/THO6lcrgnd1h9/Jc+BIQAAAAAAAAAACBLKRS5SuvmKv1em0NxHYfiWVQP8MVhgVAAAAAAAAAAAADgG0JACAAAAAAAAAAAAHxDCAgBAAAAAAAAAACAbwgBIQAAAAAAAAAAAPANISAEAAAAAAAAAAAAviEEhAAAAAAAAAAAAMA3hIAQAAAAAAAAAAAA+IYQEAIAAAAAAAAAAADfEN2sLgAAAAAAAAAAACCD/RvyekfQy5sRkSrROBoY1LK2qpfNSlehyOq6gC9CRgaEBmbWGTgaAAAAAAAAAABAWt2JjPr++q0jr14rFPHrKCrUoln09LmbsdFK93wVLMyzukAg62XgEqM6zVffyLjRAAAAAAAAAAAA0uZyeESpsxeOvQ4VEY1GVBpRaTQajYjI7Ygo74tXtge+TNfAf/9gpUhajSWvMvIOROTiCFeFInurbUHvtZ4ckldRbuaTDL6WNjcmFou/ompDC4Uix4CjGTDmxTEeCu8FQR82nxyQO6n3Nome2vzdw1ah57M92ePRvzVQ5M6Q2/j6aZtBuKpZjpHHUjOIdcetVyeVy6CKAAAAAAAAAAAA0i5SrW54+dqrOLVaNB8fVYtG1NLm2s3LpTydjQzTOHaJIbt8v4sTEZHjU2qOeNh108LmtiIiYuVm+ol1JylgY6+Bbav+1tAiMwZPI4Vr7d69XSvmyNyr5Gm3fHXnfO81Wbqm6fZzVencW989b4ZW9YnuTC2V78/Wj48PypnVlXxAW0BolSd/fm05uib8/pnT98NF4kJjMrouAAAAAAAAAACAtFjw5Nn9qGgtHdQikWrVuPsP13i4pXFsS7cKlRPOCftVKSH5ylWu/HHmo35973ZYDjeHtMaPH8tXtVrMmp5D2lRZWisda6LGBd6+r3HKZ5tBG80pi3eeVzxjhtLC2LFk5cpF03Vqwv26t5oyr1UGV/VfpW2J0cazDyZr329DvM0CnoWLjn2VQQv6lPpsBQMAAAAAAAAAACRhjX+AUqHQ3kcjsjkwKEKlztAr7+tolnfYn+vbuGR3rjfvhmpDk/cW5NzX0cyo4+74j1+dXdqjelEnKxPzHIVq9fvDL7kZWKZVJy3qqFjefbhveFKHX51f1LVakTyWZjYuxev2X3c9QkTil9AsN2Xf/Dq5cri2+8Nfnsz1UtRZeHpVt+ol89pk96g6cPtT/3/GNS5bwMHC2sV76J7n8YOpH+wc2bhEHhsTQxMbxxKNx+558uEEzLMj8ipqrHglIrvbGb2/CqhZx30iIhLzcM+4FmXd7c1MsjmXaz35UOCbcyNvrO1bq2geK8ucRWv2+eNOeqac7etolnvIoatLu9Qoky+7hYNn0yn/Bid1vw9mllMUm3hHREQi/Nb0qVXM0dLUKk+xpmN2P1S9GUwT8M9PTUs4W5ta5i7TZuH5+Hfu4hhXo3brTs9pU6VoLmuHIvXG/xv4cMvA2qVc7cxtPepPPhKacLLa/9+Z7SsVymVhYpm7eOOROx6p37w/Ru2239/Ur26F/DkssheoPXz3YxE5OSRvvuFn5MTgXAqvBf7puPFMlI7sWPXs0Nz+3cduvB6RrVT3VUsnf+9pmdIpmvCnVy9euvvSMGeBokWcrfQ+6hB2de/OS8EftuYs27JSXp24u4c2nXyaqNnYo2Yjz2xpLxwAAAAAAAAAAPyHbAoI3PQiYZc6tUYuhSUZpn0oSqVueNnPWi8hImmRPVsLW5tPriVw1YAZdfpvPFKjrIccTKaP5ubMuhUmS68piyYUMQ84/du4HjXaKY+tb50nqVTTou6MBT4ejbuO9rk02+v9KYmP5jeuMORZk8nzdpax8P9nzuD25R/p3t7SwlpE5MasPuta/bTzuHcZe4kQkRNTp3hv23p6XsSBgZVqtXLfUW/Wzn+uOgWt61K2ba9Zre9OKy5PFrRvPvNV27nLZhY2Dzi+ePiQZgOLBW9oYpDkLVT75fbjKfEfhh4bWcfnYMWGxUXk9b5u5RsfKzdh4oa5ueJu75kxokHdyP8d/Km0ScjmjhXbHyw0dNr6unnCji8a0XHHA0lm07qIh+ePHg1L3GKYq2gJx/gFXF9vHzG529yNx5fq3l3TtcL3bSd5P5pe5sP7ff32zBe/tyrb7V6TKQt3Fo25sHbC8KbNTS+cGuYsIgEre4/tOOmX7WOjT83p9UPvbp61Tg11ERGJ2f3zgsrb9lzIFbC+U+k2dV22tVy5/ejMbA/mt/LsO3BZm1MDHSX61KjK1dbkHTnlt2muuo8Pzx/VtmbIpiMLa2cTkbhj44fnmbXy0FyL57sGVm/gM8IraE3dyTeuWpQruLPF7X8HOiX9fmaZtAWE6qDTS4d2G77y/Cvzwh3mLZnRs5yNtimI8TSBx+eNnvFvVG5X28j7K5Y5t/9xTJN8H0yujXp84dixxBtrqsP8H4WWcmxeKa+O/9lt67aF5LYze3MpS6NyBIQAAAAAAAAAAHzrroZHbAoITLnfR/4OCXn7cQEToxa2n15LmEXLRav6lVKKiCq5Pn9P+/lMrV/8Z7azFBEpXaFwRFnnkfOHtZ6W9PqdVg3nzGvh0fqHH30uTi6dKF86Pmeyr+PQS2v6F1aKSLnyto+dq0xdeqvF8DwiEpz3h3XzuuYREZEIEdGr23dIcXOFmNdsXz/3gj0+Y3/wMBYxbt22Vrd19+6JFJfI3LVGrao7tI2noYhUsLu6fPP2m49FXJKsydAqZ04rEdE8Xtus76Zso48sbWIr8mjlhF+N+p/bMLSYUkTKlPWyvm/f4Octg3dUWjF1g6bdjh1TGpiJSKXKLsEexaYm8+48XN2x4ur3WvINP3NrcgkREXmtV33ooFJWIpKv/Q+NBq6/fj1cyuh+cL9vA8KL837elXPg5VV9CylEvCrmCn/c88a5AHEWkWjnbqvmtncVkcIz+/y+etSt2wm3qrZrOaSLm5FInlZtanbdcK/T5GZO+iKunVtX7jvo3n0Rx8hNk2a+bL/90o919EVEyng5BuQrPfa34bUHikhcWLmuP3rb64rkqt+jpeviPdefSj1HfQM9pSj1DA30dZK566yS+oAw9PLa0V0Hzj8RYODWbPK2XwZ7O6Tu3Njza+cfNGg89Zd2bvqawH8m9p+7dK/XtMbvZ/E2tYbPq5XodeC+sQN8S7Urpyci/s+f65ftP39g2RRmBQMAAAAAAAAAgG9JQRPjt5P/1CJbAwM1Hy6OmbTqVpZWurpvB8mQYop4Fk1hUtWTq1dDYk60t1K0T9Som/eRSHIb/Nm2+GX2Wo9OXSa0Ovvz28aQq1efZqvsXfjN1fQrepfX/e2an0byiIi1p2eexEPY2dsn5CsmJiZib28f/0JpYmKoiV84M1/j4X1vHtq5bPaNu7evndq/+4Y4p3SvkafGNf7hWNmVZ8YVNxIRuXb1qtw+WlxnWuJOno+eaa5euaaoMLCGWUKT0rNmNdupfkkPmn/0Bb8Jye1BqChYqMCbD00Sf8Y+vF8RkaiLF29ae1UulHDjOqWG7zktIhItItZlyromdMtmZ5c4tnvvvRF7e7v4V8YmJgkdbl69Ghtwu67BysTXsn/0SMRQRNwLFXoTnJm8PeXLlaqQL/LWnz/16D3j7yfKPLXG7lw4sr5z6udBxpw5eDSsQJeGbvoiorDxbuS1atRB32eNm+dI/pzQYys2a76bWN9eISKhz59H2BXMQToIAAAAAAAAAAASa2Frk3h10DLnos+EhqpTyghNdXR2FfYwUKa8RmJa6Bob6yd9RB0RES3GImJuYSE2PQ88nuWV+LDy443ZErFrt2Dm2oLdOk9t+cvb8dRqUSTea1GhVCrVcXHx921snMa8M+rs9Lr1Jz4u2qyxl2fJpqMaubyqtUv7GU83dWoyLarf32t8ciVUYWFhIZXHP93X3TpRN4WOvuJvXaVCJFGtynS+7ToGBklHWkndb1xsrFpXN/X9U8nCwkIKjTl3ZlSBxK06eiLnRcTA4AtbRFS7lALCmEd7p/fu+fOOe7F2lQetW/xj6/xpDD1fPn0Snb1IPouElwpXVxc58PSJSLIBYfTFtaueVR8zNHv8vxf/5/5iWeDRjl933X2ta+vkWaVWmZyJ3uLw8PCQRBOBNRqNRqNRqZKdvgsgMbU6YSdevnAAIDOo1Wq+uwKpp9H6h7486gPpww8jAMhY8U8sPJYAaaL9UT9jfW+f/dTrUO19FAppY2eb0engx0IDA6NFDEREdfbEmVipJCJmRYrkDdx79LZ+9YIKEZHIo9M6TQvvtO7HGtoyq1wdF09ZW2hAl1nV4+IbrAsWtA88cOiapnIBhYhIzNGDx2I8uhVM3y2pDy2e6Jtn/P29/fOIiGhOnQrQ2j/q/MTG3+8vOv/0pAqmbxvdixTRX37kWFS/ZvF50KMNvXvtLT59VSePggU12w/8He5T10RERHP1H19/yYAFXbUzLVjQ8cX/jt4S7/jJgjfnN262p8rm3T0+bdjcRYpYjjt8LMigmIOIiLzcM7rzKuuR6waW+tSCs4C2gPDF0bn9fxi9zi88W6luK5dO6ehpmY4LBAeHiJmp2dvXhmbmejEhIZEiRkn2V93ZtPREgY5L3mzIqXruHyiXt/+uV7FEHoPg81smb/2rzthZ3Yq++VrZtWvX9OnT355uZ2cXGxsbHBycjlKBb5AyLCw+9I+IiFDxhQMAGS0yMjIyMjKrqwC+GrGxsVqOqlQqHvWBdIiIiIiIiMjqKgDgvyYqKioqKiqrqwC+Gtof9TNWlxx2Cx4/9YuMTG4SoVIhFjo64xxzZ24dOq6uecMXTh2x0bZLIdXJWYO3RiVMoyref3Sdhb1bdLSe3r2M8QPfBSNH/Ftq/dwUZ7Tl7b70598LD9wdKWVFRKRCv+EVF49s2clmerfS5s//njlwaWiT37u5xy+hmVZKS0szzV3fLYeq1TF/cmL3ijlLnovh+YNXAx0LJtE7YOcPjcY+bfjbmgYmAf7+8Xdrms3GqvXIXhMq9m46KnpsI6ewy+snDVoe1v+Eq4iy45BWE1t2bpRzxoi6jpGnlg5f+dwquYAw4uH5o0fD3m+zcitfwDbti0yW7Tu88sLBLbvZTO1YVO23dfK43dJrjKvIiTSPlJhOnWEjChce1aSvwcS2hdU3d88YOvl2s/2FtO4uqKOjI89vnL/z3MzJ3vxL2odQW0C4aXT/dX4iYmoYdnRWO69ZyXa09Fl1eGTJpI9pNJr35o6KRqN5N2npIy/3rtymW3dOScM3DTHZS7f53qtM3YqOhiKiaVV6Ys+Zy7fXnufjqKVyAAAAAAAAAADwrdFTKHYULuB17sLT2CSm+SpEDBU6Wwt5OBgksxZohik+YvW0W71nd6m52r5w1c4zN5Vd1O+5pYiIQ6dNxxVDB8zpWXt4iEness1X/W9Si+wpj6dw6bvsx3WeQ08nvHbqu/OIzqDBC7vWHhpu5Vqm4W/HJ7e00TqCFmVG/TH1Xr+ZDcv+ZOlWpkG/NZc6/d5x+OwZvk1XFf6487kdfz5Sh61vl3/926b846/4jStYdvq/+yz7jhrf4penGtvCNYbsnjHCUykiFs1WHvltWN9pI5rPjXUo2WjYX8sudfufXZLp1MPVHSuu/qCt/tqonW3TsXRnnu5/HpZBg+Z3rHUv0tKtco+tq8eWUKYrQE1M4T50/2GDAUNmt//1fpSlW+WOmw+Mr5b0fLg3nBp2abJiRMvCD+c92Ns506dOpoFCy9zedZ0KTzqd3MHEtAWEzzYP7La/6JylHRJ2tIz+58cWCy3Hbu6XZP/HG3r3Ol1z2YyGyX45qA5PbT4juvfGsdXi/z28fPnSPyGjFhEZMWJEvnz5pkyZkpq6AaiDAjWzJomIplMPXRfXFPsDAFLp1atXGo3GyMjoK1t+HshSw4cP9/X13bJli6NjEn8O2KdPHwMDAx71gdSL34/D2NhYXz+zf/8FAN+Q0NBQlUplYGBgZKT9F8IA3ol/1J89e7aXl1fKvTPC85iYXrfubgsI0ojEr7oZP2nJ3Mj0aCHXQibp3oLuv0UT8+rZKx0HW9OUu35xYkOfBWvssptn9jqx8TRRwc/DDXNkS8M3/rjwF4GxVvaWWreXzFraZhD6rLzs88kXyOaQQ//F3bth4hz/T+ze/XuSo17SGxBqru8/8LhQk0qJ0sGYB8f3XdEvVbeEfcIsRI1aLQbmZm//58ba2tra+t2elwqFQqFQJLfzJIAPxCmV8X9Io1Qq+cIBgAzHd1cgTRQKbavG8KgPpA8/jAAgY8U/sfDdFUgT7Y/6mcFeX39DvhzjdDTbQsKexMaFq9Q6SoVSrSmeq0Ahky84sfnMFPoWDl/SjLa00DPLkYp5lxlFYWiVwzDlbonpmmS3z5xiMkzawtWYV4/9rtz0D0/DBrz6papWNLm8f98jlYhI6Kk9R4LzV6uSU0REExcVEREV+24G453jx144FyueeKtDfd3H+5dMWfDX8/hLRj/cseecYYkS+T/3txMAAAAAAAAAAPA1UIU9qXTmypwQdSX7XAs8ChwrXepViTzuWV0V8EVJ1d+5hN/YOmnU9K3Hr95+GhonIgpDG+eCFZoPmza2hVuKM3H1irftVWn0rBH9rxZ2CL91/kmOjuNrx0fSL/eM77gsqsPSOc3iY9SnF86/sCjs8f7swpwN+7Q5NWZJj/a78+ezjrh//bm5d/+hXuZpvU8AAAAAAAAAAPAtOPb86fE4iyWlC3Z9M11QExLzQiTdm/QB/z0pBoQv9o9u02na309ijXIU9Kxazs3VweDlXb+rF87vmNpy7/o6EzdsGFTGTOsICtvy/WY6Xrl46W6wYbVWnkWdrRK+Io3dq/v4xBV4s7qtKtaunE/HUh9ugmbg1nra8jKnT157/FpjWe97zxL5rHXScaMAAAAAAAAAAOAb8DpOJRpNjDrhpToubPL9gBciTmqVCEuMAiIpBoSPfu3UauLfypKd58+a0M0rx7veqoCji4b2HPXrkFaDPC8vraY9IhSFac7CFXIW/qDVyK26j9u7lzqOXj6OSZ9ulrd09byltV8DAAAAAAAAAACgSg47N/+nA06fXWtukksRcy4k0jq7bd2IZ3seXK0h7gdym6Y8BPBfpzUgfLS8Y9/dYe79fX1nVzB5/5CObYXeq/7JoyrcaFmnYa2uL6xmlJlVAgAAAAAAAAAApIaJRd6jxU1+eRp8IUZjbmw1Irf795YGYWHmi/xDNcap2nkN+M/T9pVw548lf4fm7rd86ofp4BvZGs6b0mBzh7Wr/p5Trb5+ptQHAAAAAAAAAACQJjZm2X9yz564xcrUZqQpuxACCZRajp09e14MK3iX1RL9WXh5FZbQCxfuZnhhAAAAAAAAAAAAADKetoAwJEQlji4uWqfb5smbVymhoaEZXBYAAAAAAAAAAACAzKAtILSx0Zc7V69GaTv/1vXraoWHh3sGlwUAAAAAAAAAAAAgM2gLCMtXKK+IO7JrX0jyXR7t3X9N8hYtap7hhQEAAAAAAAAAAADIeNoCQvs2Q77LGbi2a6cNz5I8rr49v8PowxrP9j6FM6c4AAAAAAAAAAAAABlLW0AoVnXn/9rDKWDbd8Uq9F524kXcuyNxL04u6VaubL+DMUVH/jrSU+s2hQAAAAAAAAAAAAC+FClEe+bVZ/71u94P/Rcs6FpuQS+L3K7/Z+++42s8/z+Of845ORkSMoSELCsiJCH2ik2tmm2NtqqTqlZpS+2iqKLVrx9VHarVVqfaewRRpRJEFjEyxAqJRGSd8fsjETFyEnJODvJ6/lPnvq/7vt85j57rvs753Pd116vtorxyOupUQmqO3qJ6h/d+/3ZKQ3XZRAUAAAAAAAAAAABQWsXe+2fjO+SLfT1f/XH+ojX/hEdGntgXra9cy7dtX79WA8aOf5GHDwIAAAAAAAAAAACPkxJNDqqwDxj28XfDRET0Wq1epTI4MSkAAAAAAAAAAACAR9WDPj1QoVIpTBIEAAAAAAAAAAAAgOkZKhDunPfyquiS7MSu84eLX/AxUiIAAAAAAAAAAAAAJmOoQBi95fvv95RkJ852wykQAgAAAAAAAAAAAI8BQwXCYavP9c4qyU6UFV2NFAcAAAAAAAAAAACAKRkqEFZ08apYZkEAAAAAAAAAAAAAmJ7SwLpts4YMmbwuucyyAAAAAAAAAAAAADAxQwXCU7tWr94WlVFmWQAAAAAAADIKi5sAACAASURBVAAAAACYmKECIQAAAAAAAAAAAIAnDAVCAAAAAAAAAAAAoByxKGZ95uXTMTFZxTRSOXjWcbExViQAAAAAAAAAAAAAplJcgTDis871PituJ86j91xZ3N5IiQAAAAAAAAAAAACYTHEFwiotnh8YWLGYRrZtqxsrDwAAAAAAAAAAAAATKq5A6NV/9pcTvMokCgAAAAAAAAAAAABTU5o7AAAAAAAAAAAAAICyQ4EQAAAAAAAAAAAAKEcoEAIAAAAAAAAAAADliKFnEA7/PbGfrpJrcbvQpSdc1HlUtzdiKgAAAAAAAAAAAAAmYegOQltnN7eqFVVFrc5MPPjH5+8+08K9auO5R00QDQAAAAAAAAAAAICxGbqD8P5yLh/d9ufq1atXr90Xd0MvImrnNjWdjJ8MAAAAAAAAAAAAgNGVuECoTY3avebX1atX/7kzJlUrIgq7mu1eGDJ06NBnujao/OB1RgAAAAAAAAAAAABlr7jKnv7G2ZD1v65evfr3Lccu54iIooJHyzZ2J0Iuvbj2zNJOZRERAAAAAAAAAAAAgLEYKhAe+GzQuM/X/5uYKSIqB++OLwwYMGBA/+7N3SIn1mz6jarIhxMCAAAAAAAAAAAAeEQZKhCGrf/t30RxCHx15uwxQ7r4O6vLLBUAAAAAAAAAAAAAk1AaWFfR3lYhkhr23aRRo8ZMXbL+6KWcMssFAAAAAAAAAAAAwAQMFQiHrbl0Zt/Pc0f1rpXx78/zRvcJdHOp1/XVj77ZHnVNW2YBAQAAAAAAAAAAABiPoQKhKGxrtB3y4ZJ1x5Iunti8fPKwIKekXd/NeL3bi8sTJH3/D19ujrimKaugAAAAAAAAAAAAAErPYIGwgIVTg+6vf7xy9+nLCQd//2xM/6auVtlHvxvV06+ae+P+Yz9fG51u4pgAAAAAAAAAAAAAjKFkBcIC1tVbPDN20V+Hz186uWPFjFe61so+/veica8tCTVNOgAAAAAAAAAAAABG9YAFwoLN7L07D5/27baoS+dD//7fmLZVjZsKAAAAAAAAAAAAgEk8ZIGwgOX1s6FJehtro4QBAAAAAAAAAAAAYFqlLRDK2c2fffK/7fHGyAIAAAAAAAAAAADAxCzMHQAAAAAAgCfKDxcvh6SlmzuFZGVliYhafU2lUpk7iyz1rqVSKMydAgAAAEA+CoQAAAAAABhT8PW07y5cMneKR8v/edcyf5USAAAAwC0UCAEAAAAAMKb6FWy6ODqYO4XsTEnVi/jaWLtZW5s7i3DzIAAAAPBIoUAIAAAAAIAxvefh9p6Hm7lTiDr4gEavH1PNZYSnu7mzAAAAAHi0KM0dAAAAAAAAAAAAAEDZMXQH4d9jO35xtLgdXIvJNGIcAAAAAAAAAAAAAKZkqEB4/uiePXtKshNn42QBAAAAAAAAAAAAYGKGCoRv7da/VWZBAAAAAAAAAAAAAJgezyAEAAAAAAAAAAAAyhEKhED5ptXk/0OjMdgOAAAAAAAAAAA8IQxNMfr9cx5T/ylincK6at1GTdsN/mD8wDrWpggGwLR0585od2/XnYrOe6lfuTy3lreqfWelj695gwEAAAAAAAAAAJMyVCC0sLS2Lqr4p8s8vfeP0J1/rF7z3prdCzo5mCIbANPQ6zWb12mDd4pCKXpdwUL9mVO5p0+qmra06P+cWBjqHAAAAAAAAAAAwOPLUA3ghVWnXih6rT77/I55Lw6dvnDU3OdOzGtOMQF4XGg2r9cG7xSF4nZ1UERE9Hq9iGj/Oyg6rcWgF82UDgAAAAAAAAAAmNbDP4NQYeXWddrqeT1sY5Ys2a43YiQAJqSLj9Pu3Skioi/yc6sNPayLDC+7TAAAAAAAAAAAoAw9fIFQRESq9unTUjKioxONkwaAqWmDtxffSKnQ7tpm+iwAAAAAAAAAAMAMSjszaGUXFws5eeGCiIdR8pSWXq/X6XS5ubnmDgI8knQ6XXSUgXsHbzXT6xLjc1JTFLZ2ZRILAJ5YWq2WYQlQcjqdzsBahvrAw+GDAwDGlfeIFnpX4IEYHuoDMIvSFgiT4uM14uXlZZQwRqDX6zUaTVpamrmDAI8iRXqaraZkg1e9PiMhQVfdzcSJAOCJlferQVZWVnZ2trmzAI8NjUZjYK1Wq2WoDzyE3NxcPjgAYER5Q/2cnJycnBxzZwEeG4aH+gDMonQFQn3MipX/SJXX/VyMFKfUlEqlpaVl5cqVzR0EeBTpLVQlH7ra21dS8lECgId19epVvV5va2trY2Nj7izAY8PS0tLAWgsLC4b6wEOwsrLigwMARpSamqrRaKytrW1tbc2dBXhsGB7qAzALQwXCmymX0osqJugyL8f+t3b+ezPClK0WvtfeFNEAGJuiYiWxUEtJbiJUKBRO/IgAAAAAPK6u5mryni1wKYcZ8AAAAADczVCBcMUA19F7DG9u7TNk2Xdv1TFmIgCmo1QqfXx1UeGiM/gYQoVC4eahsKtYVrEAAAAAGM3e1LSP4uKDU9N0oheR6YlJ31y5+o57tdFu1ayVSnOnAwAAAPBIMFQg9O40eLBrEesU1s61/Ru37fXcUz7cSw88RlTtO+siw4tppNdbdOxaJnEAAAAAGNPUs/Gz4xIUijuuCUzMyf7g9LmfLyev9/N1s2KCLwAAAAAGC4Tdpv7SrcyCACgTSq+aqrYdtPt2G2rTqKnSr2GZRQIAAABgFPPiEz+OSxAR/Z0zhuS9PHYjo/vxyION/W1VKnOkAwAAAPAIYXYRoNyx6NlX1aa9iIhScccKhUJElIFN1c8OMUcuAAAAAA/vdGbWlLPxiqIb6PT6ExkZ8+LPl10mAAAAAI8qQ3cQFtCnnzkcX7F5gyoSuerdxSHZd622DRq3YKi3CcIBMAml0qLPQGWDAO2e7brYk6LTiYgoFEqvmqoOXZS+fubOBwAAAOCB/e98klavN/iwcVGI4ovEpCleHpZKA5VEAAAAAE++YguEqSFzXhg6e6Pujf0Jn1eRhOBvl32TpbbQ5GpERETlUKtZUM92zE4CPHaUtb2Vtb01SYnaLz4VEcWLr6obBJg7FAAAAICHtPlqarFt9KJP02r/SUtr72BfBpEAAAAAPLIMTzGafWBSuy6TN15z6/lSt5q3FjqP3HYz5VzYhoWD69ooqnSc9M3iIbVMnhOAaagt8/9haWXWHAAAAAAenl4kPjvb8O2DBeKy7p4YCAAAAEB5Y7BAGLf8g4Xh6jYf7w3b+HGP6reXK9QOXo16jfsldOto3XfPv7rygqlTAgAAAACAoihElIqSzhpqUeKWAAAAAJ5UhgqE0T9+eyDH++3FEwNt79/ANmjWvEGqDbO+OGqSbAAAAAAAoES8ra0NzxF0u2UFG9NGAQAAAPDIM/T14dSpWKnatXuggTZ2QUGBcnrHjnPGzgUAAAAAAEqsXxUnXXFtlCLVLNVN7OzKIhAAAACAR5ihAuHFixni6OhYeFH7TyLOhc5oeXuBQ+XKKrlwgUlGAQAAAAAwn9Fu1exUKsM3EepEJnt5KJlhFAAAACj3DH13qFrVRmJPnMgstMi6sqeXh5PV7QURx49rxcHBwVT5AAAAAABAsaqo1d/61NEb/J7f3clxZHXXsssEAAAA4FFlqEDYum0bpXbfmg1Xi2yhifh74ylxaN7c2/jJAAAAAABAyT1X1fkH37pqpVIhUvguQaVCISLPVnH+o4GPSsH9gwAAAAAMFgirDB7/smfKb6NeWZWovd/6jJCJg+Ycs2j49uhOFiaKBwAAAAAASuoFlyonmzd+061aFbU6b4lKIV0d7Tf41/+tgY+tSmXeeAAAAAAeEQYfT2Df9Yuf3/NJWTfMv8nwRVujr9zMe+C5LivlzP4V73aq12lBhLrlrF+mNVGXSVYAAAAAAGCYp7XVEu9al9o0t1AoRGRxDc8tAQ16VXY0dy4AAAAAj5Bibv2zbTNn0xrr0WMWrBzbfeVYhbWTR3W7jKTEq1k6EankP3T+ks/G+XL7IAAAAAAAjyQlc4oCAAAAuEexxT11radnber62pavl609GBEVFXU2pWJg146+vn5tnx05rLULs5MAAAAAAAAAAAAAj5GS3f1n7dX97bnd3zZxFgAAAAAAAAAAAAAmZvAZhCWh02RnZ2v0xsgCAAAAAAAAAAAAwMQMFQg3Tnr66bF/XL69ICti/Vdf/XX0RuFG2990tnYfu9dE8QAAAAAAAAAAAAAYk6EC4bl/NmzYfzrz9oKMPZ+NHDl361WTpwIAAAAAAAAAAABgEqWeYhQAAAAAAAAAAADA44MCIQAAAAAAAAAAAFCOUCAEAAAAAAAAAAAAyhEKhAAAAAAAAAAAAEA5QoEQAAAAAAAAAAAAKEcoEAIAAAAAAAAAAADliEUx68M+CnD85FYVUZd9Q0RfeIlI7s0bUslk8QAAAAAAAAAAAAAYk6ECoXOdRo1SS7ITBzc7I8UBAAAAAAAAAAAAYEqGCoSDvg4bVGZBAAAAAAAAAAAAAJhecVOMAgAAAACAB/H2qTM/Xbpi7hSi0etFZNy5hInx582dRS61aa5WKMydAgAAAEC+EhQINTeSYmMSMivW9K1b1fqelWkn182ZHTdo5dhAU8QDAAAAAOAxc1OnS9FozJ0i302d7qZOZ+4UAAAAAB4thguEWdG/jHt+1LLQVL2IqJwaj1y+5ouBdgc+Hz/rxz1R56+mXL9xM1ujF+fRPcomLQAAAAAAj7pnqlSua3PPBbZlLiMjQ0SsrKwsLMw/e5BKuH0QAAAAeIQY+pKgi1w46MUvjyurNe3TKcApN+7g1iXPP2tzxHnZ3E05zt7+9RrVVSuUNg7VvYOe8SmzwAAAAAAAPNJ6ODn2cHI0dwpJTk4WETs7O2tr81crAQAAADxSDBUI//12+XGtx4jNJ5Z1rSQiojm3+KkG78y96dBjydG/RtXm+wUAAAAAAAAAAADwuFEaWHf6dKK4DRieVx0UEYsaL7/YUUTd8/URVAcBAAAAAAAAAACAx5GhAuH16zpxcXEptMTOxaWC2Ds5qUwdCwAAAAAAAAAAAIApGCoQiogolUoDLwEAAAAAAAAAAAA8Vqj3AQAAAAAAAAAAAOUIBUIAAAAAAAAAAACgHLEoZn3SjkUzshwKXsaG5sjNQ9/PmLGncKMKzYd/0MPLBOkAAAAAAAAAAAAAGFUJCoQf7bhr2aHvPzp0xwLn0R0oEAIAAAAAAAAAAACPAUMFwv6L9villmQnardGRooDAAAAAAAAAAAAwJQMFQirN2xf/QF2pT+z+9daHQeXNhEAAAAAAAAAAAAAk1Eab1e6f/73tvH2BgAAAAAAAAAAAMD4jFggBAAAAAAAAAAAAPCoo0AIAAAAAAAAAAAAlCMUCAEAAAAAAAAAAIByhAIhAAAAAAAAAAAAUI5QIAQAAAAAAAAAAADKEQqEAAAAAAAAAAAAQDlCgRAAAAAAAAAAAAAoRygQAgAAAAAAAAAAAOUIBUIAAAAAAAAAAACgHDFmgdCqopMR9wYAAAAAAAAAAADA6CyKa5CbGPLHun3hJy8p3eu36PHc0w3si2qpeuaHGOOGAwAAAAAAAAAAAGBchguEl9eP7vr8kuPpt15P/KjLwo0b32lkafJcAAAAAAAAAAAAAEzAUIHwxpaJry85nltnwMypL7d2zY3Z9Nn0/+1476V5XY5Nrf9gR8k4tWvdriNnrlm71W/To3djF9V92iRsnLfsQFqhBd79pw9vaikiok+J2LIx+HhceqUaAe16dW/goHiwwwMAAAAAAAAAAADIY+gZhIfXb7ikajJz8x9Th/Xu3K3/qEVbfnjZVXN847bEBzpEdtSPkyct23/NqYabPuqvmRM+3XP5Pq20cccPxN6s5HlbVbu8cNf/WzphyqrQTJcaLhmHf5zy4dL/0u+zPQAAAAAAAAAAAIDiGbqD8PTpy1LzlW51Cu7Xs+3cra3qu+AzZ0TcS3yE9P2//n2h4eivJnZ0EHmuZcUx7/+0Mbb9y3Xuugvw8sWLes8ur414uvJd21/a/vO23I7TZr/TxEakv5/6zVk/73y2ab+qJQ4AAAAAAAAAAAAA4BZDdxBmZ4s4ODgUWmLp4GAjer3+AQ5w81BImCKwY5u83ajrdmrrdinkwOl72l28dFHt6uJ0z/LLBw7EVmrZsbGNiIhUaNKpeaXYkAP3uwcRAAAAAAAAAAAAQHEM3UFoFFeTk/VVmrlb3nrt5uYmV5OT9XLnLYRpFy9m2ioOL578w/GLmkrV67Ue8GL/wMoqkSvJV6RaJ/dbjRVubtXkvyvJIvm3EB4/fjw4OLhgP1qtVqvVZmRkmPrvAp4M+qwstYiI5OTk5PLBAQDjybuiKicnR6fTmTsL8NjQarUG1up0Oob6wEPIzs42/OECADyQvBF+bm4uwxKg5BiNAI8gkxcIU1NTxdbW9vYB7eystFdTb4hULNzs4sULcj3nYsUhQ0ZUzk04+Ndv0z+4+NGS0Y2tU1PTxK7Q9nZ2dpKamlrwOioqauXKlQUvXVxctFptZmamKf8m4MmhzMnJKxDm5uZq+eAAgLHl5ubm5uaaOwXw2ChJgZChPvCgOBkBgCloNBqNRmPuFMBjgwIh8AgqrkB4ce/yhRa3J/6MPp4jN8N+Xbjwv8KNbBoNGtW5iKcSWllZyR1ny5ycXLGytLyrWfWn3p/X2dXH00EpIs2aN6x47fXFfwYPa9zdykotaRqNSP4WObk5ona4vbmjo6Ovr2/By5SUFIVCYWFh8sIn8IRQKm/9V6nggwMAxpM3/FEqlUqloRndARSmUCgMr2WoDzwQTkYAYAparVav19O7Ag/E8FAfgFkU9+06cdOc9zfdtSzky/dD7ljgPLppkQVCRwdHSUtLE3HNe52Rnq6rUK2y1V3N7KrX8y28lZ+fm2xMupC3/Zm0NJEKeWvS09Klcr3bJctu3bp169at4GX//v3VavWdT04EUCRNbk7e1Ts2NjaWfHAAwHiuXr2q1+ttbGxsbGzMnQV4bKjVagNrVSoVQ33ggSQnJ4tIhQoVrK2tzZ0FAJ4cqampGo3Gysqq8KRpAAwzPNQHYBaGCoS95m6ocbUkO1HX8C9yXeU6tR2TIyMvS92qIiLayMiTqtptve5qdWnXl99GeQ8b1SX/YYP6aymp4urqKmJVp471msjIjIGutiIiGZFR8da1B1cvSSwAAAAAAAAAAAAAdzFUIKzRsleNUh9A6dets9umdatC2o1p45gb+9uvB6TF2+0cRERyLkaExek8GvpXt67qbhP3xfKlzlVHP+1XzeLqsZ+/357TbGQrexFp0a19xZl//nS82esBdjeO/vTXsUodZjbn4kcAAAAAAAAAAADgYTzcAzxyUxNOn89yqFnbtULxU23XHDLl7aTpC199flmF3HSpPWDaW0F5M22lH/5x9tdZLy1fNNBVUff5SSMuffL9lJGr1Wp9rt656aDJ73RyFBFRBb467YXZM6e/tMfOKvOGZcOXp7/ir3qo1AAAAAAAAAAAAEC5V1yBUH/tyG8/rD94TlOjRacePTrWdVBc2TZh4PDP913IFVFWajB45tf/G9OqssF9qN26fPh1u+vnE1Otq3k42xSUFO1bvzGnps4173mCaq+eE5Z2Skk6fyXLxsW9mr3V7YeWWtcZOGtlr2sJSRkV3d0dLXmaKQAAAAAAAAAAAPCwDBYI9We+7Rs0Yn2SVkREZn/YaMru3zymDfh0n6p+j+EdauhP7/7r53e7XbY6vm1kzWLKdgpLe/da9ncdu3ItvztKiwprR7fajvff3trJs5ZTcX8NAAAAAAAAAAAAAIMMFQiv/TZh3Pokx3YT/2/O4AZWCdvmvj3+qSYV0ix6fBW6/o1aKhHRTFnUtdHYKZ/sGfFVR27sAwAAAAAAAAAAAB55hh4heHjHzjRV0Ixf5gxqE+DXtNe4HxYOlPT0yoM+eL1W/kMALeq89nInxdV9+6LKJCwAAAAAAAAAAACA0jFUIDx7NkVqtmlT/dZr28aN64rUrFWr0M2Cdi4utnLlyhUTRgQAAAAAAAAAAABgLIYKhFqtiIODw+0FdnZ2Jg8EAAAAAAAAAAAAwHQMFQgBAAAAAAAAAAAAPGEoEAIAAAAAAAAAAADliEUx62O+eb7rDpv8FzkJx0Q0hZeIJEdmmiwcAAAAAAAAAAAAAOMqrkCYHhuyI/bORfcucTZqJAAAAAAAAAAAAACmYqhAOGqnZqS+RHtRqIyTBgAAAAAAAAAAAIBJGSoQKpQqCn8AAAAAAAAAAADAk6S4KUbzaa+E79h1MCIyKjI6/qZTbT8//4DWXbsHupRwcwAAAAAAAAAAAACPhOIrfJqL+5Z8+M70H45ev2u6UfuAl+d8NX9ky8pKE2UDAAAAAAAAAAAAYGTFFfcu/PyMf7t3V0ZW6vTu//2+9UD42StXk2IO7/xr2fiu9tEr3mrdbPSOG2USFAAAAAAAAAAAAEDpGbyDUB//1fBRa6/VeOmXncsG17K+tdjZqVrdpp36v/jqNy+0f/3LV97vd2JZt0qmjwoAAAAAAAAAAACgtAzdQZi1fsoH2242nPhb4ergbRXqvvbtt8PcEr764IuTJssHAAAAAAAAAAAAwIgMFQhDNm9Ol1Yj3ml2n+pgHseeIwd5yvFt2y+ZIBoAAAAAAAAAAAAAYzNUIIyPTxaPJk2qGto+IDBQKfHx8UaOBQAAAAAAAAAAAMAUDBUIVSqR7Jwcg9vnZGXpxNHR0bipAAAAAAAAAAAAAJiEoQJh7drucvnQIYN3Bx4NCxO7hg1rGzkWAAAAAAAAAAAAAFMwVCBsOWBAdTn8+ZS1V4tooD/31cc/XlY3b9lYYYpsAAAAAAAAAAAAAIzM4BSjQTO/edUr8ceXn5m2NT777rVZZzeOe+69XVkBUxa+5mLChAAAAAAAAAAAAACMxsLgWvsei3784FCP+bO6+6x66pXh3Zv41qvtorxyOjrqyObvvt1yLqtS609WTmykLqOwAAAAAAAAAAAAAErHcIFQxC7o0/+iu3w27u2Pf186fWvhNbY+z8xf9sW7HaoXtwsAAAAAAAAAAAAAj4oSVPcs3bt9+FvEm/ERx8MjIyNPXtBW9fbz8/MLaFDDntogAAAAAAAAAAAA8FgpaYnPwt6zYZBnw6BeJk0DAAAAAAAAAAAAwKRKVCDUp585HF+xeYMqErnq3cUh2Xettg0at2CotwnCAQAAAAAAAAAAADCuYguEqSFzXhg6e6Pujf0Jn1eRhOBvl32TpbbQ5GpERETlUKtZUM92KpPnBAAAAAAAAAAAAGAESoNrsw9Matdl8sZrbj1f6lbz1kLnkdtuppwL27BwcF0bRZWOk75ZPKSWyXMCAAAAAAAAAAAAMAKDBcK45R8sDFe3+Xhv2MaPe1S/vVyhdvBq1GvcL6FbR+u+e/7VlRdMnRIAAAAAAAAAAACAURgqEEb/+O2BHO+3F08MtL1/A9ugWfMGqTbM+uKoSbIBAAAAAAAAAAAAMDJDBcJTp2KlatfugQba2AUFBcrpHTvOGTsXAAAAAAAAAAAAABOwMLDu4sUMcXR0LLyo/ScR56bZud5e4FC5skpiLlwQqWGagAAAAAAAAAAAAACMx9AdhFWr2kjsiROZhRZZV/b08nCyur0g4vhxrTg4OJgqHwAAAAAAAAAAAAAjMlQgbN22jVK7b82Gq0W20ET8vfGUODRv7m38ZAAAAAAAAAAAAACMzlCBsMrg8S97pvw26pVVidr7rc8ImThozjGLhm+P7mRoplIAAAAAAAAAAAAAjwpDBUKx7/rFz+/5pKwb5t9k+KKt0Vdu6kRERJeVcmb/inc71eu0IELdctYv05qoyyQrAAAAAAAAAAAAgFIq5tY/2zZzNq2xHj1mwcqx3VeOVVg7eVS3y0hKvJqlE5FK/kPnL/lsnC+3DwIAAAAAAAAAAACPiWKLe+paT8/a1PW1LV8vW3swIioq6mxKxcCuHX19/do+O3JYaxdVWYQEAAAAAAAAAAAAYBwlu/vP2qv723O7v23iLAAAAAAAAAAAAABMzOAzCAEAAAAAAAAAAAA8WSgQAgAAAAAAAAAAAOUIBUIAAAAAAAAAAACgHKFACAAAAAAAAAAAAJQjFAgBAAAAAAAAAACAcoQCIQAAAAAAAAAAAFCOUCAEAAAAAAAAAAAAyhEKhAAAAAAAAAAAAEA5QoEQAAAAAAAAAAAAKEcoEAIAAAAAAAAAAADlCAVCAAAAAAAAAAAAoByhQAgAAAAAAAAAAACUIxQIAQAAAAAAAAAAgHKEAiEAAAAAAAAAAABQjlAgBAAAAAAAAAAAAMoRCoQAAAAAAAAAAABAOUKBEAAAAAAAAAAAAChHKBACAAAAAAAAAAAA5YiFuQMYmU6n02g06enp5g4CPCYyMy1FRCQ7OzubDw4AGI9erxeR7OxsjUZj7izAY8Pw50Wr1TLUBx5CVlZWbm6uuVMAwJNDq9WKSE5Ojk6nM3cW4LHBV2PgEfSkFQgVCoVCoVAquTMSKBG9QpH3j7xPjnnDAMCTh2EJ8EAUt0YmRa3lMwU8BD44AGBcCoVCr9fTuwIPxPBQH4BZPIEFQpVKZWtra+4gQPF0p09Jxg3zZtCmXc+72s0yMd5Ca+4LeSytlPXqmzkDABhJVlaWXq+3tLS0sbExdxbgsaFSqQysVSqVDPWBB5KZmSkiVlZW1tbW5s4CAE+O3NxcnU6nVqsZlgAlZ3ioD8AsnrQCIfAY0W7bqDt3xtwp8ul3bTX7rEMKp8qW9aabOwUAAAAAAAAAAE84boQHAAAAAAAAAAAAyhHuIATMRv3Km6LTmjeDVqtNTU0VkUqVKqnVavOGEeYiBwAAAAAAAADA9CgQAuZjZWXuBCJarT4rW0TEpoKYvUAIAAAAAAAAAABMjylGAQAAAAAAAAAAgHKEAiEAAAAAAAAAAABQjlAgBAAAAAAAAAAAAMoRCoQAAAAAcohVjwAAIABJREFUAAAAAABAOUKBEAAAAAAAAAAAAChHKBACAAAAAAAAAAAA5QgFQgAAAAAAAAAAAKAcoUAIAAAAAAAAAAAAlCMUCAEAAAAAAAAAAIByhAIhAAAAAAAAAAAAUI5QIAQAAAAAAAAAAADKEQqEAAAAAAAAAAAAQDlCgRAAAAAAAAAAAAAoRygQAgAAAAAAAAAAAOUIBUIAAAAAAAAAAACgHKFACAAAAAAAAAAAAJQjFAgBAAAAAAAAAACAcoQCIQAAAAAAAAAAAFCOWJg7gPFFRER8+OGH5k4BPB70en1OTo6IqNVqpZIrBgDAaHJycvR6vYWFhUqlMncW4LERERFRbAOG+kDJZWdniwgnIwAwrtzcXJ1Op1KpLCyewF9WARMpdqgPoOw9gaexy5cv79ixw9wpAAAAABgZQ30AAAAAAIziSSsQtm3b9sqVK+ZOATw2srKy9u/fLyJNmzZ1cHAwdxwAeHLs2bNHo9H4+Ph4eHiYOwvwOFGpVPb29vdd1ahRowoVKpRxHuCxtnPnTr1eX79+/erVq5s7CwA8OQ4dOpSWlubh4eHj42PuLMDjRKVS1axZ09wpANym0Ov15s4AwGySkpL69OkjIl9//XVgYKC54wDAk6NDhw43btz44IMPBg0aZO4sAIByqkWLFlqtdtq0aXljfgCAUQwbNiwyMnLw4MHvv/++ubMAAPDweOQYAAAAAAAAAAAAUI5QIAQAAAAAAAAAAADKkSftGYQAHoidnd1LL70kIi4uLubOAgBPlKFDh2ZnZ/v6+po7CACg/Bo2bJhOp6tTp465gwDAE6V3797NmjVr2LChuYMAAFAqPIMQAAAAAAAAAAAAKEeYYhQAAAAAAAAAAAAoRygQAgAAAAAAAAAAAOUIzyDEo0Z/PfZAyJGYU6cTb1hX8fBp2aNbYBV1wcq06G1/7zwedzXXztWnZe/erapb5a/RXY/e+vfOEwlXNRWr+bTs1btFdcsHOWrcxnnLD6TdeqW0cqjq5depT7cGjioREblxcPnc9XGF2qvtnKvVbtqjd1vPCorC+7ke8tWnm+LVAUOnDmqgKlh6be//Ldx64e5jWjYdPr2/dwkD3ojds2Xv0ZNJOfbebfr2beNunb9cnxKxZWPw8bj0SjUC2vXq3sChIE5OQsi67YdOXlS41m3RtU8rj7z3I2HjvGW3/9ACXr0mvtHaroRZzCt1/9L5m89bNnxx6nP17rjCISV4yYJtScW9qwmb5y270n3msIaqotsA5Y82NXrnpuDIuEtpUqmqe93WPboGOOf1vNf2/N/CEI9XJvetXdA4K3bt4hX/WbZ9bVQPL/Wd+4n5c8YPoTnVu417q33lwst10X/M+jEsx6PHhJFtKxXTEaXuXTo/uNrwqSXoH0vT82sjfv3o59MtXp/Uu0b+kox/v52zLqHRC9Oe9c3vXLThv3y0+lzLNyb28irY7vSaWd/9l3W/PdbuO/WV5tb3WyMiEvX71FVHdbdeqWycqtUK7PZ0x9oV83rtyzsWfbH7yu3mSqtKztV92vTu0dTVqvBu7n+WKeItrdxu1Lin3IpK9OBy/vt+xt5qxt0ngHJHmxy175+jJ0+dvpht51LDr0PPTj72ytsrwzau3Rd5PlXh4F6/fZ8eDZ3zv66WcqhfTCdcyqG+ETphfUrE9q0hx2MvK6r6dRzQu/Gtv1u0l0LXbw6JOp9duVbjzn071a5wa4v7D/Ul6vfpq45q7tl//cEfP++vuGfxI6iUQ32J+nXqb5bDSv4lCygXih7qS9zGectPNRnzbpeqBa3Tw39avPpU5adGvd6u6p33FOSE/jDjzxiFd/8pw5veMejNCv3+4z9P6X2emzmsocpwR1RGQ325eeib2Wtvdh7zTqdbf9nlXYu+2Jnd+o0JBSP7jH+/nrMuu+vY0R2cb22mP/bTlN8i77dDZcALswYV+WjxrP++m7XmdMFLC1vn6t7Ne/Zu42GTd+wSDvUladuCJcEplVqNmNDbs9Di+7+l+d+tjOba3qULIwOMu08AwKOPOwjxSLkR/dfMMR8sXnf8itrVs6rq8r8/fvTm2G9PZOatzT66bOyEFaGZLr4NalqeXTdvzPS1F/OeoZlxaNE7E1YeyajqU9/L4ty6T94e/9PJ3Ac58M0LMeFxOg8/Pz8/P7/6dVxVF4K/nfjuwv3X81Zrrp4JP5Hu4JevgY+HXdrRP+a/O+mXs9rCu0nZu2HTiRMnQtfuOFZ48JZzJTY8JrOK350auFUsYbr0sK8nffh1SEolr+rKsxsXvDd7c/7fff2/pROmrArNdKnhknH4xykfLv0vPW8LXfza6RMWbjtXwdPT+uzWTz+cvj4+7yeRzAsx4XE697ui+NVyNvrFAtf2Lp28dG+q0TfPSY4NDw8/snZX+B1vvaQc2LwtPDw84ny6wf1mXowJj0vRGWwDlDPauDUTR0xYeSjV1qu+r6dd+rHV00eM+yE6W0REci7Hhkcl3ShonH1mzYxpK2Icugx56u7qoIikJ0aEh4dv2xRy7Y7F+vDd646Eh4efTs4VKa4jyrkSGx5ZzCdZRErb86uq2WaHH9x7LPnWAs2xvVvCw0O3/HOyoM3Z0N3HosS+WuHt7Nzq3wrslBEZfkFd+9bLmk6GLjxISzgRftGqTv5ppmZlzanNi8e9tyw8/wSXdelk+Okcl1v7ql+nmuWlf1bNHDNz86XCz4ou4ixTxFta17WCGJMu5Vx4zKVMo+4TQLmivxb248QxE7/ZFpNu6+bhpIvf9dX4ERP/jsvv0a7vm//OjN9PiUf9+m6aE6unjfl0X95YsNRD/WI64VIO9UvdCV8J+Xz89F+OZVWp4ZIT/vuM9z6/VW28vGfuhFlroxVuNRwu7/ty4qRVJ3NExMBQX9ISIsIvquvcfYr1tH+gt6sESjnUl4TN8yb/cEx7z/JSDvUlLf5EsW2A8sXgUF9uXogJP3np9sVvN058N3XmmiSPPoODqt7ze6EuNS48PPz4hq2H7xwPZh3evuF4ePitL9oGO6IyGupLBRera+G7D0Rk31pwPXT37vDwkB2HL95aoo85tCP8nMrpjusaHTxv5a1nmxwenmJXMPT3MFQ206WcDQ9PrdQgf1Mv+4yINZ++O/HXM3lnixIO9eXc7rV7T5w4EfL3ntg7dn//t7S2871fx0oj50rsre9rAIByhDsI8Qi5eXD5rJXnm7z/+TtB1fL+19Q/Hzx37MLPfmz57RsNFGl7ft+S0Xb84vfbVBCRfo2s3piwftvpvsPqyPnNv+zRtJsy773mNiLyTBfPiSO//Sm434wutg9y+Ep+PYcMKbhK67n6U19eujHkjbY9HfIWKGu0GzKkxe3mQ5oueGXulh0xQ1+vf2vR5eA9kRbNnu4Svy54b+ibjZsXvr5N7d1xyJCGD/6miEj8xm826PosXDCsjqXIc80qjpz8x6bYHq94y6XtP2/L7Tht9jtNbET6+6nfnPXzzmeb9qsqOUf+Wh3t/tKyGX1dFKLrUmXCm6vXhPYY09Qi/w/tPmRIzYeK8gByrsSGx7rkmGRze3d33YF9x0Y0bFzwc/zVkP1Rbu5uicn33wJAkTJ2rvjhtNfwJZ/0d837FeD5Z4PnvvXZNxu7LxhQ9c6m2WfWzpy6MrnxuDnj7r6g+DY3d7fofSHJfZ4uuBJXd2L/gRx3d8fEQq1K3xGVtud38g9wk78iI3P6trMUEX1k2DGNp6db/JHQhFfqeYiIpEZFXVTU6+t/x7XKLs0HDmme98//Lv8ZnNik75CnK9+z8/ur0qT/kF755xTR9V495q1fN/33qn9Q/gEq+HQdMqTQdcnP1J/8ypIte873GOSev8TQWaaM+nYAeHjXtn8+++/srlMXv97EMe8k8mrCX1Pe+/7zNc0XPVtdzm/+7YBNjzmzR/ipRKS3t/aV2ev3XAnqV8U4Q/1iOuFSDvVL0QlrI/5YHlJl2JKP+7gqRfrVn//6vD93DW/dr4o+ZsNPh+z6L/p4WC0LkZ41Pxrx5e8H+k/uYGt4qC9VmvQf8rSD4YOWWimH+pJ5MSY8sYVO5D4X1zDUB4zoAYb6khHx/bSZmzSdJ88eGVjUZQVqd3fH0H3/3gzqUHANRNah/f85urtfKzzWL3VHVOqe3zPAv9LvIZEn9R39FSKSfTQs0s7T0+LMkbDUZ3o4iIici4rKVPsH1Ct8e7XCK2hI/g2GWbtifj9o2eq5IR1KWobzbD14SNCt70iaTl+NnLB+a+SgNwPyFhQ/1JfTe4ITKnbs0+TwuuC90S/WuSNZ2fTtAIByiAIhHh0Ja1cFS9CHb96qDoqIwrn988/u/SLsVLw08Lp0Nd3Br0OD/GGoyquGm2xPTxcR3bkzcaqAAU1s8req2qCBs27P+QsidR4+jYObWwVFvKLoqXgqeHpVkSO5uXqR/Ebng/ecUjf54PmusQc3bwk+PKp5mweZ+yjr8IqP11k+O+35hvnDz6RtC5aENRg5odaOHQk+vWfVyduZ2v+N5X++IhYicvnAgdhKLV9unPd3V2jSqXmlySEHLvfrV/VYyIGbvi91cFGIiChdO7Wvt/LHkONjmjZ+gDz6Yz9NCfF4p5/Vvg37Yq5aebTqPahDlbitf24LjUu18mw9cFAnLxuR02tnfZ/1TMFUG+e3zl96ofP04VV2zv9i63m5vnX+5KsDJ77R0k6ffjp4w5bDp69kKGyr1Gr6VO+OdSrmtV+e1mdyq2vrtv4Tk5Tr5Nt5QN+mLuqEzXdtflc2dWBQ4O4N+4+/2Tgw/2eDlAP7I72DBtn98ndBo8y4/eu3/Rd74YbapV7bnr1aedjctRfJuRy6ZUNwZOL1HEsHN9/2vXsGuqhFJHX/svmxTaf0VG3btC8yIcOudpt+A9p55E+hknk2eN320NOXsuyq123W4+lWebOcpB1YPu9k4/HtMzZs+fdcWsXa7fo+29I6fN1fe04kZVb07vzcM81d1QaOCJhV3NkzWvt29VxvF/zsW/fuG7LZIk2k8K8GOefWz5r6/eXG780ZG1TFwAQELm2DbH/dt//y0/3yt9aG7/9H07JX4IlfTha91QMrfc/vGRBg/9s/UbHSrr6InA4LS/cd+G69P2eGhl4Z6lFFRBMVFSueA/2NftdFHmW16i4KldLAO+no5VVJwnJv//paurOMlLRjFxFtyvGt6/dHxCVr7Gs06vz0Uw0c785ZRK8up/6c8VeFV972Pr1u5+HYKwqXht0H9vJzyttae/XYpvX7IxNSLJy9/Dr06eZrrxAROb1m1h/Ww16rfmzt7oiL4tKo+3M9a1zd++emg2euKlwDnx7cq15FQ0cE8LjQhv+++pj9U/NebXK7S7Hw6PN830MrEmKuSXXHSyk3nRoH+uYP7qy9alSV02lpIpVNMdQvthM29lBfkvcs+Xx/9Vcm96+dtz997F+zV1xs994Im+17Mpq+3S3/PGzX5r1Vf2rFQkROhxy4VKNrx1p5X40c23YM/HLRgcM5HTqU3VBfH/7TlA0Ot6faPvXnjFUyeEbzMyUb6hd1Uoj5c/qyfdcl+/dpky8M/XjI3ZOflmior08/uXPd7uNnL2VWcPPv0LtH43tH1ZkJ/2zcdODUhXRNBSevht2e7lbPUSFFfgHJ26RUQ/2ijgiYU0mH+nIz6oepMzbkdp4ye0QjA8NfRZ2gtpo/9h+82aFT/k8zWYf3H7FvO7Du+l+MePdu6Yf6Ct8Af4stUZHnxd9dRBcRdkzR6K3nLZd+eiTsZo+OFUTSo6POK+r18nvA8XRJWVR3qyJKZdF9wD1DfX30nr0XK7V9a1gzxa49e4OjXq5X/8F6kBIP9SX34uGNmw5GJ6YoKns36967U+17RtXaa1HbN2w/du5alrqiq3erXr1bu1uLiDb0h2kHa4wb4vzfxj1Hz6ZYejbrPaCbd97WpRvqF3VEAIDJMcUoHhlpJ8LjLZq2a3lXGcerz9TPpvfzEhHvof9bOaVr3hVT+ptxOw+csmoY4CMiihbvrPppbOtbV5jqL4Qdvaj28HB52CR67c3LkX/9GaJs1qVZkSPj3IR/Q5OsAwNvX9SVELznjHXz9s1t6rRqXTXz372H7vuQqiJZ166qO75+Z8GERfHB6/deqlLHPef8+ct2XtUyQn5f/tnsjxd8uXpfolatUojIleQrUs3N/VYAhZtbNUm+kixyIzk5y87NrSC7o5u7TWZycsYD5dGnxIUf+uvL1efc2vd+yvfGvs+mTfxw3t+ZDbr161onddeiWb/EiIjcSIo8EX/7kSuZl2LCz6XqxMnvqa71HcSh/lP929WyEkn4e/qExbsvWnvW961uGb910cT5W5NvtT/817Jfz7m2frpPO9fEv2dN/SlW7tn8HhYNg1rKP/sKZuG/FrIvqk7bNq4FDW6GfjXmvWWH0h3q+tV1St37xYRZ6+LuusT55uGl4z/6LVxT2bt+bccboT9/NHFFlE5EJCf5dHjYluUrwyo26dG3c93rOxdMXn4kR0Qk69jXY99bvOeyXS3fGlbxW+aP+eCnU1oRkdyrZ8IP/Lx0fapv594d3ZPWfzJlwqTP91o269m3feX4NXMXbLhk8IiAWVV1cVUmB/+6Jvxy1q0ZbtT+z04a36fwt++cuI2zpnx91mdUMdVBEZEqbYN8Yvbvv/WUDV34/gP6Vm0DjHxJUul7fkW9gAB1amTkBRGR82Fhl2sGBjZp3MT+VGhomojImejoHHv/AA/j5s5Lm3sj6ciq9Sec23VsVORvEpkxB4+lOjYOLLgfpZRnGSlpx64/v2HaWx//fUblXr++pzLm948mLj18/a49FdWrS3pixImQn7/ceK1Wh769W9hGrZw2Z33eRE4Xt3709vTfIzVu9epWyQxbOXH0p3tTRETkxvnIsE3Lvwur1LJnj2YStnz6pAmzf0yo2bFvj4a6Q1/N+PZwjsEjAnhcXDgRnuzcup3vnacDC//nP/lsXEcnEUXjN7/9/q3mef26Ni1696HESgENaxh/qF+iTtjoQ31xrul07dD6vafyz7W6iJ1rD990ret09fz5TBfPyom7fvpy/sezP1/+539X9GqVQkR/Jfmqwt294CmGVm5ulbXJyallOdTXp8aFR1+4WbBVemJERGJ6iYf6RZ0UXJv271jXRqo16z+whft9whU71Jdrez8ZPennyByXeg1q2Z7f8Mn4Bbsv3TWsvrprwfhPNpxRufjW97K9sv+rSTPW5N3eVMQXkFIP9Ys+ImBOJRrq629G/zTtoz9SW334scHqoIiI1AkKqhy6/99bfU7mof2hjm3b1jFuNdwIPb+lf0A9RUJUZLqIyMmwsMz6gYFNGjdSHQ89phERXUzUSb2nf4AprgXU51yPC/l5y1n3ru2LfGrhPUN93Yk9e5Md27bzt2zYqnmFa/uDw++dhbmYw5aoYxfNyV/Gv7Nwx+UKNf3qu2YfWTFp6srom3fuSRuzctKk7/9Nq1S7fl1nXfSaTyb83z+ZInmzzB7bteKbYE2DLv26N9L/u3TKF7uvi5R6qF/kEQEAJscdhHhkJF1Ikqr+LsUPK4989ernO6+l5bj0mrEwqIKIKCysK9z6X/nmmc2LZ/94rvague0e9M6CxN/e6f+HQkSv0+n0eqnY5M1P2zjfXq07umry5HV5/8zNvJZ07qbX0JlvtiooX53ZsyfBuuWLzaxEfFq3qvz3puCDGW07FMx/kbFlWv9thX5Vr9J33vLhdQsf36lte7+vV4Qc1TRtaiESv29fnGfn970VKceuiebsV9Mia7Vu610zNWLHorEhcXM/f9FHlZqaJna2t2fYsLOzk9TUVJFKqaliW6fQ1Bt2tvlrbPP+0LH9/7hzsop7wuRJsW3+2qBWlUR8nj+1bfSfjk/N6dfUWsT7ueObJ52OTRefot5jW4+Ahh62Eu8R0LReVZH0xKu2TV4YNb5/HZWI9PVKH7okIlaechYR0ZyTRtOGtLUT8a2jPh08PiLqqtS5c/P7sAho19ri031ho5o0sxBJDgmJqRs0vqoczV999o/lm2yfWTxvsKdKRKRfi69GT160rvnnzxT6XeFi0k3Pp0eNe721g4joW9qMGHsk4pL45j1sLC6t9tQpnZxFxNshaf/I7RHnpUnN8xtXbEhvO+nrcS0qiMgzPbxnvPF/P+7oOfMpRxGRVJcOI/o2thLxfiZ009TDnu/O6xGgEvHse3DrZ7HnRFwMHxEwG+ceI1+PmLtixeTXfnKs0aBho0aNAps2a+hR8XaHpUncMntK6EWbSunnIuIzu1YpbmKfKm3b+Xy3a/+lAf1dRHQnQv7RthoXqD52R5uSd0RFMELPb+kfUE+xPyrqplTLDQs96xo42lVRs3Gg9aehR7OfapceFZVs6e9f14g/dkQuH97/G4WIXqfV6UXh3OH98Y0KvZnXD3w1+Wzea11OxpWEOH3DNz96scGtAMWcZe55S2u/+NW9M0eVoGNX7v1h5bmG45ZNaFVRRGRgjzpTX1+wovmqd5vf3omhXl2uJ9h1nj6gkVrEp0Zm2O65EdE5fV3l359XhrsOXfLJc25KERnY0nH0+yt+jw56o55CRG6K/+DhHTxE6lftvmfXcl2rGc+3dhSp98KpbSEHYi9KM0+DRwTwONAnJV0Q1x7F/7h7I3jBW8v/vZ6urz3kk9caqkTEOEP9YjrhUg71S9AJe7Vr7/XLjpBTL9etK6IL3xeS5jeoo6tcunZNUncumG3v276Ft9fl0LVz3jk8fNHcftXTU1O1Nm62t0/HFe0qSmpqqqQaHupL5DfD+393x9nrYc8IRhjq3/ek0MGrUT0XS8mtEdi41n1n0yhmqK8L/Xn5Ec9Xl8/s4aQQkYFP+88fMX/prmYzutyed0SbeCHb55kxH74YaC0iA/21w6afiMkakHdDyn2/gGSVcqhv8IiA2RQ/1JfsU79OXx9+vaL1tVMRF3Oa2hdzS52iVrsgl7/2HUzv2LmiSNahkCNOQZ/UVmy8o1GJO6KiDmKEnt8+IMBT/3dUjL5b0/jQ0BTvLo3sLK0a+2WvDI3St/I/FxWdZd/M35jXAh5YMKD/ZwWnGZVn7+l9fW7/6FrMUF/7/+3dd0CT1/oH8CcEEjYEwgx7CyFMB8hSqbg61N462jpv1bpa7dDW3mq17W2vVeumarXaWrVD7bR1IwhuXEwBmRLZJBAg8/cHYQoBBNT+/H7+0rzjHF7Cc573nPc95+aZ+EpuVIS3FjECQoP1TifE3ZgjaJlp+YFLGrDowIfDHwgwXQf20mO7fhBHf7xljo8uEdH46ANvzf/i4OBds7xazlJaKLKI+Pf8JdE2RETRFqWzf7mdTyGeRERUXGL1/sqxjgwiD17FpfhvUrJpGL+Xqb7mEgEAoF9hgBCeGCwWixrqG7re0X3M0uVD5VWpv+z6bDVv7efjmh47rS+I27v562M5eiEzPp7/gnv7L3faj//57rqSiEg7cNpHEx9MNCyiFi15xoqISKVsEOWeP7R76ft1n/1vgrM6JTN14PPVZcmqcmRlVzPOJOSM9fLTIyJSZZyNE+qHzAliERHDM3QI55e/45JqoprvUnUHzvzwhVYP6bG4D2SiJqERfrF7z19XBAczc86dK3IfPcKR6J5CQfUU9J91C3x1iYhinD6Yu+PAuYmrhrPZOiSSy4nUCbxUJiUdUxYRm80muVzecmapTEosVlOebxG1YMkzbUalOqgMEREZ2zctxM3hcMjCXj3PJoPD4dBdZfefZzMK+feaEEVtSV56cXHR3ZsnM+RKQfMzvg4+3uqLxDAzMyWFolvn1RKEhzI/jU+WDRyoU5GYkOoZtoRLt9UbxRkZ97SqLnz14S31B8oqiUqYX0DUaoDQ5fn3V6uk1UVZKcXF+VmJF0tJS9lcKUsf76ZuZzMzs8ZKSe9k3GUPfHVw01oLpuHhgo07MnIoJoiIyMLejt10eUzI1t6+8XujyzHVU1QpuiwR4PFhu4xdsWNYcWry1evXrydf/u123KFYlt/095aOVb/BK7l1TTJ51cZnRVsXfrJp2+At74So783rr+xecySbiIhMw+a9M7opkpgPDffeeSa+ePyLNopbCYmqIe/4MantAGH3A1GTirNb1p0oJiIiXsw78yMa3yfvVeQ3FgicVL+kpqtCRcnphoFTXYkYgkA/Zey124oQSXo2Y8Awfl9OA+w4btncxmunUtSVZ57cv/7Nj+vWrRqpjkw65i58vkXjvxvKMiSlN26cvHQv2tlFm7rRyjxwSfWsOR1VosvAnpee3qDUO/rZit/VR9QJGXWS/DIa1HI+jVGdPHy81ZeNZW5mSMVKJVFxRkaNW0wkT90ZxfSICLE6cjOjnLy4RET29ur21ZRjSob69pymKpqSQqnoskQA+AdgsFksamjoOtfXE0x8e3mMtOzyoV1rNthtWBZu1vh5FwG/syapRRdBuHepfneCMC8i0vX7v85nzfRwU9w6d742aFYEpzHXrzGK3vjFFCcmEb0UabV44fc/Xn3uDV82m2QdZfRdpPrkOGbZ3KFtetEftkXopp43Cl3SmOpTcUaGSKvu5LoPEtQfyMoUClnBPaKWB42Yfi9/7KeqryjMyhIW56WeviEme6WmG5Bep/qaSwR4bLpM9an02i271z7+ZEjax4u2r/8meKN65KjzuOoYHmZ7OD5JPGKkUd3lhKtm4ZPcqKJtqd0ORE0eKtXvIvLzBALz76+k5pLzjeQC+8BALhEFBnlsPno1i+wz04UsgW+fDkD5TFo9VaBFRKSSS+6n/P7tqiW1yzYsCWmMtBpTfZJeO5sothgVOYBBRDr+ocG65xLjkucFBjffjLS/pEZ2HU221GVgV9xJv6NokOxfs6JpsFFcqVWSn19PrQYIrUe8uWaETCzMSS0uLsy5fC6PlK7N4Ux/gI+j+lhDczMdhUJJVNTLVF9ziQAA0K8wQAhPDB7PjlGee1c55zzXAAAbQUlEQVRMHm3SSMX13Ut33Bu96oPhzNxcsaGTE9fY3odvT8TnlV6a9XdCwbjJ9kSKwpPrV8deZodOXfPuOF9uR19sS0HMKGsVEZGWlUVHFWBbuvP5Dk3/E/jp515c+fOx2xPm+xERkZZTxJQpg5v3njb5zxUzdv55bbrfUB0iVdrZcyXUIFo/82pjPiitJfn1uMTq6JHqGSuY5s58Pl/zJTAKjQz8alfiTUWwUfy5EsH4YZZEZMYxI12XQb5Nj4Zx/f3tFH/mFRJxTDmUIxIRqW9jxSIxmXuZEZmackgkEjUvmaISi2sYZuZN+Tjb0o3Pd25feEe02qzByNCwIqOaQt5hV0JDwenYtTvOV1u4ujrw7Jyd7HXTWzbq6el3dEyXdeOHh7LWxF+TDnQ9n5DuGf5Oq4cRlSol6btHjh/Z6gZhIpm6tj5eVXXj4JdfHs7QcXBztrdzcHCyOp/fslW/g0rV1tao9Fu/OqVjaMCqE4vV3TNajNbTLmo9eLE0lwjwuAgTdv9UHDjjX/4+Q8f5DB33MhGRNHPPG++sPyrYMsmeiEg3ZMHqqQP0iF5fFL1o9ZYtg73ei+AQEenYDRw1yp2IiHR5pq1OygkL4+86ES988cXihCRG6FJfJmW3Lbb7gaiJvmvoKGbjXEaGTnpEfRH5bfwE3P3X0u7eLL2l4//WAAYR6QUGedftv5aeRWky5zGCPl3lzsB2AJ/ffJ38BshTp286ciJv5KuNSzvpez4zZUrLNETTXzi4cMGvx9MmzfPtTivT3UvaZWBXqZRkFfDc+KDWfQ66Nq1/v5qjOlNP78EHz2tqasig1TvvZGBoQCKxiIj7QD0YHazWorFEAPhH4PF4dD43V07WbeJ1bcKX7x7QeXntgoCarIIGC1d7E46zL4eIfI3zLrx9/FJN+CjD7gT8TpukZpqDcC9T/W4FYduISLd9fyRmz3SqPpckG7J4qBE15voW/IFO6kcSGfb+ArP9aXklFMzh6MmqquuJ1LcBIpGIOGacrlL9dj+oBj1O9UmhUBLzgU973ih0o24aUn1SqpRk6jtqYrh5qw+1Ldo8d6S4n7Rn3bbjxUZOro52dk5OjpyrrfqaO7gB6W2q30WJAI9JN1J9Mot5d8WzjixyfGPOpUUbN3w9cNOCAH3SFFedwsNsfoq/IBo5NDnhmnnYyy7UfoCw24GoyUOl+l1Ffk8/Afu3tLRC2+Q7nIBZjkRE5oFBjjsTr+V6FmYyBszm92mfqIk9n89vChW+/k41N17bfTR+RshYDpHmVJ+kl88mSUj250evnmQQESkapNSQdO7qguAhTSG0e5e068CuUqmY9iHjx3u2bBpPhg5tm+Y7v2xet/+6lOfqYm/n4OjEo/KWjfp67dYFot6n+ppLBACAfoUBQnhisIOHhRh8ceTHzBGzPFq+mJJLp07flUQ4WZLi9Lfv7DBeeeCNQHU2IZfLqba2loikt3ev2prhvXTT4nDrTr/T5p5h4T15PEzb1NSQ5HJZZ9s5Tk4miswKEZE5KW6eja/QGzhzxXj3pnRQeHL9pjNxCRUjx5p1v0yjIRGB275KvDFSL75i4CsRJkREuo4Olg3ZpWKixn5qVUVFFZmZmxEZurnpHklNrZ1obUBEVJualq/rOtmWSMfN1VF6KjWbBje+spiZmqpwiHbppz93pUyqoMauAklRUUUHe0jid28+z5mzfeNocwYRKc6vPfhX78vV8gkbqvtx/NWi+wmZXmHLuETNK8GY2PIMakr0PYKD1Q/Pld38K6HKtM347J2jmw4UhX629zVvFhFR4aGkA10M15laWbMrc/NE5KU+qzA3r4Er6Pwr107PSwR4FPRrso//XBYwxn9oyx0dy9LSRFVSUaEkeyIilqlJ412gYfBri2Nurty+8ZT3qhFcIqa1b7h1h2flDA3z2flnfL6nMFEZ+q7gwc7EntO1Dwxv/VRwn0R+d1+B7i+ph+MKZfxpvo1nMQ4Ictt64tgfehUcXwFPw7G9pmdqqkuFCnknmxn2Tk5McUW5lIjVV61Md9jweHSJYR4Y7NVYlDQ34c/bTIfgVkH2IaK6tbU1XcnLU5KP+qx5uUKyse746/Og/mlHAOCR4oYN4+/f9dPvkwe/YNvSN1iRcCKhQHuysz6Jj297+w//9TunNU25oVDIVRJJHRGrOwG/8yapE10E4f5I9ckyLMJrz1/n070qkpjhbw9kExFxHR30q8tKpeTW2Aksq6gQM8zNTYl0Xd2Y+1NSlCOCtIiIhKkpFVy+q/EjTvVJJmteyLukqFBKju13eOSpPlnZ8pgimSE/MLDxminuXfnzUp2Vf6vDFcnfb/xdMWHzNy81vuqX+tUfPxdrLLC3qX7PSwR4FLpI9bWIiPRN1bOKmg9fNO/iwi827hi0+c2BRpriqkN4OO9QfJJQ58pVy/BJTn1Q0YdK9buK/EwfX2/G9usHjVN0A55Xv2HMCwyy+v7qTwkVDc4xvoYaDu41Q1NTbSqXddLMtE71qe7i2Yt13KgFS0c23X2oco5+uutC3MX6IeF9O08x05ZnpShk2QYHq4uqST99skC/zduZwr9jv77t+v6ut4cYMohIdOzO3quaz9rLVP8hSgQAgD6j1fUuAI+IUfiMV/lVv3y0fOeZtKLqBnl9Zf6lg5/HxikHTpvgRaQnCPaRxx/87lalgkhZk3Nsz+93LUMGuxBJzh/9u5L/wtQgY6mkWZ1M1XWRnVHJRDl/HzpVrOsn6HxBLB0dHZLUSohInnz2vMgk4oXnBPxm0eOH81S34+LLelSw/uDIYFnSvtj4mpDoUHUG7zlypMPtH7f8lVOrJHn5zX17Tkp8ooZwiXQHj4w0uvbz/ptiFanE1/cfvmEcFTNIl4gcokd6lZ3cf6xITiQrOvb9qXL+qOh+6ec2NjZWXT97XNigIkX5ld1HrreJKepppCQisYLBYmuriGTVeUnfHr4sVUokXa843cUsVAx+WJjupV+2nM4YEBZq3maT76gxdjcObT2ZJ1GppJWZRzZ+GntWyGqdWKvEIjFps1kMIlXd/bQ/vvu7gOoktZq+NAzBiGiL2z9uP5lfpyJlTebR2F9zHZ8Z3t0l0x6iRIBHwThk9FBWQuzH+85lloilCkV9Zd6VHzb+kMYNH+L+YJKgFzD7zTFGyTu+/LNE85fXNDScXxi3/eAFrdAw374YH2yvbyI/ky/w1rodn1DpFeDf9C6BZWCgfVF8fLauwNe9ecfqnCsXL2VVdnlCqTDl4sVb9+q72k/VUJF+5HCimOPv59DpTjo62lQrqevLVqYbuJFjBkmO79p3vVxOilph4s7P1/6QJjVuvctDRHWLiGg/0ck9392okJNKev/Szm/jmUOfGdzdN8gfth0BgCeJ5ajZLzrmffP+hwcS7wjFUpmkPCv+60+/STYe8cooGyLLgIGOpce/O5otVhIpKtN+/PZEpcuQIIt+SPW7FYT7I9UnbnikT3FC7PcXdIdFBzS2jjpBMcOMEr+NTSiqV6mkwqQd+y+wh0QF6BKZRo0cJEs4+HN2PZGiNP67P+7ajxw5gPEoU30tY2ODmounz1fKiGTFp/f8cbd1k97LVJ/qGzQ1lxpSfVbImGh2wrc7koqlKlV96c1vv/jvvuQqg9Zpi0wsrmOwWDpEpBAXXfvh4LkqktTWaCqvd6n+Q5QI8Cj0LNUnTsSC+WHK05u2JVVrPq9dWLjDzRMb/7zGDQtz6Yd691HkNxAIXGUX4y+o/AJ8msKXe2CgUUZ8QomZwLc5cPZ1qk/KupIbB3+9Irfx8+1w/iqiVqk+iZPOXpXyoifEtLQyvmOfDTetvxx3SdL1T9kzzs+M8So8GvtTpkhJcnHeiW3/3fhrHrUZKhWLxMRks5hEKml51tlvfrtNDZJaTRNP9zLVf4gSAQCgz+ANQniSWI/+aJP5vk07Yt//rU5BRMQwcnvm9VUzhnOJiLijFi3O+Tz2g+mHddgMmZTlEPn6+9P4LKLc7ByprGzHnMk7Wp3LbsqWbVM673btQOEPi8f/xCAiUimVShUZuo1dMifKuNP9eXZ29Mul83f/NaHkTKLYevRIfps+cMfhI9x+2BeXUPL8Cz2ohO6giEGbP4vXGjUzqHkyHruJ7y25t2bLm1N36mjJ5Ebezy97a6wlEREzYPaHr3yyeuX0s4bsuhqW38yVs9T98Bbj3l2Wv3L9gqn7DVU1UtuY5SvHcJvLKPxhifoHbdHxAtddc4qZEhm3ZfvcV/abMMlq3ILJA/6b0riF6+Fptu+n5bMKZm9YFj3l2TNrN06fssdIVavtOWHWzBF7dmye+5XFvhGdnrj14cNNOt6H4R0eZvjbb3e95w7ltt2i5fbS8kXln3+1aEosW1sqZbmMeGPZxDbfBob/+GlByV8vnfy3KbuuwTxi5sxJFZsPLVth9fWnQZ3VieXz6vI5VV/ELpwcq6sjq2c6PPPGuxOcuvuYhYYSR/fx+z8APWIStmhlzZ4dBza8/ZP6Hoxl6TdyyaoZAR3e0enyZy55/vry3Rt+9fv0eV7ns5GZhIYLvtqabDR6Mr+jvxLNgaj2xEfjT7U+jD9v35qYNtN9FvZN5NcXCNxU1zId/ANa+h6dggLNDhVUefu2iuq5f6775OzAFT8tHdzRWVqIL3/7yc766Tu+nNjBA7OpO2aM39XSzGiZCya/My2g81UO7ezsKCnxfMkI8y5aGcvOzvBQjMMXvFf4xfpVM3/VYSkbVOZ+U99/fXCbBoLbaVSf2+lZuTGL3y3+YtOHM46y2ap6OSfgpeXzwro9g6uGEv168aMCwKPFdJ3y+XqrXZv2bnj7QIOKiIjJ4Y9Z+tGrAw2JiBwnLp2X/7/dS17Zy9ahBrmh16ilyyY4EmX2TcDvaRDufqrfkyBsHhbB37ntls1LS7yaGkGtAdP/89oXa9a9PnmDDkOmMgua+t78oSZERAaRCz+4+9F/33rlqBGzVmIS9saqfzX+yBpTfUrdNWP87rZNrM6wFT8sDu5BPZvwn3sl+PKuz2dcMDFmMF2nzHq+cFPjqEG3Uv3OGwWy8/DUP7pz/uycRTtfH9RxOt15qk9s/xkrZmz839q5k7TYJJUbeY1d9ubINjPv6Yb+a8pfH++ZO/lnY60apcPof896tnzbnnnrrH56q7PO+t6l+ppKDMFj2fAY9TDVJ+Ohry+MWvjxls1nvD4YpmHVQPuwCIf9+9J5kxY6dbRZYyB6dKk+WQoE1qqs+97+/s2T5zO8gwLYx85R62cB+yTVp8QvJoxfr25mFEqVjmXw7GUvuXawo/rHUaf6o8Ounbmm8Hg12qn1VqZgRBT3r9/iLogjhvfpogdkOe6td4T/2/zOqwdYOvIGLZvQOSumebdp4dzGvhKZuGnV1ERTfbnEIPCV6dODt+355A3rL7e82NlJe5fqayhxygOvrQMAQB9jqFR4fwWeOCpp9b2CEqmBpa2lCVur3SZRSfF9EZlY21oY6TQmnOLClLyq9l9ktqW7u2VHizZ3SCLMyClrNZuotr65Dc/ahK3OaBUVOWnFLEcfuzYJTk1Ram4l29rTVp6TXW7o6MNrl/5IitNzyvXtfRz0yrMyS/WdB9gaUNfk5Tnpxdr2Pg4mbdNpRe39/OJ6I1s7rn7bl3FU9RUF92qN7Ow4rHad7YoaYV4pWTlYGzQfUSfMyC7rYNpUIzsfR9O2h1fn3y7WcfGy0SciUlTeTSszaLqkDcJbFwuNBwc7somIFGJhnrDOkOdgqc9UKmQqLR1m45mklfm5pdo2brZGWqSqKy8oqtQyt+dx2AwihaS6TsfYUFGSmVVn4e3EUc9Dcf/2xTzD4EGNaw60PryFrCw7o9zA1dNaj4hU1fkpBXXcpv9V5aXc03by5jU9/SYX388X1upa8myaf5V1wozsBmsfRxMGEclExQX36w1sHawMmESqBrFIpW+iqyzLyagy8nCzaByiVVZkJqUTP9RDPUypkoruF5bUG1rbWRo2PWIhL89JrzT0cLNkERE1lNy5U2s+wNmMSURUk3/larV9pK8VdVpif7xdBdBTyvrq0vsl1XI9MytLc8PmcCItzcos13f2ahO/ZGXZGUJ5059eK+Ki1Dy5jY8jh0FE9SWZWSVsnrcjR4uI6oSZ2RIzLxeudheBSFaWnSFs/96BAW+AM6fNn0ofRP6WE1UbO/jYtzwPoqzMTS1q4Lp4Wjd3nNQWpd2tMmrbDIiLUvIarL1czFs9bSUvz0kvVlp7uHHbrbjUrr4MlpGFLa8lijSU3LlTadS6RGoMaoViIzsX9v0cja2MSb0wI1vMcXO37OJBj24HdiJlfUVRYYXKxJZn0dTqqCrzUkp13TysdIk6ieo6tUWpeSr75uskKUi+UmE51I+nPoWirryoqJJpbmfD0W0K7bVFaXeVPL76FyAquH2P6eJl21jFsvQLWdq+Q9yMOy+x6yWzAOCJo6qvLCwsVxpb2VgYtctflfWV9++V1uqY29qaq6NPXwR8zUG4l6l+t4MwERHV38/MqjBwGsBrO7OdSlZ9r6BMxeHZtsRHde0kpQVCqZk9z7jdYGYHqX7HF4uIwXmg/t1vEaTVRQWlco6dg5kuKeVyFVOd63ed6mtsFFT1Zbn5VQZ2bpatm76epPqN94wyA2uetVHTtREXpBRoOar3aagsLChXmPLsuXpaRKo6kZhpZMxquK/hBqR3qX4nJaKlgidAJ6k+SYQZOWJOu4BaJ8zMLtN6MKFVVeWlFDGbgmXb9FhclJInt/NxNGFoDkSPOtVXVualFtWZN0U7Imq8wblPVs23/B2n+qqqvJRCRtuemU5TfWVlbmpRq1eGGSwTSzvb5ixaY6rvY1aTmicxd/Vof2slKkjJrzF1GsAzFBem5EmtB7iYa+466EGqTwpJaWFRNdPcztasqdWRlmVniky8XLjaRKSoLSm4V8Oysrcx1mEQyWqqZXom+lpVeSn32S0/iOjupesSpwgf9ZMyvUn1OykRnSUAAP0OA4QAAAAAAAAAAAAAAAAATxFMdgEAAAAAAAAAAAAAAADwFMEAIQAAAAAAAAAAAAAAAMBTBAOEAAAAAAAAAAAAAAAAAE8RDBACAAAAAAAAAAAAAAAAPEUwQAgAAAAAAAAAAAAAAADwFMEAIQAAAAAAPGLlaefikrKqiYhIUXwzLu5KXl2PDwQAAAAAAACAh4QBQgAAAAAAeMQSVo+ImrDxOhER1fz6VlTUzG/ye3pgQ+G1uO6PLAIAAAAAAABACwwQAgAAAADAP5Dw0Nyo7o8sAgAAAAAAAEAL7cddAQAAAAAAgJ7TcwoZNYrr6GbwuCsCAAAAAAAA8I+DAUIAAAAAAOgFVV1JZlq+nOvqZs9hM9ptlFUXpN8p0bFxd+MZ9/G9h+WIhR9wS7i+dkREVJISlyZzC/Xn6ZBSnH87s9rYxcOJw36gsg0Vd9Oyqw3sPVytDTCdCgAAAAAAADytcE8MAAAAAAAPp+bmrukCjrGVV9BAvqO5td8rW6/UNG1ryD6yJMLW0NRBMDB4gJ2F49C5e1P7drnAq2vHRY3+LImIiBQnPoiKWvRjRtxHo92s3UJGDQ9ytnQcuvzvklaVvb373wO5RuauAYMCPWwsXaPfPZIr79MKAQAAAAAAAPxTYIAQAAAAAAAeRt72F0Je+14U+f7e30+f+X3XUl/h/oXPLfqjkoio/MepgyZszRmwePvhE2dP/Lh5nkv21zPCJu4r6s8KFX4z+93cGcfyhEX3yvOOzTK98PnLK04p1ZXdPG7w7O8rI5d//evps38dWDvZ7NraiUPn/FHVnxUCAAAAAAAAeEJhilEAAAAAAOg5ybEPV56Shqw9c/RtZwYRDYvyk152nX/gm+Pbx7546aO3DtcOWZd0fKkHk4goMvr5SPOB/ivf+/zctE0R/VWl3LKBe2InubOJSNtm1LszA3csv3o1l0a4kOjwex/GsZ7de+7wNFsiIoqMeXYQ2zdy+3ublo390LO/KgQAAAAAAADwhMIAIQAAAAAA9NzNU6dKacSn/3ZuXnbQbvaBy4K7CgcVZZ89W0DuzzkVJ8QVNx9g78uj7xIScijCpZ+qxI6MiWhZddDU1JRIKJMREV0/e7ZKy8ff+k5c3J2m7SpXvjH9lnBeRJ7G/VQhAAAAAAAAgCcUBggBAAAAAKDHJJmZRcR5zsW01WcsW8FQWyKiY9nZRJKtE6O2tj+MWVnZf3WytrHpeAmFuuzsYlIWr4mJWtN+U2VlJREGCAEAAAAAAOApgwFCAAAAAADoMZaBgQ7VSSQqIsYDGy0sLIg8t5f9OtOw/SYmq//qxGA8WBUiItKzsDAgvecOl+8Z034EkaHdjxUCAAAAAAAAeEJ1/IQtAAAAAACABtpeXq5Uf+niTVXLZ+Iji0NDJm7NJHdPTy26mXyTwW7BzDq69rNNJws6GcPrX56enlSXfD2L1apCsuvff/bZrvMVj6VCAAAAAAAAAI8VBggBAAAAAKDnfKbPDWOnbXlz7TVR4wc1l/63+qukLDM3FzKZtOhVC+Gepe+eECqIiEhZlbh69sz/bLittH4slXWfuSjGIH3DgtWXKxoHNOXCv5dPf23Vnhxd7mOpEAAAAAAAAMBjhSlGAQAAAADgITgt2Lbh+JjFy4Y47woMcGYXX0tMrbaf8fN/Y7SJjMd9umNe8qsbY9wP+we5695LvnynkuE558i6Z/UeT2Wtp23aemzM3FUhrnuDApxUOVeu5on1gt4/+VEIXiAEAAAAAACApxAGCAEAAAAA4GHo+L7+x81B+2MPxSWnChkh0z5dM3f+BA+Dxo22L2y/dP25nfv+unwzq9L2xaWLJs57LcZZV30o1zsySupuSkRE2rZ+UVH2TvrdKrP1gRzPsKhwL3MiImJY8aOidF2NWu2qbeXl76VtzGz8n5bH9EPJQZN2HDh17VauJPzlmBVT588It9Hp7UUAAAAAAAAA+CdiqFSqrvcCAAAAAAAAAAAAAAAAgP8X8AYhAAAAAAA8Aervp2cIGzTtoWfr7WGBd/4AAAAAAAAAeg1vEAIAAAAAwBMgd+/0md/ka9rD8/WDsS9ZPar6AAAAAAAAAPz/hQFCAAAAAAAAAAAAAAAAgKeI1uOuAAAAAAAAAAAAAAAAAAA8OhggBAAAAAAAAAAAAAAAAHiKYIAQAAAAAAAAAAAAAAAA4CmCAUIAAAAAAAAAAAAAAACApwgGCAEAAAAAAAAAAAAAAACeIhggBAAAAAAAAAAAAAAAAHiKYIAQAAAAAAAAAAAAAAAA4CmCAUIAAAAAAAAAAAAAAACApwgGCAEAAAAAAAAAAAAAAACeIv8HGRlEAj2ZC/sAAAAASUVORK5CYII=", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 300, "width": 1200}}, "output_type": "display_data"}], "source": ["p <- ggplot(\n", "  merged %>% filter(pathway %in% set),\n", "  aes(x = cell_line, y = value, color = name, fill = name)\n", ") +\n", "  geom_errorbar(\n", "    data = summary_df,\n", "    aes(\n", "      x = cell_line,\n", "      y = mean_value,\n", "      ymin = mean_value - se,\n", "      ymax = mean_value + se,\n", "      color = name\n", "    ),\n", "    inherit.aes = FALSE,\n", "    position = position_dodge(width = dodge_width),\n", "    width = 0.2\n", "  ) +\n", "  geom_point(\n", "    data = summary_df,\n", "    aes(x = cell_line, y = mean_value, color = name, fill = name),\n", "    inherit.aes = FALSE,\n", "    position = position_dodge(width = dodge_width),\n", "    size = 3,\n", "    shape = 21\n", "  ) +\n", "  stat_compare_means(aes(group = cell_line), method = \"t.test\") +\n", "  facet_wrap(~ name, scales = \"free_x\") +  # ← test within each name\n", "  theme_bw() +\n", "  ylab(\"GOBP_REGULATION_OF_CYCLASE_ACTIVITY\")\n", "\n", "print(p)\n"]}, {"cell_type": "code", "execution_count": 239, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22mSaving 7 x 7 in image\n", "Warning message:\n", "“\u001b[1m\u001b[22mRemoved 6 rows containing non-finite outside the scale range\n", "(`stat_compare_means()`).”\n"]}], "source": ["ggsave('BRAF_enrichment_facets.pdf',p)"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}