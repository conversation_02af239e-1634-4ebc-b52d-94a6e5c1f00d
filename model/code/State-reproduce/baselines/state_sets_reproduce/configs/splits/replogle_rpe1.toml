[datasets]
replogle = "/large_storage/ctc/userspace/aadduri/datasets/hvg/replogle_copy/"

[training]
replogle = "train"

[zeroshot]

[fewshot]

[fewshot."replogle.hepg2"]
val = ['ATP6V1G1', 'TSEN34', 'TRAPPC8', 'NDUFS3', 'MRPS24', 'DNTTIP2', 'WDR77', 'DHX36', 'RTCB', 'RPA3', 'CCDC144NL', 'NFKBIE', 'NFRKB', 'TRMT10C', 'DIS3', 'CEP85', 'USP37', 'NPLOC4', 'UCHL5', 'NUP85', 'DCTN1', 'GINS4', 'ZNF317', 'OR2T29', 'ZCCHC9', 'ALG11', 'PSMG4', 'TUBD1', 'NDC1', 'HYPK', 'NASP', 'POLR3K', 'DESI1', 'TRAIP', 'ASCC3', 'HCRTR1', 'ZW10', 'MIS12', 'LAMTOR4', 'PABPC4', 'NDUFA3', 'STAT5B', 'PRDM4', 'GEMIN8', 'TRNP1', 'LCE1E', 'TP53RK', 'HNRNPH1', 'CYREN', 'SMC2', 'TAF5', 'CABIN1', 'DCTN6', 'PPAN', 'GNL3', 'SMN2', 'NAA10', 'THAP1', 'NOC4L', 'ZC3H4', 'C9orf78', 'SDHA', 'TERF2', 'RFC4', 'PEF1', 'SRSF7', 'CRKL', 'INTS13', 'GCOM1', 'LTBP4', 'OR4N2', 'ZWINT', 'SNIP1', 'HIST2H3D', 'DERL2', 'ANAPC1', 'POLR2G', 'E4F1', 'CWF19L2', 'MRGBP', 'SPG7', 'CHMP5', 'CCDC115', 'ACD', 'HMGCS1', 'DNM1', 'NOL10', 'ZNF559', 'ZNF763', 'XRCC3', 'NSF', 'SUPT5H', 'SLC1A5', 'MRPL36', 'RNMT', 'SNAPC1', 'CSNK2B', 'KANSL2', 'PEX1', 'PITRM1', 'SPATA5', 'RPN1', 'CD8B', 'PSMD9']
test = ['TTF2', 'INO80B', 'ALG14', 'SNRNP27', 'MRPS14', 'UNCX', 'HUWE1', 'KAT7', 'DIMT1', 'TCP1', 'DENR', 'NSRP1', 'VPS18', 'SRP19', 'CCDC86', 'SPOUT1', 'CARF', 'VPS13D', 'UQCRFS1', 'LPIN1', 'PLK4', 'PRC1', 'ECT2', 'PTPN11', 'CPSF1', 'PDCD5', 'TRAPPC1', 'PPWD1', 'DUT', 'EFR3A', 'LAMTOR3', 'GCLC', 'MCM4', 'NHLRC2', 'WDR92', 'LENG8', 'SRP14', 'POLE2', 'KCNA10', 'RPL7L1', 'FAM133B', 'ZNF676', 'COPS3', 'SMC5', 'HAUS1', 'CCDC78', 'NSUN4', 'ZNHIT6', 'CCDC6', 'WDTC1', 'KDM5C', 'NLE1', 'NBPF15', 'WDR46', 'TPI1', 'SUGP1', 'RABGGTA', 'MRPL9', 'GINS3', 'CCDC137', 'ZNF468', 'TIGD1', 'NOP2', 'MED26', 'AKIRIN2', 'MRPL3', 'MED14', 'ATP6V1D', 'GPN3', 'MED7', 'ISCA2', 'RBM10', 'TBCA', 'TMEM199', 'HEXIM1', 'RPL29', 'INTS3', 'RPS5', 'SF3A3', 'THOC1', 'OPA1', 'INPPL1', 'ERAL1', 'COX15', 'SET', 'BRCA2', 'RANGAP1', 'RPL23A', 'SUPT16H', 'LAMB1', 'UBQLN4', 'TBL3', 'TRMT6', 'C14orf178', 'CLP1', 'RPL35', 'RPS25', 'CPSF2', 'SPAG7', 'RFC2', 'CENPC', 'RPL7', 'FNTA', 'TAF13', 'SNAPC5', 'MRPS11', 'MEMO1', 'PGS1', 'HIST1H2AB', 'EPS8L1', 'TEFM', 'GNPNAT1', 'HNRNPDL', 'TINF2', 'PCM1', 'ZNF492', 'PC', 'MRPS23', 'GMPPB', 'TMEM161B', 'EXOSC5', 'PRPF39', 'SRP72', 'NOL11', 'RPS2', 'XPO5', 'SETX', 'GSPT1', 'AP2S1', 'SF1', 'ZFP69B', 'TPRKB', 'TTC4', 'REXO2', 'RNF8', 'MRPL24', 'DDX47', 'PAM16', 'EMC4', 'MASTL', 'KDM6A', 'PTPN23', 'AIFM1', 'GTF2H2', 'ARL4D', 'COG6', 'RPAP2', 'CWC15', 'EIF1AX', 'EIF2S1', 'DCUN1D5', 'RCL1', 'ZBTB4', 'ALG1L', 'RPIA', 'GPS1', 'HAUS6', 'NKAP', 'PTMA', 'ARHGAP11B', 'GMPS', 'USP10', 'NDC80', 'TMX2', 'SNRNP48', 'MRPL2', 'CEP192', 'CACTIN', 'CDC6', 'CDC42', 'GLE1', 'RNPC3', 'CD2BP2', 'PPP4C', 'SP2', 'CCDC59', 'CBLL1', 'USP14', 'ISG20L2', 'ZNF787', 'NUDCD3', 'NOL9', 'RFC1', 'GTF2B', 'DDX52', 'ZBTB17', 'OGT', 'SKP1', 'POGZ', 'SEPSECS', 'BTAF1', 'HARS', 'SDE2', 'CEP68', 'RARS', 'SRSF10', 'KDM1A', 'NDUFA6', 'NELFA', 'NOMO3', 'TUFM', 'SLU7', 'SPATA5L1', 'ZNF506', 'FBLIM1', 'CDK9', 'SLC16A5', 'RFFL', 'PRRC2A', 'RAC1', 'COTL1', 'ARPC4', 'DAD1', 'SMNDC1', 'ATP6V1E1', 'VPS29', 'ZNHIT3', 'FYN', 'NUTF2', 'SKP2', 'ZNF407', 'MED29', 'U2SURP', 'GSK3B', 'HIST1H2AE', 'UTP6', 'GTF2H4', 'CLCC1', 'SNF8', 'UTP15', 'DNAJC11', 'RAB6A', 'TMEM240', 'ITGB1BP1', 'BYSL', 'CDAN1', 'MIPEP', 'DHX16', 'HDAC3', 'SMG7', 'CHMP1A', 'FAU', 'MRPL13', 'CCP110', 'TARS2', 'DDX24', 'MYB', 'TTK', 'BCAR1', 'RPP21', 'ERVW-1', 'SMAGP', 'MED19', 'HIST1H2BJ', 'SRRM2', 'GTF3C6', 'ZC3H13', 'PPP1R11', 'CMTR2', 'QRSL1', 'RPL26', 'EIF2B1', 'MOK', 'NUB1', 'E2F6', 'PFDN1', 'NOL7', 'TFRC', 'CMTR1', 'UBA2', 'MTREX', 'HINFP', 'MVD', 'NELFE', 'PDCD7', 'WBP1', 'SMG8', 'GET3', 'OR4K1', 'RPP14', 'COG3', 'PELP1', 'C1D', 'TFB1M', 'MRPS16', 'RBM42', 'HIST2H2AA3', 'NOL6', 'ZDHHC7', 'DDX20', 'SEC61G', 'CHMP3', 'VARS2', 'RPS21', 'TNNT2', 'CCDC144A', 'YJEFN3', 'MAT2A', 'GSDMA', 'TXN', 'MEN1', 'ATP6V1F', 'NCAPD2', 'CS', 'MED1', 'MRPS22', 'NVL', 'BRIX1', 'MOCS3', 'EMC1', 'POTEI', 'SSBP1', 'TULP1', 'AGBL5', 'PAXBP1', 'IARS2', 'LAS1L', 'CT45A5', 'SOD2', 'SDHC', 'GTF2H3', 'UBAP1', 'PDRG1', 'INTS12', 'PAK1IP1', 'MTCP1', 'RPS26', 'ZC3H18', 'WARS', 'RUVBL2', 'KRTAP4-2', 'NBPF12', 'ANAPC5', 'OSTC', 'GPN2', 'HMGB3', 'POU5F1B', 'RPL28', 'CNOT2', 'EIF3F', 'MRPL39', 'PGK1', 'PDCD2', 'NCBP1', 'MDN1', 'MRM1', 'ABHD11', 'GART', 'CENPN', 'MT2A', 'FGFR1OP', 'AARS2', 'INTS10', 'MED9', 'HSD17B10', 'RRM1', 'DNM1L', 'COMTD1', 'SRFBP1', 'ADNP2', 'ZNF335', 'AHCY', 'UBE2N', 'CUL1', 'ANKRD49', 'C7orf26', 'TBC1D3', 'TXNL4B', 'PES1', 'CPSF3', 'DNAJA1', 'CNOT3', 'MARS2', 'PPIL2', 'CCND1', 'UTP11', 'TMEM214', 'NDUFB4', 'RPL26L1', 'ZC3H8', 'MAU2', 'PRDM8', 'CENPT', 'SYF2', 'NDOR1', 'TADA1', 'CSNK1A1', 'GAR1', 'ORC5', 'PIAS1', 'HSPD1', 'MED12', 'RMI1', 'ACTR8', 'DDX27', 'EIF2S3', 'ARHGAP6', 'MSL1', 'CNOT11', 'COX17', 'COPS2', 'PRELID3B', 'TOX4', 'DDX18', 'URB1', 'WDHD1', 'TIMELESS', 'TAF6', 'CHCHD4', 'RPS8', 'CDC20', 'IFITM3', 'TUBGCP3', 'NAA35', 'MCM2', 'C19orf53', 'DNM2', 'TM7SF2', 'SNRPC', 'PPP6C', 'U2AF1', 'DNAJC17', 'RPS14', 'SDHAF2', 'RHPN1', 'EP400', 'HIST1H2BL', 'DOLK', 'KIF18B', 'LARS', 'PRPF18', 'SNRPA', 'CRCP', 'SARS', 'IWS1', 'MED8', 'MED10', 'SLC9B1', 'PMF1', 'ABCB7', 'PPIL4', 'NRBP1', 'RPF2', 'BMS1', 'SRRM1', 'FAM102B', 'MED17', 'TRMT112', 'UQCRB', 'TICRR', 'ZNF720', 'UPF3A', 'DRG1', 'PTGR2', 'BAP1', 'PHB', 'SAMD4B', 'SNRNP25', 'OSBP', 'DBR1', 'GPR61', 'MRPS28', 'BRK1', 'RBMXL1', 'ZMAT5', 'TMEM127', 'NUDC', 'NOM1', 'EIF2B5', 'TRA2B', 'TAF10', 'GTF3C3', 'YRDC', 'SUPV3L1', 'COG4', 'CLPB', 'STRIP1', 'NUP54', 'RPL37A', 'CNIH4', 'LMO2', 'UTP3', 'RPS20', 'NAA15', 'NBAS', 'PTEN', 'GINS2', 'PRPF38B', 'POP1', 'CCND3', 'COG2', 'CASP8AP2', 'SF3B5', 'MIS18BP1', 'YPEL5', 'ZMAT2', 'SLC25A42', 'HAUS7', 'NFYB', 'BAG6', 'MVK', 'CPSF6', 'UQCRH', 'PSME2', 'FAM72D', 'PHB2', 'CHTF8', 'PPP1R8', 'FAM32A', 'CBFA2T3', 'FAM229A', 'RNF31', 'INO80E', 'GABPB1', 'MCL1', 'MRPL43', 'RRN3', 'RNASEH2C', 'TUBE1', 'TUBA1C', 'RAB18', 'INTS11', 'SHQ1', 'UROD', 'FEN1', 'PWP2', 'TP53I13', 'CHMP7', 'DPH1', 'MARS', 'TOP1', 'POLD3', 'CPNE7', 'NOS1AP', 'VEZT', 'TFDP1', 'DCTN2', 'RPL30', 'DHPS', 'BUD13', 'C6orf15', 'CEP97', 'GFER', 'INTS2', 'TSEN2', 'ARGLU1', 'ATP5F1C', 'IPO13', 'EXOC8', 'IST1', 'NUP88', 'EIF3G', 'WDR82', 'MLLT6', 'ORC3', 'NUP133', 'WDR70', 'STIL', 'PAF1', 'PPP1CB', 'BCLAF1', 'MYBBP1A', 'CLASRP', 'ZNF253', 'ZRSR2', 'MRPL35', 'HIST1H2BC', 'POLR1E', 'ACTR1B', 'HSP90B1', 'MYBL2', 'NFS1', 'CCT7', 'YARS2', 'CYFIP1', 'ABCE1', 'FBXW7', 'NEDD8-MDP1', 'EXOC7', 'TSFM', 'MED21', 'INTS14', 'KIN', 'BARD1', 'C9orf16', 'LTBP3', 'H2AFZ', 'RFT1', 'HARS2', 'VPS54', 'XRN1', 'RNF20', 'FBXO42', 'POLA2', 'CLTC', 'VPS51', 'ANAPC13', 'RRP15', 'SART3', 'ANAPC10', 'HOXA3', 'CUL3', 'RSL24D1', 'MMGT1', 'MRPL46', 'DKC1', 'HEATR1', 'POLL', 'TMEM242', 'EXOSC4', 'POLR3F', 'RPS28', 'DNLZ', 'NANOG', 'CCNQ', 'NIFK', 'LUC7L3', 'PSAT1', 'INTS6', 'ESPN', 'CDK2', 'ESF1', 'HSPH1', 'RNGTT']