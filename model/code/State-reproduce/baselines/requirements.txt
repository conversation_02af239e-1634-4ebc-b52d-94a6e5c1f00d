absl-py==2.3.0
adjustpy==0.1.1
aiohappyeyeballs==2.6.1
aiohttp==3.12.12
aiosignal==1.3.2
anndata==0.11.4
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
array-api-compat==1.12.0
attrs==25.3.0
-e git+https://github.com/Arcinstitute/cell-load.git
certifi==2025.4.26
charset-normalizer==3.4.2
chex==0.1.89
click==8.2.1
contourpy==1.3.2
cycler==0.12.1
einops==0.8.1
equinox==0.12.2
filelock==3.18.0
flash-attn==1.0.9
fonttools==4.58.2
frozenlist==1.7.0
fsspec==2025.5.1
gitdb==4.0.12
gitpython==3.1.44
h5py==3.14.0
hf-xet==1.1.3
huggingface-hub==0.32.5
hydra-core==1.3.2
idna==3.10
igraph==0.11.9
jax==0.6.1
jaxlib==0.6.1
jaxopt==0.8.5
jaxtyping==0.3.2
jinja2==3.1.6
joblib==1.5.1
kiwisolver==1.4.8
legacy-api-wrap==1.4.1
leidenalg==0.10.2
lightning==2.5.1.post0
lightning-utilities==0.14.3
lineax==0.0.8
llvmlite==0.44.0
markupsafe==3.0.2
matplotlib==3.10.3
ml-dtypes==0.5.1
mpmath==1.3.0
multidict==6.4.4
natsort==8.4.0
networkx==3.5
ninja==1.11.1.4
numba==0.61.2
numpy==2.2.6
nvidia-cublas-cu12==12.1.3.1
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==9.1.0.70
nvidia-cufft-cu12==11.0.2.54
nvidia-cufile-cu12==1.11.1.6
nvidia-curand-cu12==10.3.2.106
nvidia-cusolver-cu12==11.4.5.107
nvidia-cusparse-cu12==12.1.0.106
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.1.105
omegaconf==2.3.0
opt-einsum==3.4.0
optax==0.2.5
ott-jax==0.5.0
packaging==24.2
pandas==2.3.0
patsy==1.0.1
pillow==11.2.1
platformdirs==4.3.8
propcache==0.3.2
protobuf==6.31.1
psutil==7.0.0
pydantic==2.11.5
pydantic-core==2.33.2
pynndescent==0.5.13
pyparsing==3.2.3
python-dateutil==2.9.0.post0
pytorch-lightning==2.5.1.post0
pytz==2025.2
pyyaml==6.0.2
regex==2024.11.6
requests==2.32.4
safetensors==0.5.3
scanpy==1.11.2
scikit-learn==1.7.0
scipy==1.15.3
seaborn==0.13.2
sentry-sdk==2.29.1
session-info2==0.1.2
setproctitle==1.3.6
setuptools==80.9.0
six==1.17.0
smmap==5.0.2
statsmodels==0.14.4
sympy==1.14.0
texttable==1.7.0
threadpoolctl==3.6.0
tokenizers==0.21.1
toml==0.10.2
toolz==1.0.0
torch==2.4.1
torch-scatter==2.1.2+pt24cu121
torchmetrics==1.7.2
tqdm==4.67.1
transformers==4.52.4
triton==3.0.0
typing-extensions==4.14.0
typing-inspection==0.4.1
tzdata==2025.2
umap-learn==0.5.7
urllib3==2.4.0
wadler-lindig==0.1.6
wandb==0.20.1
yarl==1.20.1
