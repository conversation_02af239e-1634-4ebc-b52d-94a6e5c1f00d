[project]
name = "arc-state"
version = "0.9.12"
description = "State is a machine learning model that predicts cellular perturbation response across diverse contexts."
readme = "README.md"
authors = [
    { name = "Ab<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "Noam Teyssier", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>" },
    { name = "Dhruv G<PERSON><PERSON>", email = "<EMAIL>" },
]
requires-python = ">=3.10,<3.13"
dependencies = [
    "anndata>=0.11.4",
    "cell-load>=0.7.6",
    "numpy>=2.2.6",
    "pandas>=2.2.3",
    "pyyaml>=6.0.2",
    "scanpy>=1.11.2",
    "scikit-learn>=1.6.1",
    "seaborn>=0.13.2",
    "torch>=2.7.0",
    "tqdm>=4.67.1",
    "wandb>=0.19.11",
    "hydra-core>=1.3.2",
    "geomloss>=0.2.6",
    "transformers>=4.52.3",
    "cell-eval>=0.5.22",
]

[project.optional-dependencies]
vectordb = [
    "lancedb>=0.24.0"
]


[dependency-groups]
dev = ["ruff>=0.11.11", "vulture>=2.14"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
state = "state.__main__:main"

[tool.pyright]
venvPath = "."
venv = ".venv"

[tool.hatch.build.targets.wheel]
packages = ["src/state"]
