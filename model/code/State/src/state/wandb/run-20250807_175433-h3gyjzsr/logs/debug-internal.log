{"time":"2025-08-07T17:54:33.656038493+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T17:54:34.316065708+08:00","level":"INFO","msg":"stream: created new stream","id":"h3gyjzsr"}
{"time":"2025-08-07T17:54:34.316125073+08:00","level":"INFO","msg":"stream: started","id":"h3gyjzsr"}
{"time":"2025-08-07T17:54:34.316164308+08:00","level":"INFO","msg":"handler: started","stream_id":"h3gyjzsr"}
{"time":"2025-08-07T17:54:34.316171419+08:00","level":"INFO","msg":"sender: started","stream_id":"h3gyjzsr"}
{"time":"2025-08-07T17:54:34.316168156+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"h3gyjzsr"}
{"time":"2025-08-07T17:54:34.757387395+08:00","level":"ERROR","msg":"HTTP error","status":403,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-08-07T17:54:34.757493336+08:00","level":"ERROR","msg":"runupserter: failed to init run","error":"returned error 403: {\"data\":{\"upsertBucket\":null},\"errors\":[{\"message\":\"permission denied\",\"path\":[\"upsertBucket\"],\"extensions\":{\"code\":\"PERMISSION_ERROR\"}}]}"}
