2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_setup.py:_flush():80] Configure stats pid to 1006801
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_setup.py:_flush():80] Loading settings from /data/ioz_whr_wsx/model/code/State/src/state/wandb/settings
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_init.py:setup_run_log_directory():703] Logging user logs to ./wandb/run-20250807_175433-h3gyjzsr/logs/debug.log
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to ./wandb/run-20250807_175433-h3gyjzsr/logs/debug-internal.log
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_init.py:init():830] calling init triggers
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-08-07 17:54:33,442 INFO    MainThread:1006801 [wandb_init.py:init():871] starting backend
2025-08-07 17:54:33,648 INFO    MainThread:1006801 [wandb_init.py:init():874] sending inform_init request
2025-08-07 17:54:33,651 INFO    MainThread:1006801 [wandb_init.py:init():882] backend started and connected
2025-08-07 17:54:33,655 INFO    MainThread:1006801 [wandb_init.py:init():953] updated telemetry
2025-08-07 17:54:33,660 INFO    MainThread:1006801 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
