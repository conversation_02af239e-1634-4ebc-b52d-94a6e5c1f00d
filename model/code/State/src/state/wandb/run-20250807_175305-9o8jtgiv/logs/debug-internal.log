{"time":"2025-08-07T17:53:05.555250166+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T17:53:07.388602624+08:00","level":"INFO","msg":"stream: created new stream","id":"9o8jtgiv"}
{"time":"2025-08-07T17:53:07.388710271+08:00","level":"INFO","msg":"stream: started","id":"9o8jtgiv"}
{"time":"2025-08-07T17:53:07.388752025+08:00","level":"INFO","msg":"sender: started","stream_id":"9o8jtgiv"}
{"time":"2025-08-07T17:53:07.388741332+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"9o8jtgiv"}
{"time":"2025-08-07T17:53:07.388804665+08:00","level":"INFO","msg":"handler: started","stream_id":"9o8jtgiv"}
{"time":"2025-08-07T17:53:07.868845501+08:00","level":"ERROR","msg":"HTTP error","status":403,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-08-07T17:53:07.868934555+08:00","level":"ERROR","msg":"runupserter: failed to init run","error":"returned error 403: {\"data\":{\"upsertBucket\":null},\"errors\":[{\"message\":\"permission denied\",\"path\":[\"upsertBucket\"],\"extensions\":{\"code\":\"PERMISSION_ERROR\"}}]}"}
