{"time":"2025-08-11T14:47:55.278653432+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-11T14:47:55.999614876+08:00","level":"INFO","msg":"stream: created new stream","id":"4wdqmvhz"}
{"time":"2025-08-11T14:47:55.999681926+08:00","level":"INFO","msg":"stream: started","id":"4wdqmvhz"}
{"time":"2025-08-11T14:47:55.999706071+08:00","level":"INFO","msg":"handler: started","stream_id":"4wdqmvhz"}
{"time":"2025-08-11T14:47:55.999708709+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"4wdqmvhz"}
{"time":"2025-08-11T14:47:55.999745273+08:00","level":"INFO","msg":"sender: started","stream_id":"4wdqmvhz"}
{"time":"2025-08-11T14:47:56.73260181+08:00","level":"ERROR","msg":"HTTP error","status":403,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-08-11T14:47:56.732768019+08:00","level":"ERROR","msg":"runupserter: failed to init run","error":"returned error 403: {\"data\":{\"upsertBucket\":null},\"errors\":[{\"message\":\"permission denied\",\"path\":[\"upsertBucket\"],\"extensions\":{\"code\":\"PERMISSION_ERROR\"}}]}"}
{"time":"2025-08-11T15:52:13.381982586+08:00","level":"INFO","msg":"stream: closing","id":"4wdqmvhz"}
{"time":"2025-08-11T15:52:13.382584662+08:00","level":"ERROR","msg":"sender: uploadConfigFile: stream: no run"}
{"time":"2025-08-11T15:52:13.686384241+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-11T15:52:16.357794426+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-11T15:52:20.997327837+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-11T15:52:29.8749166+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-11T15:52:46.436496755+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-11T15:53:25.795465275+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-11T15:54:27.308084599+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-08-11T15:54:27.308313977+08:00","level":"ERROR","msg":"runfiles: CreateRunFiles returned error: returned error 404: {\"data\":{\"createRunFiles\":null},\"errors\":[{\"message\":\"project YOUR_ENTITY/YOUR_PROJECT not found during createRunFiles\",\"path\":[\"createRunFiles\"]}]}"}
{"time":"2025-08-11T15:54:27.309281537+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-11T15:54:27.309406064+08:00","level":"INFO","msg":"handler: closed","stream_id":"4wdqmvhz"}
{"time":"2025-08-11T15:54:27.30944802+08:00","level":"INFO","msg":"sender: closed","stream_id":"4wdqmvhz"}
{"time":"2025-08-11T15:54:27.309447304+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"4wdqmvhz"}
{"time":"2025-08-11T15:54:27.309610373+08:00","level":"INFO","msg":"stream: closed","id":"4wdqmvhz"}
