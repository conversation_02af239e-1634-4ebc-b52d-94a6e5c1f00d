Building trainer with kwargs: {'accelerator': 'gpu', 'devices': 1, 'max_steps': 40000, 'check_val_every_n_epoch': None, 'val_check_interval': 100, 'logger': [<state.tx.utils.RobustCSVLogger object at 0x7fee10ecdfd0>, <lightning.pytorch.loggers.wandb.WandbLogger object at 0x7fee10e30a90>], 'plugins': [], 'callbacks': [<lightning.pytorch.callbacks.model_checkpoint.ModelCheckpoint object at 0x7fee1056c310>, <lightning.pytorch.callbacks.model_checkpoint.ModelCheckpoint object at 0x7fee105f0d90>, <state.tx.callbacks.batch_speed_monitor.BatchSpeedMonitorCallback object at 0x7fee0fc2f2d0>], 'gradient_clip_val': 10}
GPU available: True (cuda), used: True
TPU available: False, using: 0 TPU cores
HPU available: False, using: 0 HPUs
Trainer built successfully
Model device: cpu
CUDA memory allocated: 0.00 GB
CUDA memory reserved: 0.00 GB
About to call trainer.fit() with checkpoint_path=/data/ioz_whr_wsx/datasets/state/demo/output/test/checkpoints/last.ckpt
Restoring states from the checkpoint path at /data/ioz_whr_wsx/datasets/state/demo/output/test/checkpoints/last.ckpt
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3,4,5,6,7]

  | Name                 | Type                   | Params | Mode
------------------------------------------------------------------------
0 | loss_fn              | SamplesLoss            | 0      | train
1 | gene_decoder         | LatentToGeneDecoder    | 1.7 M  | train
2 | pert_encoder         | Sequential             | 325 K  | train
3 | basal_encoder        | Linear                 | 16.7 K | train
4 | transformer_backbone | GPT2BidirectionalModel | 26.9 M | train
5 | project_out          | Sequential             | 340 K  | train
6 | final_down_then_up   | Sequential             | 656    | train
7 | relu                 | ReLU                   | 0      | train
------------------------------------------------------------------------
12.7 M    Trainable params
16.5 M    Non-trainable params
29.2 M    Total params
116.835   Total estimated model params size (MB)
155       Modules in train mode
0         Modules in eval mode
Restored all states from the checkpoint at /data/ioz_whr_wsx/datasets/state/demo/output/test/checkpoints/last.ckpt
Epoch 409:  35%|███████████████████▊                                    | 6/17 [00:01<00:02,  4.91it/s, v_num=w30p]
