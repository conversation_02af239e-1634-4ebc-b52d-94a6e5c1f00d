{"os": "Linux-5.15.0-88-generic-x86_64-with-glibc2.35", "python": "CPython 3.11.13", "startedAt": "2025-08-07T09:57:23.242164Z", "args": ["tx", "train", "data.kwargs.toml_config_path=/data/ioz_whr_wsx/model/code/State/examples/zeroshot.toml", "data.kwargs.embed_key=X_hvg", "data.kwargs.num_workers=12", "data.kwargs.batch_col=batch_var", "data.kwargs.pert_col=target_gene", "data.kwargs.cell_type_key=cell_type", "data.kwargs.control_pert=TARGET1", "training.max_steps=40000", "training.val_freq=100", "training.ckpt_every_n_steps=100", "training.batch_size=8", "training.lr=1e-4", "model.kwargs.cell_set_len=64", "model.kwargs.hidden_dim=328", "model=pertsets", "wandb.tags=[test]", "output_dir=/data/ioz_whr_wsx/datasets/state/demo/output", "name=test"], "program": "/home/<USER>/.local/bin/state", "git": {"remote": "https://github.com/ArcInstitute/State.git", "commit": "efff9da57c4212c726e464b184680a909bbaefde"}, "email": "<EMAIL>", "root": ".", "host": "bm-2209bmw", "executable": "/home/<USER>/.local/share/uv/tools/arc-state/bin/python", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA A800-SXM4-80GB", "gpu_count": 8, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "1081813745664"}, "gpu_nvidia": [{"name": "NVIDIA A800-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-94852939-7b80-940b-b760-ede0ff1cc756"}, {"name": "NVIDIA A800-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-ef8397ee-6351-cc12-0061-28ab6f9b2695"}, {"name": "NVIDIA A800-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-81424747-246d-c0f3-a91a-c3486baea58a"}, {"name": "NVIDIA A800-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-b1e304d3-ff19-8aac-7b9b-9079c1df686a"}, {"name": "NVIDIA A800-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-52cbaa90-8578-82ad-7daa-b13613e8c398"}, {"name": "NVIDIA A800-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-aa0ba1af-2b61-fa28-a254-d6d9b8ca7f39"}, {"name": "NVIDIA A800-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-7c0c3374-b7a7-b8f8-23e2-6a20777794c3"}, {"name": "NVIDIA A800-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-82fe26f5-6115-16e2-33dd-a698284eed65"}], "cudaVersion": "12.4", "writerId": "sybnhq3rb52uhr3rxycmn32sdj3zgkx6"}