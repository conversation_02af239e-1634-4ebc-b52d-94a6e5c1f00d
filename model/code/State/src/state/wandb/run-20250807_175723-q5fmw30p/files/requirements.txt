gitdb==4.0.12
session_info==1.0.1
numba==0.61.2
interface-meta==1.3.0
nvidia-cufft-cu12==11.3.3.83
numcodecs==0.16.1
frozenlist==1.7.0
pydantic_core==2.33.2
formulaic-contrasts==1.0.0
regex==2025.7.34
six==1.17.0
scikit-learn==1.7.1
hydra-core==1.3.2
torchmetrics==1.8.0
toml==0.10.2
aiohttp==3.12.15
nvidia-cudnn-cu12==9.10.2.21
GitPython==3.1.45
pytest==8.4.1
contourpy==1.3.3
umap-learn==0.5.9.post2
pandas==2.3.1
triton==3.4.0
lightning==2.5.2
zarr==3.1.1
tokenizers==0.21.4
seaborn==0.13.2
setuptools==80.9.0
llvmlite==0.44.0
polars==1.32.0
pyparsing==3.2.3
Jinja2==3.1.6
natsort==8.4.0
nvidia-nvjitlink-cu12==12.8.93
wrapt==1.17.2
joblib==1.5.1
MarkupSafe==3.0.2
tqdm==4.67.1
array-api-compat==1.12.0
session-info2==0.2
nvidia-cufile-cu12==********
fsspec==2025.7.0
click==8.2.1
propcache==0.3.2
numpy==2.2.6
packaging==25.0
matplotlib==3.10.5
h5py==3.14.0
geomloss==0.2.6
scipy==1.16.1
typing_extensions==4.14.1
attrs==25.3.0
formulaic==1.2.0
pynndescent==0.5.13
sympy==1.14.0
cycler==0.12.1
statsmodels==0.14.5
nvidia-cusparselt-cu12==0.7.1
transformers==4.55.0
igraph==0.11.9
aiosignal==1.4.0
pydeseq2==0.5.2
tzdata==2025.2
adpbulk==0.1.4
patsy==1.0.1
scanpy==1.11.4
cell-load==0.7.6
filelock==3.18.0
Pygments==2.19.2
hf-xet==1.1.7
idna==3.10
certifi==2025.8.3
urllib3==2.5.0
sentry-sdk==2.34.1
torch==2.8.0
mpmath==1.3.0
fonttools==4.59.0
iniconfig==2.1.0
lightning-utilities==0.15.2
aiohappyeyeballs==2.6.1
pluggy==1.6.0
wandb==0.21.0
protobuf==6.31.1
narwhals==2.0.1
omegaconf==2.3.0
nvidia-cuda-nvrtc-cu12==12.8.93
python-dateutil==2.9.0.post0
pytz==2025.2
antlr4-python3-runtime==4.9.3
pytorch-lightning==2.5.2
multidict==6.6.3
nvidia-cuda-cupti-cu12==12.8.90
networkx==3.5
nvidia-nvtx-cu12==12.8.90
nvidia-cublas-cu12==********
legacy-api-wrap==1.4.1
nvidia-cusolver-cu12==*********
typing-inspection==0.4.1
charset-normalizer==3.4.2
platformdirs==4.3.8
anndata==0.12.1
nvidia-nccl-cu12==2.27.3
requests==2.32.4
arc-state==0.9.12
texttable==1.7.0
safetensors==0.6.1
huggingface-hub==0.34.3
stdlib-list==0.11.1
pydantic==2.11.7
nvidia-cusparse-cu12==12.5.8.93
nvidia-curand-cu12==10.3.9.90
pyarrow==21.0.0
smmap==5.0.2
yarl==1.20.1
annotated-types==0.7.0
kiwisolver==1.4.8
crc32c==2.7.1
donfig==0.8.1.post1
pillow==11.3.0
cell-eval==0.5.42
nvidia-cuda-runtime-cu12==12.8.90
threadpoolctl==3.6.0
PyYAML==6.0.2
pdex==0.1.21
