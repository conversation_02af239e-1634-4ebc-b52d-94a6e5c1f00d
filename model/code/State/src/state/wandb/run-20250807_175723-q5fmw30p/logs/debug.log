2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_setup.py:_flush():80] Configure stats pid to 1009317
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_setup.py:_flush():80] Loading settings from /data/ioz_whr_wsx/model/code/State/src/state/wandb/settings
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_init.py:setup_run_log_directory():703] Logging user logs to ./wandb/run-20250807_175723-q5fmw30p/logs/debug.log
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to ./wandb/run-20250807_175723-q5fmw30p/logs/debug-internal.log
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_init.py:init():830] calling init triggers
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-08-07 17:57:23,243 INFO    MainThread:1009317 [wandb_init.py:init():871] starting backend
2025-08-07 17:57:23,448 INFO    MainThread:1009317 [wandb_init.py:init():874] sending inform_init request
2025-08-07 17:57:23,450 INFO    MainThread:1009317 [wandb_init.py:init():882] backend started and connected
2025-08-07 17:57:23,453 INFO    MainThread:1009317 [wandb_init.py:init():953] updated telemetry
2025-08-07 17:57:23,458 INFO    MainThread:1009317 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-07 17:57:24,823 INFO    MainThread:1009317 [wandb_init.py:init():1029] starting run threads in backend
2025-08-07 17:57:24,963 INFO    MainThread:1009317 [wandb_run.py:_console_start():2458] atexit reg
2025-08-07 17:57:24,963 INFO    MainThread:1009317 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-07 17:57:24,963 INFO    MainThread:1009317 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-07 17:57:24,963 INFO    MainThread:1009317 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-07 17:57:24,967 INFO    MainThread:1009317 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-07 17:57:24,968 INFO    MainThread:1009317 [wandb_run.py:_config_callback():1363] config_cb None None {'data': {'name': 'PerturbationDataModule', 'kwargs': {'toml_config_path': '/data/ioz_whr_wsx/model/code/State/examples/zeroshot.toml', 'embed_key': 'X_hvg', 'output_space': 'all', 'pert_rep': 'onehot', 'basal_rep': 'sample', 'num_workers': 12, 'pin_memory': True, 'n_basal_samples': 1, 'basal_mapping_strategy': 'random', 'should_yield_control_cells': True, 'batch_col': 'batch_var', 'pert_col': 'target_gene', 'cell_type_key': 'cell_type', 'control_pert': 'TARGET1', 'map_controls': True, 'perturbation_features_file': None, 'store_raw_basal': False, 'int_counts': False, 'barcode': True, 'batch_size': 8, 'cell_sentence_len': 64}, 'output_dir': None, 'debug': True}, 'model': {'name': 'PertSets', 'checkpoint': None, 'device': 'cuda', 'kwargs': {'cell_set_len': 64, 'extra_tokens': 1, 'decoder_hidden_dims': [1024, 1024, 512], 'blur': 0.05, 'hidden_dim': 328, 'loss': 'energy', 'confidence_token': False, 'n_encoder_layers': 4, 'n_decoder_layers': 4, 'predict_residual': True, 'freeze_pert_backbone': False, 'finetune_vci_decoder': False, 'residual_decoder': False, 'batch_encoder': False, 'nb_decoder': False, 'decoder_loss_weight': 1.0, 'use_basal_projection': False, 'mask_attn': False, 'distributional_loss': 'energy', 'regularization': 0.0, 'init_from': None, 'transformer_backbone_key': 'GPT2', 'transformer_backbone_kwargs': {'max_position_embeddings': 64, 'n_positions': 65, 'hidden_size': 328, 'n_embd': 328, 'n_layer': 8, 'n_head': 8, 'resid_pdrop': 0.0, 'embd_pdrop': 0.0, 'attn_pdrop': 0.0, 'use_cache': False}, 'decoder_cfg': {'latent_dim': 50, 'gene_dim': 50, 'hidden_dims': [1024, 1024, 512], 'dropout': 0.1, 'residual_decoder': False}}}, 'training': {'wandb_track': False, 'weight_decay': 0.0005, 'batch_size': 8, 'lr': 0.0001, 'max_steps': 40000, 'train_seed': 42, 'val_freq': 100, 'ckpt_every_n_steps': 100, 'gradient_clip_val': 10, 'loss_fn': 'mse', 'devices': 1, 'strategy': 'auto'}, 'wandb': {'entity': 'wanghaoran08042-chinese-academy-of-sciences', 'project': 'state', 'local_wandb_dir': './wandb_logs', 'tags': ['test']}, 'name': 'test', 'output_dir': '/data/ioz_whr_wsx/datasets/state/demo/output', 'use_wandb': True, 'overwrite': False, 'return_adatas': False, 'pred_adata_path': None, 'true_adata_path': None}
2025-08-07 17:57:25,465 INFO    MainThread:1009317 [wandb_run.py:_config_callback():1363] config_cb None None {'input_dim': 50, 'hidden_dim': 328, 'output_dim': 50, 'pert_dim': 5, 'batch_dim': 1, 'dropout': 0.1, 'lr': 0.0001, 'loss_fn': 'mse', 'control_pert': 'TARGET1', 'embed_key': 'X_hvg', 'output_space': 'all', 'gene_names': ['GENE1', 'GENE2', 'GENE3', 'GENE4', 'GENE5', 'GENE6', 'GENE7', 'GENE8', 'GENE9', 'GENE10', 'GENE11', 'GENE12', 'GENE13', 'GENE14', 'GENE15', 'GENE16', 'GENE17', 'GENE18', 'GENE19', 'GENE20', 'GENE21', 'GENE22', 'GENE23', 'GENE24', 'GENE25', 'GENE26', 'GENE27', 'GENE28', 'GENE29', 'GENE30', 'GENE31', 'GENE32', 'GENE33', 'GENE34', 'GENE35', 'GENE36', 'GENE37', 'GENE38', 'GENE39', 'GENE40', 'GENE41', 'GENE42', 'GENE43', 'GENE44', 'GENE45', 'GENE46', 'GENE47', 'GENE48', 'GENE49', 'GENE50'], 'batch_size': 8, 'gene_dim': 50, 'hvg_dim': 50, 'decoder_cfg': {'latent_dim': 50, 'gene_dim': 50, 'hidden_dims': [1024, 1024, 512], 'dropout': 0.1, 'residual_decoder': False}, 'cell_set_len': 64, 'extra_tokens': 1, 'decoder_hidden_dims': [1024, 1024, 512], 'blur': 0.05, 'loss': 'energy', 'confidence_token': False, 'n_encoder_layers': 4, 'n_decoder_layers': 4, 'freeze_pert_backbone': False, 'finetune_vci_decoder': False, 'residual_decoder': False, 'batch_encoder': False, 'nb_decoder': False, 'decoder_loss_weight': 1.0, 'use_basal_projection': False, 'mask_attn': False, 'regularization': 0.0, 'init_from': None, 'wandb_track': False, 'weight_decay': 0.0005, 'max_steps': 40000, 'train_seed': 42, 'val_freq': 100, 'ckpt_every_n_steps': 100, 'gradient_clip_val': 10, 'devices': 1, 'strategy': 'auto', 'predict_residual': True, 'distributional_loss': 'energy', 'transformer_backbone_key': 'GPT2', 'transformer_backbone_kwargs': {'max_position_embeddings': 64, 'n_positions': 64, 'hidden_size': 328, 'n_embd': 328, 'n_layer': 8, 'n_head': 8, 'resid_pdrop': 0.0, 'embd_pdrop': 0.0, 'attn_pdrop': 0.0, 'use_cache': False}}
2025-08-08 11:23:29,209 INFO    MsgRouterThr:1009317 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 3 handles.
