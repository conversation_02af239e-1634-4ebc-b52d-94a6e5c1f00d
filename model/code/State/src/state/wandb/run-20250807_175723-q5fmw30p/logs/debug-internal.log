{"time":"2025-08-07T17:57:23.454465451+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T17:57:24.128842903+08:00","level":"INFO","msg":"stream: created new stream","id":"q5fmw30p"}
{"time":"2025-08-07T17:57:24.128897403+08:00","level":"INFO","msg":"stream: started","id":"q5fmw30p"}
{"time":"2025-08-07T17:57:24.128924602+08:00","level":"INFO","msg":"handler: started","stream_id":"q5fmw30p"}
{"time":"2025-08-07T17:57:24.128929583+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"q5fmw30p"}
{"time":"2025-08-07T17:57:24.128936193+08:00","level":"INFO","msg":"sender: started","stream_id":"q5fmw30p"}
{"time":"2025-08-07T19:47:46.387019178+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:32994->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T19:48:45.317613878+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:38832->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T19:48:52.48090346+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:38838->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T19:49:01.877870842+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:57276->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T19:50:14.85120779+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:54474->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T19:53:59.850494851+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:54462->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T20:17:44.851947975+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:48384->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T20:18:59.95150269+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:40288->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T20:24:41.980760486+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T20:24:51.225835292+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:37964->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T20:24:58.684203972+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:44278->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T20:25:17.579127436+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:34184->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T20:42:14.835633705+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:60162->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T21:20:25.079391786+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:20:40.296128101+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:20:42.726001639+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:21:10.234096234+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:21:12.991794926+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:21:17.94598453+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:21:40.084710794+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:21:42.314583085+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:21:55.084196875+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:22:10.485551704+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:22:13.252300005+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:22:25.064698068+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:23:10.099912905+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:23:12.366568416+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:23:25.163680051+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:23:33.772864771+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:26:38.52821016+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": context deadline exceeded"}
{"time":"2025-08-07T21:26:51.799278479+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:26:55.57795161+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:27:04.37355257+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:27:14.473510054+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:27:33.383786057+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:28:10.220335127+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:28:14.269203519+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:28:25.213444623+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:28:27.801365876+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:28:33.56515112+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:28:41.926226516+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:29:01.358896844+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:29:10.181670579+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:29:24.978011034+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:29:40.056379211+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:29:42.504363533+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:30:26.11269599+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:31:10.123712135+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:31:12.481219843+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:31:25.112276233+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:31:40.095506789+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:31:42.60216029+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:31:49.041069026+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:31:58.499650088+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:32:18.337213482+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:42:10.367050161+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:43:10.12634435+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:43:13.219346086+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:43:18.830888089+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:43:27.561876305+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:43:55.17094637+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:43:57.642879768+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:47:02.546279625+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": context deadline exceeded"}
{"time":"2025-08-07T21:47:11.676187823+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:47:31.779587419+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:48:05.466112645+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:49:06.858403558+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:50:25.477669966+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:50:29.157844494+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:50:34.809639304+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:50:45.234283925+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:51:03.099452972+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:51:43.185922389+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:52:43.367379052+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:53:47.03984713+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:55:48.096753709+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:57:11.169235597+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:57:14.740779713+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:57:19.05095769+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:58:11.469698549+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:58:13.799788315+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:58:18.345426446+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:58:26.95950061+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:58:44.565122122+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T21:59:57.949363602+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:01:30.1182477+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:02:30.612673283+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:03:30.836808902+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:04:31.055057517+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:05:25.381490062+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:05:27.642357431+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:05:31.31071774+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:05:32.376749651+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:05:42.00864458+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:06:02.480007575+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:06:31.616326461+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:06:35.159512013+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:07:32.078049107+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:07:36.8471085+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:08:32.316521222+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:08:39.168952991+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:09:32.667014415+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:09:40.493323592+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:10:32.931401428+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:10:41.180334498+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:11:33.198856499+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:11:41.473408175+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:12:33.395568784+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:12:41.671023049+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:13:33.608778899+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:13:42.096483375+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:14:35.937286554+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:14:42.343901079+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:15:25.097098009+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000387043,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"vztrwmxphnxx\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T22:15:36.280725987+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:15:43.607017096+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:16:36.492130598+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:16:43.893882556+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:17:37.47751199+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:17:45.543118471+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:18:37.73032541+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:18:45.899639862+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:19:38.102032983+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:19:47.20826052+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:20:41.446663184+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:20:47.675385536+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:21:41.723830482+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:22:17.675865911+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-07T22:22:17.676056535+08:00","level":"ERROR","msg":"sender: sendStopStatus: failed to get run stopped status: api: failed sending: POST https://api.wandb.ai/graphql giving up after 21 attempt(s): Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-07T22:22:17.677537885+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":1012.580800983,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"vztrwmxphnxx\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T22:22:26.196496215+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:22:28.985410365+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:22:35.431961991+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:22:41.944634767+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:23:13.457999947+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T22:23:32.522512756+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:23:42.126470807+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:24:09.991457549+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:24:42.565943079+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:25:10.51144289+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:25:43.834328996+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:26:10.777064554+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:26:45.090643633+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:27:40.778544767+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T22:27:46.348432295+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:27:47.080173759+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:28:41.05882409+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:28:47.46320499+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:29:41.561002412+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:30:18.503702885+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:30:20.882804839+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:30:26.026114188+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:30:35.440628201+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:30:42.237465392+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:30:54.647152519+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:31:29.505626204+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:31:42.537236892+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:32:25.109011412+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000096954,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"l3a6wwmgcqq1\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T22:32:30.269022583+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:32:32.689897026+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:32:37.451496558+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:32:42.848259639+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:32:47.568255308+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:33:43.518034477+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:34:43.778292366+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:35:44.229783954+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:36:44.862229044+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:37:39.103406288+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:37:42.165830264+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:37:46.360313639+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:37:46.850490647+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:37:56.395064563+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:38:16.062607669+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:38:46.590099182+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:39:47.017777808+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:39:47.017898467+08:00","level":"ERROR","msg":"sender: sendStopStatus: failed to get run stopped status: api: failed sending: POST https://api.wandb.ai/graphql giving up after 21 attempt(s): Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:39:47.019024166+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":1041.910136923,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"l3a6wwmgcqq1\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T22:40:25.121362527+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T22:40:27.741273054+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:40:32.085793325+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:40:40.753209168+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:41:00.701542209+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:41:37.849881076+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:41:52.597624414+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T22:42:39.219887419+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:43:24.133582294+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:43:40.837590876+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:44:41.177274754+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:44:55.208156596+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:45:55.481063233+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:46:11.178914567+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T22:46:57.724511632+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:47:11.468991522+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:47:59.8019582+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:48:12.286631788+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:49:00.679263013+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:49:12.499928205+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:49:55.122055032+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000747797,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"z9vjg24ulyna\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T22:50:00.962704949+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:50:12.79719021+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:51:13.082756078+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:51:32.485079315+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:52:13.343137586+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:53:04.637908603+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:53:14.06456718+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:54:04.929491571+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:54:14.225003521+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:55:05.127197647+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:55:44.226558225+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-07T22:56:06.385663734+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:56:44.43761525+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:57:06.690927841+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:57:45.001253815+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:57:45.001318181+08:00","level":"ERROR","msg":"sender: sendStopStatus: failed to get run stopped status: api: failed sending: POST https://api.wandb.ai/graphql giving up after 21 attempt(s): Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:57:45.001830681+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":1069.880502888,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"z9vjg24ulyna\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T22:58:06.959471745+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T22:58:25.13460472+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T22:58:28.037281205+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:58:33.858371505+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:58:45.436628827+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:59:02.191400893+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T22:59:07.334360532+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:00:07.213527809+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-07T23:00:14.525409247+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:00:43.135407521+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:01:08.949825735+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:02:38.951332013+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T23:03:39.510105089+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:03:45.27428364+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T23:03:50.603726604+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:03:59.18641252+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:04:17.877234185+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:04:41.256230687+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:04:55.599168021+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:05:41.448236274+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:05:55.816545716+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:06:41.869586743+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:06:56.002963652+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:07:42.381281662+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:07:55.134491185+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000515974,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"lsee0kq1req2\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T23:07:56.420552941+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:08:42.536662829+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:08:57.077165191+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:09:43.812115256+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:10:27.943071168+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:10:30.691224667+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:10:35.421912865+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:10:44.339796925+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:10:44.563411932+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:11:01.039301117+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:11:38.493710164+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:11:44.752111929+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:12:40.948887717+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:12:45.703530887+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:13:41.170492274+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:13:46.94982771+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:14:42.369905633+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:14:47.396184168+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:15:43.025049747+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:15:47.799260017+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:15:47.799376798+08:00","level":"ERROR","msg":"sender: sendStopStatus: failed to get run stopped status: api: failed sending: POST https://api.wandb.ai/graphql giving up after 21 attempt(s): Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:15:47.800614718+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":1072.666689487,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"lsee0kq1req2\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T23:15:55.383189813+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:15:57.830147434+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:16:04.20114586+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:16:12.627766009+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:16:29.98086377+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:16:43.515268359+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:17:08.030160048+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:17:44.232002097+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:18:09.519485072+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:18:44.822240046+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:19:10.168117301+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:19:45.022922159+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:20:11.391790602+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:20:45.710008034+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:21:11.650343618+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:21:46.104800832+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:22:11.833713088+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:22:47.520380874+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:23:12.185258542+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:24:12.783854849+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:24:48.38745922+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:24:50.880339178+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:24:55.898059045+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:25:05.90457201+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:25:13.025394147+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:25:23.977839628+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:25:55.14737933+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000802243,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"htq6tpjomipd\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T23:26:04.245905175+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:26:13.332561166+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:27:04.484766123+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:27:13.614265985+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:28:04.728865401+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:28:13.826323945+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:29:06.119339224+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:29:14.085993473+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:30:11.254617693+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:37598->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-07T23:30:14.768721321+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:31:11.506137067+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:31:15.056806958+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:32:11.807116139+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:32:15.431364202+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:32:15.431472691+08:00","level":"ERROR","msg":"sender: sendStopStatus: failed to get run stopped status: api: failed sending: POST https://api.wandb.ai/graphql giving up after 21 attempt(s): Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:32:15.43260571+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":980.285996274,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"htq6tpjomipd\"  connection_id:\"1(@)\")"}
{"time":"2025-08-07T23:32:25.382013053+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:32:28.178885742+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:32:33.919659762+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:32:43.446351993+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:33:00.243580643+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-08-07T23:33:51.972228439+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:33:54.43527568+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:39:22.038485293+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:40:22.019054867+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:41:21.962875516+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:52:22.39624301+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-07T23:55:56.784217842+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:34136->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T00:16:41.783303739+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:48928->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T00:17:55.187671173+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T00:18:23.156460203+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-08-08T01:26:57.035066459+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-08T01:32:14.92557114+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:52708->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T01:35:59.8341667+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:34166->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T02:12:29.833452644+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:38658->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T02:16:44.832280582+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:36486->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T02:18:14.831820964+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:55694->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T02:18:21.937671357+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:55700->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T02:18:31.06659011+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:47804->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T03:51:40.530133825+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": EOF"}
{"time":"2025-08-08T04:09:52.679976479+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-08-08T04:22:25.303141405+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T04:37:55.310674567+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T04:49:03.202292086+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": unexpected EOF"}
{"time":"2025-08-08T05:09:10.325868794+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T07:29:13.283608553+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-08-08T08:54:17.911370038+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:51262->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T08:55:29.847120514+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:50232->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T08:59:54.831276983+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:10:59.834576334+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:44380->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T10:11:07.223700672+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:60638->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T10:11:17.089633033+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:38046->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T10:11:25.466484802+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T10:11:30.726769773+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:35062->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T10:11:54.350689398+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:53226->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T10:11:57.812252899+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:12:32.270061068+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:12:34.609550351+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:41948->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T10:13:11.611614338+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T10:13:59.300400298+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T10:15:05.693154549+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T10:16:35.695416866+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T10:18:05.696482936+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:19:35.698104005+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:20:55.466547072+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000430681,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"geitfiblixbm\"  connection_id:\"1(@)\")"}
{"time":"2025-08-08T10:21:05.699365375+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:22:35.701109984+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:24:05.703370086+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:25:35.704794835+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T10:27:05.706185972+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T10:28:06.341275339+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":1030.87516909,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"geitfiblixbm\"  connection_id:\"1(@)\")"}
{"time":"2025-08-08T10:45:07.958163589+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": unexpected EOF"}
{"time":"2025-08-08T11:08:14.834621627+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:50672->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:08:22.034862207+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:50684->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:08:31.48848326+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:37276->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:08:40.499105968+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T11:08:46.430838699+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:40720->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:09:08.037756779+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:41322->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:09:12.952047737+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:09:47.003582954+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:09:51.491257761+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:34740->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:10:25.86421702+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:10:56.495001979+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:54512->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:11:15.810708974+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:12:01.498686114+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:52498->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:12:22.971698155+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:13:06.502496875+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:40240->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:13:52.973928209+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T11:14:11.506229673+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:41180->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:15:16.510034623+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:48156->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:15:22.974949565+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:16:21.513309741+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:53204->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:16:52.975783455+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:17:26.517825452+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:54666->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:18:10.498953805+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000895622,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"vh41x2ppplxl\"  connection_id:\"1(@)\")"}
{"time":"2025-08-08T11:18:22.977016459+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:18:31.521185575+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:56692->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:19:36.524138311+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:47250->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:19:52.97811314+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-08-08T11:20:41.528829795+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:60850->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:21:22.979276886+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-08T11:21:46.533165463+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:48108->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:22:51.537056472+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/wanghaoran08042-chinese-academy-of-sciences/state/q5fmw30p/file_stream\": read tcp 127.0.0.1:56106->127.0.0.1:7900: read: connection reset by peer"}
{"time":"2025-08-08T11:22:52.980413686+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
