{"time":"2025-08-07T17:33:13.978889711+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T17:33:14.721832027+08:00","level":"INFO","msg":"stream: created new stream","id":"lad0dyl1"}
{"time":"2025-08-07T17:33:14.721884762+08:00","level":"INFO","msg":"stream: started","id":"lad0dyl1"}
{"time":"2025-08-07T17:33:14.721922396+08:00","level":"INFO","msg":"sender: started","stream_id":"lad0dyl1"}
{"time":"2025-08-07T17:33:14.721917634+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"lad0dyl1"}
{"time":"2025-08-07T17:33:14.721959344+08:00","level":"INFO","msg":"handler: started","stream_id":"lad0dyl1"}
{"time":"2025-08-07T17:33:15.20310923+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-08-07T17:33:15.203313977+08:00","level":"ERROR","msg":"runupserter: failed to init run","error":"returned error 404: {\"data\":{\"upsertBucket\":null},\"errors\":[{\"message\":\"entity your_entity_name not found during upsertBucket\",\"path\":[\"upsertBucket\"]}]}"}
{"time":"2025-08-07T17:35:41.746178801+08:00","level":"INFO","msg":"stream: closing","id":"lad0dyl1"}
{"time":"2025-08-07T17:35:41.746483973+08:00","level":"ERROR","msg":"sender: uploadConfigFile: stream: no run"}
{"time":"2025-08-07T17:35:42.04466102+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-08-07T17:35:42.044825501+08:00","level":"ERROR","msg":"runfiles: CreateRunFiles returned error: returned error 404: {\"data\":{\"createRunFiles\":null},\"errors\":[{\"message\":\"entity your_entity_name not found during createRunFiles\",\"path\":[\"createRunFiles\"]}]}"}
{"time":"2025-08-07T17:35:42.04579793+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-07T17:35:42.045881902+08:00","level":"INFO","msg":"handler: closed","stream_id":"lad0dyl1"}
{"time":"2025-08-07T17:35:42.045922141+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"lad0dyl1"}
{"time":"2025-08-07T17:35:42.045941062+08:00","level":"INFO","msg":"sender: closed","stream_id":"lad0dyl1"}
{"time":"2025-08-07T17:35:42.046012428+08:00","level":"INFO","msg":"stream: closed","id":"lad0dyl1"}
