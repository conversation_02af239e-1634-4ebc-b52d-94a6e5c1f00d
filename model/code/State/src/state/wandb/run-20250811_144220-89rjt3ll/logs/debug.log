2025-08-11 14:42:20,675 INFO    MainThread:634813 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-11 14:42:20,675 INFO    MainThread:634813 [wandb_setup.py:_flush():80] Configure stats pid to 634813
2025-08-11 14:42:20,675 INFO    MainThread:634813 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-11 14:42:20,675 INFO    MainThread:634813 [wandb_setup.py:_flush():80] Loading settings from /data/ioz_whr_wsx/model/code/State/src/state/wandb/settings
2025-08-11 14:42:20,675 INFO    MainThread:634813 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-11 14:42:20,675 INFO    MainThread:634813 [wandb_init.py:setup_run_log_directory():703] Logging user logs to ./wandb/run-20250811_144220-89rjt3ll/logs/debug.log
2025-08-11 14:42:20,676 INFO    MainThread:634813 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to ./wandb/run-20250811_144220-89rjt3ll/logs/debug-internal.log
2025-08-11 14:42:20,676 INFO    MainThread:634813 [wandb_init.py:init():830] calling init triggers
2025-08-11 14:42:20,676 INFO    MainThread:634813 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-08-11 14:42:20,676 INFO    MainThread:634813 [wandb_init.py:init():871] starting backend
2025-08-11 14:42:20,883 INFO    MainThread:634813 [wandb_init.py:init():874] sending inform_init request
2025-08-11 14:42:20,885 INFO    MainThread:634813 [wandb_init.py:init():882] backend started and connected
2025-08-11 14:42:20,886 INFO    MainThread:634813 [wandb_init.py:init():953] updated telemetry
2025-08-11 14:42:20,890 INFO    MainThread:634813 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-11 14:42:26,258 WARNING MainThread:634813 [wandb_init.py:init():1610] [no run ID] interrupted
Traceback (most recent call last):
  File "/home/<USER>/.local/share/uv/tools/arc-state/lib/python3.11/site-packages/wandb/sdk/wandb_init.py", line 1606, in init
    return wi.init(run_settings, run_config, run_printer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/share/uv/tools/arc-state/lib/python3.11/site-packages/wandb/sdk/wandb_init.py", line 996, in init
    result = wait_with_progress(
             ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/share/uv/tools/arc-state/lib/python3.11/site-packages/wandb/sdk/mailbox/wait_with_progress.py", line 24, in wait_with_progress
    return wait_all_with_progress(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/share/uv/tools/arc-state/lib/python3.11/site-packages/wandb/sdk/mailbox/wait_with_progress.py", line 87, in wait_all_with_progress
    return asyncio_compat.run(progress_loop_with_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/share/uv/tools/arc-state/lib/python3.11/site-packages/wandb/sdk/lib/asyncio_compat.py", line 30, in run
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.11.13-linux-x86_64-gnu/lib/python3.11/concurrent/futures/_base.py", line 451, in result
    self._condition.wait(timeout)
  File "/home/<USER>/.local/share/uv/python/cpython-3.11.13-linux-x86_64-gnu/lib/python3.11/threading.py", line 327, in wait
    waiter.acquire()
KeyboardInterrupt
