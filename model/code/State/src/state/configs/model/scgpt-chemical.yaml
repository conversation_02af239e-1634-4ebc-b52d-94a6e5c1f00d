name: scGPT-chemical
checkpoint: null
device: cuda

kwargs:
  hidden_dim: 256 # not used
  pad_token: "<pad>"
  special_tokens:
    - "<pad>"
    - "<cls>"
    - "<eoc>"
  
  pad_value: 0
  pert_pad_id: 2

  include_zero_gene: "all"  # include zero expr genes in training input, "all", "batch-wise", "row-wise", or False
  max_seq_len: 1536

  do_MLM: true  # whether to use masked language modeling, currently it is always on.
  do_CLS: false  # celltype classification objective
  do_CCE: false  # Contrastive cell embedding objective
  do_MVC: false  # Masked value prediction for cell embedding
  do_ECS: false  # Elastic cell similarity objective
  cell_emb_style: "cls"
  mvc_decoder_style: "inner product, detach"
  use_amp: true
  pretrained_path: "/large_storage/goodarzilab/userspace/mohsen/scGPT/scGPT_human/"
  load_param_prefixes:
    - "encoder"
    - "value_encoder"
    - "transformer_encoder"

  # settings for the model
  embsize: 512  # embedding dimension
  d_hid: 512  # dimension of the feedforward network model in nn.TransformerEncoder
  nlayers: 12  # number of nn.TransformerEncoderLayer in nn.TransformerEncoder
  nhead: 8  # number of heads in nn.MultiheadAttention
  n_layers_cls: 3
  dropout: 0.2  # dropout probability
  use_fast_transformer: true  # whether to use fast transformer

  expr_transform: none
  perturbation_type: "chemical"
  cell_sentence_len: 2048
  seed: 2025
  nb_decoder: false