# Dataset paths - maps dataset names to their directories
[datasets]
example = "/home/<USER>/state/examples"

# Training specifications
# All cell types in a dataset automatically go into training (excluding zeroshot/fewshot overrides)
[training]
example = "train"

# Zeroshot specifications - entire cell types go to val or test
[zeroshot]
"example.CT3" = "test"

# Fewshot specifications - explicit perturbation lists
[fewshot]

[fewshot."example.CT4"]
val = ["TARGET3"]
test = ["TARGET4", "TARGET5"]  # can overlap with val
