#!/bin/bash

# ========== 配置 ==========
BASE_DIR="/data/ioz_whr_wsx/model"
CODE_DIR="$BASE_DIR/code"
MODEL_DIR="$BASE_DIR/models"
HFD_SCRIPT="/data/ioz_whr_wsx/codes/hfd.sh"

# Huggingface credentials
HF_USERNAME="Arginine"
HF_TOKEN="*************************************"

# # ========== 创建目录结构 ==========
# mkdir -p "$CODE_DIR"
# mkdir -p "$MODEL_DIR"

# # ========== 下载 GitHub 仓库 ==========
# echo "[✔] Cloning GitHub repositories..."
# cd "$CODE_DIR"
# git clone https://github.com/ArcInstitute/State.git
# git clone https://github.com/ArcInstitute/Cell-load.git
# git clone https://github.com/ArcInstitute/Cell-eval.git
# git clone https://github.com/ArcInstitute/State-reproduce.git
# cd -

# ========== 下载 Huggingface 模型 ==========
echo "[✔] Downloading Huggingface models using hfd.sh..."

models=(
    "arcinstitute/SE-600M"
    "arcinstitute/ST-Tahoe"
    "arcinstitute/ST-Parse"
)

for model_id in "${models[@]}"; do
    name=$(basename "$model_id")
    echo "[⬇] Downloading $model_id into $MODEL_DIR/$name ..."
    bash "$HFD_SCRIPT" "$model_id" \
        --local-dir "$MODEL_DIR/$name" \
        --hf_username "$HF_USERNAME" \
        --hf_token "$HF_TOKEN" \
        --tool aria2c \
        -x 8
done

echo "[✅] All repositories and models downloaded into: $BASE_DIR"
