data:
  name: PerturbationDataModule
  kwargs:
    toml_config_path: /data/ioz_whr_wsx/model/code/State/examples/zeroshot.toml
    embed_key: X_hvg
    output_space: all
    pert_rep: onehot
    basal_rep: sample
    num_workers: 12
    pin_memory: true
    n_basal_samples: 1
    basal_mapping_strategy: random
    should_yield_control_cells: true
    batch_col: batch_var
    pert_col: target_gene
    cell_type_key: cell_type
    control_pert: TARGET1
    map_controls: true
    perturbation_features_file: null
    store_raw_basal: false
    int_counts: false
    barcode: true
  output_dir: null
  debug: true
model:
  name: PertSets
  checkpoint: null
  device: cuda
  kwargs:
    cell_set_len: 64
    extra_tokens: 1
    decoder_hidden_dims:
    - 1024
    - 1024
    - 512
    blur: 0.05
    hidden_dim: 328
    loss: energy
    confidence_token: false
    n_encoder_layers: 4
    n_decoder_layers: 4
    predict_residual: true
    freeze_pert_backbone: false
    finetune_vci_decoder: false
    residual_decoder: false
    batch_encoder: false
    nb_decoder: false
    decoder_loss_weight: 1.0
    use_basal_projection: false
    mask_attn: false
    distributional_loss: energy
    regularization: 0.0
    init_from: null
    transformer_backbone_key: GPT2
    transformer_backbone_kwargs:
      max_position_embeddings: 64
      n_positions: 64
      hidden_size: 328
      n_embd: 328
      n_layer: 8
      n_head: 8
      resid_pdrop: 0.0
      embd_pdrop: 0.0
      attn_pdrop: 0.0
      use_cache: false
training:
  wandb_track: false
  weight_decay: 0.0005
  batch_size: 8
  lr: 0.0001
  max_steps: 40000
  train_seed: 42
  val_freq: 100
  ckpt_every_n_steps: 100
  gradient_clip_val: 10
  loss_fn: mse
  devices: 1
  strategy: auto
wandb:
  entity: YOUR_ENTITY
  project: YOUR_PROJECT
  local_wandb_dir: ./wandb_logs
  tags:
  - test
name: test
output_dir: /data/ioz_whr_wsx/datasets/state/demo/output
use_wandb: true
overwrite: false
return_adatas: false
pred_adata_path: null
true_adata_path: null
