{"output_dir": "/data/ioz_whr_wsx/datasets/VCC/Jiang_converted2h5/converted_h5ad", "files": [{"file": "/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Bulk_RNAseq_Seurat_object_IFNG_and_TGFB_stim.rds", "assay": "RNA", "cells": 9, "genes": 60649, "nnz": 143247, "zero_frac": 0.737566, "per_cell_sum": {"mean": 639630.1111111111, "median": 637770.0, "min": 500294.0, "max": 855684.0}, "per_gene_sum": {"mean": 94.91782222295504, "median": 0.0, "min": 0.0, "max": 6910.0}, "obs_cols": {"orig.ident": "category", "nCount_RNA": "float64", "nFeature_RNA": "int32", "sample": "category", "stim": "category", "replicate": "category"}, "var_cols": {"vf_vst_counts.1_mean": "float64", "vf_vst_counts.1_variance": "float64", "vf_vst_counts.1_variance.expected": "float64", "vf_vst_counts.1_variance.standardized": "float64", "vf_vst_counts.1_variable": "bool", "vf_vst_counts.1_rank": "int32", "vf_vst_counts.2_mean": "float64", "vf_vst_counts.2_variance": "float64", "vf_vst_counts.2_variance.expected": "float64", "vf_vst_counts.2_variance.standardized": "float64"}}, {"file": "/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_IFNB_Perturb_seq.rds", "assay": "RNA", "cells": 328542, "genes": 34025, "nnz": 980979021, "zero_frac": 0.912245, "per_cell_sum": {"mean": 6524.078690091374, "median": 5130.0, "min": 568.0, "max": 89641.0}, "per_gene_sum": {"mean": 62995.85190301249, "median": 2626.0, "min": 0.0, "max": 60315643.0}, "obs_cols": {"orig.ident": "category", "nCount_RNA": "float64", "nFeature_RNA": "int32", "sample": "category", "bc1_well": "category", "bc2_well": "category", "bc3_well": "category", "percent.mito": "float64", "cell_type": "category", "pathway": "category"}, "var_cols": {"vst.mean": "float64", "vst.variance": "float64", "vst.variance.expected": "float64", "vst.variance.standardized": "float64", "vst.variable": "bool"}}, {"file": "/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_IFNG_Perturb_seq.rds", "assay": "RNA", "cells": 245240, "genes": 33525, "nnz": 667070362, "zero_frac": 0.918864, "per_cell_sum": {"mean": 5695.431324416898, "median": 4364.5, "min": 543.0, "max": 88482.0}, "per_gene_sum": {"mean": 41662.86586129754, "median": 1766.0, "min": 0.0, "max": 34417070.0}, "obs_cols": {"orig.ident": "category", "nCount_RNA": "float64", "nFeature_RNA": "int32", "sample": "category", "cell_type": "category", "pathway": "category", "percent.mito": "float64", "sample_ID": "category", "Batch_info": "category", "bc1_well": "category"}, "var_cols": {"vst.mean": "float64", "vst.variance": "float64", "vst.variance.expected": "float64", "vst.variance.standardized": "float64", "vst.variable": "bool"}}, {"file": "/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_INS_Perturb_seq.rds", "assay": "RNA", "cells": 431457, "genes": 34025, "nnz": 1280241271, "zero_frac": 0.912792, "per_cell_sum": {"mean": 6481.334373993237, "median": 5228.0, "min": 554.0, "max": 91046.0}, "per_gene_sum": {"mean": 82187.12961058045, "median": 3127.0, "min": 0.0, "max": 85694800.0}, "obs_cols": {"orig.ident": "category", "nCount_RNA": "float64", "nFeature_RNA": "int32", "sample": "category", "bc1_well": "category", "bc2_well": "category", "bc3_well": "category", "percent.mito": "float64", "cell_type": "category", "pathway": "category"}, "var_cols": {"vst.mean": "float64", "vst.variance": "float64", "vst.variance.expected": "float64", "vst.variance.standardized": "float64", "vst.variable": "bool"}}, {"file": "/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_TGFB_Perturb_seq.rds", "assay": "RNA", "cells": 236606, "genes": 33525, "nnz": 605806920, "zero_frac": 0.923627, "per_cell_sum": {"mean": 5048.215290398384, "median": 3873.0, "min": 542.0, "max": 85032.0}, "per_gene_sum": {"mean": 35628.27821029083, "median": 1587.0, "min": 0.0, "max": 27617731.0}, "obs_cols": {"orig.ident": "category", "nCount_RNA": "float64", "nFeature_RNA": "int32", "sample": "category", "cell_type": "category", "pathway": "category", "percent.mito": "float64", "sample_ID": "category", "Batch_info": "category", "bc1_well": "category"}, "var_cols": {"vst.mean": "float64", "vst.variance": "float64", "vst.variance.expected": "float64", "vst.variance.standardized": "float64", "vst.variable": "bool"}}, {"file": "/data/vcc/raw_data_vcc/raw_data_vcc/Jiang_et_al_2025/Seurat_object_TNFA_Perturb_seq.rds", "assay": "RNA", "cells": 386631, "genes": 33525, "nnz": 1066935493, "zero_frac": 0.917686, "per_cell_sum": {"mean": 5653.2257346151755, "median": 4618.0, "min": 560.0, "max": 105379.0}, "per_gene_sum": {"mean": 65196.48975391499, "median": 2723.0, "min": 0.0, "max": 87254045.0}, "obs_cols": {"orig.ident": "category", "nCount_RNA": "float64", "nFeature_RNA": "int32", "sample": "category", "cell_type": "category", "pathway": "category", "percent.mito": "float64", "sample_ID": "category", "Batch_info": "category", "bc1_well": "category"}, "var_cols": {"vst.mean": "float64", "vst.variance": "float64", "vst.variance.expected": "float64", "vst.variance.standardized": "float64", "vst.variable": "bool"}}]}