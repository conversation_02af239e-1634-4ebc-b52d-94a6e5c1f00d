2025-09-02 17:03:47,326 - INFO - Found 1 files to process
2025-09-02 17:03:47,326 - INFO - Processing file 1/1: Seurat_object_IFNG_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:03:47,327 - INFO - Processing Seurat_object_IFNG_Perturb_seq.RNA.counts.aligned.h5ad: 1.4GB file, estimated memory: 8.0GB, available: 795.3GB
2025-09-02 17:03:47,328 - INFO - [load_data] Start - Memory: 0.24 GB
2025-09-02 17:03:47,328 - INFO - Memory-efficient environment configured
2025-09-02 17:03:47,328 - INFO - Loading 1.4GB file with chunk_size=200000
2025-09-02 17:03:47,328 - INFO - Available memory: 795.3GB
2025-09-02 17:03:48,424 - INFO - Total observations: 245,240
2025-09-02 17:03:48,424 - INFO - Counting perturbations...
2025-09-02 17:03:48,474 - INFO - Keeping 60 perturbations out of 60
2025-09-02 17:03:48,474 - INFO - Collecting cell indices...
2025-09-02 17:03:48,515 - INFO - Keep statistics: 245,240/245,240 cells (100.0%)
2025-09-02 17:03:48,515 - INFO - High keep ratio and sufficient memory, attempting full load
2025-09-02 17:03:48,515 - INFO - Attempting careful full load...
2025-09-02 17:03:48,515 - INFO - Step 1: Loading to memory...
2025-09-02 17:04:04,718 - INFO - Step 2: Converting sparse matrix...
2025-09-02 17:04:04,719 - INFO - Step 3: Creating subset...
2025-09-02 17:04:04,777 - INFO - Creating subset with 245,240 cells using batched approach
2025-09-02 17:04:10,182 - INFO - Created 5 subset chunks
2025-09-02 17:04:10,252 - INFO - Combining 5 chunks...
2025-09-02 17:04:10,252 - INFO - [FIX] Preserving var.columns before concat: ['gene_name', 'gene_id']
2025-09-02 17:04:15,357 - INFO - [FIX] Restoring var.columns after concat: ['gene_name', 'gene_id']
2025-09-02 17:04:15,358 - INFO - [FIX] Successfully restored var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:15,945 - INFO - [load_data] End - Memory: 4.89 GB (Δ: ***** GB)
2025-09-02 17:04:15,945 - INFO - [debug] after load: X=sparse, dtype=float32
2025-09-02 17:04:15,945 - INFO - [DEBUG] After load - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:15,945 - INFO - [DEBUG] After load - var.shape: (18080, 2)
2025-09-02 17:04:17,820 - INFO - [DEBUG] After clean_data_matrix - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:17,820 - INFO - [DEBUG] After clean_data_matrix - var.shape: (18080, 2)
2025-09-02 17:04:17,820 - INFO - Validating data integrity for: after_load
2025-09-02 17:04:18,169 - INFO - Data validation passed: 245240 cells, 18080 genes
2025-09-02 17:04:18,169 - INFO - [DEBUG] Before detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,169 - INFO - [DEBUG] Before detect_format - var.shape: (18080, 2)
2025-09-02 17:04:18,169 - INFO - [detect_format] Start - Memory: 4.89 GB
2025-09-02 17:04:18,271 - INFO - [detect_format] End - Memory: 4.89 GB (Δ: +0.00 GB)
2025-09-02 17:04:18,271 - INFO - [detect_format] detected is_log1p=False, frac_1e4_like=0.000
2025-09-02 17:04:18,271 - INFO - [DEBUG] After detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,271 - INFO - [DEBUG] After detect_format - var.shape: (18080, 2)
2025-09-02 17:04:18,271 - INFO - [PROCESS] 检测为计数数据，使用标准化+log1p分支处理
2025-09-02 17:04:18,271 - INFO - [process_count_branch] Start - Memory: 4.89 GB
2025-09-02 17:04:18,271 - INFO - Processing count data branch
2025-09-02 17:04:18,271 - INFO - [DEBUG] process_count_branch entry - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,271 - INFO - [DEBUG] process_count_branch entry - var.shape: (18080, 2)
2025-09-02 17:04:18,271 - INFO - [DEBUG] Before validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,272 - INFO - [DEBUG] Before validation - var.shape: (18080, 2)
2025-09-02 17:04:18,272 - INFO - Validating data integrity for: before_normalization
2025-09-02 17:04:18,609 - INFO - Data validation passed: 245240 cells, 18080 genes
2025-09-02 17:04:18,609 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,609 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 17:04:18,609 - INFO - [PROCESS] 跳过归一化步骤，直接使用原始count数据
2025-09-02 17:04:18,609 - INFO - [DEBUG] Skip normalization - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,609 - INFO - [DEBUG] Skip normalization - var.shape: (18080, 2)
2025-09-02 17:04:18,609 - INFO - Validating data integrity for: after_normalization
2025-09-02 17:04:18,948 - INFO - Data validation passed: 245240 cells, 18080 genes
2025-09-02 17:04:18,948 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,948 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 17:04:18,948 - INFO - [DEBUG] Before KD call - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,948 - INFO - [DEBUG] Before KD call - var.shape: (18080, 2)
2025-09-02 17:04:18,948 - INFO - start KD
2025-09-02 17:04:18,959 - INFO - [GENE_CHECK] Checking for gene column: 'gene_name'
2025-09-02 17:04:18,959 - INFO - [GENE_CHECK] Available columns: ['gene_name', 'gene_id']
2025-09-02 17:04:18,959 - INFO - [GENE_CHECK] Found using method 'direct'
2025-09-02 17:04:19,169 - INFO - [KD] 总细胞: 245,240 | 对照细胞: 12,780 | 待检查扰动基因: 59
2025-09-02 17:04:19,174 - INFO - [DEBUG] Data characteristics (sample (1000, 1000)):
2025-09-02 17:04:19,175 - INFO - [DEBUG] - Value range: [0.000000, 52.000000]
2025-09-02 17:04:19,177 - INFO - [DEBUG] - Mean: 0.125443, Std: 0.559061
2025-09-02 17:04:19,181 - INFO - [DEBUG] - Sparsity: 84940/1000000 (8.5% non-zero)
2025-09-02 17:04:19,181 - INFO - [DEBUG] - Skip normalization: True
2025-09-02 17:04:19,182 - INFO - [DEBUG] Control group sample ((100, 100)):
2025-09-02 17:04:19,182 - INFO - [DEBUG] - Control range: [0.000000, 10.000000]
2025-09-02 17:04:19,182 - INFO - [DEBUG] - Control mean: 0.117100
2025-09-02 17:04:19,182 - INFO - [KD] Stage 1 - 开始基因级KD判定，共 59 个基因
2025-09-02 17:04:19,298 - INFO - [DEBUG] Gene ATF3: found cols.size=1, ctrl_mean=0.408764, threshold=-1e-06
2025-09-02 17:04:19,299 - INFO - [DEBUG] Gene ATF3: sample ctrl values - min=0.000000, max=2.000000, mean=0.160000, nonzero=14/100
2025-09-02 17:04:19,499 - INFO - [DEBUG] Gene STAT2: found cols.size=1, ctrl_mean=1.125900, threshold=-1e-06
2025-09-02 17:04:19,500 - INFO - [DEBUG] Gene STAT2: sample ctrl values - min=0.000000, max=6.000000, mean=1.030000, nonzero=53/100
2025-09-02 17:04:19,633 - INFO - [DEBUG] Gene CUL1: found cols.size=1, ctrl_mean=1.318153, threshold=-1e-06
2025-09-02 17:04:19,634 - INFO - [DEBUG] Gene CUL1: sample ctrl values - min=0.000000, max=7.000000, mean=0.730000, nonzero=47/100
2025-09-02 17:04:19,766 - INFO - [DEBUG] Gene IRF2: found cols.size=1, ctrl_mean=0.461737, threshold=-1e-06
2025-09-02 17:04:19,767 - INFO - [DEBUG] Gene IRF2: sample ctrl values - min=0.000000, max=4.000000, mean=0.520000, nonzero=37/100
2025-09-02 17:04:19,906 - INFO - [DEBUG] Gene MYC: found cols.size=1, ctrl_mean=0.348748, threshold=-1e-06
2025-09-02 17:04:19,907 - INFO - [DEBUG] Gene MYC: sample ctrl values - min=0.000000, max=1.000000, mean=0.120000, nonzero=12/100
2025-09-02 17:04:20,758 - INFO - [KD/stage1] 10/59 gene=JAK1 cols=1 ctrl_mean=1.086 pert_mean=0.464 ratio=0.427 keep=False (0.18s)
2025-09-02 17:04:22,476 - INFO - [KD/stage1] 20/59 gene=PRDM1 cols=1 ctrl_mean=0.07199 pert_mean=0.0331 ratio=0.460 keep=False (0.17s)
2025-09-02 17:04:23,901 - INFO - [KD/stage1] 30/59 gene=IFNGR1 cols=1 ctrl_mean=0.3152 pert_mean=0.1036 ratio=0.329 keep=False (0.15s)
2025-09-02 17:04:25,380 - INFO - [KD/stage1] 40/59 gene=MCRS1 cols=1 ctrl_mean=0.1081 pert_mean=0.1374 ratio=1.272 keep=False (0.13s)
2025-09-02 17:04:26,823 - INFO - [KD/stage1] 50/59 gene=FBXO6 cols=1 ctrl_mean=0.02222 pert_mean=0.005225 ratio=0.235 keep=True (0.15s)
2025-09-02 17:04:28,012 - INFO - [KD/stage1] 59/59 gene=IFI16 cols=1 ctrl_mean=0.6808 pert_mean=0.1835 ratio=0.270 keep=True (0.12s)
2025-09-02 17:04:28,227 - INFO - [KD/stage2] gene=ATF3 分片 1/1 kept_in_chunk=10052 kept_total=10052 rows 0-10622
2025-09-02 17:04:28,227 - INFO - [KD/stage2] gene=ATF3 完成，保留细胞 10052/10623（0.21s）
2025-09-02 17:04:28,388 - INFO - [KD/stage2] gene=STAT2 分片 1/1 kept_in_chunk=1342 kept_total=1342 rows 0-1668
2025-09-02 17:04:28,389 - INFO - [KD/stage2] gene=STAT2 完成，保留细胞 1342/1669（0.16s）
2025-09-02 17:04:28,536 - INFO - [KD/stage2] gene=IRF2 分片 1/1 kept_in_chunk=2441 kept_total=2441 rows 0-2666
2025-09-02 17:04:28,536 - INFO - [KD/stage2] gene=IRF2 完成，保留细胞 2441/2667（0.15s）
2025-09-02 17:04:28,667 - INFO - [KD/stage2] gene=SP110 分片 1/1 kept_in_chunk=1338 kept_total=1338 rows 0-1505
2025-09-02 17:04:28,667 - INFO - [KD/stage2] gene=SP110 完成，保留细胞 1338/1506（0.13s）
2025-09-02 17:04:28,857 - INFO - [KD/stage2] gene=PARP12 分片 1/1 kept_in_chunk=6329 kept_total=6329 rows 0-7442
2025-09-02 17:04:28,857 - INFO - [KD/stage2] gene=PARP12 完成，保留细胞 6329/7443（0.19s）
2025-09-02 17:04:29,005 - INFO - [KD/stage2] gene=ZNFX1 分片 1/1 kept_in_chunk=2340 kept_total=2340 rows 0-2944
2025-09-02 17:04:29,005 - INFO - [KD/stage2] gene=ZNFX1 完成，保留细胞 2340/2945（0.15s）
2025-09-02 17:04:29,177 - INFO - [KD/stage2] gene=BATF2 分片 1/1 kept_in_chunk=5153 kept_total=5153 rows 0-5315
2025-09-02 17:04:29,178 - INFO - [KD/stage2] gene=BATF2 完成，保留细胞 5153/5316（0.17s）
2025-09-02 17:04:29,365 - INFO - [KD/stage2] gene=SP100 分片 1/1 kept_in_chunk=6668 kept_total=6668 rows 0-7433
2025-09-02 17:04:29,365 - INFO - [KD/stage2] gene=SP100 完成，保留细胞 6668/7434（0.19s）
2025-09-02 17:04:29,513 - INFO - [KD/stage2] gene=FBXO6 分片 1/1 kept_in_chunk=2857 kept_total=2857 rows 0-2870
2025-09-02 17:04:29,513 - INFO - [KD/stage2] gene=FBXO6 完成，保留细胞 2857/2871（0.15s）
2025-09-02 17:04:29,676 - INFO - [KD/stage2] gene=MAFF 分片 1/1 kept_in_chunk=4008 kept_total=4008 rows 0-4082
2025-09-02 17:04:29,676 - INFO - [KD/stage2] gene=MAFF 完成，保留细胞 4008/4083（0.16s）
2025-09-02 17:04:29,804 - INFO - [KD/stage2] gene=TAPBPL 分片 1/1 kept_in_chunk=1011 kept_total=1011 rows 0-1041
2025-09-02 17:04:29,804 - INFO - [KD/stage2] gene=TAPBPL 完成，保留细胞 1011/1042（0.13s）
2025-09-02 17:04:29,930 - INFO - [KD/stage2] gene=IFI16 分片 1/1 kept_in_chunk=591 kept_total=591 rows 0-653
2025-09-02 17:04:29,930 - INFO - [KD/stage2] gene=IFI16 完成，保留细胞 591/654（0.13s）
2025-09-02 17:04:29,968 - INFO - [KD] 完成：保留 56,910/245,240 细胞 | 对照 12,780 | 通过基因 12 | 用时 10.79s
2025-09-02 17:04:30,090 - INFO - [FIX] materialize KD result (copy) to avoid sparse view assignment
2025-09-02 17:04:30,703 - INFO - Validating data integrity for: after_filter_count
2025-09-02 17:04:30,778 - INFO - Data validation passed: 56910 cells, 18080 genes
2025-09-02 17:04:30,778 - INFO - [PROCESS] 正在执行 log1p 转换...
2025-09-02 17:04:31,069 - INFO - [PROCESS] log1p 转换完成
2025-09-02 17:04:31,168 - INFO - [process_count_branch] End - Memory: 5.88 GB (Δ: +0.99 GB)
2025-09-02 17:04:31,168 - INFO - [standardize] Start - Memory: 5.88 GB
2025-09-02 17:04:31,169 - INFO - [standardize_names] Start - Memory: 5.88 GB
2025-09-02 17:04:31,169 - INFO - Standardizing: pert_col=gene, gene_col=gene_name
2025-09-02 17:04:31,169 - INFO - Control label: non-targeting -> non-targeting
2025-09-02 17:04:31,169 - INFO - Perturbation column type: <class 'pandas.core.series.Series'>, dtype: object
2025-09-02 17:04:31,171 - INFO - [STANDARDIZE] Checking for gene column: 'gene_name'
2025-09-02 17:04:31,171 - INFO - [STANDARDIZE] Available columns: ['gene_name', 'gene_id']
2025-09-02 17:04:31,171 - INFO - [STANDARDIZE] Found using method 'direct'
2025-09-02 17:04:31,349 - INFO - [standardize_names] End - Memory: 5.88 GB (Δ: +0.00 GB)
2025-09-02 17:04:31,429 - INFO - [standardize] End - Memory: 5.88 GB (Δ: +0.00 GB)
2025-09-02 17:04:31,429 - INFO - Validating data integrity for: final_validation
2025-09-02 17:04:31,503 - INFO - Data validation passed: 56910 cells, 18080 genes
2025-09-02 17:04:31,506 - INFO - [write_output] Start - Memory: 5.88 GB
2025-09-02 17:04:31,506 - INFO - [WRITE] 开始写入输出文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_IFNG_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:04:31,506 - INFO - [WRITE] 最终数据维度: 56910 细胞 × 18080 基因
... storing 'orig.ident' as categorical
... storing 'sample' as categorical
... storing 'cell_type' as categorical
... storing 'pathway' as categorical
... storing 'sample_ID' as categorical
... storing 'Batch_info' as categorical
... storing 'bc1_well' as categorical
... storing 'bc2_well' as categorical
... storing 'bc3_well' as categorical
... storing 'guide' as categorical
... storing 'gene' as categorical
2025-09-02 17:04:42,172 - INFO - Successfully wrote /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_IFNG_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:04:42,253 - INFO - [write_output] End - Memory: 5.88 GB (Δ: -0.00 GB)
2025-09-02 17:04:42,253 - INFO - Successfully processed Seurat_object_IFNG_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:04:42,253 - INFO - Output: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_IFNG_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:04:42,253 - INFO - Cells kept: 56910
2025-09-02 17:04:42,254 - INFO - Genes kept: 18080
2025-09-02 17:04:42,330 - INFO - ✓ Successfully processed Seurat_object_IFNG_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:04:42,330 - INFO - ============================================================
2025-09-02 17:04:42,330 - INFO - PROCESSING SUMMARY
2025-09-02 17:04:42,331 - INFO - ============================================================
2025-09-02 17:04:42,331 - INFO - Total files: 1
2025-09-02 17:04:42,331 - INFO - Successful: 1
2025-09-02 17:04:42,331 - INFO - Failed: 0
2025-09-02 17:04:42,331 - INFO - ✓ 1 files processed successfully
2025-09-02 17:04:42,331 - INFO - 🎉 All files processed successfully!
