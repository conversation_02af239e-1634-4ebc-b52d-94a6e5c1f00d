2025-09-02 17:00:03,962 - INFO - Found 1 files to process
2025-09-02 17:00:03,962 - INFO - Processing file 1/1: Seurat_object_TNFA_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:00:03,963 - INFO - Processing Seurat_object_TNFA_Perturb_seq.RNA.counts.aligned.h5ad: 2.1GB file, estimated memory: 8.0GB, available: 796.8GB
2025-09-02 17:00:03,964 - INFO - [load_data] Start - Memory: 0.24 GB
2025-09-02 17:00:03,964 - INFO - Memory-efficient environment configured
2025-09-02 17:00:03,964 - INFO - Loading 2.1GB file with chunk_size=200000
2025-09-02 17:00:03,964 - INFO - Available memory: 796.8GB
2025-09-02 17:00:05,718 - INFO - Total observations: 386,631
2025-09-02 17:00:05,718 - INFO - Counting perturbations...
2025-09-02 17:00:05,774 - INFO - Keeping 56 perturbations out of 56
2025-09-02 17:00:05,774 - INFO - Collecting cell indices...
2025-09-02 17:00:05,842 - INFO - Keep statistics: 386,631/386,631 cells (100.0%)
2025-09-02 17:00:05,842 - INFO - High keep ratio and sufficient memory, attempting full load
2025-09-02 17:00:05,842 - INFO - Attempting careful full load...
2025-09-02 17:00:05,842 - INFO - Step 1: Loading to memory...
2025-09-02 17:00:32,158 - INFO - Step 2: Converting sparse matrix...
2025-09-02 17:00:32,160 - INFO - Step 3: Creating subset...
2025-09-02 17:00:32,276 - INFO - Creating subset with 386,631 cells using batched approach
2025-09-02 17:00:43,534 - INFO - Created 5 subset chunks
2025-09-02 17:00:50,453 - INFO - Combining 8 chunks...
2025-09-02 17:00:50,454 - INFO - [FIX] Preserving var.columns before concat: ['gene_name', 'gene_id']
2025-09-02 17:01:01,992 - INFO - [FIX] Restoring var.columns after concat: ['gene_name', 'gene_id']
2025-09-02 17:01:01,993 - INFO - [FIX] Successfully restored var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:02,523 - INFO - [load_data] End - Memory: 7.67 GB (Δ: ***** GB)
2025-09-02 17:01:02,523 - INFO - [debug] after load: X=sparse, dtype=float32
2025-09-02 17:01:02,523 - INFO - [DEBUG] After load - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:02,523 - INFO - [DEBUG] After load - var.shape: (18080, 2)
2025-09-02 17:01:15,896 - INFO - [DEBUG] After clean_data_matrix - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:15,896 - INFO - [DEBUG] After clean_data_matrix - var.shape: (18080, 2)
2025-09-02 17:01:15,896 - INFO - Validating data integrity for: after_load
2025-09-02 17:01:17,318 - INFO - Data validation passed: 386631 cells, 18080 genes
2025-09-02 17:01:17,319 - INFO - [DEBUG] Before detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:17,319 - INFO - [DEBUG] Before detect_format - var.shape: (18080, 2)
2025-09-02 17:01:17,319 - INFO - [detect_format] Start - Memory: 7.67 GB
2025-09-02 17:01:17,432 - INFO - [detect_format] End - Memory: 7.67 GB (Δ: +0.00 GB)
2025-09-02 17:01:17,432 - INFO - [detect_format] detected is_log1p=False, frac_1e4_like=0.000
2025-09-02 17:01:17,432 - INFO - [DEBUG] After detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:17,432 - INFO - [DEBUG] After detect_format - var.shape: (18080, 2)
2025-09-02 17:01:17,432 - INFO - [PROCESS] 检测为计数数据，使用标准化+log1p分支处理
2025-09-02 17:01:17,432 - INFO - [process_count_branch] Start - Memory: 7.67 GB
2025-09-02 17:01:17,432 - INFO - Processing count data branch
2025-09-02 17:01:17,432 - INFO - [DEBUG] process_count_branch entry - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:17,432 - INFO - [DEBUG] process_count_branch entry - var.shape: (18080, 2)
2025-09-02 17:01:17,432 - INFO - [DEBUG] Before validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:17,433 - INFO - [DEBUG] Before validation - var.shape: (18080, 2)
2025-09-02 17:01:17,433 - INFO - Validating data integrity for: before_normalization
2025-09-02 17:01:18,872 - INFO - Data validation passed: 386631 cells, 18080 genes
2025-09-02 17:01:18,872 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:18,872 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 17:01:18,872 - INFO - [PROCESS] 跳过归一化步骤，直接使用原始count数据
2025-09-02 17:01:18,872 - INFO - [DEBUG] Skip normalization - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:18,872 - INFO - [DEBUG] Skip normalization - var.shape: (18080, 2)
2025-09-02 17:01:18,874 - INFO - Validating data integrity for: after_normalization
2025-09-02 17:01:20,266 - INFO - Data validation passed: 386631 cells, 18080 genes
2025-09-02 17:01:20,266 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:20,266 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 17:01:20,266 - INFO - [DEBUG] Before KD call - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:01:20,266 - INFO - [DEBUG] Before KD call - var.shape: (18080, 2)
2025-09-02 17:01:20,267 - INFO - start KD
2025-09-02 17:01:20,286 - INFO - [GENE_CHECK] Checking for gene column: 'gene_name'
2025-09-02 17:01:20,286 - INFO - [GENE_CHECK] Available columns: ['gene_name', 'gene_id']
2025-09-02 17:01:20,286 - INFO - [GENE_CHECK] Found using method 'direct'
2025-09-02 17:01:20,489 - INFO - [KD] 总细胞: 386,631 | 对照细胞: 21,097 | 待检查扰动基因: 55
2025-09-02 17:01:20,493 - INFO - [DEBUG] Data characteristics (sample (1000, 1000)):
2025-09-02 17:01:20,493 - INFO - [DEBUG] - Value range: [0.000000, 21.000000]
2025-09-02 17:01:20,496 - INFO - [DEBUG] - Mean: 0.091469, Std: 0.385607
2025-09-02 17:01:20,500 - INFO - [DEBUG] - Sparsity: 71231/1000000 (7.1% non-zero)
2025-09-02 17:01:20,500 - INFO - [DEBUG] - Skip normalization: True
2025-09-02 17:01:20,501 - INFO - [DEBUG] Control group sample ((100, 100)):
2025-09-02 17:01:20,501 - INFO - [DEBUG] - Control range: [0.000000, 14.000000]
2025-09-02 17:01:20,501 - INFO - [DEBUG] - Control mean: 0.125000
2025-09-02 17:01:20,501 - INFO - [KD] Stage 1 - 开始基因级KD判定，共 55 个基因
2025-09-02 17:01:20,712 - INFO - [DEBUG] Gene MAP2K3: found cols.size=1, ctrl_mean=0.233303, threshold=-1e-06
2025-09-02 17:01:20,713 - INFO - [DEBUG] Gene MAP2K3: sample ctrl values - min=0.000000, max=2.000000, mean=0.150000, nonzero=14/100
2025-09-02 17:01:21,007 - INFO - [DEBUG] Gene TNFRSF1B: found cols.size=1, ctrl_mean=0.020856, threshold=-1e-06
2025-09-02 17:01:21,009 - INFO - [DEBUG] Gene TNFRSF1B: sample ctrl values - min=0.000000, max=1.000000, mean=0.010000, nonzero=1/100
2025-09-02 17:01:21,326 - INFO - [DEBUG] Gene ID2: found cols.size=1, ctrl_mean=0.022088, threshold=-1e-06
2025-09-02 17:01:21,327 - INFO - [DEBUG] Gene ID2: sample ctrl values - min=0.000000, max=1.000000, mean=0.010000, nonzero=1/100
2025-09-02 17:01:21,630 - INFO - [DEBUG] Gene ZNF22: found cols.size=1, ctrl_mean=0.054700, threshold=-1e-06
2025-09-02 17:01:21,631 - INFO - [DEBUG] Gene ZNF22: sample ctrl values - min=0.000000, max=0.000000, mean=0.000000, nonzero=0/100
2025-09-02 17:01:21,926 - INFO - [DEBUG] Gene ZFP36L1: found cols.size=1, ctrl_mean=0.728966, threshold=-1e-06
2025-09-02 17:01:21,927 - INFO - [DEBUG] Gene ZFP36L1: sample ctrl values - min=0.000000, max=7.000000, mean=1.130000, nonzero=53/100
2025-09-02 17:01:23,439 - INFO - [KD/stage1] 10/55 gene=BATF cols=1 ctrl_mean=0.03854 pert_mean=0.0175 ratio=0.454 keep=False (0.28s)
2025-09-02 17:01:26,358 - INFO - [KD/stage1] 20/55 gene=CHUK cols=1 ctrl_mean=0.2482 pert_mean=0.07072 ratio=0.285 keep=True (0.24s)
2025-09-02 17:01:29,405 - INFO - [KD/stage1] 30/55 gene=NFKB1 cols=1 ctrl_mean=1.03 pert_mean=0.5359 ratio=0.521 keep=False (0.24s)
2025-09-02 17:01:31,950 - INFO - [KD/stage1] 40/55 gene=IKBKG cols=1 ctrl_mean=0.05015 pert_mean=0.03682 ratio=0.734 keep=False (0.25s)
2025-09-02 17:01:34,494 - INFO - [KD/stage1] 50/55 gene=CASP3 cols=1 ctrl_mean=0.1178 pert_mean=0.02361 ratio=0.200 keep=True (0.23s)
2025-09-02 17:01:35,667 - INFO - [KD/stage1] 55/55 gene=MTOR cols=1 ctrl_mean=0.8556 pert_mean=0.4484 ratio=0.524 keep=False (0.23s)
2025-09-02 17:01:35,994 - INFO - [KD/stage2] gene=MAP2K3 分片 1/1 kept_in_chunk=8834 kept_total=8834 rows 0-9313
2025-09-02 17:01:35,994 - INFO - [KD/stage2] gene=MAP2K3 完成，保留细胞 8834/9314（0.30s）
2025-09-02 17:01:36,308 - INFO - [KD/stage2] gene=TNFRSF1B 分片 1/1 kept_in_chunk=11019 kept_total=11019 rows 0-11076
2025-09-02 17:01:36,308 - INFO - [KD/stage2] gene=TNFRSF1B 完成，保留细胞 11019/11077（0.31s）
2025-09-02 17:01:36,606 - INFO - [KD/stage2] gene=ZNF22 分片 1/1 kept_in_chunk=8835 kept_total=8835 rows 0-8927
2025-09-02 17:01:36,606 - INFO - [KD/stage2] gene=ZNF22 完成，保留细胞 8835/8928（0.30s）
2025-09-02 17:01:36,947 - INFO - [KD/stage2] gene=MMP9 分片 1/1 kept_in_chunk=13712 kept_total=13712 rows 0-13726
2025-09-02 17:01:36,947 - INFO - [KD/stage2] gene=MMP9 完成，保留细胞 13712/13727（0.34s）
2025-09-02 17:01:37,235 - INFO - [KD/stage2] gene=RELB 分片 1/1 kept_in_chunk=7728 kept_total=7728 rows 0-8379
2025-09-02 17:01:37,235 - INFO - [KD/stage2] gene=RELB 完成，保留细胞 7728/8380（0.29s）
2025-09-02 17:01:37,522 - INFO - [KD/stage2] gene=PTGS2 分片 1/1 kept_in_chunk=7511 kept_total=7511 rows 0-7908
2025-09-02 17:01:37,522 - INFO - [KD/stage2] gene=PTGS2 完成，保留细胞 7511/7909（0.29s）
2025-09-02 17:01:37,847 - INFO - [KD/stage2] gene=MTF1 分片 1/1 kept_in_chunk=10927 kept_total=10927 rows 0-11573
2025-09-02 17:01:37,847 - INFO - [KD/stage2] gene=MTF1 完成，保留细胞 10927/11574（0.33s）
2025-09-02 17:01:38,091 - INFO - [KD/stage2] gene=CHUK 分片 1/1 kept_in_chunk=3489 kept_total=3489 rows 0-3718
2025-09-02 17:01:38,091 - INFO - [KD/stage2] gene=CHUK 完成，保留细胞 3489/3719（0.24s）
2025-09-02 17:01:38,423 - INFO - [KD/stage2] gene=CSF2 分片 1/1 kept_in_chunk=12348 kept_total=12348 rows 0-12361
2025-09-02 17:01:38,423 - INFO - [KD/stage2] gene=CSF2 完成，保留细胞 12348/12362（0.33s）
2025-09-02 17:01:38,635 - INFO - [KD/stage2] gene=CASP10 分片 1/1 kept_in_chunk=845 kept_total=845 rows 0-854
2025-09-02 17:01:38,635 - INFO - [KD/stage2] gene=CASP10 完成，保留细胞 845/855（0.21s）
2025-09-02 17:01:38,932 - INFO - [KD/stage2] gene=FADD 分片 1/1 kept_in_chunk=8262 kept_total=8262 rows 0-8393
2025-09-02 17:01:38,932 - INFO - [KD/stage2] gene=FADD 完成，保留细胞 8262/8394（0.30s）
2025-09-02 17:01:39,206 - INFO - [KD/stage2] gene=BIRC3 分片 1/1 kept_in_chunk=5866 kept_total=5866 rows 0-6511
2025-09-02 17:01:39,206 - INFO - [KD/stage2] gene=BIRC3 完成，保留细胞 5866/6512（0.27s）
2025-09-02 17:01:39,491 - INFO - [KD/stage2] gene=CREB1 分片 1/1 kept_in_chunk=6357 kept_total=6357 rows 0-7268
2025-09-02 17:01:39,491 - INFO - [KD/stage2] gene=CREB1 完成，保留细胞 6357/7269（0.29s）
2025-09-02 17:01:39,776 - INFO - [KD/stage2] gene=IKBKE 分片 1/1 kept_in_chunk=6739 kept_total=6739 rows 0-7041
2025-09-02 17:01:39,776 - INFO - [KD/stage2] gene=IKBKE 完成，保留细胞 6739/7042（0.28s）
2025-09-02 17:01:40,015 - INFO - [KD/stage2] gene=CASP3 分片 1/1 kept_in_chunk=2859 kept_total=2859 rows 0-2921
2025-09-02 17:01:40,015 - INFO - [KD/stage2] gene=CASP3 完成，保留细胞 2859/2922（0.24s）
2025-09-02 17:01:40,272 - INFO - [KD/stage2] gene=CSF1 分片 1/1 kept_in_chunk=4467 kept_total=4467 rows 0-4610
2025-09-02 17:01:40,272 - INFO - [KD/stage2] gene=CSF1 完成，保留细胞 4467/4611（0.26s）
2025-09-02 17:01:40,354 - INFO - [KD] 完成：保留 140,895/386,631 细胞 | 对照 21,097 | 通过基因 16 | 用时 19.85s
2025-09-02 17:01:40,627 - INFO - [FIX] materialize KD result (copy) to avoid sparse view assignment
2025-09-02 17:01:42,359 - INFO - Validating data integrity for: after_filter_count
2025-09-02 17:01:42,556 - INFO - Data validation passed: 140895 cells, 18080 genes
2025-09-02 17:01:42,556 - INFO - [PROCESS] 正在执行 log1p 转换...
2025-09-02 17:01:43,301 - INFO - [PROCESS] log1p 转换完成
2025-09-02 17:01:43,440 - INFO - [process_count_branch] End - Memory: 10.26 GB (Δ: ***** GB)
2025-09-02 17:01:43,440 - INFO - [standardize] Start - Memory: 10.26 GB
2025-09-02 17:01:43,440 - INFO - [standardize_names] Start - Memory: 10.26 GB
2025-09-02 17:01:43,440 - INFO - Standardizing: pert_col=gene, gene_col=gene_name
2025-09-02 17:01:43,440 - INFO - Control label: non-targeting -> non-targeting
2025-09-02 17:01:43,440 - INFO - Perturbation column type: <class 'pandas.core.series.Series'>, dtype: object
2025-09-02 17:01:43,445 - INFO - [STANDARDIZE] Checking for gene column: 'gene_name'
2025-09-02 17:01:43,445 - INFO - [STANDARDIZE] Available columns: ['gene_name', 'gene_id']
2025-09-02 17:01:43,445 - INFO - [STANDARDIZE] Found using method 'direct'
2025-09-02 17:01:43,747 - INFO - [standardize_names] End - Memory: 10.26 GB (Δ: +0.00 GB)
2025-09-02 17:01:43,845 - INFO - [standardize] End - Memory: 10.26 GB (Δ: +0.00 GB)
2025-09-02 17:01:43,845 - INFO - Validating data integrity for: final_validation
2025-09-02 17:01:44,044 - INFO - Data validation passed: 140895 cells, 18080 genes
2025-09-02 17:01:44,050 - INFO - [write_output] Start - Memory: 10.26 GB
2025-09-02 17:01:44,050 - INFO - [WRITE] 开始写入输出文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TNFA_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:01:44,050 - INFO - [WRITE] 最终数据维度: 140895 细胞 × 18080 基因
... storing 'orig.ident' as categorical
... storing 'sample' as categorical
... storing 'cell_type' as categorical
... storing 'pathway' as categorical
... storing 'sample_ID' as categorical
... storing 'Batch_info' as categorical
... storing 'bc1_well' as categorical
... storing 'bc2_well' as categorical
... storing 'bc3_well' as categorical
... storing 'guide' as categorical
... storing 'gene' as categorical
2025-09-02 17:02:06,366 - INFO - Successfully wrote /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TNFA_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:02:06,512 - INFO - [write_output] End - Memory: 10.25 GB (Δ: -0.01 GB)
2025-09-02 17:02:06,512 - INFO - Successfully processed Seurat_object_TNFA_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:02:06,512 - INFO - Output: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TNFA_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:02:06,512 - INFO - Cells kept: 140895
2025-09-02 17:02:06,513 - INFO - Genes kept: 18080
2025-09-02 17:02:06,616 - INFO - ✓ Successfully processed Seurat_object_TNFA_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:02:06,616 - INFO - ============================================================
2025-09-02 17:02:06,616 - INFO - PROCESSING SUMMARY
2025-09-02 17:02:06,616 - INFO - ============================================================
2025-09-02 17:02:06,616 - INFO - Total files: 1
2025-09-02 17:02:06,616 - INFO - Successful: 1
2025-09-02 17:02:06,616 - INFO - Failed: 0
2025-09-02 17:02:06,616 - INFO - ✓ 1 files processed successfully
2025-09-02 17:02:06,616 - INFO - 🎉 All files processed successfully!
