2025-09-02 16:58:12,699 - INFO - Found 1 files to process
2025-09-02 16:58:12,699 - INFO - Processing file 1/1: Seurat_object_INS_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 16:58:12,700 - INFO - Processing Seurat_object_INS_Perturb_seq.RNA.counts.aligned.h5ad: 2.5GB file, estimated memory: 8.9GB, available: 796.8GB
2025-09-02 16:58:12,701 - INFO - [load_data] Start - Memory: 0.24 GB
2025-09-02 16:58:12,701 - INFO - Memory-efficient environment configured
2025-09-02 16:58:12,701 - INFO - Loading 2.5GB file with chunk_size=200000
2025-09-02 16:58:12,701 - INFO - Available memory: 796.8GB
2025-09-02 16:58:15,023 - INFO - Total observations: 431,457
2025-09-02 16:58:15,024 - INFO - Counting perturbations...
2025-09-02 16:58:15,147 - INFO - Keeping 45 perturbations out of 45
2025-09-02 16:58:15,148 - INFO - Collecting cell indices...
2025-09-02 16:58:15,223 - INFO - Keep statistics: 431,457/431,457 cells (100.0%)
2025-09-02 16:58:15,223 - INFO - High keep ratio and sufficient memory, attempting full load
2025-09-02 16:58:15,223 - INFO - Attempting careful full load...
2025-09-02 16:58:15,223 - INFO - Step 1: Loading to memory...
2025-09-02 16:58:46,693 - INFO - Step 2: Converting sparse matrix...
2025-09-02 16:58:46,694 - INFO - Step 3: Creating subset...
2025-09-02 16:58:46,820 - INFO - Creating subset with 431,457 cells using batched approach
2025-09-02 16:59:00,582 - INFO - Created 5 subset chunks
2025-09-02 16:59:14,836 - INFO - Combining 9 chunks...
2025-09-02 16:59:14,837 - INFO - [FIX] Preserving var.columns before concat: ['gene_name', 'gene_id']
2025-09-02 16:59:17,379 - INFO - [FIX] Restoring var.columns after concat: ['gene_name', 'gene_id']
2025-09-02 16:59:17,380 - INFO - [FIX] Successfully restored var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:18,400 - INFO - [load_data] End - Memory: 9.14 GB (Δ: ***** GB)
2025-09-02 16:59:18,400 - INFO - [debug] after load: X=sparse, dtype=float32
2025-09-02 16:59:18,400 - INFO - [DEBUG] After load - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:18,402 - INFO - [DEBUG] After load - var.shape: (18080, 2)
2025-09-02 16:59:21,508 - INFO - [DEBUG] After clean_data_matrix - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:21,509 - INFO - [DEBUG] After clean_data_matrix - var.shape: (18080, 2)
2025-09-02 16:59:21,509 - INFO - Validating data integrity for: after_load
2025-09-02 16:59:22,127 - INFO - Data validation passed: 431457 cells, 18080 genes
2025-09-02 16:59:22,127 - INFO - [DEBUG] Before detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:22,127 - INFO - [DEBUG] Before detect_format - var.shape: (18080, 2)
2025-09-02 16:59:22,128 - INFO - [detect_format] Start - Memory: 9.14 GB
2025-09-02 16:59:22,264 - INFO - [detect_format] End - Memory: 9.14 GB (Δ: +0.00 GB)
2025-09-02 16:59:22,264 - INFO - [detect_format] detected is_log1p=False, frac_1e4_like=0.000
2025-09-02 16:59:22,264 - INFO - [DEBUG] After detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:22,265 - INFO - [DEBUG] After detect_format - var.shape: (18080, 2)
2025-09-02 16:59:22,265 - INFO - [PROCESS] 检测为计数数据，使用标准化+log1p分支处理
2025-09-02 16:59:22,265 - INFO - [process_count_branch] Start - Memory: 9.14 GB
2025-09-02 16:59:22,266 - INFO - Processing count data branch
2025-09-02 16:59:22,266 - INFO - [DEBUG] process_count_branch entry - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:22,266 - INFO - [DEBUG] process_count_branch entry - var.shape: (18080, 2)
2025-09-02 16:59:22,266 - INFO - [DEBUG] Before validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:22,266 - INFO - [DEBUG] Before validation - var.shape: (18080, 2)
2025-09-02 16:59:22,266 - INFO - Validating data integrity for: before_normalization
2025-09-02 16:59:22,881 - INFO - Data validation passed: 431457 cells, 18080 genes
2025-09-02 16:59:22,881 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:22,881 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 16:59:22,881 - INFO - [PROCESS] 跳过归一化步骤，直接使用原始count数据
2025-09-02 16:59:22,882 - INFO - [DEBUG] Skip normalization - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:22,882 - INFO - [DEBUG] Skip normalization - var.shape: (18080, 2)
2025-09-02 16:59:22,882 - INFO - Validating data integrity for: after_normalization
2025-09-02 16:59:23,502 - INFO - Data validation passed: 431457 cells, 18080 genes
2025-09-02 16:59:23,502 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:23,502 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 16:59:23,502 - INFO - [DEBUG] Before KD call - var.columns: ['gene_name', 'gene_id']
2025-09-02 16:59:23,502 - INFO - [DEBUG] Before KD call - var.shape: (18080, 2)
2025-09-02 16:59:23,502 - INFO - start KD
2025-09-02 16:59:23,522 - INFO - [GENE_CHECK] Checking for gene column: 'gene_name'
2025-09-02 16:59:23,522 - INFO - [GENE_CHECK] Available columns: ['gene_name', 'gene_id']
2025-09-02 16:59:23,522 - INFO - [GENE_CHECK] Found using method 'direct'
2025-09-02 16:59:23,735 - INFO - [KD] 总细胞: 431,457 | 对照细胞: 26,001 | 待检查扰动基因: 44
2025-09-02 16:59:23,739 - INFO - [DEBUG] Data characteristics (sample (1000, 1000)):
2025-09-02 16:59:23,739 - INFO - [DEBUG] - Value range: [0.000000, 44.000000]
2025-09-02 16:59:23,742 - INFO - [DEBUG] - Mean: 0.130343, Std: 0.564012
2025-09-02 16:59:23,746 - INFO - [DEBUG] - Sparsity: 85395/1000000 (8.5% non-zero)
2025-09-02 16:59:23,746 - INFO - [DEBUG] - Skip normalization: True
2025-09-02 16:59:23,747 - INFO - [DEBUG] Control group sample ((100, 100)):
2025-09-02 16:59:23,747 - INFO - [DEBUG] - Control range: [0.000000, 12.000000]
2025-09-02 16:59:23,747 - INFO - [DEBUG] - Control mean: 0.131100
2025-09-02 16:59:23,748 - INFO - [KD] Stage 1 - 开始基因级KD判定，共 44 个基因
2025-09-02 16:59:23,995 - INFO - [DEBUG] Gene EIF2B1: found cols.size=1, ctrl_mean=0.213799, threshold=-1e-06
2025-09-02 16:59:23,996 - INFO - [DEBUG] Gene EIF2B1: sample ctrl values - min=0.000000, max=2.000000, mean=0.150000, nonzero=13/100
2025-09-02 16:59:24,357 - INFO - [DEBUG] Gene MTOR: found cols.size=1, ctrl_mean=0.966194, threshold=-1e-06
2025-09-02 16:59:24,359 - INFO - [DEBUG] Gene MTOR: sample ctrl values - min=0.000000, max=4.000000, mean=0.570000, nonzero=37/100
2025-09-02 16:59:24,647 - INFO - [DEBUG] Gene PTEN: found cols.size=1, ctrl_mean=1.208838, threshold=-1e-06
2025-09-02 16:59:24,648 - INFO - [DEBUG] Gene PTEN: sample ctrl values - min=0.000000, max=4.000000, mean=0.960000, nonzero=57/100
2025-09-02 16:59:25,072 - INFO - [DEBUG] Gene FOXO3: found cols.size=1, ctrl_mean=1.514211, threshold=-1e-06
2025-09-02 16:59:25,073 - INFO - [DEBUG] Gene FOXO3: sample ctrl values - min=0.000000, max=4.000000, mean=0.850000, nonzero=45/100
2025-09-02 16:59:25,419 - INFO - [DEBUG] Gene SREBF1: found cols.size=1, ctrl_mean=0.358640, threshold=-1e-06
2025-09-02 16:59:25,421 - INFO - [DEBUG] Gene SREBF1: sample ctrl values - min=0.000000, max=1.000000, mean=0.110000, nonzero=11/100
2025-09-02 16:59:25,885 - INFO - [DEBUG/stage1] 7/44 gene=IGF2 → REJECTED: cols.size=0, ctrl_mean=0.000000 <= -1e-06
2025-09-02 16:59:26,856 - INFO - [KD/stage1] 10/44 gene=SHC1 cols=1 ctrl_mean=0.2588 pert_mean=0.1544 ratio=0.597 keep=False (0.31s)
2025-09-02 16:59:30,052 - INFO - [KD/stage1] 20/44 gene=RPS6KB1 → 跳过（无有效组件或ctrl_mean<=-1e-06）
2025-09-02 16:59:33,580 - INFO - [KD/stage1] 30/44 gene=TTF1 cols=1 ctrl_mean=0.287 pert_mean=0.289 ratio=1.007 keep=False (0.31s)
2025-09-02 16:59:36,816 - INFO - [KD/stage1] 40/44 gene=SRF cols=1 ctrl_mean=0.2164 pert_mean=0.1189 ratio=0.549 keep=False (0.30s)
2025-09-02 16:59:37,932 - INFO - [KD/stage1] 44/44 gene=RAD51 cols=1 ctrl_mean=0.2361 pert_mean=0.2529 ratio=1.071 keep=False (0.26s)
2025-09-02 16:59:38,379 - INFO - [KD/stage2] gene=PTEN 分片 1/1 kept_in_chunk=13682 kept_total=13682 rows 0-16549
2025-09-02 16:59:38,379 - INFO - [KD/stage2] gene=PTEN 完成，保留细胞 13682/16550（0.43s）
2025-09-02 16:59:38,692 - INFO - [KD/stage2] gene=MAPK1 分片 1/1 kept_in_chunk=5063 kept_total=5063 rows 0-6206
2025-09-02 16:59:38,692 - INFO - [KD/stage2] gene=MAPK1 完成，保留细胞 5063/6207（0.31s）
2025-09-02 16:59:39,115 - INFO - [KD/stage2] gene=GRB10 分片 1/1 kept_in_chunk=13917 kept_total=13917 rows 0-16496
2025-09-02 16:59:39,115 - INFO - [KD/stage2] gene=GRB10 完成，保留细胞 13917/16497（0.42s）
2025-09-02 16:59:39,438 - INFO - [KD/stage2] gene=CHUK 分片 1/1 kept_in_chunk=6054 kept_total=6054 rows 0-6488
2025-09-02 16:59:39,438 - INFO - [KD/stage2] gene=CHUK 完成，保留细胞 6054/6489（0.32s）
2025-09-02 16:59:39,463 - INFO - [KD] 完成：保留 64,717/431,457 细胞 | 对照 26,001 | 通过基因 4 | 用时 15.71s
2025-09-02 16:59:39,838 - INFO - [FIX] materialize KD result (copy) to avoid sparse view assignment
2025-09-02 16:59:40,595 - INFO - Validating data integrity for: after_filter_count
2025-09-02 16:59:40,685 - INFO - Data validation passed: 64717 cells, 18080 genes
2025-09-02 16:59:40,685 - INFO - [PROCESS] 正在执行 log1p 转换...
2025-09-02 16:59:41,032 - INFO - [PROCESS] log1p 转换完成
2025-09-02 16:59:41,124 - INFO - [process_count_branch] End - Memory: 10.39 GB (Δ: ***** GB)
2025-09-02 16:59:41,124 - INFO - [standardize] Start - Memory: 10.39 GB
2025-09-02 16:59:41,124 - INFO - [standardize_names] Start - Memory: 10.39 GB
2025-09-02 16:59:41,124 - INFO - Standardizing: pert_col=gene, gene_col=gene_name
2025-09-02 16:59:41,124 - INFO - Control label: non-targeting -> non-targeting
2025-09-02 16:59:41,124 - INFO - Perturbation column type: <class 'pandas.core.series.Series'>, dtype: object
2025-09-02 16:59:41,127 - INFO - [STANDARDIZE] Checking for gene column: 'gene_name'
2025-09-02 16:59:41,127 - INFO - [STANDARDIZE] Available columns: ['gene_name', 'gene_id']
2025-09-02 16:59:41,127 - INFO - [STANDARDIZE] Found using method 'direct'
2025-09-02 16:59:41,295 - INFO - [standardize_names] End - Memory: 10.39 GB (Δ: +0.00 GB)
2025-09-02 16:59:41,373 - INFO - [standardize] End - Memory: 10.39 GB (Δ: +0.00 GB)
2025-09-02 16:59:41,373 - INFO - Validating data integrity for: final_validation
2025-09-02 16:59:41,463 - INFO - Data validation passed: 64717 cells, 18080 genes
2025-09-02 16:59:41,466 - INFO - [write_output] Start - Memory: 10.39 GB
2025-09-02 16:59:41,466 - INFO - [WRITE] 开始写入输出文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_INS_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 16:59:41,466 - INFO - [WRITE] 最终数据维度: 64717 细胞 × 18080 基因
... storing 'orig.ident' as categorical
... storing 'sample' as categorical
... storing 'bc1_well' as categorical
... storing 'bc2_well' as categorical
... storing 'bc3_well' as categorical
... storing 'cell_type' as categorical
... storing 'pathway' as categorical
... storing 'RNA_snn_res.0.9' as categorical
... storing 'seurat_clusters' as categorical
... storing 'sample_ID' as categorical
... storing 'Batch_info' as categorical
... storing 'guide' as categorical
... storing 'gene' as categorical
2025-09-02 16:59:52,251 - INFO - Successfully wrote /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_INS_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 16:59:52,343 - INFO - [write_output] End - Memory: 10.39 GB (Δ: -0.00 GB)
2025-09-02 16:59:52,343 - INFO - Successfully processed Seurat_object_INS_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 16:59:52,343 - INFO - Output: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_INS_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 16:59:52,343 - INFO - Cells kept: 64717
2025-09-02 16:59:52,343 - INFO - Genes kept: 18080
2025-09-02 16:59:52,423 - INFO - ✓ Successfully processed Seurat_object_INS_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 16:59:52,423 - INFO - ============================================================
2025-09-02 16:59:52,423 - INFO - PROCESSING SUMMARY
2025-09-02 16:59:52,423 - INFO - ============================================================
2025-09-02 16:59:52,423 - INFO - Total files: 1
2025-09-02 16:59:52,423 - INFO - Successful: 1
2025-09-02 16:59:52,423 - INFO - Failed: 0
2025-09-02 16:59:52,423 - INFO - ✓ 1 files processed successfully
2025-09-02 16:59:52,423 - INFO - 🎉 All files processed successfully!
