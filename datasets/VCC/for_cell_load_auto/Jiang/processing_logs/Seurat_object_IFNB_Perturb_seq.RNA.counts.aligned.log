2025-09-02 17:02:18,297 - INFO - Found 1 files to process
2025-09-02 17:02:18,297 - INFO - Processing file 1/1: Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:02:18,298 - INFO - Processing Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.h5ad: 2.0GB file, estimated memory: 8.0GB, available: 796.8GB
2025-09-02 17:02:18,299 - INFO - [load_data] Start - Memory: 0.24 GB
2025-09-02 17:02:18,299 - INFO - Memory-efficient environment configured
2025-09-02 17:02:18,299 - INFO - Loading 2.0GB file with chunk_size=200000
2025-09-02 17:02:18,299 - INFO - Available memory: 796.8GB
2025-09-02 17:02:19,971 - INFO - Total observations: 328,542
2025-09-02 17:02:19,971 - INFO - Counting perturbations...
2025-09-02 17:02:20,022 - INFO - Keeping 62 perturbations out of 62
2025-09-02 17:02:20,022 - INFO - Collecting cell indices...
2025-09-02 17:02:20,077 - INFO - Keep statistics: 328,542/328,542 cells (100.0%)
2025-09-02 17:02:20,077 - INFO - High keep ratio and sufficient memory, attempting full load
2025-09-02 17:02:20,077 - INFO - Attempting careful full load...
2025-09-02 17:02:20,077 - INFO - Step 1: Loading to memory...
2025-09-02 17:02:45,096 - INFO - Step 2: Converting sparse matrix...
2025-09-02 17:02:45,099 - INFO - Step 3: Creating subset...
2025-09-02 17:02:45,406 - INFO - Creating subset with 328,542 cells using batched approach
2025-09-02 17:02:49,707 - INFO - Created 5 subset chunks
2025-09-02 17:02:51,110 - INFO - Combining 7 chunks...
2025-09-02 17:02:51,110 - INFO - [FIX] Preserving var.columns before concat: ['gene_name', 'gene_id']
2025-09-02 17:02:53,175 - INFO - [FIX] Restoring var.columns after concat: ['gene_name', 'gene_id']
2025-09-02 17:02:53,176 - INFO - [FIX] Successfully restored var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:53,751 - INFO - [load_data] End - Memory: 7.08 GB (Δ: ***** GB)
2025-09-02 17:02:53,752 - INFO - [debug] after load: X=sparse, dtype=float32
2025-09-02 17:02:53,752 - INFO - [DEBUG] After load - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:53,752 - INFO - [DEBUG] After load - var.shape: (18080, 2)
2025-09-02 17:02:56,222 - INFO - [DEBUG] After clean_data_matrix - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:56,222 - INFO - [DEBUG] After clean_data_matrix - var.shape: (18080, 2)
2025-09-02 17:02:56,222 - INFO - Validating data integrity for: after_load
2025-09-02 17:02:56,722 - INFO - Data validation passed: 328542 cells, 18080 genes
2025-09-02 17:02:56,722 - INFO - [DEBUG] Before detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:56,722 - INFO - [DEBUG] Before detect_format - var.shape: (18080, 2)
2025-09-02 17:02:56,722 - INFO - [detect_format] Start - Memory: 7.08 GB
2025-09-02 17:02:56,815 - INFO - [detect_format] End - Memory: 7.08 GB (Δ: +0.00 GB)
2025-09-02 17:02:56,815 - INFO - [detect_format] detected is_log1p=False, frac_1e4_like=0.000
2025-09-02 17:02:56,815 - INFO - [DEBUG] After detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:56,815 - INFO - [DEBUG] After detect_format - var.shape: (18080, 2)
2025-09-02 17:02:56,815 - INFO - [PROCESS] 检测为计数数据，使用标准化+log1p分支处理
2025-09-02 17:02:56,815 - INFO - [process_count_branch] Start - Memory: 7.08 GB
2025-09-02 17:02:56,815 - INFO - Processing count data branch
2025-09-02 17:02:56,815 - INFO - [DEBUG] process_count_branch entry - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:56,815 - INFO - [DEBUG] process_count_branch entry - var.shape: (18080, 2)
2025-09-02 17:02:56,815 - INFO - [DEBUG] Before validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:56,816 - INFO - [DEBUG] Before validation - var.shape: (18080, 2)
2025-09-02 17:02:56,816 - INFO - Validating data integrity for: before_normalization
2025-09-02 17:02:57,313 - INFO - Data validation passed: 328542 cells, 18080 genes
2025-09-02 17:02:57,313 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:57,313 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 17:02:57,313 - INFO - [PROCESS] 跳过归一化步骤，直接使用原始count数据
2025-09-02 17:02:57,313 - INFO - [DEBUG] Skip normalization - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:57,313 - INFO - [DEBUG] Skip normalization - var.shape: (18080, 2)
2025-09-02 17:02:57,313 - INFO - Validating data integrity for: after_normalization
2025-09-02 17:02:57,806 - INFO - Data validation passed: 328542 cells, 18080 genes
2025-09-02 17:02:57,806 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:57,806 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 17:02:57,806 - INFO - [DEBUG] Before KD call - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:02:57,806 - INFO - [DEBUG] Before KD call - var.shape: (18080, 2)
2025-09-02 17:02:57,806 - INFO - start KD
2025-09-02 17:02:57,819 - INFO - [GENE_CHECK] Checking for gene column: 'gene_name'
2025-09-02 17:02:57,819 - INFO - [GENE_CHECK] Available columns: ['gene_name', 'gene_id']
2025-09-02 17:02:57,819 - INFO - [GENE_CHECK] Found using method 'direct'
2025-09-02 17:02:58,031 - INFO - [KD] 总细胞: 328,542 | 对照细胞: 14,582 | 待检查扰动基因: 61
2025-09-02 17:02:58,035 - INFO - [DEBUG] Data characteristics (sample (1000, 1000)):
2025-09-02 17:02:58,036 - INFO - [DEBUG] - Value range: [0.000000, 38.000000]
2025-09-02 17:02:58,039 - INFO - [DEBUG] - Mean: 0.150437, Std: 0.626666
2025-09-02 17:02:58,043 - INFO - [DEBUG] - Sparsity: 95506/1000000 (9.6% non-zero)
2025-09-02 17:02:58,043 - INFO - [DEBUG] - Skip normalization: True
2025-09-02 17:02:58,044 - INFO - [DEBUG] Control group sample ((100, 100)):
2025-09-02 17:02:58,044 - INFO - [DEBUG] - Control range: [0.000000, 12.000000]
2025-09-02 17:02:58,044 - INFO - [DEBUG] - Control mean: 0.143600
2025-09-02 17:02:58,044 - INFO - [KD] Stage 1 - 开始基因级KD判定，共 61 个基因
2025-09-02 17:02:58,189 - INFO - [DEBUG] Gene TRAFD1: found cols.size=1, ctrl_mean=0.310383, threshold=-1e-06
2025-09-02 17:02:58,190 - INFO - [DEBUG] Gene TRAFD1: sample ctrl values - min=0.000000, max=3.000000, mean=0.180000, nonzero=14/100
2025-09-02 17:02:58,399 - INFO - [DEBUG] Gene HES4: found cols.size=1, ctrl_mean=0.056508, threshold=-1e-06
2025-09-02 17:02:58,400 - INFO - [DEBUG] Gene HES4: sample ctrl values - min=0.000000, max=1.000000, mean=0.030000, nonzero=3/100
2025-09-02 17:02:58,594 - INFO - [DEBUG] Gene STAT5A: found cols.size=1, ctrl_mean=0.084693, threshold=-1e-06
2025-09-02 17:02:58,595 - INFO - [DEBUG] Gene STAT5A: sample ctrl values - min=0.000000, max=1.000000, mean=0.030000, nonzero=3/100
2025-09-02 17:02:58,834 - INFO - [DEBUG] Gene STAT4: found cols.size=1, ctrl_mean=0.029626, threshold=-1e-06
2025-09-02 17:02:58,835 - INFO - [DEBUG] Gene STAT4: sample ctrl values - min=0.000000, max=2.000000, mean=0.070000, nonzero=6/100
2025-09-02 17:02:59,043 - INFO - [DEBUG] Gene RNF114: found cols.size=1, ctrl_mean=0.290289, threshold=-1e-06
2025-09-02 17:02:59,044 - INFO - [DEBUG] Gene RNF114: sample ctrl values - min=0.000000, max=2.000000, mean=0.070000, nonzero=6/100
2025-09-02 17:03:00,127 - INFO - [KD/stage1] 10/61 gene=FOS cols=1 ctrl_mean=0.35 pert_mean=0.227 ratio=0.649 keep=False (0.19s)
2025-09-02 17:03:02,160 - INFO - [KD/stage1] 20/61 gene=VAV1 cols=1 ctrl_mean=0.01996 pert_mean=0.008426 ratio=0.422 keep=False (0.23s)
2025-09-02 17:03:04,229 - INFO - [KD/stage1] 30/61 gene=SOCS1 cols=1 ctrl_mean=0.008709 pert_mean=0.005102 ratio=0.586 keep=False (0.21s)
2025-09-02 17:03:06,300 - INFO - [KD/stage1] 40/61 gene=ID2 cols=1 ctrl_mean=0.06357 pert_mean=0.02333 ratio=0.367 keep=False (0.21s)
2025-09-02 17:03:08,143 - INFO - [KD/stage1] 50/61 gene=RAPGEF1 cols=1 ctrl_mean=0.9713 pert_mean=0.9174 ratio=0.945 keep=False (0.17s)
2025-09-02 17:03:10,040 - INFO - [KD/stage1] 60/61 gene=UBA7 cols=1 ctrl_mean=0.1194 pert_mean=0.02099 ratio=0.176 keep=True (0.18s)
2025-09-02 17:03:10,198 - INFO - [KD/stage1] 61/61 gene=POU2F1 cols=1 ctrl_mean=1.377 pert_mean=0.545 ratio=0.396 keep=False (0.16s)
2025-09-02 17:03:10,402 - INFO - [KD/stage2] gene=HES4 分片 1/1 kept_in_chunk=4495 kept_total=4495 rows 0-4561
2025-09-02 17:03:10,402 - INFO - [KD/stage2] gene=HES4 完成，保留细胞 4495/4562（0.20s）
2025-09-02 17:03:10,641 - INFO - [KD/stage2] gene=STAT5A 分片 1/1 kept_in_chunk=8813 kept_total=8813 rows 0-8980
2025-09-02 17:03:10,641 - INFO - [KD/stage2] gene=STAT5A 完成，保留细胞 8813/8981（0.24s）
2025-09-02 17:03:10,833 - INFO - [KD/stage2] gene=UBE2L6 分片 1/1 kept_in_chunk=4366 kept_total=4366 rows 0-4634
2025-09-02 17:03:10,833 - INFO - [KD/stage2] gene=UBE2L6 完成，保留细胞 4366/4635（0.19s）
2025-09-02 17:03:11,044 - INFO - [KD/stage2] gene=PARP12 分片 1/1 kept_in_chunk=5094 kept_total=5094 rows 0-6425
2025-09-02 17:03:11,044 - INFO - [KD/stage2] gene=PARP12 完成，保留细胞 5094/6426（0.21s）
2025-09-02 17:03:11,234 - INFO - [KD/stage2] gene=TRIM22 分片 1/1 kept_in_chunk=4152 kept_total=4152 rows 0-4560
2025-09-02 17:03:11,234 - INFO - [KD/stage2] gene=TRIM22 完成，保留细胞 4152/4561（0.19s）
2025-09-02 17:03:11,434 - INFO - [KD/stage2] gene=BATF2 分片 1/1 kept_in_chunk=5247 kept_total=5247 rows 0-5342
2025-09-02 17:03:11,434 - INFO - [KD/stage2] gene=BATF2 完成，保留细胞 5247/5343（0.20s）
2025-09-02 17:03:11,622 - INFO - [KD/stage2] gene=STAT2 分片 1/1 kept_in_chunk=3608 kept_total=3608 rows 0-4290
2025-09-02 17:03:11,622 - INFO - [KD/stage2] gene=STAT2 完成，保留细胞 3608/4291（0.19s）
2025-09-02 17:03:11,828 - INFO - [KD/stage2] gene=HERC6 分片 1/1 kept_in_chunk=4707 kept_total=4707 rows 0-5763
2025-09-02 17:03:11,828 - INFO - [KD/stage2] gene=HERC6 完成，保留细胞 4707/5764（0.21s）
2025-09-02 17:03:12,037 - INFO - [KD/stage2] gene=TYK2 分片 1/1 kept_in_chunk=6394 kept_total=6394 rows 0-6621
2025-09-02 17:03:12,037 - INFO - [KD/stage2] gene=TYK2 完成，保留细胞 6394/6622（0.21s）
2025-09-02 17:03:12,208 - INFO - [KD/stage2] gene=STAT6 分片 1/1 kept_in_chunk=2521 kept_total=2521 rows 0-2695
2025-09-02 17:03:12,208 - INFO - [KD/stage2] gene=STAT6 完成，保留细胞 2521/2696（0.17s）
2025-09-02 17:03:12,393 - INFO - [KD/stage2] gene=ZNFX1 分片 1/1 kept_in_chunk=3458 kept_total=3458 rows 0-3846
2025-09-02 17:03:12,393 - INFO - [KD/stage2] gene=ZNFX1 完成，保留细胞 3458/3847（0.19s）
2025-09-02 17:03:12,605 - INFO - [KD/stage2] gene=ZBP1 分片 1/1 kept_in_chunk=6206 kept_total=6206 rows 0-6339
2025-09-02 17:03:12,605 - INFO - [KD/stage2] gene=ZBP1 完成，保留细胞 6206/6340（0.21s）
2025-09-02 17:03:12,767 - INFO - [KD/stage2] gene=IFI16 分片 1/1 kept_in_chunk=1144 kept_total=1144 rows 0-1271
2025-09-02 17:03:12,767 - INFO - [KD/stage2] gene=IFI16 完成，保留细胞 1144/1272（0.16s）
2025-09-02 17:03:12,949 - INFO - [KD/stage2] gene=SP110 分片 1/1 kept_in_chunk=2982 kept_total=2982 rows 0-3628
2025-09-02 17:03:12,950 - INFO - [KD/stage2] gene=SP110 完成，保留细胞 2982/3629（0.18s）
2025-09-02 17:03:13,160 - INFO - [KD/stage2] gene=CEBPG 分片 1/1 kept_in_chunk=6024 kept_total=6024 rows 0-6387
2025-09-02 17:03:13,160 - INFO - [KD/stage2] gene=CEBPG 完成，保留细胞 6024/6388（0.21s）
2025-09-02 17:03:13,326 - INFO - [KD/stage2] gene=TRIM21 分片 1/1 kept_in_chunk=1946 kept_total=1946 rows 0-1995
2025-09-02 17:03:13,327 - INFO - [KD/stage2] gene=TRIM21 完成，保留细胞 1946/1996（0.17s）
2025-09-02 17:03:13,493 - INFO - [KD/stage2] gene=ID1 分片 1/1 kept_in_chunk=1338 kept_total=1338 rows 0-1352
2025-09-02 17:03:13,494 - INFO - [KD/stage2] gene=ID1 完成，保留细胞 1338/1353（0.17s）
2025-09-02 17:03:13,680 - INFO - [KD/stage2] gene=UBA7 分片 1/1 kept_in_chunk=3701 kept_total=3701 rows 0-3763
2025-09-02 17:03:13,680 - INFO - [KD/stage2] gene=UBA7 完成，保留细胞 3701/3764（0.19s）
2025-09-02 17:03:13,752 - INFO - [KD] 完成：保留 90,778/328,542 细胞 | 对照 14,582 | 通过基因 18 | 用时 15.71s
2025-09-02 17:03:13,877 - INFO - [FIX] materialize KD result (copy) to avoid sparse view assignment
2025-09-02 17:03:14,943 - INFO - Validating data integrity for: after_filter_count
2025-09-02 17:03:15,070 - INFO - Data validation passed: 90778 cells, 18080 genes
2025-09-02 17:03:15,070 - INFO - [PROCESS] 正在执行 log1p 转换...
2025-09-02 17:03:15,570 - INFO - [PROCESS] log1p 转换完成
2025-09-02 17:03:15,667 - INFO - [process_count_branch] End - Memory: 8.85 GB (Δ: ***** GB)
2025-09-02 17:03:15,667 - INFO - [standardize] Start - Memory: 8.85 GB
2025-09-02 17:03:15,667 - INFO - [standardize_names] Start - Memory: 8.85 GB
2025-09-02 17:03:15,668 - INFO - Standardizing: pert_col=gene, gene_col=gene_name
2025-09-02 17:03:15,668 - INFO - Control label: non-targeting -> non-targeting
2025-09-02 17:03:15,668 - INFO - Perturbation column type: <class 'pandas.core.series.Series'>, dtype: object
2025-09-02 17:03:15,670 - INFO - [STANDARDIZE] Checking for gene column: 'gene_name'
2025-09-02 17:03:15,670 - INFO - [STANDARDIZE] Available columns: ['gene_name', 'gene_id']
2025-09-02 17:03:15,671 - INFO - [STANDARDIZE] Found using method 'direct'
2025-09-02 17:03:15,882 - INFO - [standardize_names] End - Memory: 8.85 GB (Δ: +0.00 GB)
2025-09-02 17:03:15,961 - INFO - [standardize] End - Memory: 8.85 GB (Δ: +0.00 GB)
2025-09-02 17:03:15,961 - INFO - Validating data integrity for: final_validation
2025-09-02 17:03:16,089 - INFO - Data validation passed: 90778 cells, 18080 genes
2025-09-02 17:03:16,093 - INFO - [write_output] Start - Memory: 8.85 GB
2025-09-02 17:03:16,093 - INFO - [WRITE] 开始写入输出文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:03:16,093 - INFO - [WRITE] 最终数据维度: 90778 细胞 × 18080 基因
... storing 'orig.ident' as categorical
... storing 'sample' as categorical
... storing 'bc1_well' as categorical
... storing 'bc2_well' as categorical
... storing 'bc3_well' as categorical
... storing 'cell_type' as categorical
... storing 'pathway' as categorical
... storing 'RNA_snn_res.0.9' as categorical
... storing 'seurat_clusters' as categorical
... storing 'sample_ID' as categorical
... storing 'Batch_info' as categorical
... storing 'guide' as categorical
... storing 'gene' as categorical
2025-09-02 17:03:35,583 - INFO - Successfully wrote /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:03:35,668 - INFO - [write_output] End - Memory: 8.84 GB (Δ: -0.01 GB)
2025-09-02 17:03:35,668 - INFO - Successfully processed Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:03:35,668 - INFO - Output: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:03:35,668 - INFO - Cells kept: 90778
2025-09-02 17:03:35,668 - INFO - Genes kept: 18080
2025-09-02 17:03:35,751 - INFO - ✓ Successfully processed Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:03:35,751 - INFO - ============================================================
2025-09-02 17:03:35,751 - INFO - PROCESSING SUMMARY
2025-09-02 17:03:35,751 - INFO - ============================================================
2025-09-02 17:03:35,751 - INFO - Total files: 1
2025-09-02 17:03:35,751 - INFO - Successful: 1
2025-09-02 17:03:35,751 - INFO - Failed: 0
2025-09-02 17:03:35,751 - INFO - ✓ 1 files processed successfully
2025-09-02 17:03:35,751 - INFO - 🎉 All files processed successfully!
