2025-09-02 17:04:53,830 - INFO - Found 1 files to process
2025-09-02 17:04:53,830 - INFO - Processing file 1/1: Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:04:53,831 - INFO - Processing Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad: 1.3GB file, estimated memory: 8.0GB, available: 795.7GB
2025-09-02 17:04:53,831 - INFO - [load_data] Start - Memory: 0.24 GB
2025-09-02 17:04:53,831 - INFO - Memory-efficient environment configured
2025-09-02 17:04:53,832 - INFO - Loading 1.3GB file with chunk_size=200000
2025-09-02 17:04:53,832 - INFO - Available memory: 795.7GB
2025-09-02 17:04:54,868 - INFO - Total observations: 236,606
2025-09-02 17:04:54,868 - INFO - Counting perturbations...
2025-09-02 17:04:54,915 - INFO - Keeping 53 perturbations out of 53
2025-09-02 17:04:54,915 - INFO - Collecting cell indices...
2025-09-02 17:04:54,956 - INFO - Keep statistics: 236,606/236,606 cells (100.0%)
2025-09-02 17:04:54,956 - INFO - High keep ratio and sufficient memory, attempting full load
2025-09-02 17:04:54,956 - INFO - Attempting careful full load...
2025-09-02 17:04:54,956 - INFO - Step 1: Loading to memory...
2025-09-02 17:05:09,603 - INFO - Step 2: Converting sparse matrix...
2025-09-02 17:05:09,603 - INFO - Step 3: Creating subset...
2025-09-02 17:05:09,661 - INFO - Creating subset with 236,606 cells using batched approach
2025-09-02 17:05:15,237 - INFO - Created 5 subset chunks
2025-09-02 17:05:15,313 - INFO - Combining 5 chunks...
2025-09-02 17:05:15,313 - INFO - [FIX] Preserving var.columns before concat: ['gene_name', 'gene_id']
2025-09-02 17:05:16,532 - INFO - [FIX] Restoring var.columns after concat: ['gene_name', 'gene_id']
2025-09-02 17:05:16,533 - INFO - [FIX] Successfully restored var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:17,054 - INFO - [load_data] End - Memory: 4.49 GB (Δ: ***** GB)
2025-09-02 17:05:17,054 - INFO - [debug] after load: X=sparse, dtype=float32
2025-09-02 17:05:17,054 - INFO - [DEBUG] After load - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:17,054 - INFO - [DEBUG] After load - var.shape: (18080, 2)
2025-09-02 17:05:18,521 - INFO - [DEBUG] After clean_data_matrix - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:18,521 - INFO - [DEBUG] After clean_data_matrix - var.shape: (18080, 2)
2025-09-02 17:05:18,521 - INFO - Validating data integrity for: after_load
2025-09-02 17:05:18,820 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-09-02 17:05:18,820 - INFO - [DEBUG] Before detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:18,820 - INFO - [DEBUG] Before detect_format - var.shape: (18080, 2)
2025-09-02 17:05:18,820 - INFO - [detect_format] Start - Memory: 4.49 GB
2025-09-02 17:05:18,936 - INFO - [detect_format] End - Memory: 4.49 GB (Δ: +0.00 GB)
2025-09-02 17:05:18,936 - INFO - [detect_format] detected is_log1p=False, frac_1e4_like=0.000
2025-09-02 17:05:18,936 - INFO - [DEBUG] After detect_format - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:18,936 - INFO - [DEBUG] After detect_format - var.shape: (18080, 2)
2025-09-02 17:05:18,936 - INFO - [PROCESS] 检测为计数数据，使用标准化+log1p分支处理
2025-09-02 17:05:18,936 - INFO - [process_count_branch] Start - Memory: 4.49 GB
2025-09-02 17:05:18,936 - INFO - Processing count data branch
2025-09-02 17:05:18,936 - INFO - [DEBUG] process_count_branch entry - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:18,936 - INFO - [DEBUG] process_count_branch entry - var.shape: (18080, 2)
2025-09-02 17:05:18,936 - INFO - [DEBUG] Before validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:18,937 - INFO - [DEBUG] Before validation - var.shape: (18080, 2)
2025-09-02 17:05:18,937 - INFO - Validating data integrity for: before_normalization
2025-09-02 17:05:19,238 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-09-02 17:05:19,238 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:19,238 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 17:05:19,238 - INFO - [PROCESS] 跳过归一化步骤，直接使用原始count数据
2025-09-02 17:05:19,238 - INFO - [DEBUG] Skip normalization - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:19,239 - INFO - [DEBUG] Skip normalization - var.shape: (18080, 2)
2025-09-02 17:05:19,239 - INFO - Validating data integrity for: after_normalization
2025-09-02 17:05:19,545 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-09-02 17:05:19,545 - INFO - [DEBUG] After validation - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:19,545 - INFO - [DEBUG] After validation - var.shape: (18080, 2)
2025-09-02 17:05:19,545 - INFO - [DEBUG] Before KD call - var.columns: ['gene_name', 'gene_id']
2025-09-02 17:05:19,545 - INFO - [DEBUG] Before KD call - var.shape: (18080, 2)
2025-09-02 17:05:19,545 - INFO - start KD
2025-09-02 17:05:19,555 - INFO - [GENE_CHECK] Checking for gene column: 'gene_name'
2025-09-02 17:05:19,555 - INFO - [GENE_CHECK] Available columns: ['gene_name', 'gene_id']
2025-09-02 17:05:19,555 - INFO - [GENE_CHECK] Found using method 'direct'
2025-09-02 17:05:19,757 - INFO - [KD] 总细胞: 236,606 | 对照细胞: 9,809 | 待检查扰动基因: 52
2025-09-02 17:05:19,762 - INFO - [DEBUG] Data characteristics (sample (1000, 1000)):
2025-09-02 17:05:19,763 - INFO - [DEBUG] - Value range: [0.000000, 20.000000]
2025-09-02 17:05:19,765 - INFO - [DEBUG] - Mean: 0.128048, Std: 0.497622
2025-09-02 17:05:19,769 - INFO - [DEBUG] - Sparsity: 91516/1000000 (9.2% non-zero)
2025-09-02 17:05:19,769 - INFO - [DEBUG] - Skip normalization: True
2025-09-02 17:05:19,770 - INFO - [DEBUG] Control group sample ((100, 100)):
2025-09-02 17:05:19,770 - INFO - [DEBUG] - Control range: [0.000000, 6.000000]
2025-09-02 17:05:19,770 - INFO - [DEBUG] - Control mean: 0.058600
2025-09-02 17:05:19,770 - INFO - [KD] Stage 1 - 开始基因级KD判定，共 52 个基因
2025-09-02 17:05:19,856 - INFO - [DEBUG] Gene RUNX2: found cols.size=1, ctrl_mean=0.718014, threshold=-1e-06
2025-09-02 17:05:19,857 - INFO - [DEBUG] Gene RUNX2: sample ctrl values - min=0.000000, max=6.000000, mean=0.840000, nonzero=43/100
2025-09-02 17:05:19,887 - INFO - [DEBUG] Gene RPS6KB1: found cols.size=0, ctrl_mean=0.000000, threshold=-1e-06
2025-09-02 17:05:19,887 - INFO - [DEBUG/stage1] 2/52 gene=RPS6KB1 → REJECTED: cols.size=0, ctrl_mean=0.000000 <= -1e-06
2025-09-02 17:05:19,972 - INFO - [DEBUG] Gene PPP2CA: found cols.size=1, ctrl_mean=0.487613, threshold=-1e-06
2025-09-02 17:05:19,973 - INFO - [DEBUG] Gene PPP2CA: sample ctrl values - min=0.000000, max=2.000000, mean=0.210000, nonzero=18/100
2025-09-02 17:05:20,083 - INFO - [DEBUG] Gene RUNX1: found cols.size=1, ctrl_mean=2.148537, threshold=-1e-06
2025-09-02 17:05:20,084 - INFO - [DEBUG] Gene RUNX1: sample ctrl values - min=0.000000, max=8.000000, mean=1.690000, nonzero=74/100
2025-09-02 17:05:20,269 - INFO - [DEBUG] Gene EP300: found cols.size=1, ctrl_mean=0.892955, threshold=-1e-06
2025-09-02 17:05:20,270 - INFO - [DEBUG] Gene EP300: sample ctrl values - min=0.000000, max=3.000000, mean=0.350000, nonzero=29/100
2025-09-02 17:05:20,922 - INFO - [KD/stage1] 10/52 gene=PIK3CA cols=1 ctrl_mean=0.3678 pert_mean=0.2678 ratio=0.728 keep=False (0.11s)
2025-09-02 17:05:22,283 - INFO - [KD/stage1] 20/52 gene=RAF1 cols=1 ctrl_mean=0.5486 pert_mean=0.516 ratio=0.941 keep=False (0.11s)
2025-09-02 17:05:23,557 - INFO - [KD/stage1] 30/52 gene=MAPK14 cols=1 ctrl_mean=0.7847 pert_mean=0.3144 ratio=0.401 keep=False (0.13s)
2025-09-02 17:05:24,802 - INFO - [KD/stage1] 40/52 gene=SMAD5 cols=1 ctrl_mean=0.4996 pert_mean=0.5192 ratio=1.039 keep=False (0.10s)
2025-09-02 17:05:25,908 - INFO - [KD/stage1] 50/52 gene=HDAC4 cols=1 ctrl_mean=0.8019 pert_mean=0.2223 ratio=0.277 keep=True (0.10s)
2025-09-02 17:05:26,166 - INFO - [KD/stage1] 52/52 gene=TGFBR1 cols=1 ctrl_mean=0.4075 pert_mean=0.07975 ratio=0.196 keep=True (0.14s)
2025-09-02 17:05:26,284 - INFO - [KD/stage2] gene=MAPK1 分片 1/1 kept_in_chunk=2205 kept_total=2205 rows 0-2660
2025-09-02 17:05:26,284 - INFO - [KD/stage2] gene=MAPK1 完成，保留细胞 2205/2661（0.11s）
2025-09-02 17:05:26,426 - INFO - [KD/stage2] gene=KRAS 分片 1/1 kept_in_chunk=5446 kept_total=5446 rows 0-5799
2025-09-02 17:05:26,426 - INFO - [KD/stage2] gene=KRAS 完成，保留细胞 5446/5800（0.14s）
2025-09-02 17:05:26,626 - INFO - [KD/stage2] gene=SMURF1 分片 1/1 kept_in_chunk=9407 kept_total=9407 rows 0-12093
2025-09-02 17:05:26,626 - INFO - [KD/stage2] gene=SMURF1 完成，保留细胞 9407/12094（0.20s）
2025-09-02 17:05:26,750 - INFO - [KD/stage2] gene=SMAD1 分片 1/1 kept_in_chunk=3684 kept_total=3684 rows 0-3795
2025-09-02 17:05:26,750 - INFO - [KD/stage2] gene=SMAD1 完成，保留细胞 3684/3796（0.12s）
2025-09-02 17:05:26,890 - INFO - [KD/stage2] gene=RUNX3 分片 1/1 kept_in_chunk=5285 kept_total=5285 rows 0-5323
2025-09-02 17:05:26,890 - INFO - [KD/stage2] gene=RUNX3 完成，保留细胞 5285/5324（0.14s）
2025-09-02 17:05:27,035 - INFO - [KD/stage2] gene=SMAD9 分片 1/1 kept_in_chunk=5809 kept_total=5809 rows 0-5866
2025-09-02 17:05:27,035 - INFO - [KD/stage2] gene=SMAD9 完成，保留细胞 5809/5867（0.15s）
2025-09-02 17:05:27,140 - INFO - [KD/stage2] gene=HDAC4 分片 1/1 kept_in_chunk=1232 kept_total=1232 rows 0-1452
2025-09-02 17:05:27,140 - INFO - [KD/stage2] gene=HDAC4 完成，保留细胞 1232/1453（0.10s）
2025-09-02 17:05:27,285 - INFO - [KD/stage2] gene=TGFBR1 分片 1/1 kept_in_chunk=5535 kept_total=5535 rows 0-5905
2025-09-02 17:05:27,285 - INFO - [KD/stage2] gene=TGFBR1 完成，保留细胞 5535/5906（0.15s）
2025-09-02 17:05:27,311 - INFO - [KD] 完成：保留 48,412/236,606 细胞 | 对照 9,809 | 通过基因 8 | 用时 7.54s
2025-09-02 17:05:27,505 - INFO - [FIX] materialize KD result (copy) to avoid sparse view assignment
2025-09-02 17:05:28,016 - INFO - Validating data integrity for: after_filter_count
2025-09-02 17:05:28,076 - INFO - Data validation passed: 48412 cells, 18080 genes
2025-09-02 17:05:28,076 - INFO - [PROCESS] 正在执行 log1p 转换...
2025-09-02 17:05:28,305 - INFO - [PROCESS] log1p 转换完成
2025-09-02 17:05:28,397 - INFO - [process_count_branch] End - Memory: 5.31 GB (Δ: +0.82 GB)
2025-09-02 17:05:28,397 - INFO - [standardize] Start - Memory: 5.31 GB
2025-09-02 17:05:28,397 - INFO - [standardize_names] Start - Memory: 5.31 GB
2025-09-02 17:05:28,397 - INFO - Standardizing: pert_col=gene, gene_col=gene_name
2025-09-02 17:05:28,397 - INFO - Control label: non-targeting -> non-targeting
2025-09-02 17:05:28,397 - INFO - Perturbation column type: <class 'pandas.core.series.Series'>, dtype: object
2025-09-02 17:05:28,399 - INFO - [STANDARDIZE] Checking for gene column: 'gene_name'
2025-09-02 17:05:28,399 - INFO - [STANDARDIZE] Available columns: ['gene_name', 'gene_id']
2025-09-02 17:05:28,399 - INFO - [STANDARDIZE] Found using method 'direct'
2025-09-02 17:05:28,542 - INFO - [standardize_names] End - Memory: 5.31 GB (Δ: +0.00 GB)
2025-09-02 17:05:28,613 - INFO - [standardize] End - Memory: 5.31 GB (Δ: +0.00 GB)
2025-09-02 17:05:28,613 - INFO - Validating data integrity for: final_validation
2025-09-02 17:05:28,673 - INFO - Data validation passed: 48412 cells, 18080 genes
2025-09-02 17:05:28,675 - INFO - [write_output] Start - Memory: 5.31 GB
2025-09-02 17:05:28,675 - INFO - [WRITE] 开始写入输出文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:05:28,675 - INFO - [WRITE] 最终数据维度: 48412 细胞 × 18080 基因
... storing 'orig.ident' as categorical
... storing 'sample' as categorical
... storing 'cell_type' as categorical
... storing 'pathway' as categorical
... storing 'sample_ID' as categorical
... storing 'Batch_info' as categorical
... storing 'bc1_well' as categorical
... storing 'bc2_well' as categorical
... storing 'bc3_well' as categorical
... storing 'guide' as categorical
... storing 'gene' as categorical
2025-09-02 17:05:38,049 - INFO - Successfully wrote /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:05:38,141 - INFO - [write_output] End - Memory: 5.31 GB (Δ: -0.00 GB)
2025-09-02 17:05:38,141 - INFO - Successfully processed Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:05:38,141 - INFO - Output: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-02 17:05:38,141 - INFO - Cells kept: 48412
2025-09-02 17:05:38,142 - INFO - Genes kept: 18080
2025-09-02 17:05:38,225 - INFO - ✓ Successfully processed Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-02 17:05:38,225 - INFO - ============================================================
2025-09-02 17:05:38,225 - INFO - PROCESSING SUMMARY
2025-09-02 17:05:38,225 - INFO - ============================================================
2025-09-02 17:05:38,226 - INFO - Total files: 1
2025-09-02 17:05:38,226 - INFO - Successful: 1
2025-09-02 17:05:38,226 - INFO - Failed: 0
2025-09-02 17:05:38,226 - INFO - ✓ 1 files processed successfully
2025-09-02 17:05:38,226 - INFO - 🎉 All files processed successfully!
