#!/usr/bin/env python3
"""
验证基因名称检测修复是否成功
"""

import sys
import os
from pathlib import Path
import logging

# 设置日志以便看到详细的检测信息
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_fix():
    """测试修复效果"""
    
    print("🧪 测试基因名称检测修复效果")
    print("=" * 60)
    
    # 导入修复后的模块
    try:
        import cellload_auto
        print("✅ 成功导入修复后的 cellload_auto")
    except Exception as e:
        print(f"❌ 导入 cellload_auto 失败: {e}")
        return False
    
    # 测试文件
    test_file = "/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.h5ad"
    output_dir = "test_fix_output"
    
    if not Path(test_file).exists():
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)
    
    print(f"📁 测试文件: {Path(test_file).name}")
    print(f"📁 输出目录: {output_dir}")
    
    try:
        print("\n🚀 开始测试处理...")
        print("注意观察日志中的 [GENE_CHECK] 和 [STANDARDIZE] 信息")
        print("-" * 60)
        
        result = cellload_auto.process_one_file_optimized(
            infile=Path(test_file),
            outdir=Path(output_dir),
            perturbation_column="gene",
            control_label="non-targeting",
            residual_expression=0.30,
            cell_residual_expression=0.50,
            min_cells=30,
            layer=None,
            aggregate_transcripts=False,
            gene_name_col="gene_name",
            chr_col="chr",
            start_col="start",
            end_col="end",
            min_overlap_frac=0.5,
            dataset_name="fix_test",
            norm_tol_abs=1.0,
            max_memory_gb=50.0,
            skip_normalization=False,  # 先测试 False
        )
        
        print("-" * 60)
        print(f"✅ 测试完成，返回码: {result}")
        
        if result == 0:
            print("🎉 修复验证成功！")
            return True
        else:
            print(f"⚠️ 处理返回非零码: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    print("🔧 基因名称检测修复验证")
    print("=" * 60)
    
    success = test_fix()
    
    if success:
        print("\n✅ 修复验证成功！")
        print("现在应该看到详细的基因检测日志，而不是警告信息。")
    else:
        print("\n❌ 修复验证失败！")
        print("请检查修复是否正确应用。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
