#!/usr/bin/env python3
"""
诊断 K562_gwps_raw_singlecell_01.aligned.h5ad 的数据类型问题
"""

import scanpy as sc
import numpy as np
import scipy.sparse as sp
import sys

def diagnose_file():
    """诊断文件的数据类型问题"""
    
    file_path = "/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Replogle/K562_gwps_raw_singlecell_01.aligned.h5ad"
    
    print("🔍 诊断数据类型问题")
    print("=" * 60)
    
    try:
        print("📁 加载文件头信息...")
        # 只加载基本信息，不加载完整数据
        adata = sc.read_h5ad(file_path, backed='r')
        
        print(f"✅ 文件基本信息:")
        print(f"   维度: {adata.n_obs} × {adata.n_vars}")
        print(f"   X 类型: {type(adata.X)}")
        
        if hasattr(adata.X, 'dtype'):
            print(f"   X dtype: {adata.X.dtype}")
        
        if sp.issparse(adata.X):
            print(f"   稀疏矩阵格式: {adata.X.format}")
            print(f"   非零元素数: {adata.X.nnz:,}")
            
            # 检查关键的数据类型
            if hasattr(adata.X, 'data'):
                print(f"   data dtype: {adata.X.data.dtype}")
                print(f"   data 范围: {adata.X.data.min()} ~ {adata.X.data.max()}")
            
            if hasattr(adata.X, 'indices'):
                print(f"   indices dtype: {adata.X.indices.dtype}")
                print(f"   indices 最大值: {adata.X.indices.max():,}")
                print(f"   int32 最大值: {np.iinfo(np.int32).max:,}")
                
                # 关键检查：indices 是否超出 int32 范围
                if adata.X.indices.max() > np.iinfo(np.int32).max:
                    print(f"   ❌ 问题发现：indices 超出 int32 范围！")
                else:
                    print(f"   ✅ indices 在 int32 范围内")
            
            if hasattr(adata.X, 'indptr'):
                print(f"   indptr dtype: {adata.X.indptr.dtype}")
                print(f"   indptr 最大值: {adata.X.indptr.max():,}")
                
                # 关键检查：indptr 是否超出 int32 范围
                if adata.X.indptr.max() > np.iinfo(np.int32).max:
                    print(f"   ❌ 问题发现：indptr 超出 int32 范围！")
                else:
                    print(f"   ✅ indptr 在 int32 范围内")
        
        # 检查数据密度
        if sp.issparse(adata.X):
            density = adata.X.nnz / (adata.n_obs * adata.n_vars)
            print(f"   数据密度: {density:.6f} ({density*100:.4f}%)")
        
        print(f"\n🧮 内存估算:")
        if sp.issparse(adata.X):
            # 估算内存使用
            data_bytes = adata.X.nnz * 4  # float32
            indices_bytes = adata.X.nnz * 8  # 当前可能是 int64
            indptr_bytes = (adata.n_obs + 1) * 8  # 当前可能是 int64
            total_gb = (data_bytes + indices_bytes + indptr_bytes) / (1024**3)
            
            print(f"   当前估算内存: {total_gb:.2f} GB")
            
            # 如果转换为 int32
            indices_bytes_int32 = adata.X.nnz * 4
            indptr_bytes_int32 = (adata.n_obs + 1) * 4
            total_gb_int32 = (data_bytes + indices_bytes_int32 + indptr_bytes_int32) / (1024**3)
            
            print(f"   转换为 int32 后: {total_gb_int32:.2f} GB")
            print(f"   内存节省: {total_gb - total_gb_int32:.2f} GB")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()

def suggest_solution():
    """提供解决方案建议"""
    
    print("\n" + "=" * 60)
    print("🛠️ 解决方案建议")
    print("=" * 60)
    
    print("基于诊断结果，可能的解决方案：")
    print()
    print("1. **修改数据类型转换逻辑**：")
    print("   - 检查数据范围后再决定是否转换为 int32")
    print("   - 对于超出范围的数据，保持原始类型")
    print()
    print("2. **增加错误处理**：")
    print("   - 在 safe_sparse_conversion 中添加范围检查")
    print("   - 提供更详细的错误信息")
    print()
    print("3. **使用替代策略**：")
    print("   - 对于超大文件，跳过数据类型优化")
    print("   - 使用更大的内存限制")

if __name__ == "__main__":
    diagnose_file()
    suggest_solution()
