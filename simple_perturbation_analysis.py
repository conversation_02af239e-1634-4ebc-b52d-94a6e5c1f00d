#!/usr/bin/env python3
"""
简化的扰动效率分析脚本
专门处理您指定的三个数据集
"""

import pandas as pd
import numpy as np
import scanpy as sc
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def analyze_perturbation_efficiency():
    """分析扰动效率"""
    
    print("🔬 简化的扰动效率分析")
    print("=" * 60)
    
    # 数据文件路径
    data_files = {
        'Replogle_original': '/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Replogle/K562_essential_raw_singlecell_01.aligned.h5ad',
        'Compass_batch': '/data/vcc/compass_loaded/colab_like/K562_essential_Training.h5ad'
    }
    
    # 输出目录
    output_dir = Path('./simple_analysis')
    output_dir.mkdir(exist_ok=True)
    
    # 加载数据
    adatas = {}
    for name, path in data_files.items():
        print(f"加载 {name}...")
        try:
            adata = sc.read_h5ad(path)
            adatas[name] = adata
            print(f"  成功: {adata.n_obs:,} 细胞, {adata.n_vars:,} 基因")
            print(f"  obs列: {list(adata.obs.columns)}")
        except Exception as e:
            print(f"  失败: {e}")
    
    if len(adatas) < 2:
        print("❌ 需要至少2个数据集进行比较")
        return
    
    # 分析每个数据集的扰动列
    print(f"\n📊 扰动信息分析:")
    perturbation_info = {}
    
    for name, adata in adatas.items():
        print(f"\n{name}:")
        
        # 查找扰动列
        possible_pert_cols = []
        for col in adata.obs.columns:
            if any(keyword in col.lower() for keyword in ['gene', 'target', 'pert', 'guide']):
                possible_pert_cols.append(col)
        
        print(f"  可能的扰动列: {possible_pert_cols}")
        
        if possible_pert_cols:
            pert_col = possible_pert_cols[0]  # 使用第一个找到的列
            pert_counts = adata.obs[pert_col].value_counts()
            
            # 找到对照组
            control_names = []
            for ctrl_name in ['non-targeting', 'control', 'ctrl', 'negative']:
                if ctrl_name in pert_counts.index:
                    control_names.append(ctrl_name)
            
            print(f"  扰动列: {pert_col}")
            print(f"  对照组: {control_names}")
            print(f"  总扰动数: {len(pert_counts)}")
            print(f"  前5个扰动:")
            for pert, count in pert_counts.head(5).items():
                print(f"    {pert}: {count:,} 细胞")
            
            perturbation_info[name] = {
                'pert_col': pert_col,
                'control_names': control_names,
                'pert_counts': pert_counts
            }
    
    # 找到共同的扰动
    print(f"\n🔍 寻找共同扰动:")
    
    if len(perturbation_info) >= 2:
        dataset_names = list(perturbation_info.keys())
        
        # 获取每个数据集中细胞数>=100的扰动
        valid_perts = {}
        for name in dataset_names:
            perts = perturbation_info[name]['pert_counts']
            controls = perturbation_info[name]['control_names']
            # 过滤：细胞数>=100且不是对照组
            valid = perts[(perts >= 100) & (~perts.index.isin(controls))].index.tolist()
            valid_perts[name] = set(valid)
            print(f"  {name}: {len(valid)} 个有效扰动")
        
        # 找共同扰动
        common_perts = set.intersection(*valid_perts.values())
        print(f"  共同扰动: {len(common_perts)} 个")
        
        if common_perts:
            # 选择前5个进行分析
            selected_perts = list(common_perts)[:5]
            print(f"  选择分析: {selected_perts}")
            
            # 分析这些扰动的效率
            analyze_selected_perturbations(adatas, perturbation_info, selected_perts, output_dir)
        else:
            print("  ❌ 没有找到共同扰动")
    
    print(f"\n✅ 分析完成，结果保存在: {output_dir}")

def analyze_selected_perturbations(adatas, pert_info, perturbations, output_dir):
    """分析选定的扰动"""
    
    print(f"\n📈 分析选定扰动的单细胞效率...")
    
    results = {}
    
    for pert in perturbations:
        print(f"\n分析扰动: {pert}")
        results[pert] = {}
        
        for dataset_name, adata in adatas.items():
            print(f"  数据集: {dataset_name}")
            
            try:
                # 获取扰动和对照信息
                pert_col = pert_info[dataset_name]['pert_col']
                control_names = pert_info[dataset_name]['control_names']
                
                # 检查目标基因是否在数据中
                if pert not in adata.var_names:
                    print(f"    跳过: 基因 {pert} 不在 var_names 中")
                    continue
                
                # 获取细胞掩码
                pert_mask = adata.obs[pert_col] == pert
                ctrl_mask = adata.obs[pert_col].isin(control_names)
                
                if pert_mask.sum() == 0:
                    print(f"    跳过: 没有 {pert} 扰动细胞")
                    continue
                
                if ctrl_mask.sum() == 0:
                    print(f"    跳过: 没有对照细胞")
                    continue
                
                # 获取基因表达数据
                gene_idx = adata.var_names.get_loc(pert)
                
                # 安全地提取表达数据
                try:
                    if hasattr(adata.X, 'toarray'):
                        # 稀疏矩阵
                        pert_expr = adata.X[pert_mask, gene_idx].toarray().flatten()
                        ctrl_expr = adata.X[ctrl_mask, gene_idx].toarray().flatten()
                    else:
                        # 密集矩阵
                        pert_expr = adata.X[pert_mask, gene_idx].flatten()
                        ctrl_expr = adata.X[ctrl_mask, gene_idx].flatten()
                except Exception as e:
                    print(f"    跳过: 数据提取失败 - {e}")
                    continue
                
                # 计算效率
                ctrl_mean = np.mean(ctrl_expr)
                
                # 判断数据类型并计算效率
                if np.max(adata.X) < 20:  # 可能是log转换数据
                    efficiency = pert_expr - ctrl_mean
                else:  # 原始计数数据
                    efficiency = np.log2(pert_expr + 1) - np.log2(ctrl_mean + 1)
                
                # 存储结果
                results[pert][dataset_name] = {
                    'efficiency': efficiency,
                    'pert_expr': pert_expr,
                    'ctrl_expr': ctrl_expr,
                    'n_pert': len(pert_expr),
                    'n_ctrl': len(ctrl_expr),
                    'mean_eff': np.mean(efficiency),
                    'std_eff': np.std(efficiency)
                }
                
                print(f"    成功: {len(pert_expr)} 扰动细胞, {len(ctrl_expr)} 对照细胞")
                print(f"    平均效率: {np.mean(efficiency):.3f}")
                
            except Exception as e:
                print(f"    失败: {e}")
                continue
    
    # 生成图表
    create_efficiency_plots(results, output_dir)
    
    # 生成报告
    create_efficiency_report(results, output_dir)

def create_efficiency_plots(results, output_dir):
    """创建效率分布图"""
    
    print(f"\n📊 生成分布图...")
    
    for pert, pert_data in results.items():
        if not pert_data:
            continue
        
        print(f"  绘制 {pert}")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'扰动效率分析: {pert}', fontsize=16, fontweight='bold')
        
        # 准备数据
        plot_data = []
        for dataset, data in pert_data.items():
            for eff in data['efficiency']:
                plot_data.append({'Dataset': dataset, 'Efficiency': eff})
        
        if not plot_data:
            plt.close()
            continue
        
        df = pd.DataFrame(plot_data)
        
        # 1. 直方图
        ax1 = axes[0, 0]
        for dataset in pert_data.keys():
            data = pert_data[dataset]['efficiency']
            ax1.hist(data, alpha=0.6, label=dataset, bins=30, density=True)
        ax1.set_xlabel('扰动效率')
        ax1.set_ylabel('密度')
        ax1.set_title('效率分布直方图')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 箱线图
        ax2 = axes[0, 1]
        sns.boxplot(data=df, x='Dataset', y='Efficiency', ax=ax2)
        ax2.set_title('效率箱线图')
        ax2.grid(True, alpha=0.3)
        
        # 3. 小提琴图
        ax3 = axes[1, 0]
        sns.violinplot(data=df, x='Dataset', y='Efficiency', ax=ax3)
        ax3.set_title('效率小提琴图')
        ax3.grid(True, alpha=0.3)
        
        # 4. 统计信息
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        stats_text = f"统计摘要 - {pert}\n" + "="*30 + "\n\n"
        for dataset, data in pert_data.items():
            eff = data['efficiency']
            stats_text += f"{dataset}:\n"
            stats_text += f"  细胞数: {data['n_pert']:,}\n"
            stats_text += f"  平均: {np.mean(eff):.3f}\n"
            stats_text += f"  中位数: {np.median(eff):.3f}\n"
            stats_text += f"  标准差: {np.std(eff):.3f}\n"
            stats_text += f"  有效比例: {(eff < -0.1).sum()/len(eff)*100:.1f}%\n\n"
        
        ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        # 保存
        output_file = output_dir / f'efficiency_distribution_{pert}.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

def create_efficiency_report(results, output_dir):
    """创建详细报告"""
    
    print(f"\n📝 生成报告...")
    
    report = []
    report.append("=" * 80)
    report.append("单细胞扰动效率分析报告")
    report.append("=" * 80)
    report.append("")
    
    for pert, pert_data in results.items():
        if not pert_data:
            continue
        
        report.append(f"扰动: {pert}")
        report.append("-" * 50)
        
        for dataset, data in pert_data.items():
            eff = data['efficiency']
            report.append(f"\n数据集: {dataset}")
            report.append(f"  扰动细胞数: {data['n_pert']:,}")
            report.append(f"  对照细胞数: {data['n_ctrl']:,}")
            report.append(f"  平均效率: {np.mean(eff):.4f}")
            report.append(f"  中位数效率: {np.median(eff):.4f}")
            report.append(f"  标准差: {np.std(eff):.4f}")
            report.append(f"  效率范围: [{np.min(eff):.3f}, {np.max(eff):.3f}]")
            
            # 有效扰动比例
            effective_ratio = (eff < -0.1).sum() / len(eff) * 100
            report.append(f"  有效扰动比例: {effective_ratio:.1f}%")
        
        report.append("")
    
    # 保存报告
    report_file = output_dir / 'perturbation_efficiency_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print(f"报告保存到: {report_file}")

if __name__ == "__main__":
    analyze_perturbation_efficiency()
